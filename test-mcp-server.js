import { spawn } from 'child_process';

// Start the MCP server
const server = spawn('cmd', [
  '/c',
  'npx',
  '-y',
  '@supabase/mcp-server-supabase@latest',
  '--access-token',
  '********************************************'
], {
  stdio: ['pipe', 'pipe', 'pipe']
});

let responseBuffer = '';

server.stdout.on('data', (data) => {
  responseBuffer += data.toString();
  
  // Try to parse JSON responses
  const lines = responseBuffer.split('\n');
  for (const line of lines) {
    if (line.trim()) {
      try {
        const response = JSON.parse(line.trim());
        console.log('Server response:', JSON.stringify(response, null, 2));
      } catch (e) {
        // Not valid JSON, continue
      }
    }
  }
});

server.stderr.on('data', (data) => {
  console.error('Server error:', data.toString());
});

// Initialize the server
const initMessage = {
  jsonrpc: "2.0",
  id: 1,
  method: "initialize",
  params: {
    protocolVersion: "2024-11-05",
    capabilities: { tools: {} },
    clientInfo: { name: "test-client", version: "1.0.0" }
  }
};

console.log('Sending initialize message...');
server.stdin.write(JSON.stringify(initMessage) + '\n');

// Wait a bit then list tools
setTimeout(() => {
  const listToolsMessage = {
    jsonrpc: "2.0",
    id: 2,
    method: "tools/list"
  };
  
  console.log('Sending tools/list message...');
  server.stdin.write(JSON.stringify(listToolsMessage) + '\n');
}, 2000);

// Wait a bit then try to list projects
setTimeout(() => {
  const listProjectsMessage = {
    jsonrpc: "2.0",
    id: 3,
    method: "tools/call",
    params: {
      name: "list_projects",
      arguments: {}
    }
  };
  
  console.log('Sending list_projects call...');
  server.stdin.write(JSON.stringify(listProjectsMessage) + '\n');
}, 4000);

// Clean up after 10 seconds
setTimeout(() => {
  console.log('Terminating server...');
  server.kill();
  process.exit(0);
}, 10000);