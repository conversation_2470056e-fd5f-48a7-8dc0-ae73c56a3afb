/**
 * Tenant Theme System
 * Customizable themes for multi-tenant applications
 * Phase 3: UI/UX Enhancement
 */

import { BaseTheme, lightTheme, darkTheme, themeUtils } from '../base/BaseTheme';
import { baseColors, colorUtils, type ColorToken } from '../../tokens/colors';
import { fontFamily, type FontFamily } from '../../tokens/typography';

export interface TenantBranding {
  name: string;
  logo: {
    light: string;
    dark: string;
    favicon: string;
    width?: number;
    height?: number;
  };
  colors: {
    primary: ColorToken;
    secondary?: ColorToken;
    accent?: ColorToken;
  };
  typography: {
    fontFamily: string[];
    headingFontFamily?: string[];
    displayFontFamily?: string[];
  };
  customCSS?: string;
  assets: {
    backgroundImage?: string;
    patterns?: string[];
    illustrations?: string[];
  };
}

export interface TenantThemeConfig {
  id: string;
  name: string;
  branding: TenantBranding;
  baseTheme: 'light' | 'dark';
  customizations: {
    borderRadius?: 'sharp' | 'rounded' | 'pill';
    shadows?: 'minimal' | 'normal' | 'elevated';
    animations?: 'reduced' | 'normal' | 'enhanced';
    density?: 'compact' | 'normal' | 'comfortable';
  };
  rtl?: boolean;
  locale?: string;
}

export interface TenantTheme extends BaseTheme {
  tenant: TenantThemeConfig;
  branding: TenantBranding;
}

/**
 * Tenant Theme Manager
 */
export class TenantThemeManager {
  private themes: Map<string, TenantTheme> = new Map();
  private currentTheme: TenantTheme | null = null;
  private observers: Set<(theme: TenantTheme) => void> = new Set();

  /**
   * Create tenant theme from configuration
   */
  createTenantTheme(config: TenantThemeConfig): TenantTheme {
    const baseTheme = config.baseTheme === 'dark' ? darkTheme : lightTheme;
    
    // Apply customizations
    const customizedTheme = this.applyCustomizations(baseTheme, config);
    
    // Apply branding
    const brandedTheme = this.applyBranding(customizedTheme, config.branding);
    
    const tenantTheme: TenantTheme = {
      ...brandedTheme,
      name: `${config.name}-${config.baseTheme}`,
      tenant: config,
      branding: config.branding,
    };

    this.themes.set(config.id, tenantTheme);
    return tenantTheme;
  }

  /**
   * Apply customizations to base theme
   */
  private applyCustomizations(baseTheme: BaseTheme, config: TenantThemeConfig): BaseTheme {
    let customizedTheme = { ...baseTheme };

    // Border radius customization
    if (config.customizations.borderRadius) {
      customizedTheme = this.applyBorderRadiusStyle(customizedTheme, config.customizations.borderRadius);
    }

    // Shadow customization
    if (config.customizations.shadows) {
      customizedTheme = this.applyShadowStyle(customizedTheme, config.customizations.shadows);
    }

    // Density customization
    if (config.customizations.density) {
      customizedTheme = this.applyDensityStyle(customizedTheme, config.customizations.density);
    }

    return customizedTheme;
  }

  /**
   * Apply branding to theme
   */
  private applyBranding(theme: BaseTheme, branding: TenantBranding): BaseTheme {
    const brandedTheme = { ...theme };

    // Apply primary color
    if (branding.colors.primary) {
      brandedTheme.colors = {
        ...brandedTheme.colors,
        interactive: {
          ...brandedTheme.colors.interactive,
          primary: branding.colors.primary[600],
          primaryHover: branding.colors.primary[700],
          primaryActive: branding.colors.primary[800],
        },
        border: {
          ...brandedTheme.colors.border,
          focus: branding.colors.primary[500],
        },
        text: {
          ...brandedTheme.colors.text,
          link: branding.colors.primary[600],
        },
      };
    }

    // Apply typography
    if (branding.typography.fontFamily) {
      brandedTheme.typography = {
        ...brandedTheme.typography,
        fontFamily: {
          ...brandedTheme.typography.fontFamily,
          sans: branding.typography.fontFamily,
          display: branding.typography.displayFontFamily || branding.typography.fontFamily,
        },
      };
    }

    return brandedTheme;
  }

  /**
   * Apply border radius style
   */
  private applyBorderRadiusStyle(theme: BaseTheme, style: 'sharp' | 'rounded' | 'pill'): BaseTheme {
    const radiusMultipliers = {
      sharp: 0,
      rounded: 1,
      pill: 2,
    };

    const multiplier = radiusMultipliers[style];
    
    return {
      ...theme,
      borderRadius: {
        none: '0',
        xs: `${0.125 * multiplier}rem`,
        sm: `${0.25 * multiplier}rem`,
        md: `${0.375 * multiplier}rem`,
        lg: `${0.5 * multiplier}rem`,
        xl: `${0.75 * multiplier}rem`,
        '2xl': `${1 * multiplier}rem`,
        '3xl': `${1.5 * multiplier}rem`,
        full: style === 'pill' ? '9999px' : `${2 * multiplier}rem`,
      },
    };
  }

  /**
   * Apply shadow style
   */
  private applyShadowStyle(theme: BaseTheme, style: 'minimal' | 'normal' | 'elevated'): BaseTheme {
    const shadowStyles = {
      minimal: {
        xs: '0 1px 2px 0 rgba(0, 0, 0, 0.02)',
        sm: '0 1px 3px 0 rgba(0, 0, 0, 0.05)',
        md: '0 2px 4px 0 rgba(0, 0, 0, 0.05)',
        lg: '0 4px 6px 0 rgba(0, 0, 0, 0.05)',
        xl: '0 8px 15px 0 rgba(0, 0, 0, 0.05)',
        '2xl': '0 15px 25px 0 rgba(0, 0, 0, 0.1)',
        inner: 'inset 0 1px 2px 0 rgba(0, 0, 0, 0.03)',
        none: 'none',
      },
      normal: theme.shadows,
      elevated: {
        xs: '0 2px 4px 0 rgba(0, 0, 0, 0.1)',
        sm: '0 4px 8px 0 rgba(0, 0, 0, 0.12)',
        md: '0 8px 16px 0 rgba(0, 0, 0, 0.15)',
        lg: '0 16px 32px 0 rgba(0, 0, 0, 0.15)',
        xl: '0 24px 48px 0 rgba(0, 0, 0, 0.2)',
        '2xl': '0 32px 64px 0 rgba(0, 0, 0, 0.25)',
        inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.1)',
        none: 'none',
      },
    };

    return {
      ...theme,
      shadows: shadowStyles[style],
    };
  }

  /**
   * Apply density style
   */
  private applyDensityStyle(theme: BaseTheme, density: 'compact' | 'normal' | 'comfortable'): BaseTheme {
    const densityMultipliers = {
      compact: 0.75,
      normal: 1,
      comfortable: 1.25,
    };

    const multiplier = densityMultipliers[density];

    // Scale semantic spacing
    const scaledSemanticSpacing = { ...theme.semanticSpacing };
    
    Object.keys(scaledSemanticSpacing.component.padding).forEach(key => {
      const currentValue = scaledSemanticSpacing.component.padding[key as keyof typeof scaledSemanticSpacing.component.padding];
      const numericValue = parseFloat(currentValue);
      scaledSemanticSpacing.component.padding[key as keyof typeof scaledSemanticSpacing.component.padding] = 
        `${numericValue * multiplier}rem`;
    });

    return {
      ...theme,
      semanticSpacing: scaledSemanticSpacing,
    };
  }

  /**
   * Get tenant theme by ID
   */
  getTenantTheme(tenantId: string): TenantTheme | null {
    return this.themes.get(tenantId) || null;
  }

  /**
   * Set current tenant theme
   */
  setCurrentTheme(tenantId: string): boolean {
    const theme = this.getTenantTheme(tenantId);
    if (!theme) return false;

    this.currentTheme = theme;
    this.applyThemeToDocument(theme);
    this.notifyObservers(theme);
    
    return true;
  }

  /**
   * Get current tenant theme
   */
  getCurrentTheme(): TenantTheme | null {
    return this.currentTheme;
  }

  /**
   * Apply theme to document
   */
  private applyThemeToDocument(theme: TenantTheme): void {
    // Apply base theme
    themeUtils.applyTheme(theme);

    // Apply tenant-specific styles
    this.applyTenantStyles(theme);
    
    // Apply RTL if needed
    if (theme.tenant.rtl) {
      document.documentElement.setAttribute('dir', 'rtl');
      document.documentElement.classList.add('rtl');
    } else {
      document.documentElement.setAttribute('dir', 'ltr');
      document.documentElement.classList.remove('rtl');
    }

    // Set tenant attributes
    document.documentElement.setAttribute('data-tenant', theme.tenant.id);
    document.documentElement.setAttribute('data-tenant-name', theme.tenant.name);
  }

  /**
   * Apply tenant-specific styles
   */
  private applyTenantStyles(theme: TenantTheme): void {
    const root = document.documentElement;

    // Apply branding colors as CSS custom properties
    if (theme.branding.colors.primary) {
      Object.entries(theme.branding.colors.primary).forEach(([shade, color]) => {
        root.style.setProperty(`--brand-primary-${shade}`, color);
      });
    }

    if (theme.branding.colors.secondary) {
      Object.entries(theme.branding.colors.secondary).forEach(([shade, color]) => {
        root.style.setProperty(`--brand-secondary-${shade}`, color);
      });
    }

    // Apply custom CSS if provided
    if (theme.branding.customCSS) {
      this.injectCustomCSS(theme.branding.customCSS, theme.tenant.id);
    }

    // Apply font families
    if (theme.branding.typography.fontFamily) {
      root.style.setProperty('--font-family-brand', theme.branding.typography.fontFamily.join(', '));
    }
  }

  /**
   * Inject custom CSS
   */
  private injectCustomCSS(css: string, tenantId: string): void {
    const styleId = `tenant-styles-${tenantId}`;
    let styleElement = document.getElementById(styleId) as HTMLStyleElement;

    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = styleId;
      document.head.appendChild(styleElement);
    }

    styleElement.textContent = css;
  }

  /**
   * Subscribe to theme changes
   */
  subscribe(callback: (theme: TenantTheme) => void): () => void {
    this.observers.add(callback);
    
    return () => {
      this.observers.delete(callback);
    };
  }

  /**
   * Notify observers of theme change
   */
  private notifyObservers(theme: TenantTheme): void {
    this.observers.forEach(callback => callback(theme));
  }

  /**
   * Generate theme preview
   */
  generateThemePreview(config: TenantThemeConfig): {
    colors: Record<string, string>;
    typography: Record<string, string>;
    spacing: Record<string, string>;
  } {
    const theme = this.createTenantTheme(config);
    
    return {
      colors: {
        primary: theme.branding.colors.primary[500],
        secondary: theme.branding.colors.secondary?.[500] || theme.colors.interactive.secondary,
        background: theme.colors.background.primary,
        surface: theme.colors.surface.primary,
        text: theme.colors.text.primary,
      },
      typography: {
        fontFamily: theme.branding.typography.fontFamily.join(', '),
        headingFont: theme.branding.typography.headingFontFamily?.join(', ') || theme.branding.typography.fontFamily.join(', '),
      },
      spacing: {
        borderRadius: theme.borderRadius.md,
        shadow: theme.shadows.md,
      },
    };
  }

  /**
   * Export theme configuration
   */
  exportThemeConfig(tenantId: string): TenantThemeConfig | null {
    const theme = this.getTenantTheme(tenantId);
    return theme ? theme.tenant : null;
  }

  /**
   * Import theme configuration
   */
  importThemeConfig(config: TenantThemeConfig): TenantTheme {
    return this.createTenantTheme(config);
  }

  /**
   * Clear all themes
   */
  clearThemes(): void {
    this.themes.clear();
    this.currentTheme = null;
  }
}

// Export singleton instance
export const tenantThemeManager = new TenantThemeManager();
