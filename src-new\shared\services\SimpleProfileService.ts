/**
 * Simple Profile Service
 * Handles user profile operations with direct database access
 */

import { supabase } from '../lib/supabase';

export interface SimpleProfileUpdateData {
  name?: string;
  phone?: string;
}

export class SimpleProfileService {
  /**
   * Update user profile using the most reliable method
   */
  static async updateProfile(userId: string, data: SimpleProfileUpdateData): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    try {
      console.log('SimpleProfileService: Updating profile for user:', userId, 'with data:', data);

      // Method 1: Try using the simple RPC function first
      try {
        const { data: rpcResult, error: rpcError } = await supabase.rpc('simple_update_user_profile', {
          p_user_id: userId,
          p_name: data.name || null,
          p_phone: data.phone || null
        });

        if (!rpcError && rpcResult) {
          console.log('Simple RPC update successful');

          // Fetch the updated user data
          const { data: updatedUser, error: fetchError } = await supabase
            .from('users')
            .select('id, name, email, phone, updated_at')
            .eq('id', userId)
            .single();

          if (!fetchError && updatedUser) {
            return {
              success: true,
              data: updatedUser
            };
          }
        }

        console.log('Simple RPC method failed, trying direct approach...');
      } catch (rpcError) {
        console.log('Simple RPC method not available, using direct approach...');
      }

      // Method 2: Use direct update with minimal data
      const updateData: any = {};

      if (data.name !== undefined) {
        updateData.name = data.name;
      }

      if (data.phone !== undefined) {
        updateData.phone = data.phone;
      }

      // Only proceed if there's something to update
      if (Object.keys(updateData).length === 0) {
        return {
          success: true,
          data: { message: 'No changes to update' }
        };
      }

      updateData.updated_at = new Date().toISOString();

      console.log('Attempting direct update with minimal data:', updateData);

      // Use the simplest possible update query
      const { data: updatedUser, error: updateError } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', userId)
        .select('id, name, email, phone, updated_at')
        .single();

      if (updateError) {
        console.error('Direct update failed:', updateError);
        return {
          success: false,
          error: `Update failed: ${updateError.message}`
        };
      }

      console.log('Profile updated successfully via direct method:', updatedUser);

      return {
        success: true,
        data: updatedUser
      };
    } catch (error) {
      console.error('Unexpected error updating profile:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred'
      };
    }
  }

  /**
   * Get user profile
   */
  static async getProfile(userId: string): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, name, email, phone, updated_at')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        return {
          success: false,
          error: error.message || 'Failed to fetch profile'
        };
      }

      return {
        success: true,
        data
      };
    } catch (error) {
      console.error('Unexpected error fetching profile:', error);
      return {
        success: false,
        error: 'An unexpected error occurred'
      };
    }
  }
}

export default SimpleProfileService;
