/**
 * Real-Time Bus Tracking Service
 * Phase 4: Core System Functionality Development
 */

import { supabase } from '../lib/supabase';
import { RealtimeChannel } from '@supabase/supabase-js';

export interface BusLocation {
  id: string;
  bus_id: string;
  latitude: number;
  longitude: number;
  speed: number;
  heading: number;
  accuracy: number;
  timestamp: string;
  tenant_id: string;
}

export interface TrackingConfig {
  updateInterval: number; // milliseconds
  maxAccuracy: number; // meters
  enableGeofencing: boolean;
  enableOfflineMode: boolean;
  autoReconnect: boolean;
}

export interface GeofenceAlert {
  id: string;
  bus_id: string;
  geofence_id: string;
  alert_type: 'enter' | 'exit' | 'dwell';
  location: {
    latitude: number;
    longitude: number;
  };
  timestamp: string;
}

export interface TrackingSubscription {
  busId: string;
  callback: (location: BusLocation) => void;
  errorCallback?: (error: Error) => void;
}

export class RealTimeTrackingService {
  private static instance: RealTimeTrackingService;
  private subscriptions = new Map<string, RealtimeChannel>();
  private trackingConfig: TrackingConfig = {
    updateInterval: 5000, // 5 seconds
    maxAccuracy: 50, // 50 meters
    enableGeofencing: true,
    enableOfflineMode: true,
    autoReconnect: true,
  };
  private offlineQueue: BusLocation[] = [];
  private isOnline = navigator.onLine;

  private constructor() {
    this.setupNetworkListeners();
  }

  static getInstance(): RealTimeTrackingService {
    if (!RealTimeTrackingService.instance) {
      RealTimeTrackingService.instance = new RealTimeTrackingService();
    }
    return RealTimeTrackingService.instance;
  }

  /**
   * Subscribe to real-time bus location updates
   */
  async subscribeToBusLocation(
    busId: string,
    callback: (location: BusLocation) => void,
    errorCallback?: (error: Error) => void
  ): Promise<void> {
    try {
      // Unsubscribe if already subscribed
      if (this.subscriptions.has(busId)) {
        await this.unsubscribeFromBusLocation(busId);
      }

      const channel = supabase
        .channel(`bus-location-${busId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'bus_locations',
            filter: `bus_id=eq.${busId}`,
          },
          (payload) => {
            const location = payload.new as BusLocation;
            callback(location);
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'bus_locations',
            filter: `bus_id=eq.${busId}`,
          },
          (payload) => {
            const location = payload.new as BusLocation;
            callback(location);
          }
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            console.log(`Subscribed to bus ${busId} location updates`);
          } else if (status === 'CHANNEL_ERROR') {
            errorCallback?.(new Error(`Failed to subscribe to bus ${busId}`));
          }
        });

      this.subscriptions.set(busId, channel);
    } catch (error) {
      console.error('Error subscribing to bus location:', error);
      errorCallback?.(error as Error);
    }
  }

  /**
   * Unsubscribe from bus location updates
   */
  async unsubscribeFromBusLocation(busId: string): Promise<void> {
    const channel = this.subscriptions.get(busId);
    if (channel) {
      await supabase.removeChannel(channel);
      this.subscriptions.delete(busId);
      console.log(`Unsubscribed from bus ${busId} location updates`);
    }
  }

  /**
   * Subscribe to multiple buses
   */
  async subscribeToMultipleBuses(
    busIds: string[],
    callback: (busId: string, location: BusLocation) => void,
    errorCallback?: (busId: string, error: Error) => void
  ): Promise<void> {
    for (const busId of busIds) {
      await this.subscribeToBusLocation(
        busId,
        (location) => callback(busId, location),
        (error) => errorCallback?.(busId, error)
      );
    }
  }

  /**
   * Update bus location (for drivers/tracking devices)
   */
  async updateBusLocation(
    busId: string,
    location: {
      latitude: number;
      longitude: number;
      speed?: number;
      heading?: number;
      accuracy?: number;
    },
    tenantId: string
  ): Promise<void> {
    try {
      const locationData: Partial<BusLocation> = {
        bus_id: busId,
        latitude: location.latitude,
        longitude: location.longitude,
        speed: location.speed || 0,
        heading: location.heading || 0,
        accuracy: location.accuracy || 0,
        timestamp: new Date().toISOString(),
        tenant_id: tenantId,
      };

      // Check accuracy threshold
      if (location.accuracy && location.accuracy > this.trackingConfig.maxAccuracy) {
        console.warn(`Location accuracy too low: ${location.accuracy}m`);
        return;
      }

      if (this.isOnline) {
        // Insert new location record
        const { error } = await supabase
          .from('bus_locations')
          .insert([locationData]);

        if (error) throw error;

        // Update bus last_location
        await supabase
          .from('buses')
          .update({
            last_location: `POINT(${location.longitude} ${location.latitude})`,
            last_updated: new Date().toISOString(),
          })
          .eq('id', busId);

        // Process offline queue if any
        await this.processOfflineQueue();
      } else if (this.trackingConfig.enableOfflineMode) {
        // Store in offline queue
        this.offlineQueue.push(locationData as BusLocation);
        console.log('Location stored offline, will sync when online');
      }
    } catch (error) {
      console.error('Error updating bus location:', error);
      
      // Store in offline queue if enabled
      if (this.trackingConfig.enableOfflineMode) {
        this.offlineQueue.push({
          bus_id: busId,
          latitude: location.latitude,
          longitude: location.longitude,
          speed: location.speed || 0,
          heading: location.heading || 0,
          accuracy: location.accuracy || 0,
          timestamp: new Date().toISOString(),
          tenant_id: tenantId,
        } as BusLocation);
      }
      
      throw error;
    }
  }

  /**
   * Get current bus location
   */
  async getCurrentBusLocation(busId: string): Promise<BusLocation | null> {
    try {
      const { data, error } = await supabase
        .from('bus_locations')
        .select('*')
        .eq('bus_id', busId)
        .order('timestamp', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      console.error('Error getting current bus location:', error);
      return null;
    }
  }

  /**
   * Get bus location history
   */
  async getBusLocationHistory(
    busId: string,
    startTime: string,
    endTime: string
  ): Promise<BusLocation[]> {
    try {
      const { data, error } = await supabase
        .from('bus_locations')
        .select('*')
        .eq('bus_id', busId)
        .gte('timestamp', startTime)
        .lte('timestamp', endTime)
        .order('timestamp', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting bus location history:', error);
      return [];
    }
  }

  /**
   * Subscribe to geofence alerts
   */
  async subscribeToGeofenceAlerts(
    tenantId: string,
    callback: (alert: GeofenceAlert) => void
  ): Promise<void> {
    if (!this.trackingConfig.enableGeofencing) return;

    try {
      const channel = supabase
        .channel(`geofence-alerts-${tenantId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'geofence_alerts',
            filter: `tenant_id=eq.${tenantId}`,
          },
          (payload) => {
            const alert = payload.new as GeofenceAlert;
            callback(alert);
          }
        )
        .subscribe();

      this.subscriptions.set(`geofence-${tenantId}`, channel);
    } catch (error) {
      console.error('Error subscribing to geofence alerts:', error);
    }
  }

  /**
   * Calculate distance between two points
   */
  calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Setup network listeners for offline mode
   */
  private setupNetworkListeners(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      console.log('Network connection restored');
      this.processOfflineQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      console.log('Network connection lost');
    });
  }

  /**
   * Process offline queue when connection is restored
   */
  private async processOfflineQueue(): Promise<void> {
    if (this.offlineQueue.length === 0) return;

    console.log(`Processing ${this.offlineQueue.length} offline location updates`);

    try {
      // Insert all queued locations
      const { error } = await supabase
        .from('bus_locations')
        .insert(this.offlineQueue);

      if (error) throw error;

      console.log('Offline queue processed successfully');
      this.offlineQueue = [];
    } catch (error) {
      console.error('Error processing offline queue:', error);
      // Keep the queue for next attempt
    }
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Update tracking configuration
   */
  updateConfig(config: Partial<TrackingConfig>): void {
    this.trackingConfig = { ...this.trackingConfig, ...config };
  }

  /**
   * Get current configuration
   */
  getConfig(): TrackingConfig {
    return { ...this.trackingConfig };
  }

  /**
   * Cleanup all subscriptions
   */
  async cleanup(): Promise<void> {
    for (const [key, channel] of this.subscriptions) {
      await supabase.removeChannel(channel);
    }
    this.subscriptions.clear();
    console.log('All tracking subscriptions cleaned up');
  }
}

export default RealTimeTrackingService;
