/**
 * مكون مؤشر قوة كلمة المرور
 * Password Strength Indicator Component
 * 
 * المرحلة 1: تأمين الدخول والسياسات الأمنية الأساسية
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCircle, XCircle, AlertCircle, Shield, Eye, EyeOff } from 'lucide-react';
import { PasswordStrengthValidator, PasswordStrengthResult } from '../../utils/passwordStrength';

interface PasswordStrengthIndicatorProps {
  password: string;
  showDetails?: boolean;
  className?: string;
}

export const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  password,
  showDetails = true,
  className = ''
}) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  // فحص قوة كلمة المرور
  const strengthResult: PasswordStrengthResult = React.useMemo(() => {
    if (!password) {
      return {
        score: 0,
        level: 'very-weak',
        feedback: [],
        isValid: false,
        requirements: {
          minLength: false,
          hasUppercase: false,
          hasLowercase: false,
          hasNumbers: false,
          hasSpecialChars: false,
          noCommonPatterns: false
        }
      };
    }
    return PasswordStrengthValidator.validatePassword(password);
  }, [password]);

  // ألوان مؤشر القوة
  const getProgressColor = (level: PasswordStrengthResult['level']) => {
    const colors = {
      'very-weak': 'bg-red-500',
      'weak': 'bg-orange-500',
      'fair': 'bg-yellow-500',
      'good': 'bg-green-400',
      'strong': 'bg-green-600'
    };
    return colors[level];
  };

  // عرض أيقونة المتطلب
  const RequirementIcon: React.FC<{ met: boolean }> = ({ met }) => {
    return met ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <XCircle className="w-4 h-4 text-red-500" />
    );
  };

  if (!password) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* مؤشر القوة الرئيسي */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 space-x-reverse">
            <Shield className="w-4 h-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              قوة كلمة المرور
            </span>
          </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            <span className="text-sm font-medium" style={{ color: PasswordStrengthValidator.getStrengthColor(strengthResult.level) }}>
              {PasswordStrengthValidator.getStrengthText(strengthResult.level)}
            </span>
            <span className="text-xs text-gray-500">
              {strengthResult.score}/100
            </span>
          </div>
        </div>

        {/* شريط التقدم */}
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(strengthResult.level)}`}
            style={{ width: `${strengthResult.score}%` }}
          />
        </div>
      </div>

      {/* تفاصيل المتطلبات */}
      {showDetails && (
        <div className="space-y-3">
          {/* المتطلبات الأساسية */}
          <div className="grid grid-cols-1 gap-2">
            <div className="flex items-center space-x-2 space-x-reverse text-sm">
              <RequirementIcon met={strengthResult.requirements.minLength} />
              <span className={strengthResult.requirements.minLength ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                8 أحرف على الأقل
              </span>
            </div>

            <div className="flex items-center space-x-2 space-x-reverse text-sm">
              <RequirementIcon met={strengthResult.requirements.hasUppercase} />
              <span className={strengthResult.requirements.hasUppercase ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                حرف كبير واحد على الأقل (A-Z)
              </span>
            </div>

            <div className="flex items-center space-x-2 space-x-reverse text-sm">
              <RequirementIcon met={strengthResult.requirements.hasLowercase} />
              <span className={strengthResult.requirements.hasLowercase ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                حرف صغير واحد على الأقل (a-z)
              </span>
            </div>

            <div className="flex items-center space-x-2 space-x-reverse text-sm">
              <RequirementIcon met={strengthResult.requirements.hasNumbers} />
              <span className={strengthResult.requirements.hasNumbers ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                رقم واحد على الأقل (0-9)
              </span>
            </div>

            <div className="flex items-center space-x-2 space-x-reverse text-sm">
              <RequirementIcon met={strengthResult.requirements.hasSpecialChars} />
              <span className={strengthResult.requirements.hasSpecialChars ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                رمز خاص واحد على الأقل (!@#$%^&*)
              </span>
            </div>

            <div className="flex items-center space-x-2 space-x-reverse text-sm">
              <RequirementIcon met={strengthResult.requirements.noCommonPatterns} />
              <span className={strengthResult.requirements.noCommonPatterns ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                تجنب الكلمات الشائعة
              </span>
            </div>
          </div>

          {/* ملاحظات وتحسينات */}
          {strengthResult.feedback.length > 0 && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3">
              <div className="flex items-start space-x-2 space-x-reverse">
                <AlertCircle className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    نصائح لتحسين كلمة المرور:
                  </p>
                  <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    {strengthResult.feedback.map((feedback, index) => (
                      <li key={index} className="flex items-start space-x-1 space-x-reverse">
                        <span className="text-blue-500 mt-1">•</span>
                        <span>{feedback}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* رسالة النجاح */}
          {strengthResult.isValid && strengthResult.score >= 80 && (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-3">
              <div className="flex items-center space-x-2 space-x-reverse">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <p className="text-sm font-medium text-green-800 dark:text-green-200">
                  ممتاز! كلمة المرور قوية وآمنة
                </p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * مكون حقل كلمة المرور مع مؤشر القوة
 */
interface PasswordInputWithStrengthProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  autoComplete?: string;
  className?: string;
  showStrengthDetails?: boolean;
  label?: string;
  error?: string;
}

export const PasswordInputWithStrength: React.FC<PasswordInputWithStrengthProps> = ({
  value,
  onChange,
  placeholder = '••••••••',
  required = false,
  autoComplete = 'new-password',
  className = '',
  showStrengthDetails = true,
  label = 'كلمة المرور',
  error
}) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const { i18n } = useTranslation();

  return (
    <div className={`space-y-3 ${className}`}>
      {/* حقل كلمة المرور */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </label>
        <div className="relative">
          <input
            type={showPassword ? 'text' : 'password'}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
            autoComplete={autoComplete}
            required={required}
            className={`w-full px-3 py-2 pr-10 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 ${
              error 
                ? 'border-red-300 dark:border-red-600' 
                : 'border-gray-300 dark:border-gray-600'
            }`}
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
          >
            {showPassword ? (
              <EyeOff className="w-4 h-4" />
            ) : (
              <Eye className="w-4 h-4" />
            )}
          </button>
        </div>
        {error && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{error}</p>
        )}
      </div>

      {/* مؤشر القوة */}
      {value && (
        <PasswordStrengthIndicator 
          password={value} 
          showDetails={showStrengthDetails}
        />
      )}
    </div>
  );
};
