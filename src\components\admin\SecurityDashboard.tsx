/**
 * Security Dashboard Component
 * لوحة تحكم الأمان - المرحلة الأولى
 * 
 * تتضمن:
 * - عرض محاولات تسجيل الدخول
 * - مراقبة الجلسات النشطة
 * - سجل العمليات الأمنية
 * - إحصائيات الأمان
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Shield, 
  AlertTriangle, 
  Users, 
  Activity, 
  Eye, 
  Ban, 
  Clock,
  TrendingUp,
  RefreshCw,
  Download,
  Filter
} from 'lucide-react';
import { Button } from '../ui/Button';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';

interface SecurityStats {
  totalLoginAttempts: number;
  failedLoginAttempts: number;
  successfulLogins: number;
  activeSessions: number;
  blockedIPs: number;
  highRiskEvents: number;
}

interface LoginAttempt {
  id: string;
  email: string;
  ip_address?: string;
  success: boolean;
  failure_reason?: string;
  attempted_at: string;
  device_info?: any;
}

interface SecurityEvent {
  id: string;
  user_id?: string;
  action: string;
  resource_type?: string;
  risk_score: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  ip_address?: string;
  metadata?: any;
}

export const SecurityDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [stats, setStats] = useState<SecurityStats>({
    totalLoginAttempts: 0,
    failedLoginAttempts: 0,
    successfulLogins: 0,
    activeSessions: 0,
    blockedIPs: 0,
    highRiskEvents: 0
  });
  const [recentAttempts, setRecentAttempts] = useState<LoginAttempt[]>([]);
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [timeFilter, setTimeFilter] = useState<'1h' | '24h' | '7d' | '30d'>('24h');

  useEffect(() => {
    if (user?.role === 'admin') {
      loadSecurityData();
    }
  }, [user, timeFilter]);

  const loadSecurityData = async () => {
    try {
      setIsLoading(true);
      setError('');

      // حساب التاريخ بناءً على الفلتر
      const now = new Date();
      const timeMap = {
        '1h': 1 * 60 * 60 * 1000,
        '24h': 24 * 60 * 60 * 1000,
        '7d': 7 * 24 * 60 * 60 * 1000,
        '30d': 30 * 24 * 60 * 60 * 1000
      };
      const startTime = new Date(now.getTime() - timeMap[timeFilter]);

      // تحميل محاولات تسجيل الدخول
      const { data: attempts, error: attemptsError } = await supabase
        .from('login_attempts')
        .select('*')
        .gte('attempted_at', startTime.toISOString())
        .order('attempted_at', { ascending: false })
        .limit(50);

      if (attemptsError) throw attemptsError;

      // تحميل الأحداث الأمنية
      const { data: events, error: eventsError } = await supabase
        .from('security_audit_logs')
        .select('*')
        .gte('timestamp', startTime.toISOString())
        .order('timestamp', { ascending: false })
        .limit(50);

      if (eventsError) throw eventsError;

      // تحميل الجلسات النشطة
      const { data: sessions, error: sessionsError } = await supabase
        .from('active_sessions')
        .select('*')
        .eq('is_active', true);

      if (sessionsError) throw sessionsError;

      // حساب الإحصائيات
      const totalAttempts = attempts?.length || 0;
      const failedAttempts = attempts?.filter(a => !a.success).length || 0;
      const successfulAttempts = attempts?.filter(a => a.success).length || 0;
      const highRiskEvents = events?.filter(e => e.risk_score > 70).length || 0;

      // حساب عناوين IP المحظورة (محاولات فاشلة > 5)
      const ipAttempts = new Map();
      attempts?.forEach(attempt => {
        if (!attempt.success && attempt.ip_address) {
          const count = ipAttempts.get(attempt.ip_address) || 0;
          ipAttempts.set(attempt.ip_address, count + 1);
        }
      });
      const blockedIPs = Array.from(ipAttempts.values()).filter(count => count >= 5).length;

      setStats({
        totalLoginAttempts: totalAttempts,
        failedLoginAttempts: failedAttempts,
        successfulLogins: successfulAttempts,
        activeSessions: sessions?.length || 0,
        blockedIPs,
        highRiskEvents
      });

      setRecentAttempts(attempts || []);
      setSecurityEvents(events || []);

    } catch (error) {
      console.error('Error loading security data:', error);
      setError('فشل في تحميل بيانات الأمان');
    } finally {
      setIsLoading(false);
    }
  };

  const exportSecurityReport = async () => {
    try {
      const reportData = {
        stats,
        recentAttempts: recentAttempts.slice(0, 100),
        securityEvents: securityEvents.slice(0, 100),
        generatedAt: new Date().toISOString(),
        timeFilter
      };

      const blob = new Blob([JSON.stringify(reportData, null, 2)], {
        type: 'application/json'
      });
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `security-report-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting report:', error);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('ar-SA');
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'high': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      default: return 'text-green-600 bg-green-100 dark:bg-green-900/20';
    }
  };

  const getRiskScoreColor = (score: number) => {
    if (score >= 80) return 'text-red-600';
    if (score >= 60) return 'text-orange-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-green-600';
  };

  if (user?.role !== 'admin') {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Ban className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">
            غير مصرح لك بالوصول إلى لوحة تحكم الأمان
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 space-x-reverse">
          <Shield className="w-8 h-8 text-blue-500" />
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            لوحة تحكم الأمان
          </h1>
        </div>
        
        <div className="flex items-center space-x-3 space-x-reverse">
          <select
            value={timeFilter}
            onChange={(e) => setTimeFilter(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="1h">آخر ساعة</option>
            <option value="24h">آخر 24 ساعة</option>
            <option value="7d">آخر 7 أيام</option>
            <option value="30d">آخر 30 يوم</option>
          </select>
          
          <Button
            onClick={exportSecurityReport}
            variant="outline"
            size="sm"
          >
            <Download className="w-4 h-4 mr-2" />
            تصدير التقرير
          </Button>
          
          <Button
            onClick={loadSecurityData}
            variant="outline"
            size="sm"
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
        </div>
      </div>

      {error && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-800 dark:text-red-200">{error}</p>
        </div>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                محاولات تسجيل الدخول
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {stats.totalLoginAttempts}
              </p>
            </div>
            <Activity className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                محاولات فاشلة
              </p>
              <p className="text-2xl font-bold text-red-600">
                {stats.failedLoginAttempts}
              </p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                الجلسات النشطة
              </p>
              <p className="text-2xl font-bold text-green-600">
                {stats.activeSessions}
              </p>
            </div>
            <Users className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                عناوين IP محظورة
              </p>
              <p className="text-2xl font-bold text-orange-600">
                {stats.blockedIPs}
              </p>
            </div>
            <Ban className="w-8 h-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                أحداث عالية المخاطر
              </p>
              <p className="text-2xl font-bold text-red-600">
                {stats.highRiskEvents}
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-red-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                معدل النجاح
              </p>
              <p className="text-2xl font-bold text-blue-600">
                {stats.totalLoginAttempts > 0 
                  ? Math.round((stats.successfulLogins / stats.totalLoginAttempts) * 100)
                  : 0}%
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-blue-500" />
          </div>
        </div>
      </div>

      {/* Recent Login Attempts */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            محاولات تسجيل الدخول الأخيرة
          </h2>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  البريد الإلكتروني
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  عنوان IP
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  الوقت
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  سبب الفشل
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {recentAttempts.slice(0, 10).map((attempt) => (
                <tr key={attempt.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {attempt.email}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {attempt.ip_address || 'غير معروف'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      attempt.success 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200'
                        : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-200'
                    }`}>
                      {attempt.success ? 'نجح' : 'فشل'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {formatTimestamp(attempt.attempted_at)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {attempt.failure_reason || '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};
