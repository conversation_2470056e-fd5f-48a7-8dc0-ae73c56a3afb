-- إنشاء سياسات RLS
-- Create RLS Policies

-- سياسة سياسة قراءة جدول المستأجرين (المدارس)
CREATE POLICY "tenants_select_policy" ON tenants
  FOR SELECT
  USING ((get_current_user_role() = 'system_admin') OR
          (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة إدراج جدول المستأجرين (المدارس)
CREATE POLICY "tenants_insert_policy" ON tenants
  FOR INSERT
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة تحديث جدول المستأجرين (المدارس)
CREATE POLICY "tenants_update_policy" ON tenants
  FOR UPDATE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة حذف جدول المستأجرين (المدارس)
CREATE POLICY "tenants_delete_policy" ON tenants
  FOR DELETE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin'));

-- سياسة سياسة قراءة جدول المستخدمين
CREATE POLICY "users_select_policy" ON users
  FOR SELECT
  USING ((get_current_user_role() = 'system_admin') OR
          (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')) OR
          (id = auth.uid()));

-- سياسة سياسة إدراج جدول المستخدمين
CREATE POLICY "users_insert_policy" ON users
  FOR INSERT
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة تحديث جدول المستخدمين
CREATE POLICY "users_update_policy" ON users
  FOR UPDATE
  USING ((get_current_user_role() = 'system_admin') OR
        (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')) OR
        (id = auth.uid() AND get_current_user_role() IN ('parent', 'student', 'driver')));

-- سياسة سياسة حذف جدول المستخدمين
CREATE POLICY "users_delete_policy" ON users
  FOR DELETE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin'));

-- سياسة سياسة قراءة جدول الحافلات
CREATE POLICY "buses_select_policy" ON buses
  FOR SELECT
  USING ((get_current_user_role() = 'system_admin') OR
          (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')) OR
          (can_access_bus(id)));

-- سياسة سياسة إدراج جدول الحافلات
CREATE POLICY "buses_insert_policy" ON buses
  FOR INSERT
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة تحديث جدول الحافلات
CREATE POLICY "buses_update_policy" ON buses
  FOR UPDATE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة حذف جدول الحافلات
CREATE POLICY "buses_delete_policy" ON buses
  FOR DELETE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin'));

-- سياسة سياسة قراءة جدول الطلاب
CREATE POLICY "students_select_policy" ON students
  FOR SELECT
  USING ((get_current_user_role() = 'system_admin') OR
          (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')) OR
          (can_access_student(id)));

-- سياسة سياسة إدراج جدول الطلاب
CREATE POLICY "students_insert_policy" ON students
  FOR INSERT
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة تحديث جدول الطلاب
CREATE POLICY "students_update_policy" ON students
  FOR UPDATE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة حذف جدول الطلاب
CREATE POLICY "students_delete_policy" ON students
  FOR DELETE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin'));

-- سياسة سياسة قراءة جدول المسارات
CREATE POLICY "routes_select_policy" ON routes
  FOR SELECT
  USING ((get_current_user_role() = 'system_admin') OR
          (tenant_id = get_current_tenant_id()));

-- سياسة سياسة إدراج جدول المسارات
CREATE POLICY "routes_insert_policy" ON routes
  FOR INSERT
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة تحديث جدول المسارات
CREATE POLICY "routes_update_policy" ON routes
  FOR UPDATE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة حذف جدول المسارات
CREATE POLICY "routes_delete_policy" ON routes
  FOR DELETE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin'));

-- سياسة سياسة قراءة جدول الحضور
CREATE POLICY "attendance_select_policy" ON attendance
  FOR SELECT
  USING ((get_current_user_role() = 'system_admin') OR
          (tenant_id = get_current_tenant_id()));

-- سياسة سياسة إدراج جدول الحضور
CREATE POLICY "attendance_insert_policy" ON attendance
  FOR INSERT
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة تحديث جدول الحضور
CREATE POLICY "attendance_update_policy" ON attendance
  FOR UPDATE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة حذف جدول الحضور
CREATE POLICY "attendance_delete_policy" ON attendance
  FOR DELETE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin'));

-- سياسة سياسة قراءة جدول الإشعارات
CREATE POLICY "notifications_select_policy" ON notifications
  FOR SELECT
  USING ((get_current_user_role() = 'system_admin') OR
          (tenant_id = get_current_tenant_id()));

-- سياسة سياسة إدراج جدول الإشعارات
CREATE POLICY "notifications_insert_policy" ON notifications
  FOR INSERT
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة تحديث جدول الإشعارات
CREATE POLICY "notifications_update_policy" ON notifications
  FOR UPDATE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة حذف جدول الإشعارات
CREATE POLICY "notifications_delete_policy" ON notifications
  FOR DELETE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin'));

-- سياسة سياسة قراءة جدول سجلات المراجعة
CREATE POLICY "audit_logs_select_policy" ON audit_logs
  FOR SELECT
  USING ((get_current_user_role() = 'system_admin') OR
          (tenant_id = get_current_tenant_id()));

-- سياسة سياسة إدراج جدول سجلات المراجعة
CREATE POLICY "audit_logs_insert_policy" ON audit_logs
  FOR INSERT
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة تحديث جدول سجلات المراجعة
CREATE POLICY "audit_logs_update_policy" ON audit_logs
  FOR UPDATE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة حذف جدول سجلات المراجعة
CREATE POLICY "audit_logs_delete_policy" ON audit_logs
  FOR DELETE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin'));

