import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Users,
  UserCheck,
  UserX,
  TrendingUp,
  TrendingDown,
  Calendar,
  Clock,
  AlertTriangle,
  BarChart3,
} from "lucide-react";
import { supabase } from "../../lib/supabase";
import { useDatabase } from "../../contexts/DatabaseContext";
import type { Tables } from "../../lib/api";

interface AttendanceMetrics {
  totalStudents: number;
  presentToday: number;
  absentToday: number;
  attendanceRate: number;
  weeklyTrend: number;
  frequentAbsentees: number;
  perfectAttendance: number;
  lateArrivals: number;
}

export const EnhancedAttendanceStats: React.FC = () => {
  const { t } = useTranslation();
  const { students } = useDatabase();
  const [metrics, setMetrics] = useState<AttendanceMetrics>({
    totalStudents: 0,
    presentToday: 0,
    absentToday: 0,
    attendanceRate: 0,
    weeklyTrend: 0,
    frequentAbsentees: 0,
    perfectAttendance: 0,
    lateArrivals: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAttendanceMetrics();
  }, [students]);

  const fetchAttendanceMetrics = async () => {
    try {
      setLoading(true);

      const today = new Date();
      const startOfToday = new Date(today);
      startOfToday.setHours(0, 0, 0, 0);

      const endOfToday = new Date(today);
      endOfToday.setHours(23, 59, 59, 999);

      // Get today's attendance
      const { data: todayAttendance } = await supabase
        .from("attendance")
        .select("student_id, recorded_at")
        .gte("recorded_at", startOfToday.toISOString())
        .lte("recorded_at", endOfToday.toISOString());

      // Get last week's attendance for trend calculation
      const lastWeekStart = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      lastWeekStart.setHours(0, 0, 0, 0);

      const { data: lastWeekAttendance } = await supabase
        .from("attendance")
        .select("student_id")
        .gte("recorded_at", lastWeekStart.toISOString())
        .lt("recorded_at", startOfToday.toISOString());

      // Get last month's data for frequent absentees
      const lastMonthStart = new Date(
        today.getTime() - 30 * 24 * 60 * 60 * 1000,
      );
      lastMonthStart.setHours(0, 0, 0, 0);

      const { data: monthlyAttendance } = await supabase
        .from("attendance")
        .select("student_id, recorded_at")
        .gte("recorded_at", lastMonthStart.toISOString())
        .lte("recorded_at", endOfToday.toISOString());

      // Calculate metrics
      const totalStudents = students.length;
      const uniquePresentToday = new Set(
        todayAttendance?.map((a) => a.student_id) || [],
      ).size;
      const absentToday = totalStudents - uniquePresentToday;
      const attendanceRate =
        totalStudents > 0 ? (uniquePresentToday / totalStudents) * 100 : 0;

      // Calculate weekly trend
      const lastWeekUnique = new Set(
        lastWeekAttendance?.map((a) => a.student_id) || [],
      ).size;
      const lastWeekRate =
        totalStudents > 0 ? (lastWeekUnique / (totalStudents * 7)) * 100 : 0;
      const weeklyTrend = attendanceRate - lastWeekRate;

      // Calculate frequent absentees (students with < 80% attendance in last month)
      const studentAttendanceMap = new Map<string, number>();
      monthlyAttendance?.forEach((record) => {
        const studentId = record.student_id;
        const date = new Date(record.recorded_at).toDateString();
        const key = `${studentId}-${date}`;
        studentAttendanceMap.set(key, (studentAttendanceMap.get(key) || 0) + 1);
      });

      const schoolDaysInMonth = 22; // Approximate school days in a month
      let frequentAbsentees = 0;
      let perfectAttendance = 0;

      students.forEach((student) => {
        const studentDays = Array.from(studentAttendanceMap.keys()).filter(
          (key) => key.startsWith(student.id),
        ).length;

        const attendanceRate = (studentDays / schoolDaysInMonth) * 100;

        if (attendanceRate < 80) {
          frequentAbsentees++;
        } else if (attendanceRate === 100) {
          perfectAttendance++;
        }
      });

      // Calculate late arrivals (attendance recorded after 8:00 AM)
      const lateArrivals =
        todayAttendance?.filter((record) => {
          const recordTime = new Date(record.recorded_at);
          const cutoffTime = new Date(recordTime);
          cutoffTime.setHours(8, 0, 0, 0);
          return recordTime > cutoffTime;
        }).length || 0;

      setMetrics({
        totalStudents,
        presentToday: uniquePresentToday,
        absentToday,
        attendanceRate,
        weeklyTrend,
        frequentAbsentees,
        perfectAttendance,
        lateArrivals,
      });
    } catch (error) {
      console.error("Error fetching attendance metrics:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 animate-pulse"
          >
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2" />
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded" />
          </div>
        ))}
      </div>
    );
  }

  const statCards = [
    {
      title: t("students.totalStudents"),
      value: metrics.totalStudents,
      icon: <Users size={24} />,
      color: "blue",
      bgColor: "bg-blue-100 dark:bg-blue-900/20",
      textColor: "text-blue-600 dark:text-blue-400",
    },
    {
      title: t("students.presentToday"),
      value: metrics.presentToday,
      icon: <UserCheck size={24} />,
      color: "green",
      bgColor: "bg-green-100 dark:bg-green-900/20",
      textColor: "text-green-600 dark:text-green-400",
      percentage: metrics.attendanceRate,
    },
    {
      title: t("students.absentToday"),
      value: metrics.absentToday,
      icon: <UserX size={24} />,
      color: "red",
      bgColor: "bg-red-100 dark:bg-red-900/20",
      textColor: "text-red-600 dark:text-red-400",
    },
    {
      title: t("students.attendanceRate"),
      value: `${metrics.attendanceRate.toFixed(1)}%`,
      icon:
        metrics.weeklyTrend >= 0 ? (
          <TrendingUp size={24} />
        ) : (
          <TrendingDown size={24} />
        ),
      color: metrics.weeklyTrend >= 0 ? "green" : "red",
      bgColor:
        metrics.weeklyTrend >= 0
          ? "bg-green-100 dark:bg-green-900/20"
          : "bg-red-100 dark:bg-red-900/20",
      textColor:
        metrics.weeklyTrend >= 0
          ? "text-green-600 dark:text-green-400"
          : "text-red-600 dark:text-red-400",
      trend: metrics.weeklyTrend,
    },
    {
      title: t("students.frequentAbsentees"),
      value: metrics.frequentAbsentees,
      icon: <AlertTriangle size={24} />,
      color: "orange",
      bgColor: "bg-orange-100 dark:bg-orange-900/20",
      textColor: "text-orange-600 dark:text-orange-400",
    },
    {
      title: t("students.perfectAttendance"),
      value: metrics.perfectAttendance,
      icon: <BarChart3 size={24} />,
      color: "purple",
      bgColor: "bg-purple-100 dark:bg-purple-900/20",
      textColor: "text-purple-600 dark:text-purple-400",
    },
    {
      title: t("students.lateArrivals"),
      value: metrics.lateArrivals,
      icon: <Clock size={24} />,
      color: "yellow",
      bgColor: "bg-yellow-100 dark:bg-yellow-900/20",
      textColor: "text-yellow-600 dark:text-yellow-400",
    },
    {
      title: t("students.weeklyTrend"),
      value: `${metrics.weeklyTrend >= 0 ? "+" : ""}${metrics.weeklyTrend.toFixed(1)}%`,
      icon:
        metrics.weeklyTrend >= 0 ? (
          <TrendingUp size={24} />
        ) : (
          <TrendingDown size={24} />
        ),
      color: metrics.weeklyTrend >= 0 ? "green" : "red",
      bgColor:
        metrics.weeklyTrend >= 0
          ? "bg-green-100 dark:bg-green-900/20"
          : "bg-red-100 dark:bg-red-900/20",
      textColor:
        metrics.weeklyTrend >= 0
          ? "text-green-600 dark:text-green-400"
          : "text-red-600 dark:text-red-400",
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          {t("dashboard.attendanceOverview")}
        </h2>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {t("dashboard.lastUpdated")}: {new Date().toLocaleTimeString()}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statCards.map((card, index) => (
          <div
            key={index}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                  {card.title}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {card.value}
                </p>
                {card.percentage && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {card.percentage.toFixed(1)}% {t("common.of")}{" "}
                    {metrics.totalStudents}
                  </p>
                )}
                {card.trend !== undefined && (
                  <p className={`text-xs mt-1 ${card.textColor}`}>
                    {card.trend >= 0 ? "↗" : "↘"}{" "}
                    {Math.abs(card.trend).toFixed(1)}%{" "}
                    {t("dashboard.fromLastWeek")}
                  </p>
                )}
              </div>
              <div className={`p-3 rounded-lg ${card.bgColor}`}>
                <div className={card.textColor}>{card.icon}</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
          {t("dashboard.quickActions")}
        </h3>
        <div className="flex flex-wrap gap-2">
          <button className="px-3 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-md text-sm hover:bg-blue-200 dark:hover:bg-blue-900/40 transition-colors">
            {t("students.viewAttendanceReport")}
          </button>
          <button className="px-3 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-md text-sm hover:bg-green-200 dark:hover:bg-green-900/40 transition-colors">
            {t("students.exportData")}
          </button>
          <button className="px-3 py-1 bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 rounded-md text-sm hover:bg-orange-200 dark:hover:bg-orange-900/40 transition-colors">
            {t("students.contactAbsentees")}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EnhancedAttendanceStats;
