/**
 * Services Index
 * Centralized export for all services
 * Phase 2: Application Structure Reorganization
 */

// Base Service
export { BaseService } from './base/BaseService';

// Data Services
export { UserService, userService } from './data/UserService';
export { SchoolService, schoolService } from './data/SchoolService';
export { BusService, busService } from './data/BusService';
export { RouteService, routeService } from './data/RouteService';
export { StudentService, studentService } from './data/StudentService';
export { AttendanceService, attendanceService } from './data/AttendanceService';

// Service Types
export type {
  UserListParams,
  CreateUserRequest,
  UpdateUserRequest,
} from './data/UserService';

export type {
  SchoolListParams,
} from './data/SchoolService';

export type {
  BusListParams,
} from './data/BusService';

export type {
  RouteListParams,
} from './data/RouteService';

export type {
  StudentListParams,
  CreateStudentRequest,
  UpdateStudentRequest,
} from './data/StudentService';

export type {
  AttendanceListParams,
  CreateAttendanceRequest,
  UpdateAttendanceRequest,
  AttendanceRecord,
  AttendanceStatus,
} from './data/AttendanceService';

/**
 * Service Factory
 * Creates service instances with custom configuration
 */
export class ServiceFactory {
  private static instance: ServiceFactory;
  private services: Map<string, any> = new Map();

  private constructor() {}

  static getInstance(): ServiceFactory {
    if (!ServiceFactory.instance) {
      ServiceFactory.instance = new ServiceFactory();
    }
    return ServiceFactory.instance;
  }

  /**
   * Get or create service instance
   */
  getService<T>(serviceClass: new (...args: any[]) => T, config?: any): T {
    const serviceName = serviceClass.name;
    
    if (!this.services.has(serviceName)) {
      this.services.set(serviceName, new serviceClass(config));
    }
    
    return this.services.get(serviceName);
  }

  /**
   * Clear all service instances
   */
  clearServices(): void {
    this.services.clear();
  }

  /**
   * Set authentication token for all services
   */
  setAuthToken(token: string): void {
    this.services.forEach(service => {
      if (service.setAuthToken) {
        service.setAuthToken(token);
      }
    });
  }

  /**
   * Remove authentication token from all services
   */
  removeAuthToken(): void {
    this.services.forEach(service => {
      if (service.removeAuthToken) {
        service.removeAuthToken();
      }
    });
  }

  /**
   * Clear cache for all services
   */
  clearAllCaches(): void {
    this.services.forEach(service => {
      if (service.clearCache) {
        service.clearCache();
      }
    });
  }
}

/**
 * Default service instances
 */
export const services = {
  user: userService,
  school: schoolService,
  bus: busService,
  route: routeService,
  student: studentService,
  attendance: attendanceService,
};

/**
 * Service manager for global operations
 */
export const serviceManager = {
  /**
   * Initialize all services with auth token
   */
  initialize(token: string): void {
    Object.values(services).forEach(service => {
      if (service.setAuthToken) {
        service.setAuthToken(token);
      }
    });
  },

  /**
   * Clear all service caches
   */
  clearCaches(): void {
    Object.values(services).forEach(service => {
      if (service.clearCache) {
        service.clearCache();
      }
    });
  },

  /**
   * Remove auth tokens from all services
   */
  logout(): void {
    Object.values(services).forEach(service => {
      if (service.removeAuthToken) {
        service.removeAuthToken();
      }
    });
  },

  /**
   * Health check for all services
   */
  async healthCheck(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};
    
    for (const [name, service] of Object.entries(services)) {
      try {
        if (service.healthCheck) {
          const response = await service.healthCheck();
          results[name] = response.success;
        } else {
          results[name] = true; // Assume healthy if no health check method
        }
      } catch (error) {
        results[name] = false;
      }
    }
    
    return results;
  },
};

/**
 * Service configuration
 */
export interface ServiceConfig {
  baseURL?: string;
  timeout?: number;
  retries?: number;
  headers?: Record<string, string>;
}

/**
 * Configure all services
 */
export function configureServices(config: ServiceConfig): void {
  Object.values(services).forEach(service => {
    if (service.config) {
      service.config = { ...service.config, ...config };
    }
  });
}

/**
 * Service hooks for React components
 */
export const useServices = () => {
  return {
    services,
    serviceManager,
    configureServices,
  };
};

// Re-export commonly used types
export type {
  APIResponse,
  PaginatedResponse,
  PaginationParams,
  User,
  School,
  Bus,
  Route,
  Student,
  UserRole,
  BusStatus,
} from '../api/types';
