import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Bus,
  Search,
  MapPin,
  Clock,
  Users,
  Route,
  AlertTriangle,
} from "lucide-react";
import { Navbar } from "../../components/layout/Navbar";
import { Sidebar } from "../../components/layout/Sidebar";
import { Button } from "../../components/ui/Button";
import { LiveMap } from "../../components/map/LiveMap";
import { LiveBusTracking } from "../../components/buses/LiveBusTracking";
import { TrackingDashboard } from "../../components/tracking/TrackingDashboard";
import { LoadingFallback } from "../../components/debug/LoadingFallback";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import type { Tables } from "../../lib/api";

export const TrackingPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { buses, routes, loading, error, refreshData } = useDatabase();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedBus, setSelectedBus] = useState<Tables<"buses"> | null>(null);
  const [viewMode, setViewMode] = useState<"overview" | "detailed">("overview");

  console.log('🔍 TrackingPage render:', { loading, error, busesCount: buses.length, user: !!user });

  // Check if there's a specific bus ID in URL params
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const busId = urlParams.get("bus");
    if (busId) {
      const bus = buses.find((b) => b.id === busId);
      if (bus) {
        setSelectedBus(bus);
        setViewMode("detailed");
      }
    }
  }, [buses]);

  // Filter buses based on search query
  const filteredBuses = buses.filter((bus) => {
    if (!searchQuery) return true;
    return bus.plate_number.toLowerCase().includes(searchQuery.toLowerCase());
  });

  if (loading) {
    console.log('⏳ TrackingPage showing loading state');
    return (
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar />
          <main className="flex-1 overflow-y-auto p-4 md:p-6">
            <div className="max-w-7xl mx-auto">
              <LoadingFallback
                title={t('tracking.loading') || 'Loading Tracking Data'}
                description="Fetching bus locations and route information..."
                onRetry={refreshData}
                timeout={15000}
              />
            </div>
          </main>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar />
          <main className="flex-1 overflow-y-auto p-4 md:p-6">
            <div className="max-w-7xl mx-auto">
              <div className="text-center py-12">
                <AlertTriangle
                  size={48}
                  className="mx-auto mb-4 text-red-500"
                />
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  {t("common.error")}
                </h2>
                <p className="text-gray-600 dark:text-gray-400">{error}</p>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  console.log('✅ TrackingPage rendering successfully with data');

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />

        <main className="flex-1 overflow-hidden">
          {/* Use the new Professional Tracking Dashboard */}
          <TrackingDashboard />
        </main>
      </div>
    </div>
  );
};
