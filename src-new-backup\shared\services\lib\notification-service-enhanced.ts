/**
 * Enhanced Notification Service - خدمة الإشعارات المحسنة
 * يحل مشاكل منطق الإشعارات غير المكتمل وإضافة آلية إعادة المحاولة
 */

import { supabase } from './supabase';

export interface NotificationData {
  title: string;
  message: string;
  type: "geofence" | "attendance" | "maintenance" | "announcements" | "emergency" | "route_changes";
  tenantId: string;
  userIds?: string[];
  roleTargets?: string[];
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  scheduledFor?: string;
  metadata?: any;
}

export interface NotificationResult {
  success: boolean;
  notificationId?: string;
  error?: string;
  deliveredCount?: number;
  failedCount?: number;
}

export interface NotificationAnalytics {
  sent: number;
  delivered: number;
  clicked: number;
  failed: number;
}

export class EnhancedNotificationService {
  
  private static retryAttempts = 3;
  private static retryDelay = 1000; // 1 second
  private static maxBatchSize = 100;
  
  /**
   * إرسال إشعار مع إعادة المحاولة
   */
  static async sendNotificationWithRetry(
    data: NotificationData,
    attempt: number = 1
  ): Promise<NotificationResult> {
    
    try {
      // التحقق من صحة البيانات
      const validation = this.validateNotificationData(data);
      if (!validation.isValid) {
        return { 
          success: false, 
          error: validation.errors.join(', ') 
        };
      }
      
      // إرسال الإشعار
      const result = await this.sendNotification(data);
      
      if (result.success) {
        // تسجيل نجاح الإرسال
        await this.logNotificationEvent('sent', data, result.notificationId);
        return result;
      } else {
        throw new Error(result.error);
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      // إعادة المحاولة
      if (attempt < this.retryAttempts) {
        console.warn(`Notification send failed (attempt ${attempt}), retrying...`, errorMessage);
        
        // انتظار قبل إعادة المحاولة
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
        
        return this.sendNotificationWithRetry(data, attempt + 1);
      } else {
        // تسجيل فشل الإرسال
        await this.logNotificationEvent('failed', data, null, errorMessage);
        return { 
          success: false, 
          error: errorMessage 
        };
      }
    }
  }
  
  /**
   * التحقق من صحة بيانات الإشعار
   */
  private static validateNotificationData(data: NotificationData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!data.title || data.title.trim().length === 0) {
      errors.push('Notification title is required');
    } else if (data.title.length > 100) {
      errors.push('Notification title is too long (max 100 characters)');
    }
    
    if (!data.message || data.message.trim().length === 0) {
      errors.push('Notification message is required');
    } else if (data.message.length > 500) {
      errors.push('Notification message is too long (max 500 characters)');
    }
    
    if (!data.tenantId || data.tenantId.trim().length === 0) {
      errors.push('Tenant ID is required');
    }
    
    const validTypes = ["geofence", "attendance", "maintenance", "announcements", "emergency", "route_changes"];
    if (!validTypes.includes(data.type)) {
      errors.push('Invalid notification type');
    }
    
    if (data.userIds && data.userIds.length === 0) {
      errors.push('At least one user ID is required when userIds is specified');
    }
    
    if (data.userIds && data.userIds.length > this.maxBatchSize) {
      errors.push(`Too many recipients (max ${this.maxBatchSize})`);
    }
    
    const validPriorities = ['low', 'normal', 'high', 'urgent'];
    if (data.priority && !validPriorities.includes(data.priority)) {
      errors.push('Invalid priority level');
    }
    
    // التحقق من تاريخ الجدولة
    if (data.scheduledFor) {
      const scheduledDate = new Date(data.scheduledFor);
      if (isNaN(scheduledDate.getTime())) {
        errors.push('Invalid scheduled date format');
      } else if (scheduledDate < new Date()) {
        errors.push('Scheduled date cannot be in the past');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  /**
   * إرسال إشعار أساسي
   */
  private static async sendNotification(data: NotificationData): Promise<NotificationResult> {
    try {
      // إنشاء الإشعار في قاعدة البيانات
      const { data: notification, error: insertError } = await supabase
        .from('notifications')
        .insert({
          title: data.title,
          message: data.message,
          type: data.type,
          tenant_id: data.tenantId,
          priority: data.priority || 'normal',
          scheduled_for: data.scheduledFor || new Date().toISOString(),
          metadata: data.metadata,
          status: 'pending'
        })
        .select()
        .single();
        
      if (insertError) {
        throw new Error(`Failed to create notification: ${insertError.message}`);
      }
      
      // تحديد المستلمين
      const recipients = await this.determineRecipients(data);
      
      if (recipients.length === 0) {
        throw new Error('No valid recipients found');
      }
      
      // إرسال للمستلمين
      const deliveryResults = await this.deliverToRecipients(notification.id, recipients, data);
      
      // تحديث حالة الإشعار
      await supabase
        .from('notifications')
        .update({
          status: deliveryResults.failedCount > 0 ? 'partial' : 'sent',
          delivered_count: deliveryResults.deliveredCount,
          failed_count: deliveryResults.failedCount
        })
        .eq('id', notification.id);
      
      return {
        success: true,
        notificationId: notification.id,
        deliveredCount: deliveryResults.deliveredCount,
        failedCount: deliveryResults.failedCount
      };
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send notification';
      return {
        success: false,
        error: errorMessage
      };
    }
  }
  
  /**
   * تحديد المستلمين
   */
  private static async determineRecipients(data: NotificationData): Promise<string[]> {
    let recipients: string[] = [];
    
    // إذا تم تحديد مستخدمين محددين
    if (data.userIds && data.userIds.length > 0) {
      recipients = data.userIds;
    }
    // إذا تم تحديد أدوار
    else if (data.roleTargets && data.roleTargets.length > 0) {
      const { data: users, error } = await supabase
        .from('users')
        .select('id')
        .eq('tenant_id', data.tenantId)
        .in('role', data.roleTargets)
        .eq('is_active', true);
        
      if (!error && users) {
        recipients = users.map(user => user.id);
      }
    }
    // إرسال لجميع المستخدمين في المؤسسة
    else {
      const { data: users, error } = await supabase
        .from('users')
        .select('id')
        .eq('tenant_id', data.tenantId)
        .eq('is_active', true);
        
      if (!error && users) {
        recipients = users.map(user => user.id);
      }
    }
    
    return recipients;
  }
  
  /**
   * توصيل الإشعارات للمستلمين
   */
  private static async deliverToRecipients(
    notificationId: string,
    recipients: string[],
    data: NotificationData
  ): Promise<{ deliveredCount: number; failedCount: number }> {
    
    let deliveredCount = 0;
    let failedCount = 0;
    
    // تقسيم المستلمين إلى مجموعات
    const batches = this.chunkArray(recipients, this.maxBatchSize);
    
    for (const batch of batches) {
      try {
        // إنشاء سجلات التوصيل
        const deliveryRecords = batch.map(userId => ({
          notification_id: notificationId,
          user_id: userId,
          tenant_id: data.tenantId,
          status: 'pending',
          created_at: new Date().toISOString()
        }));
        
        const { error: deliveryError } = await supabase
          .from('notification_deliveries')
          .insert(deliveryRecords);
          
        if (deliveryError) {
          console.error('Failed to create delivery records:', deliveryError);
          failedCount += batch.length;
          continue;
        }
        
        // إرسال Push Notifications (إذا كان متاحاً)
        await this.sendPushNotifications(batch, data);
        
        deliveredCount += batch.length;
        
      } catch (error) {
        console.error('Failed to deliver to batch:', error);
        failedCount += batch.length;
      }
    }
    
    return { deliveredCount, failedCount };
  }
  
  /**
   * إرسال Push Notifications
   */
  private static async sendPushNotifications(userIds: string[], data: NotificationData): Promise<void> {
    try {
      // الحصول على tokens الخاصة بالمستخدمين
      const { data: tokens, error } = await supabase
        .from('push_tokens')
        .select('token, user_id')
        .in('user_id', userIds)
        .eq('is_active', true);
        
      if (error || !tokens || tokens.length === 0) {
        console.warn('No push tokens found for users');
        return;
      }
      
      // إرسال عبر خدمة Push Notifications
      const pushPayload = {
        title: data.title,
        body: data.message,
        data: {
          type: data.type,
          notificationId: data.metadata?.notificationId,
          priority: data.priority || 'normal'
        }
      };
      
      // استخدام Edge Function لإرسال Push Notifications
      const { error: pushError } = await supabase.functions.invoke('send-push-notifications', {
        body: {
          tokens: tokens.map(t => t.token),
          payload: pushPayload
        }
      });
      
      if (pushError) {
        console.error('Failed to send push notifications:', pushError);
      }
      
    } catch (error) {
      console.error('Error in sendPushNotifications:', error);
    }
  }
  
  /**
   * تسجيل أحداث الإشعارات
   */
  private static async logNotificationEvent(
    eventType: 'sent' | 'failed' | 'delivered' | 'clicked',
    data: NotificationData,
    notificationId?: string,
    error?: string
  ): Promise<void> {
    try {
      await supabase.from('notification_analytics').insert({
        event_type: eventType,
        notification_id: notificationId,
        notification_type: data.type,
        tenant_id: data.tenantId,
        timestamp: new Date().toISOString(),
        metadata: {
          title: data.title,
          priority: data.priority,
          error: error
        }
      });
    } catch (logError) {
      console.error('Failed to log notification event:', logError);
    }
  }
  
  /**
   * الحصول على إحصائيات الإشعارات
   */
  static async getNotificationAnalytics(
    tenantId: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<NotificationAnalytics> {
    
    try {
      const { data, error } = await supabase.rpc('get_notification_analytics', {
        p_tenant_id: tenantId,
        p_date_from: dateFrom || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        p_date_to: dateTo || new Date().toISOString()
      });
      
      if (error) {
        console.error('Failed to get notification analytics:', error);
        return { sent: 0, delivered: 0, clicked: 0, failed: 0 };
      }
      
      return data || { sent: 0, delivered: 0, clicked: 0, failed: 0 };
      
    } catch (error) {
      console.error('Error in getNotificationAnalytics:', error);
      return { sent: 0, delivered: 0, clicked: 0, failed: 0 };
    }
  }
  
  /**
   * إرسال إشعار طارئ
   */
  static async sendEmergencyNotification(
    tenantId: string,
    title: string,
    message: string,
    metadata?: any
  ): Promise<NotificationResult> {
    
    const emergencyData: NotificationData = {
      title,
      message,
      type: 'emergency',
      tenantId,
      priority: 'urgent',
      metadata
    };
    
    // إرسال فوري بدون جدولة
    return this.sendNotificationWithRetry(emergencyData);
  }
  
  /**
   * إرسال إشعار تغيير المسار
   */
  static async sendRouteChangeNotification(
    tenantId: string,
    routeId: string,
    changeDetails: string,
    affectedStudentIds: string[]
  ): Promise<NotificationResult> {
    
    // الحصول على أولياء أمور الطلاب المتأثرين
    const { data: parents, error } = await supabase
      .from('students')
      .select('parent_id')
      .in('id', affectedStudentIds)
      .not('parent_id', 'is', null);
      
    if (error || !parents) {
      return { success: false, error: 'Failed to get affected parents' };
    }
    
    const parentIds = [...new Set(parents.map(p => p.parent_id))];
    
    const routeChangeData: NotificationData = {
      title: 'تغيير في مسار الحافلة',
      message: changeDetails,
      type: 'route_changes',
      tenantId,
      userIds: parentIds,
      priority: 'high',
      metadata: {
        routeId,
        affectedStudents: affectedStudentIds.length
      }
    };
    
    return this.sendNotificationWithRetry(routeChangeData);
  }
  
  /**
   * تقسيم المصفوفة إلى مجموعات
   */
  private static chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
  
  /**
   * تنظيف الإشعارات القديمة
   */
  static async cleanupOldNotifications(tenantId: string, daysToKeep: number = 90): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      
      await supabase
        .from('notifications')
        .delete()
        .eq('tenant_id', tenantId)
        .lt('created_at', cutoffDate.toISOString());
        
    } catch (error) {
      console.error('Failed to cleanup old notifications:', error);
    }
  }
}

export default EnhancedNotificationService;
