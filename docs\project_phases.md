المرحلة 1: تأمين الدخول والسياسات الأمنية الأساسية 🔐
الخطوات التفصيلية:

نموذج دخول آمن:

استخدام البريد الإلكتروني مع كلمة مرور قوية (≥ 8 رموز) مع فحص قوة كلمة المرور.

التحقق من القوة باستخدام خوارزمية تتحقق من التعقيد (حروف كبيرة، صغيرة، أرقام، رموز).

حماية ضد الهجمات:

حماية من هجمات brute-force عبر تقييد عدد المحاولات خلال فترة زمنية (مثلاً 5 محاولات خلال 15 دقيقة).

حماية CSRF باستخدام Laravel CSRF Tokens.

تقييد عدد المحاولات مع عمل تأخير متزايد بعد كل محاولة فاشلة.

التحقق الثنائي (2FA):

دعم 2FA عبر Google Authenticator (TOTP).

دعم إرسال رمز التحقق عبر البريد الإلكتروني أو SMS.

جعل 2FA إلزامي لأدوار: الأدمن، المدير.

نظام الجلسات:

استخدام Laravel Sanctum أو JWT.

توكنات قصيرة العمر مع إمكانية تجديد التوكن (Refresh Tokens).

ميزة تسجيل الخروج من كل الأجهزة (Logout from all devices).

إمكانية منع الدخول المتزامن (اختياري) — أي منع أكثر من جلسة واحدة بنفس الحساب.

سجل الدخول والتدقيق:

تسجيل معلومات كل محاولة تسجيل دخول (IP، نوع الجهاز، توقيت الدخول).

لوحة عرض للأدمن لمراجعة محاولات الدخول.

مراقبة السلوك الشاذ (Anomaly Detection):

كشف محاولات دخول غير منطقية (مثلاً من بلد مختلف فجأة، جهاز غير مألوف).

عند الكشف: إرسال إشعار، طلب تحقق 2FA إضافي، أو إغلاق الجلسة تلقائيًا.

دعم الدخول الموحد SSO (اختياري):

دمج تسجيل الدخول عبر Google, Microsoft, OAuth.

التوثيق:

كل هذه الخطوات سيتم توثيقها مفصلاً في ملف docs/changelog.md تحت عنوان "المرحلة 1: تأمين الدخول والسياسات الأمنية الأساسية".

المرحلة 2: تنظيف وإعادة تنظيم شامل للنظام 🧹
التفاصيل:

نسخ احتياطي كامل للبيانات:

أخذ نسخة احتياطية كاملة من قاعدة البيانات والملفات.

حفظ النسخ بشكل آمن مع تاريخ ووقت النسخة.

إزالة RLS والدوال القديمة:

مراجعة قاعدة البيانات لإزالة أي قواعد RLS قديمة غير مستخدمة.

إزالة الدوال المخزنة (Stored Procedures) القديمة غير الضرورية.

تنظيف الكود والواجهة من التكرار:

فحص كل ملفات الواجهة الأمامية والخلفية.

إزالة الأكواد المكررة أو غير المستخدمة.

إعادة هيكلة الكود بحيث يكون أكثر وضوحاً.

توثيق التغييرات:

تسجيل كل التعديلات التي تمت في docs/changelog.md ضمن المرحلة 2.

المرحلة 3: إعادة بناء صلاحيات النظام RLS + طبقة الصلاحيات 🧱
التفصيل:

Backend:

تحديث قواعد RLS لكل جدول في PostgreSQL، بحيث يتم تصفية البيانات بناءً على tenant_id، role، و user_id.

إنشاء Middleware مخصص في Laravel للتحقق من أدوار المستخدم قبل الوصول للموارد.

تسجيل تدقيق (Audit Logging) لكل عمليات CRUD، مع حفظ نسخة قبل وبعد التعديل.

Frontend:

تطوير Hook usePermissions() لإدارة الصلاحيات.

إضافة PermissionGuard لحماية الصفحات والعناصر بناءً على الدور.

دعم ميزة عرض الواجهة كدور مختلف (View As) لتجربة الصلاحيات المختلفة.

التوثيق:

توثيق كل هذه الخطوات في changelog تحت المرحلة 3.

المرحلة 4: بناء هيكل الأدوار والصلاحيات 🧑‍🤝‍🧑
التفصيل:

الدور	الصلاحيات
الأدمن	وصول شامل، إدارة كل المدارس، إدارة الصلاحيات
مدير المدرسة	CRUD على مدرسته فقط + التقارير الخاصة بها
المشرف	مشاهدة فقط داخل مدرسته
السائق	الوصول فقط لمساره وحافلته
ولي الأمر	مشاهدة بيانات الأبناء فقط

لا توجد صلاحيات افتراضية؛ يجب إعطاء الصلاحيات يدويًا أو عبر قالب معد مسبقًا.

بناء نموذج بيانات مرن يسمح بتحديث الصلاحيات بسهولة.

توثيق كامل في changelog تحت المرحلة 4.

المرحلة 5: تطوير نظام إحصائيات وتحليلات مرن 📊
التفصيل:

للأدمن:

إحصائيات المدارس النشطة.

نسبة تغطية المسارات.

عدد الحافلات قيد التشغيل.

الحضور العام.

لمدير المدرسة:

نسب الحضور.

تغطية خطوط السير.

تتبع مباشر للحافلات الخاصة.

التقنيات المستخدمة:

WebSocket و Redis لتحديث البيانات في الوقت الفعلي.

إمكانية تصدير البيانات بصيغ JSON و Excel.

دعم فلاتر متعددة للإحصائيات.

التوثيق الكامل في changelog المرحلة 5.

المرحلة 6: الترجمة والدعم اللغوي 🌐
التفصيل:

دعم i18n كامل (عربي/إنجليزي).

تبديل اتجاه الصفحة تلقائيًا بين RTL و LTR.

اختبار توافق الخطوط مثل Cairo مع النظام.

استخدام مفاتيح ترجمة موحدة.

توفير ملفات الترجمة بصيغة JSON قابلة للتصدير والاستيراد.

توثيق في changelog تحت المرحلة 6.

المرحلة 7: تنظيم الكود والهندسة المعمارية وإكمال قاعدة البيانات 🛠️🟠
التفصيل التفصيلي:

تحليل شامل لكود المشروع (Backend + Frontend):

مراجعة كل صفحات ومكونات الواجهة الأمامية.

فحص ملفات Controllers، Services، Repositories في الـ Backend.

تتبع جميع نقاط الاتصال (API Endpoints) والتأكد من تفعيلها.

تحديد الوظائف أو الحقول الناقصة أو غير المتصلة.

إكمال قاعدة البيانات وتحديثها:

التأكد من وجود الحقول الضرورية (مثل حالة الحافلة، إحداثيات التوقف، حالة الطالب).

إضافة جداول ناقصة مثل جدول الصيانة، سجل الحضور، نقاط التوقف.

تحديث العلاقات بين الجداول (One-to-Many، Many-to-Many، Constraints).

توثيق كل تعديل في ملف docs/changelog.sql.

تنظيم هيكل الكود المعماري (Refactor & Structure):

نقل منطق العمل إلى Service Layer موحدة.

بناء مصفوفة صلاحيات مركزية (Role-to-Permission Mapping) لتقليل التكرار.

فصل الأكواد الخاصة بكل لوحة (admin، school، driver) في مجلدات منفصلة:

dashboards/admin/

dashboards/school/

dashboards/driver/

اعتماد بنية واضحة للملفات مثل:

hooks/

guards/

helpers/

layouts/

تحسين التناسق وتوحيد التجربة:

توحيد إدارة الثيمات باستخدام Design Tokens (ألوان، خطوط، مساحات).

اختبار دعم RTL/LTR لجميع الصفحات.

عزل إعدادات كل مدرسة (شعار، ألوان، بيانات) بشكل مستقل.

التوثيق المفصل في docs/changelog.md تحت المرحلة 7.

المرحلة 8: دعم Multi-Tenant بشكل كامل 🗂️
عزل بيانات المدارس على جميع الطبقات: قاعدة البيانات، API، وواجهة المستخدم.

لوحات تحكم مستقلة لكل مدرسة مع تخصيص اسم، شعار، وألوان.

قوالب صلاحيات جاهزة عند إنشاء مدرسة جديدة.

فصل واجهات كل Tenant بشكل كامل لضمان عزل البيانات.

توثيق كامل في changelog المرحلة 8.

المرحلة 9: مكونات النظام التشغيلية 🚌
إدارة الطلاب، الحافلات، السائقين، المشرفين، ونقاط التوقف.

دعم حضور QR.

تتبع لحظي للحافلات عبر Mapbox + PostGIS.

نظام صيانة وإشعارات ذكية بناءً على الدور.

تسجيل حالات الدخول والخروج من الحافلة.

دعم رفع تقارير وتصدير بيانات وإحصائيات لحظية.

توثيق كامل في changelog المرحلة 9.

المرحلة 10: ميزات متقدمة اختيارية (Enhancements) 📈
محرر مصفوفة الصلاحيات بصري.

نظام تنبيهات ديناميكي حسب الدور.

مخططات تدفق الأدوار (Role Flow Diagrams).

إدارة الجلسات (عرض، حذف).

نظام نسخ احتياطي واستعادة مرن.

سجل تغييرات الصلاحيات + اختبارات وحدة RLS.

توثيق كامل في changelog المرحلة 10.

المرحلة 11: الاختبارات النهائية وضمان الجودة 🧪
اختبار كل صلاحية ودور وسيناريوهات الدخول.

اختبارات اختراق، أداء، وتوافق على أجهزة ومتصفحات متعددة.

Unit + Integration Tests لكل طبقة.

اختبار تغطية الترجمة، الصلاحيات، البيانات، والتقارير.

توثيق كامل في changelog المرحلة 11.

المرحلة 12: تجهيز للنشر والتسليم 🚀
إعداد وثائق الاستخدام والبرمجة.

تجهيز سكريبتات التثبيت والبيئة.

إرشادات الصيانة والدعم الفني.

مراجعة نهائية للتوثيق.

توثيق كامل في changelog المرحلة 12. 