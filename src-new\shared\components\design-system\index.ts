/**
 * Design System Index
 * Centralized export for the complete design system
 * Phase 3: UI/UX Enhancement
 */

// ============================================================================
// Design Tokens
// ============================================================================

// Colors
export {
  baseColors,
  lightSemanticColors,
  darkSemanticColors,
  colorUtils,
  type ColorToken,
  type ColorPalette,
  type SemanticColors,
} from './tokens/colors';

// Typography
export {
  fontFamily,
  fontSize,
  fontWeight,
  lineHeight,
  letterSpacing,
  typographyTokens,
  typographyUtils,
  type FontFamily,
  type FontSize,
  type FontWeight,
  type LineHeight,
  type LetterSpacing,
  type TypographyScale,
  type TypographyTokens,
} from './tokens/typography';

// Spacing
export {
  spacing,
  semanticSpacing,
  spacingUtils,
  type SpacingScale,
  type SemanticSpacing,
  type ResponsiveSpacing,
} from './tokens/spacing';

// ============================================================================
// Theme System
// ============================================================================

// Base Themes
export {
  lightTheme,
  darkTheme,
  themeUtils,
  shadows,
  borderRadius,
  zIndex,
  transitions,
  breakpoints,
  type BaseTheme,
  type ShadowTokens,
  type BorderRadiusTokens,
  type ZIndexTokens,
  type TransitionTokens,
  type BreakpointTokens,
} from './themes/base/BaseTheme';

// Tenant Themes
export {
  TenantThemeManager,
  tenantThemeManager,
  type TenantBranding,
  type TenantThemeConfig,
  type TenantTheme,
} from './themes/tenant/TenantTheme';

// ============================================================================
// Components
// ============================================================================

// Atoms
export {
  Button,
  ButtonGroup,
  IconButton,
  type ButtonProps,
  type ButtonGroupProps,
  type IconButtonProps,
} from './components/atoms/Button/Button';

// ============================================================================
// Design System Utilities
// ============================================================================

/**
 * Design System Configuration
 */
export interface DesignSystemConfig {
  theme: {
    default: 'light' | 'dark';
    respectSystemPreference: boolean;
    persistTheme: boolean;
  };
  tokens: {
    prefix: string;
    generateCSSCustomProperties: boolean;
  };
  components: {
    prefix: string;
    defaultProps: Record<string, any>;
  };
  rtl: {
    enabled: boolean;
    autoDetect: boolean;
    supportedLanguages: string[];
  };
}

/**
 * Default Design System Configuration
 */
export const defaultDesignSystemConfig: DesignSystemConfig = {
  theme: {
    default: 'light',
    respectSystemPreference: true,
    persistTheme: true,
  },
  tokens: {
    prefix: 'ds',
    generateCSSCustomProperties: true,
  },
  components: {
    prefix: 'DS',
    defaultProps: {},
  },
  rtl: {
    enabled: true,
    autoDetect: true,
    supportedLanguages: ['ar', 'he', 'fa', 'ur'],
  },
};

/**
 * Design System Manager
 */
export class DesignSystemManager {
  private config: DesignSystemConfig;
  private initialized: boolean = false;

  constructor(config: Partial<DesignSystemConfig> = {}) {
    this.config = { ...defaultDesignSystemConfig, ...config };
  }

  /**
   * Initialize the design system
   */
  initialize(): void {
    if (this.initialized) return;

    // Generate CSS custom properties
    if (this.config.tokens.generateCSSCustomProperties) {
      this.generateCSSCustomProperties();
    }

    // Set up theme system
    this.initializeThemeSystem();

    // Set up RTL support
    if (this.config.rtl.enabled) {
      this.initializeRTLSupport();
    }

    this.initialized = true;
  }

  /**
   * Generate CSS custom properties
   */
  private generateCSSCustomProperties(): void {
    const root = document.documentElement;
    const prefix = this.config.tokens.prefix;

    // Colors
    Object.entries(lightSemanticColors).forEach(([category, colors]) => {
      if (typeof colors === 'object') {
        Object.entries(colors).forEach(([key, value]) => {
          root.style.setProperty(`--${prefix}-color-${category}-${key}`, value);
        });
      }
    });

    // Spacing
    Object.entries(spacing).forEach(([key, value]) => {
      root.style.setProperty(`--${prefix}-spacing-${key}`, value);
    });

    // Typography
    Object.entries(fontSize).forEach(([key, value]) => {
      root.style.setProperty(`--${prefix}-font-size-${key}`, value);
    });

    // Shadows
    Object.entries(shadows).forEach(([key, value]) => {
      root.style.setProperty(`--${prefix}-shadow-${key}`, value);
    });

    // Border Radius
    Object.entries(borderRadius).forEach(([key, value]) => {
      root.style.setProperty(`--${prefix}-radius-${key}`, value);
    });
  }

  /**
   * Initialize theme system
   */
  private initializeThemeSystem(): void {
    const defaultTheme = this.config.theme.default === 'dark' ? darkTheme : lightTheme;
    themeUtils.applyTheme(defaultTheme);
  }

  /**
   * Initialize RTL support
   */
  private initializeRTLSupport(): void {
    if (this.config.rtl.autoDetect) {
      const browserLang = navigator.language || navigator.languages?.[0] || 'en';
      const langCode = browserLang.split('-')[0];
      
      if (this.config.rtl.supportedLanguages.includes(langCode)) {
        document.documentElement.setAttribute('dir', 'rtl');
        document.documentElement.classList.add('rtl');
      }
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): DesignSystemConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<DesignSystemConfig>): void {
    this.config = { ...this.config, ...updates };
    
    if (this.initialized) {
      // Re-initialize with new config
      this.initialized = false;
      this.initialize();
    }
  }

  /**
   * Check if design system is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }
}

/**
 * Global Design System Instance
 */
export const designSystem = new DesignSystemManager();

/**
 * Design System Hooks
 */
export const useDesignSystem = () => {
  return {
    designSystem,
    config: designSystem.getConfig(),
    isInitialized: designSystem.isInitialized(),
    initialize: () => designSystem.initialize(),
    updateConfig: (config: Partial<DesignSystemConfig>) => designSystem.updateConfig(config),
  };
};

/**
 * Design System Provider Props
 */
export interface DesignSystemProviderProps {
  children: React.ReactNode;
  config?: Partial<DesignSystemConfig>;
  autoInitialize?: boolean;
}

/**
 * Design System CSS Generator
 */
export const generateDesignSystemCSS = (config: Partial<DesignSystemConfig> = {}): string => {
  const mergedConfig = { ...defaultDesignSystemConfig, ...config };
  const prefix = mergedConfig.tokens.prefix;
  
  let css = ':root {\n';
  
  // Colors
  Object.entries(lightSemanticColors).forEach(([category, colors]) => {
    if (typeof colors === 'object') {
      Object.entries(colors).forEach(([key, value]) => {
        css += `  --${prefix}-color-${category}-${key}: ${value};\n`;
      });
    }
  });
  
  // Spacing
  Object.entries(spacing).forEach(([key, value]) => {
    css += `  --${prefix}-spacing-${key}: ${value};\n`;
  });
  
  // Typography
  Object.entries(fontSize).forEach(([key, value]) => {
    css += `  --${prefix}-font-size-${key}: ${value};\n`;
  });
  
  // Shadows
  Object.entries(shadows).forEach(([key, value]) => {
    css += `  --${prefix}-shadow-${key}: ${value};\n`;
  });
  
  // Border Radius
  Object.entries(borderRadius).forEach(([key, value]) => {
    css += `  --${prefix}-radius-${key}: ${value};\n`;
  });
  
  css += '}\n';
  
  // Dark theme
  css += '\n[data-theme="dark"] {\n';
  Object.entries(darkSemanticColors).forEach(([category, colors]) => {
    if (typeof colors === 'object') {
      Object.entries(colors).forEach(([key, value]) => {
        css += `  --${prefix}-color-${category}-${key}: ${value};\n`;
      });
    }
  });
  css += '}\n';
  
  // RTL support
  if (mergedConfig.rtl.enabled) {
    css += '\n[dir="rtl"] {\n';
    css += '  --text-align: right;\n';
    css += '  --start: right;\n';
    css += '  --end: left;\n';
    css += '}\n';
    
    css += '\n[dir="ltr"] {\n';
    css += '  --text-align: left;\n';
    css += '  --start: left;\n';
    css += '  --end: right;\n';
    css += '}\n';
  }
  
  return css;
};

/**
 * Design System Validation
 */
export const validateDesignSystem = (): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Check if required tokens are defined
  if (!baseColors.primary) {
    errors.push('Primary color palette is missing');
  }
  
  if (!fontFamily.sans || fontFamily.sans.length === 0) {
    errors.push('Sans-serif font family is missing');
  }
  
  if (!spacing[4]) {
    errors.push('Base spacing unit (4) is missing');
  }
  
  // Check for accessibility
  const contrastRatio = colorUtils.getContrastRatio(
    lightSemanticColors.text.primary,
    lightSemanticColors.background.primary
  );
  
  if (contrastRatio < 4.5) {
    warnings.push('Text contrast ratio may not meet WCAG AA standards');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

// ============================================================================
// Export All
// ============================================================================

export default {
  designSystem,
  DesignSystemManager,
  generateDesignSystemCSS,
  validateDesignSystem,
  defaultDesignSystemConfig,
};
