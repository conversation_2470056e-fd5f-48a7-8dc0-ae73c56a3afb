/**
 * Environment Variables Helper
 * Safe access to environment variables with fallbacks
 * Phase 3: UI/UX Enhancement - Environment
 */

/**
 * Get environment variable with fallback
 */
function getEnvVar(key: keyof ImportMetaEnv, fallback: string = ''): string {
  try {
    return import.meta.env[key] || fallback;
  } catch {
    return fallback;
  }
}

/**
 * Get boolean environment variable
 */
function getBooleanEnvVar(key: keyof ImportMetaEnv, fallback: boolean = false): boolean {
  try {
    const value = import.meta.env[key];
    return value === 'true' || value === '1';
  } catch {
    return fallback;
  }
}

/**
 * Environment configuration object
 */
export const env = {
  // Supabase
  supabase: {
    url: getEnvVar('VITE_SUPABASE_URL', 'https://pcavtwqvgnkgybzfqeuz.supabase.co'),
    anonKey: getEnvVar('VITE_SUPABASE_ANON_KEY', ''),
  },
  
  // API
  api: {
    url: getEnvVar('VITE_API_URL', 'http://localhost:3000/api'),
  },
  
  // App
  app: {
    name: getEnvVar('VITE_APP_NAME', 'School Bus Management System'),
    version: getEnvVar('VITE_APP_VERSION', '1.0.0'),
  },
  
  // Theme
  theme: {
    default: getEnvVar('VITE_DEFAULT_THEME', 'light') as 'light' | 'dark',
    customizationEnabled: getBooleanEnvVar('VITE_ENABLE_THEME_CUSTOMIZATION', true),
  },
  
  // Development
  dev: {
    toolsEnabled: getBooleanEnvVar('VITE_ENABLE_DEV_TOOLS', true),
    logLevel: getEnvVar('VITE_LOG_LEVEL', 'debug') as 'debug' | 'info' | 'warn' | 'error',
  },
  
  // Helper methods
  isDevelopment: () => {
    try {
      return import.meta.env.DEV;
    } catch {
      return false;
    }
  },
  
  isProduction: () => {
    try {
      return import.meta.env.PROD;
    } catch {
      return true;
    }
  },
};

/**
 * Validate required environment variables
 */
export function validateEnvironment(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Check required variables
  if (!env.supabase.url) {
    errors.push('VITE_SUPABASE_URL is required');
  }
  
  if (!env.supabase.anonKey) {
    errors.push('VITE_SUPABASE_ANON_KEY is required');
  }
  
  if (!env.api.url) {
    errors.push('VITE_API_URL is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Log environment configuration (safe for production)
 */
export function logEnvironmentInfo(): void {
  if (env.isDevelopment()) {
    console.group('🌍 Environment Configuration');
    console.log('App Name:', env.app.name);
    console.log('App Version:', env.app.version);
    console.log('API URL:', env.api.url);
    console.log('Default Theme:', env.theme.default);
    console.log('Theme Customization:', env.theme.customizationEnabled ? 'Enabled' : 'Disabled');
    console.log('Development Mode:', env.isDevelopment());
    console.groupEnd();
  }
}
