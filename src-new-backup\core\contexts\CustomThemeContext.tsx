/**
 * Custom Theme Context
 * Manages custom theme configurations from database
 * Phase 3: UI/UX Enhancement - Theme Management
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ThemeService, ThemeConfig } from '../services/ThemeService';
import { useAuth } from './AuthContext';

interface CustomThemeContextType {
  theme: ThemeConfig | null;
  setTheme: (theme: ThemeConfig) => void;
  loadTheme: () => Promise<void>;
  refreshTheme: () => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

const CustomThemeContext = createContext<CustomThemeContextType | undefined>(undefined);

export const CustomThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user, tenant } = useAuth();
  const [theme, setThemeState] = useState<ThemeConfig | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const setTheme = (newTheme: ThemeConfig) => {
    setThemeState(newTheme);
    ThemeService.applyTheme(newTheme);
  };

  const loadTheme = async () => {
    if (!user?.tenant_id) {
      console.log('No tenant ID, skipping theme load');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      console.log('Loading theme for tenant:', user.tenant_id);

      // Try to load from database first
      const dbTheme = await ThemeService.loadThemeFromDatabase(user.tenant_id);
      
      if (dbTheme) {
        console.log('✅ Theme loaded from database:', dbTheme);
        setThemeState(dbTheme);
        ThemeService.applyTheme(dbTheme);
      } else {
        console.log('No theme found in database, using default');
        // Fallback to localStorage or default
        const localTheme = ThemeService.loadStoredTheme();
        if (localTheme) {
          setThemeState(localTheme);
          ThemeService.applyTheme(localTheme);
        }
      }
    } catch (error) {
      console.error('Error loading theme:', error);
      setError(error instanceof Error ? error.message : 'خطأ في تحميل الثيم');
      
      // Fallback to localStorage
      const localTheme = ThemeService.loadStoredTheme();
      if (localTheme) {
        setThemeState(localTheme);
        ThemeService.applyTheme(localTheme);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const refreshTheme = async () => {
    console.log('🔄 Refreshing theme...');
    await loadTheme();
  };

  // Load theme when user/tenant changes
  useEffect(() => {
    if (user?.tenant_id) {
      loadTheme();
    }
  }, [user?.tenant_id]);

  // Apply theme when it changes
  useEffect(() => {
    if (theme) {
      ThemeService.applyTheme(theme);
    }
  }, [theme]);

  return (
    <CustomThemeContext.Provider
      value={{
        theme,
        setTheme,
        loadTheme,
        refreshTheme,
        isLoading,
        error
      }}
    >
      {children}
    </CustomThemeContext.Provider>
  );
};

export const useCustomTheme = (): CustomThemeContextType => {
  const context = useContext(CustomThemeContext);
  if (!context) {
    throw new Error('useCustomTheme must be used within a CustomThemeProvider');
  }
  return context;
};
