/**
 * Encryption utilities for sensitive data
 * Uses Web Crypto API for client-side encryption
 */

// Generate a key for encryption/decryption
export const generateKey = async (): Promise<CryptoKey> => {
  return await window.crypto.subtle.generateKey(
    {
      name: "AES-GCM",
      length: 256,
    },
    true,
    ["encrypt", "decrypt"],
  );
};

// Convert key to exportable format
export const exportKey = async (key: CryptoKey): Promise<string> => {
  const exported = await window.crypto.subtle.exportKey("raw", key);
  return btoa(String.fromCharCode(...new Uint8Array(exported)));
};

// Import key from string
export const importKey = async (keyString: string): Promise<CryptoKey> => {
  const keyData = new Uint8Array(
    atob(keyString)
      .split("")
      .map((char) => char.charCodeAt(0)),
  );

  return await window.crypto.subtle.importKey(
    "raw",
    keyData,
    {
      name: "AES-GCM",
      length: 256,
    },
    true,
    ["encrypt", "decrypt"],
  );
};

// Encrypt sensitive data
export const encryptData = async (
  data: string,
  key: CryptoKey,
): Promise<{ encrypted: string; iv: string }> => {
  const encoder = new TextEncoder();
  const dataBuffer = encoder.encode(data);

  // Generate a random initialization vector
  const iv = window.crypto.getRandomValues(new Uint8Array(12));

  const encrypted = await window.crypto.subtle.encrypt(
    {
      name: "AES-GCM",
      iv: iv,
    },
    key,
    dataBuffer,
  );

  return {
    encrypted: btoa(String.fromCharCode(...new Uint8Array(encrypted))),
    iv: btoa(String.fromCharCode(...iv)),
  };
};

// Decrypt sensitive data
export const decryptData = async (
  encryptedData: string,
  iv: string,
  key: CryptoKey,
): Promise<string> => {
  const encryptedBuffer = new Uint8Array(
    atob(encryptedData)
      .split("")
      .map((char) => char.charCodeAt(0)),
  );

  const ivBuffer = new Uint8Array(
    atob(iv)
      .split("")
      .map((char) => char.charCodeAt(0)),
  );

  const decrypted = await window.crypto.subtle.decrypt(
    {
      name: "AES-GCM",
      iv: ivBuffer,
    },
    key,
    encryptedBuffer,
  );

  const decoder = new TextDecoder();
  return decoder.decode(decrypted);
};

// Hash sensitive data (one-way)
export const hashData = async (data: string): Promise<string> => {
  const encoder = new TextEncoder();
  const dataBuffer = encoder.encode(data);

  const hashBuffer = await window.crypto.subtle.digest("SHA-256", dataBuffer);
  const hashArray = new Uint8Array(hashBuffer);

  return btoa(String.fromCharCode(...hashArray));
};

// Utility to encrypt sensitive fields in objects
export const encryptSensitiveFields = async (
  obj: Record<string, any>,
  sensitiveFields: string[],
  key: CryptoKey,
): Promise<Record<string, any>> => {
  const result = { ...obj };

  for (const field of sensitiveFields) {
    if (result[field] && typeof result[field] === "string") {
      const { encrypted, iv } = await encryptData(result[field], key);
      result[field] = encrypted;
      result[`${field}_iv`] = iv;
    }
  }

  return result;
};

// Utility to decrypt sensitive fields in objects
export const decryptSensitiveFields = async (
  obj: Record<string, any>,
  sensitiveFields: string[],
  key: CryptoKey,
): Promise<Record<string, any>> => {
  const result = { ...obj };

  for (const field of sensitiveFields) {
    if (result[field] && result[`${field}_iv`]) {
      try {
        result[field] = await decryptData(
          result[field],
          result[`${field}_iv`],
          key,
        );
        // Remove the IV field from the result
        delete result[`${field}_iv`];
      } catch (error) {
        console.error(`Failed to decrypt field ${field}:`, error);
        // Keep the encrypted value if decryption fails
      }
    }
  }

  return result;
};

// Generate a secure random string for tokens/IDs
export const generateSecureToken = (length: number = 32): string => {
  const array = new Uint8Array(length);
  window.crypto.getRandomValues(array);
  return btoa(String.fromCharCode(...array))
    .replace(/[+/]/g, "")
    .substring(0, length);
};

// Validate if data appears to be encrypted
export const isEncrypted = (data: string): boolean => {
  try {
    // Check if it's base64 encoded and has reasonable length
    const decoded = atob(data);
    return decoded.length > 0 && data.length > 20;
  } catch {
    return false;
  }
};
