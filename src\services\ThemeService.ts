/**
 * Theme Service
 * Service for managing theme customization and persistence
 * Phase 3: UI/UX Enhancement - Theme Service
 */

import { supabase } from '../lib/supabase';
import { ThemeDatabaseService } from './ThemeDatabaseService';

export interface ThemeConfig {
  id?: string;
  tenant_id?: string;
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    // Sidebar specific colors
    sidebarBackground: string;
    sidebarText: string;
    sidebarActiveBackground: string;
    sidebarActiveText: string;
    sidebarHoverBackground: string;
  };
  typography: {
    fontFamily: string;
    fontSize: string;
    fontWeight: string;
  };
  layout: {
    borderRadius: string;
    spacing: string;
    shadows: boolean;
  };
  branding: {
    logo?: string;
    schoolName?: string;
    tagline?: string;
  };
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export class ThemeService {
  /**
   * Get theme for a specific tenant (from database first, then localStorage)
   */
  static async getThemeByTenant(tenantId: string): Promise<ThemeConfig | null> {
    try {
      // First try to get from database
      const dbTheme = await ThemeDatabaseService.getActiveTheme(tenantId);
      if (dbTheme) {
        const themeConfig = ThemeDatabaseService.toThemeConfig(dbTheme);
        // Cache in localStorage for quick access
        localStorage.setItem(`theme_${tenantId}`, JSON.stringify(themeConfig));
        localStorage.setItem('currentTheme', JSON.stringify(themeConfig));
        localStorage.setItem('currentThemeId', dbTheme.id);
        return themeConfig;
      }

      // Fallback to localStorage if database doesn't have theme
      const stored = localStorage.getItem(`theme_${tenantId}`);
      if (stored) {
        return JSON.parse(stored);
      }

      // If not found anywhere, return null
      return null;
    } catch (error) {
      console.error('Error fetching theme:', error);

      // Try localStorage as final fallback
      try {
        const stored = localStorage.getItem(`theme_${tenantId}`);
        if (stored) {
          return JSON.parse(stored);
        }
      } catch (localError) {
        console.error('Error reading from localStorage:', localError);
      }

      return null;
    }
  }

  /**
   * Save theme for a tenant (using localStorage for now)
   */
  static async saveTheme(tenantId: string, theme: Omit<ThemeConfig, 'id' | 'tenant_id' | 'created_at' | 'updated_at'>): Promise<boolean> {
    try {
      // For now, save to localStorage
      const themeWithId: ThemeConfig = {
        id: `theme_${tenantId}_${Date.now()}`,
        tenant_id: tenantId,
        ...theme,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Save to localStorage
      localStorage.setItem(`theme_${tenantId}`, JSON.stringify(themeWithId));

      // Also save as current theme
      localStorage.setItem('currentTheme', JSON.stringify(themeWithId));

      console.log('Theme saved successfully:', themeWithId);
      return true;
    } catch (error) {
      console.error('Error saving theme:', error);
      return false;
    }
  }

  /**
   * Get all themes (for admin)
   */
  static async getAllThemes(): Promise<ThemeConfig[]> {
    try {
      const { data, error } = await supabase
        .from('themes')
        .select(`
          *,
          tenant:tenants(name)
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching all themes:', error);
      return [];
    }
  }

  /**
   * Apply theme to CSS variables
   */
  static applyTheme(theme: ThemeConfig): void {
    const root = document.documentElement;

    console.log('Applying theme:', theme);

    // Apply colors with !important to override existing styles
    root.style.setProperty('--color-primary', theme.colors.primary);
    root.style.setProperty('--color-secondary', theme.colors.secondary);
    root.style.setProperty('--color-accent', theme.colors.accent);
    root.style.setProperty('--color-background', theme.colors.background);
    root.style.setProperty('--color-surface', theme.colors.surface);
    root.style.setProperty('--color-text', theme.colors.text);
    root.style.setProperty('--color-text-secondary', theme.colors.textSecondary);

    // Apply typography
    root.style.setProperty('--font-family', theme.typography.fontFamily);
    root.style.setProperty('--font-size', theme.typography.fontSize);
    root.style.setProperty('--font-weight', theme.typography.fontWeight);

    // Apply layout
    root.style.setProperty('--border-radius', theme.layout.borderRadius);
    root.style.setProperty('--spacing', theme.layout.spacing);

    // Apply colors to common Tailwind classes
    this.applyTailwindOverrides(theme);

    // Store in localStorage for persistence
    localStorage.setItem('currentTheme', JSON.stringify(theme));

    console.log('Theme applied successfully');
  }

  /**
   * Apply theme colors to Tailwind classes
   */
  static applyTailwindOverrides(theme: ThemeConfig): void {
    // Create or update style element for theme overrides
    let styleElement = document.getElementById('theme-overrides');
    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = 'theme-overrides';
      document.head.appendChild(styleElement);
    }

    const css = `
      /* Primary color overrides */
      .bg-blue-600, .bg-blue-500 {
        background-color: ${theme.colors.primary} !important;
      }

      .text-blue-600, .text-blue-500 {
        color: ${theme.colors.primary} !important;
      }

      .border-blue-600, .border-blue-500 {
        border-color: ${theme.colors.primary} !important;
      }

      .hover\\:bg-blue-700:hover {
        background-color: ${this.darkenColor(theme.colors.primary, 10)} !important;
      }

      .hover\\:text-blue-700:hover {
        color: ${this.darkenColor(theme.colors.primary, 10)} !important;
      }

      /* Secondary color overrides */
      .bg-gray-600, .bg-gray-500 {
        background-color: ${theme.colors.secondary} !important;
      }

      .text-gray-600, .text-gray-500 {
        color: ${theme.colors.secondary} !important;
      }

      /* Accent color overrides */
      .bg-green-600, .bg-green-500 {
        background-color: ${theme.colors.accent} !important;
      }

      .text-green-600, .text-green-500 {
        color: ${theme.colors.accent} !important;
      }

      .hover\\:bg-green-700:hover {
        background-color: ${this.darkenColor(theme.colors.accent, 10)} !important;
      }

      /* Sidebar specific overrides - Chrome compatible */
      aside.sidebar,
      aside[class*="sidebar"],
      .sidebar,
      [data-sidebar="true"] {
        background-color: ${theme.colors.sidebarBackground} !important;
        color: ${theme.colors.sidebarText} !important;
      }

      /* Sidebar items - multiple selectors for Chrome compatibility */
      .sidebar-item,
      aside.sidebar a,
      aside[class*="sidebar"] a,
      .sidebar a,
      [data-sidebar="true"] a {
        color: ${theme.colors.sidebarText} !important;
        transition: all 0.2s ease !important;
      }

      /* Hover states */
      .sidebar-item:hover,
      aside.sidebar a:hover,
      aside[class*="sidebar"] a:hover,
      .sidebar a:hover,
      [data-sidebar="true"] a:hover {
        background-color: ${theme.colors.sidebarHoverBackground} !important;
        color: ${theme.colors.sidebarText} !important;
      }

      /* Active states - multiple selectors */
      .sidebar-item.active,
      .sidebar-item[aria-current="page"],
      aside.sidebar a.active,
      aside.sidebar a[aria-current="page"],
      aside[class*="sidebar"] a.active,
      aside[class*="sidebar"] a[aria-current="page"],
      .sidebar a.active,
      .sidebar a[aria-current="page"],
      [data-sidebar="true"] a.active,
      [data-sidebar="true"] a[aria-current="page"] {
        background-color: ${theme.colors.sidebarActiveBackground} !important;
        color: ${theme.colors.sidebarActiveText} !important;
      }

      /* Chrome specific fixes */
      @supports (-webkit-appearance: none) {
        aside.sidebar,
        aside[data-sidebar="true"] {
          background-color: ${theme.colors.sidebarBackground} !important;
          color: ${theme.colors.sidebarText} !important;
          -webkit-font-smoothing: antialiased;
        }

        aside.sidebar *,
        aside[data-sidebar="true"] * {
          color: inherit !important;
        }

        aside.sidebar a,
        aside[data-sidebar="true"] a {
          color: ${theme.colors.sidebarText} !important;
          -webkit-transition: all 0.2s ease !important;
          transition: all 0.2s ease !important;
        }

        aside.sidebar a:hover,
        aside[data-sidebar="true"] a:hover {
          background-color: ${theme.colors.sidebarHoverBackground} !important;
          color: ${theme.colors.sidebarText} !important;
        }

        aside.sidebar a.active,
        aside.sidebar a[aria-current="page"],
        aside[data-sidebar="true"] a.active,
        aside[data-sidebar="true"] a[aria-current="page"] {
          background-color: ${theme.colors.sidebarActiveBackground} !important;
          color: ${theme.colors.sidebarActiveText} !important;
        }
      }

      /* Force Chrome to apply styles immediately */
      @media screen and (-webkit-min-device-pixel-ratio:0) {
        aside.sidebar,
        aside[data-sidebar="true"] {
          background-color: ${theme.colors.sidebarBackground} !important;
          color: ${theme.colors.sidebarText} !important;
        }

        aside.sidebar a,
        aside[data-sidebar="true"] a {
          color: ${theme.colors.sidebarText} !important;
        }

        aside.sidebar a:hover,
        aside[data-sidebar="true"] a:hover {
          background-color: ${theme.colors.sidebarHoverBackground} !important;
        }

        aside.sidebar a.active,
        aside.sidebar a[aria-current="page"],
        aside[data-sidebar="true"] a.active,
        aside[data-sidebar="true"] a[aria-current="page"] {
          background-color: ${theme.colors.sidebarActiveBackground} !important;
          color: ${theme.colors.sidebarActiveText} !important;
        }
      }

      /* Background overrides */
      .bg-white {
        background-color: ${theme.colors.background} !important;
      }

      .bg-gray-50 {
        background-color: ${theme.colors.surface} !important;
      }

      /* Text overrides */
      .text-gray-900 {
        color: ${theme.colors.text} !important;
      }

      .text-gray-700 {
        color: ${theme.colors.textSecondary} !important;
      }

      /* Border radius overrides */
      .rounded-lg {
        border-radius: ${theme.layout.borderRadius} !important;
      }

      /* Typography overrides */
      body, .font-primary {
        font-family: ${theme.typography.fontFamily} !important;
        font-size: ${theme.typography.fontSize} !important;
        font-weight: ${theme.typography.fontWeight} !important;
      }

      /* Shadow overrides */
      ${!theme.layout.shadows ? `
      .shadow, .shadow-sm, .shadow-md, .shadow-lg {
        box-shadow: none !important;
      }
      ` : ''}
    `;

    styleElement.textContent = css;

    // Force Chrome to apply sidebar colors immediately
    this.forceChromeStyleUpdate(theme);
  }

  /**
   * Force Chrome to apply sidebar styles immediately
   */
  static forceChromeStyleUpdate(theme: ThemeConfig): void {
    // Check if we're in Chrome
    const isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);

    if (isChrome) {
      // Force repaint of sidebar elements
      setTimeout(() => {
        const sidebarElements = document.querySelectorAll('[data-sidebar="true"], aside.sidebar, .sidebar');

        sidebarElements.forEach((element) => {
          const htmlElement = element as HTMLElement;

          // Force style recalculation
          htmlElement.style.backgroundColor = theme.colors.sidebarBackground;
          htmlElement.style.color = theme.colors.sidebarText;

          // Force repaint
          htmlElement.style.display = 'none';
          htmlElement.offsetHeight; // Trigger reflow
          htmlElement.style.display = '';

          // Apply to child links
          const links = htmlElement.querySelectorAll('a');
          links.forEach((link) => {
            const linkElement = link as HTMLElement;
            linkElement.style.color = theme.colors.sidebarText;

            // Add hover listeners for Chrome
            linkElement.addEventListener('mouseenter', () => {
              linkElement.style.backgroundColor = theme.colors.sidebarHoverBackground;
            });

            linkElement.addEventListener('mouseleave', () => {
              if (!linkElement.classList.contains('active') && !linkElement.hasAttribute('aria-current')) {
                linkElement.style.backgroundColor = '';
              }
            });

            // Apply active styles
            if (linkElement.classList.contains('active') || linkElement.hasAttribute('aria-current')) {
              linkElement.style.backgroundColor = theme.colors.sidebarActiveBackground;
              linkElement.style.color = theme.colors.sidebarActiveText;
            }
          });
        });
      }, 100);
    }
  }

  /**
   * Darken a color by a percentage
   */
  static darkenColor(color: string, percent: number): string {
    // Simple color darkening - convert hex to RGB, darken, convert back
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    const darkenedR = Math.max(0, Math.floor(r * (100 - percent) / 100));
    const darkenedG = Math.max(0, Math.floor(g * (100 - percent) / 100));
    const darkenedB = Math.max(0, Math.floor(b * (100 - percent) / 100));

    return `#${darkenedR.toString(16).padStart(2, '0')}${darkenedG.toString(16).padStart(2, '0')}${darkenedB.toString(16).padStart(2, '0')}`;
  }

  /**
   * Load theme from database for tenant
   */
  static async loadThemeFromDatabase(tenantId: string): Promise<ThemeConfig | null> {
    try {
      const dbTheme = await ThemeDatabaseService.getActiveTheme(tenantId);
      if (dbTheme) {
        const theme = ThemeDatabaseService.toThemeConfig(dbTheme);
        // Also store in localStorage for offline access
        localStorage.setItem('currentTheme', JSON.stringify(theme));
        localStorage.setItem('currentThemeId', dbTheme.id);
        return theme;
      }
      return null;
    } catch (error) {
      console.error('Error loading theme from database:', error);
      // Fallback to localStorage
      return this.loadStoredTheme();
    }
  }

  /**
   * Save theme to database permanently
   */
  static async saveThemeToDatabase(
    theme: ThemeConfig,
    name: string,
    tenantId: string,
    userId: string,
    options: {
      description?: string;
      setAsActive?: boolean;
    } = {}
  ): Promise<string> {
    try {
      const { setAsActive = true } = options;

      // Use saveOrUpdateTheme to update existing theme instead of creating new ones
      const savedTheme = await ThemeDatabaseService.saveOrUpdateTheme(
        theme,
        name,
        tenantId,
        userId,
        {
          ...options,
          themeType: 'school',
          setAsActive,
        }
      );

      // Apply the theme immediately if it's set as active
      if (setAsActive) {
        this.applyTheme(theme);
        // Store in localStorage for quick access
        localStorage.setItem('currentTheme', JSON.stringify(theme));
        localStorage.setItem('currentThemeId', savedTheme.id);
      }

      return savedTheme.id;
    } catch (error) {
      console.error('Error saving theme to database:', error);
      throw error;
    }
  }

  /**
   * Update existing theme in database
   */
  static async updateThemeInDatabase(
    themeId: string,
    theme: ThemeConfig,
    options: {
      name?: string;
      description?: string;
      setAsActive?: boolean;
    } = {}
  ): Promise<void> {
    try {
      await ThemeDatabaseService.updateTheme(themeId, theme, options);

      // Apply the theme immediately if it's set as active
      if (options.setAsActive !== false) {
        this.applyTheme(theme);
        // Store in localStorage for quick access
        localStorage.setItem('currentTheme', JSON.stringify(theme));
        localStorage.setItem('currentThemeId', themeId);
      }
    } catch (error) {
      console.error('Error updating theme in database:', error);
      throw error;
    }
  }

  /**
   * Load theme from localStorage (fallback)
   */
  static loadStoredTheme(): ThemeConfig | null {
    try {
      const stored = localStorage.getItem('currentTheme');
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Error loading stored theme:', error);
      return null;
    }
  }

  /**
   * Reset theme to default
   */
  static resetTheme(): void {
    const root = document.documentElement;
    
    // Remove custom properties
    root.style.removeProperty('--color-primary');
    root.style.removeProperty('--color-secondary');
    root.style.removeProperty('--color-accent');
    root.style.removeProperty('--color-background');
    root.style.removeProperty('--color-surface');
    root.style.removeProperty('--color-text');
    root.style.removeProperty('--color-text-secondary');
    root.style.removeProperty('--font-family');
    root.style.removeProperty('--font-size');
    root.style.removeProperty('--font-weight');
    root.style.removeProperty('--border-radius');
    root.style.removeProperty('--spacing');
    
    // Remove from localStorage
    localStorage.removeItem('currentTheme');
  }

  /**
   * Export theme as JSON
   */
  static exportTheme(theme: ThemeConfig): string {
    return JSON.stringify(theme, null, 2);
  }

  /**
   * Import theme from JSON
   */
  static importTheme(jsonString: string): ThemeConfig | null {
    try {
      const theme = JSON.parse(jsonString);
      
      // Validate theme structure
      if (!theme.colors || !theme.typography || !theme.layout || !theme.branding) {
        throw new Error('Invalid theme structure');
      }
      
      return theme;
    } catch (error) {
      console.error('Error importing theme:', error);
      return null;
    }
  }

  /**
   * Generate CSS from theme
   */
  static generateCSS(theme: ThemeConfig): string {
    return `
:root {
  --color-primary: ${theme.colors.primary};
  --color-secondary: ${theme.colors.secondary};
  --color-accent: ${theme.colors.accent};
  --color-background: ${theme.colors.background};
  --color-surface: ${theme.colors.surface};
  --color-text: ${theme.colors.text};
  --color-text-secondary: ${theme.colors.textSecondary};
  --font-family: ${theme.typography.fontFamily};
  --font-size: ${theme.typography.fontSize};
  --font-weight: ${theme.typography.fontWeight};
  --border-radius: ${theme.layout.borderRadius};
  --spacing: ${theme.layout.spacing};
}

/* Primary color utilities */
.bg-primary { background-color: var(--color-primary); }
.text-primary { color: var(--color-primary); }
.border-primary { border-color: var(--color-primary); }

/* Secondary color utilities */
.bg-secondary { background-color: var(--color-secondary); }
.text-secondary { color: var(--color-secondary); }
.border-secondary { border-color: var(--color-secondary); }

/* Accent color utilities */
.bg-accent { background-color: var(--color-accent); }
.text-accent { color: var(--color-accent); }
.border-accent { border-color: var(--color-accent); }

/* Typography utilities */
.font-primary { font-family: var(--font-family); }
.text-base { font-size: var(--font-size); }
.font-normal { font-weight: var(--font-weight); }

/* Layout utilities */
.rounded-theme { border-radius: var(--border-radius); }
.spacing-theme { padding: var(--spacing); }
    `.trim();
  }
}
