/**
 * نظام الترحيل المتقدم - تطبيق خطة الترحيل
 * Advanced Migration System - Apply Migration Plan
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 بدء نظام الترحيل المتقدم...\n');

class AdvancedMigrationSystem {
  constructor() {
    this.migrationLog = [];
    this.errors = [];
    this.warnings = [];
    this.stats = {
      totalFiles: 0,
      copiedFiles: 0,
      skippedFiles: 0,
      errors: 0
    };
  }

  /**
   * تحليل البنية الحالية وإنشاء خطة ترحيل محدثة
   */
  async analyzeAndCreateUpdatedPlan() {
    console.log('🔍 تحليل البنية الحالية وإنشاء خطة ترحيل محدثة...');

    const updatedPlan = {
      phase1: {
        name: 'نقل الملفات الأساسية',
        priority: 'high',
        files: [
          { from: 'src/types', to: 'src-new/core/types', type: 'directory' },
          { from: 'src/utils', to: 'src-new/core/utils', type: 'directory' },
          { from: 'src/hooks', to: 'src-new/core/hooks', type: 'directory' },
          { from: 'src/contexts', to: 'src-new/core/contexts', type: 'directory' },
          { from: 'src/config', to: 'src-new/core/constants', type: 'directory' }
        ]
      },
      phase2: {
        name: 'نقل الخدمات والمكتبات',
        priority: 'high',
        files: [
          { from: 'src/services', to: 'src-new/shared/services', type: 'directory' },
          { from: 'src/lib', to: 'src-new/shared/services/lib', type: 'directory' },
          { from: 'src/middleware', to: 'src-new/shared/services/middleware', type: 'directory' },
          { from: 'src/api', to: 'src-new/shared/services/api', type: 'directory' }
        ]
      },
      phase3: {
        name: 'نقل المكونات المشتركة',
        priority: 'medium',
        files: [
          { from: 'src/components/ui', to: 'src-new/shared/components/ui', type: 'directory' },
          { from: 'src/components/layout', to: 'src-new/shared/layouts', type: 'directory' },
          { from: 'src/components/common', to: 'src-new/shared/components/common', type: 'directory' },
          { from: 'src/design-system', to: 'src-new/shared/components/design-system', type: 'directory' }
        ]
      },
      phase4: {
        name: 'نقل ميزات المصادقة والأمان',
        priority: 'high',
        files: [
          { from: 'src/components/auth', to: 'src-new/features/auth/components', type: 'directory' },
          { from: 'src/pages/auth', to: 'src-new/features/auth/pages', type: 'directory' },
          { from: 'src/pages/login', to: 'src-new/features/auth/pages/login', type: 'directory' },
          { from: 'src/components/security', to: 'src-new/features/auth/security', type: 'directory' }
        ]
      },
      phase5: {
        name: 'نقل ميزات لوحة التحكم',
        priority: 'medium',
        files: [
          { from: 'src/components/dashboard', to: 'src-new/features/dashboard/components', type: 'directory' },
          { from: 'src/components/dashboards', to: 'src-new/features/dashboard/variants', type: 'directory' },
          { from: 'src/pages/dashboard', to: 'src-new/features/dashboard/pages', type: 'directory' },
          { from: 'src/components/admin', to: 'src-new/features/dashboard/admin', type: 'directory' }
        ]
      },
      phase6: {
        name: 'نقل ميزات إدارة الحافلات',
        priority: 'medium',
        files: [
          { from: 'src/components/buses', to: 'src-new/features/buses/components', type: 'directory' },
          { from: 'src/components/routes', to: 'src-new/features/routes/components', type: 'directory' },
          { from: 'src/components/drivers', to: 'src-new/features/drivers/components', type: 'directory' },
          { from: 'src/components/tracking', to: 'src-new/features/tracking/components', type: 'directory' }
        ]
      },
      phase7: {
        name: 'نقل ميزات إدارة الطلاب والمدارس',
        priority: 'medium',
        files: [
          { from: 'src/components/students', to: 'src-new/features/students/components', type: 'directory' },
          { from: 'src/components/schools', to: 'src-new/features/schools/components', type: 'directory' },
          { from: 'src/pages/school', to: 'src-new/features/schools/pages', type: 'directory' }
        ]
      },
      phase8: {
        name: 'نقل الميزات المتقدمة',
        priority: 'low',
        files: [
          { from: 'src/components/notifications', to: 'src-new/features/notifications/components', type: 'directory' },
          { from: 'src/components/reports', to: 'src-new/features/reports/components', type: 'directory' },
          { from: 'src/components/maintenance', to: 'src-new/features/maintenance/components', type: 'directory' },
          { from: 'src/components/maps', to: 'src-new/features/tracking/maps', type: 'directory' },
          { from: 'src/components/map', to: 'src-new/features/tracking/map', type: 'directory' }
        ]
      },
      phase9: {
        name: 'نقل الملفات الثابتة والموضوعات',
        priority: 'low',
        files: [
          { from: 'src/themes', to: 'src-new/assets/themes', type: 'directory' },
          { from: 'src/i18n', to: 'src-new/assets/locales', type: 'directory' }
        ]
      },
      phase10: {
        name: 'نقل الملفات الجذرية والاختبارات',
        priority: 'low',
        files: [
          { from: 'src/App.tsx', to: 'src-new/App.tsx', type: 'file' },
          { from: 'src/main.tsx', to: 'src-new/main.tsx', type: 'file' },
          { from: 'src/index.css', to: 'src-new/assets/styles/index.css', type: 'file' },
          { from: 'src/tests', to: 'src-new/tests', type: 'directory' },
          { from: 'src/pages/testing', to: 'src-new/features/testing', type: 'directory' }
        ]
      }
    };

    // حفظ الخطة المحدثة
    const planPath = path.join(process.cwd(), 'migration-plan-updated.json');
    fs.writeFileSync(planPath, JSON.stringify(updatedPlan, null, 2));
    
    console.log('✅ تم إنشاء خطة الترحيل المحدثة');
    console.log(`📄 الخطة محفوظة في: ${planPath}`);
    console.log(`📊 إجمالي المراحل: ${Object.keys(updatedPlan).length}`);
    
    return updatedPlan;
  }

  /**
   * نسخ مجلد بشكل تكراري
   */
  copyDirectory(source, destination) {
    if (!fs.existsSync(source)) {
      this.warnings.push(`المجلد المصدر غير موجود: ${source}`);
      return false;
    }

    // إنشاء المجلد الوجهة
    if (!fs.existsSync(destination)) {
      fs.mkdirSync(destination, { recursive: true });
    }

    const items = fs.readdirSync(source);
    let copiedCount = 0;

    for (const item of items) {
      const sourcePath = path.join(source, item);
      const destPath = path.join(destination, item);
      const stat = fs.statSync(sourcePath);

      try {
        if (stat.isDirectory()) {
          // نسخ المجلد تكرارياً
          this.copyDirectory(sourcePath, destPath);
        } else {
          // نسخ الملف
          fs.copyFileSync(sourcePath, destPath);
          copiedCount++;
          this.stats.copiedFiles++;
        }
      } catch (error) {
        this.errors.push(`خطأ في نسخ ${sourcePath}: ${error.message}`);
        this.stats.errors++;
      }
    }

    return copiedCount > 0;
  }

  /**
   * نسخ ملف واحد
   */
  copyFile(source, destination) {
    if (!fs.existsSync(source)) {
      this.warnings.push(`الملف المصدر غير موجود: ${source}`);
      return false;
    }

    try {
      // إنشاء المجلد الوجهة إذا لم يكن موجوداً
      const destDir = path.dirname(destination);
      if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
      }

      fs.copyFileSync(source, destination);
      this.stats.copiedFiles++;
      return true;
    } catch (error) {
      this.errors.push(`خطأ في نسخ الملف ${source}: ${error.message}`);
      this.stats.errors++;
      return false;
    }
  }

  /**
   * تنفيذ مرحلة واحدة من الترحيل
   */
  async executePhase(phaseName, phaseData) {
    console.log(`\n🔄 تنفيذ ${phaseName}: ${phaseData.name}`);
    console.log(`📋 الأولوية: ${phaseData.priority}`);
    console.log(`📁 عدد العناصر: ${phaseData.files.length}`);

    const phaseStats = {
      total: phaseData.files.length,
      success: 0,
      skipped: 0,
      errors: 0
    };

    for (const fileItem of phaseData.files) {
      const { from, to, type } = fileItem;
      
      console.log(`  📂 ${type === 'directory' ? 'مجلد' : 'ملف'}: ${from} → ${to}`);

      let success = false;
      
      if (type === 'directory') {
        success = this.copyDirectory(from, to);
      } else {
        success = this.copyFile(from, to);
      }

      if (success) {
        phaseStats.success++;
        console.log(`    ✅ تم النسخ بنجاح`);
      } else {
        phaseStats.skipped++;
        console.log(`    ⚠️ تم التخطي`);
      }

      this.stats.totalFiles++;
    }

    this.migrationLog.push({
      phase: phaseName,
      name: phaseData.name,
      stats: phaseStats,
      timestamp: new Date().toISOString()
    });

    console.log(`✅ اكتمل ${phaseName}: ${phaseStats.success}/${phaseStats.total} نجح`);
    return phaseStats;
  }

  /**
   * تنفيذ الترحيل الكامل
   */
  async executeMigration(migrationPlan, selectedPhases = null) {
    console.log('🚀 بدء تنفيذ الترحيل الكامل...\n');

    const startTime = Date.now();
    const phasesToExecute = selectedPhases || Object.keys(migrationPlan);

    for (const phaseName of phasesToExecute) {
      if (migrationPlan[phaseName]) {
        await this.executePhase(phaseName, migrationPlan[phaseName]);
      }
    }

    const totalTime = Date.now() - startTime;

    // إنشاء تقرير الترحيل
    await this.generateMigrationReport(totalTime);

    console.log('\n🎉 تم إكمال الترحيل بنجاح!');
    this.showMigrationSummary(totalTime);
  }

  /**
   * إنشاء تقرير الترحيل
   */
  async generateMigrationReport(executionTime) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportDir = path.join(process.cwd(), 'reports', 'migration');
    
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const report = {
      migration_info: {
        timestamp: timestamp,
        execution_time_ms: executionTime,
        execution_time_formatted: `${(executionTime / 1000).toFixed(1)}s`
      },
      statistics: this.stats,
      phase_details: this.migrationLog,
      errors: this.errors,
      warnings: this.warnings,
      summary: {
        total_phases: this.migrationLog.length,
        successful_files: this.stats.copiedFiles,
        error_count: this.stats.errors,
        success_rate: ((this.stats.copiedFiles / this.stats.totalFiles) * 100).toFixed(1)
      }
    };

    const reportPath = path.join(reportDir, `migration-report-${timestamp}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log(`📊 تم إنشاء تقرير الترحيل: ${reportPath}`);
  }

  /**
   * عرض ملخص الترحيل
   */
  showMigrationSummary(executionTime) {
    console.log('\n' + '=' .repeat(60));
    console.log('📊 ملخص الترحيل');
    console.log('=' .repeat(60));
    console.log(`⏱️ الوقت الإجمالي: ${(executionTime / 1000).toFixed(1)} ثانية`);
    console.log(`📁 إجمالي الملفات: ${this.stats.totalFiles}`);
    console.log(`✅ تم نسخها: ${this.stats.copiedFiles}`);
    console.log(`⚠️ تم تخطيها: ${this.stats.skippedFiles}`);
    console.log(`❌ أخطاء: ${this.stats.errors}`);
    console.log(`📈 معدل النجاح: ${((this.stats.copiedFiles / this.stats.totalFiles) * 100).toFixed(1)}%`);
    
    if (this.errors.length > 0) {
      console.log('\n❌ الأخطاء:');
      this.errors.forEach(error => console.log(`  • ${error}`));
    }
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️ التحذيرات:');
      this.warnings.forEach(warning => console.log(`  • ${warning}`));
    }
    
    console.log('\n🎯 الخطوات التالية:');
    console.log('1. مراجعة البنية الجديدة في src-new/');
    console.log('2. تحديث مسارات الاستيراد');
    console.log('3. اختبار التطبيق');
    console.log('4. حذف البنية القديمة (بعد التأكد)');
    console.log('=' .repeat(60));
  }
}

/**
 * الدالة الرئيسية
 */
async function main() {
  try {
    const migrationSystem = new AdvancedMigrationSystem();
    
    // تحليل وإنشاء خطة محدثة
    const updatedPlan = await migrationSystem.analyzeAndCreateUpdatedPlan();
    
    console.log('\n🤔 هل تريد تنفيذ الترحيل الكامل؟');
    console.log('📋 المراحل المتاحة:');
    Object.entries(updatedPlan).forEach(([key, phase]) => {
      console.log(`  ${key}: ${phase.name} (${phase.priority})`);
    });
    
    // تنفيذ الترحيل (يمكن تخصيص المراحل هنا)
    console.log('\n🚀 بدء الترحيل...');
    await migrationSystem.executeMigration(updatedPlan);
    
  } catch (error) {
    console.error('💥 خطأ في نظام الترحيل:', error);
    process.exit(1);
  }
}

// تشغيل النظام
main();
