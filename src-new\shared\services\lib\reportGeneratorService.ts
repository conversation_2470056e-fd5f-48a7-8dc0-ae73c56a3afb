import { supabase } from "./supabase";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import * as api from "./api";

export interface ReportConfig {
  type:
    | "attendance"
    | "bus_utilization"
    | "driver_performance"
    | "route_delays"
    | "combined";
  format: "csv" | "pdf" | "json";
  filters: {
    startDate: string;
    endDate: string;
    tenantId: string;
    busId?: string;
    routeId?: string;
    driverId?: string;
    studentId?: string;
  };
  options?: {
    includeCharts?: boolean;
    includeStats?: boolean;
    customTitle?: string;
    groupBy?: "day" | "week" | "month";
  };
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  config: ReportConfig;
  created_by: string;
  tenant_id: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

export interface ScheduledReport {
  id: string;
  name: string;
  template_id: string;
  schedule: {
    frequency: "daily" | "weekly" | "monthly";
    time: string; // HH:MM format
    dayOfWeek?: number; // 0-6 for weekly
    dayOfMonth?: number; // 1-31 for monthly
  };
  recipients: string[]; // email addresses
  tenant_id: string;
  is_active: boolean;
  last_run?: string;
  next_run: string;
  created_at: string;
}

class ReportGeneratorService {
  // Generate report data based on configuration
  async generateReportData(config: ReportConfig): Promise<any> {
    const { type, filters } = config;

    try {
      switch (type) {
        case "attendance":
          return await this.generateAttendanceData(filters);
        case "bus_utilization":
          return await this.generateBusUtilizationData(filters);
        case "driver_performance":
          return await this.generateDriverPerformanceData(filters);
        case "route_delays":
          return await this.generateRouteDelayData(filters);
        case "combined":
          return await this.generateCombinedData(filters);
        default:
          throw new Error(`Unsupported report type: ${type}`);
      }
    } catch (error) {
      console.error("Error generating report data:", error);
      throw error;
    }
  }

  // Generate attendance report data
  private async generateAttendanceData(filters: ReportConfig["filters"]) {
    const data = await api.getAttendance(
      filters.tenantId,
      filters.studentId,
      filters.busId,
    );

    // Filter by date range
    const filteredData =
      data?.filter((record) => {
        const recordDate = new Date(record.recorded_at)
          .toISOString()
          .split("T")[0];
        return recordDate >= filters.startDate && recordDate <= filters.endDate;
      }) || [];

    // Calculate statistics
    const stats = this.calculateAttendanceStats(filteredData);

    return {
      data: filteredData,
      stats,
      metadata: {
        totalRecords: filteredData.length,
        dateRange: { start: filters.startDate, end: filters.endDate },
        filters: filters,
      },
    };
  }

  // Generate bus utilization report data
  private async generateBusUtilizationData(filters: ReportConfig["filters"]) {
    const data = await api.getBusUtilization(
      filters.tenantId,
      filters.startDate,
      filters.endDate,
    );

    const stats = this.calculateUtilizationStats(data || []);

    return {
      data: data || [],
      stats,
      metadata: {
        totalRecords: data?.length || 0,
        dateRange: { start: filters.startDate, end: filters.endDate },
        filters: filters,
      },
    };
  }

  // Generate driver performance report data
  private async generateDriverPerformanceData(
    filters: ReportConfig["filters"],
  ) {
    const data = await api.getDriverPerformance(
      filters.driverId || null,
      filters.tenantId,
      filters.startDate,
      filters.endDate,
    );

    const stats = this.calculateDriverPerformanceStats(data || []);

    return {
      data: data || [],
      stats,
      metadata: {
        totalRecords: data?.length || 0,
        dateRange: { start: filters.startDate, end: filters.endDate },
        filters: filters,
      },
    };
  }

  // Generate route delay report data
  private async generateRouteDelayData(filters: ReportConfig["filters"]) {
    const data = await api.getRouteDelays(
      filters.tenantId,
      filters.routeId,
      filters.startDate,
      filters.endDate,
    );

    const stats = this.calculateDelayStats(data || []);
    const analytics = this.analyzeDelayPatterns(data || []);

    return {
      data: data || [],
      stats,
      analytics,
      metadata: {
        totalRecords: data?.length || 0,
        dateRange: { start: filters.startDate, end: filters.endDate },
        filters: filters,
      },
    };
  }

  // Generate combined report data
  private async generateCombinedData(filters: ReportConfig["filters"]) {
    const [attendance, utilization, performance, delays] = await Promise.all([
      this.generateAttendanceData(filters),
      this.generateBusUtilizationData(filters),
      this.generateDriverPerformanceData(filters),
      this.generateRouteDelayData(filters),
    ]);

    return {
      attendance,
      utilization,
      performance,
      delays,
      metadata: {
        dateRange: { start: filters.startDate, end: filters.endDate },
        filters: filters,
      },
    };
  }

  // Export report to CSV format
  async exportToCSV(config: ReportConfig): Promise<string> {
    const reportData = await this.generateReportData(config);

    switch (config.type) {
      case "attendance":
        return this.attendanceToCSV(reportData.data);
      case "bus_utilization":
        return this.utilizationToCSV(reportData.data);
      case "driver_performance":
        return this.performanceToCSV(reportData.data);
      case "route_delays":
        return this.delaysToCSV(reportData.data);
      case "combined":
        return this.combinedToCSV(reportData);
      default:
        throw new Error(`CSV export not supported for type: ${config.type}`);
    }
  }

  // Export report to PDF format
  async exportToPDF(config: ReportConfig, elementId?: string): Promise<Blob> {
    const reportData = await this.generateReportData(config);

    if (elementId) {
      // Capture existing HTML element
      const element = document.getElementById(elementId);
      if (!element) throw new Error(`Element with ID ${elementId} not found`);

      const canvas = await html2canvas(element, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
      });

      const pdf = new jsPDF("p", "mm", "a4");
      const imgWidth = 210;
      const pageHeight = 295;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      pdf.addImage(
        canvas.toDataURL("image/png"),
        "PNG",
        0,
        position,
        imgWidth,
        imgHeight,
      );
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(
          canvas.toDataURL("image/png"),
          "PNG",
          0,
          position,
          imgWidth,
          imgHeight,
        );
        heightLeft -= pageHeight;
      }

      return pdf.output("blob");
    } else {
      // Generate PDF from data
      return this.generatePDFFromData(config, reportData);
    }
  }

  // Generate PDF from raw data
  private async generatePDFFromData(
    config: ReportConfig,
    data: any,
  ): Promise<Blob> {
    const pdf = new jsPDF("p", "mm", "a4");
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Add title
    pdf.setFontSize(20);
    pdf.text(
      config.options?.customTitle || `${config.type.toUpperCase()} Report`,
      pageWidth / 2,
      20,
      { align: "center" },
    );

    // Add date range
    pdf.setFontSize(12);
    pdf.text(
      `Period: ${config.filters.startDate} to ${config.filters.endDate}`,
      pageWidth / 2,
      30,
      { align: "center" },
    );

    let yPosition = 50;

    // Add statistics if available
    if (data.stats && config.options?.includeStats !== false) {
      pdf.setFontSize(14);
      pdf.text("Summary Statistics", 20, yPosition);
      yPosition += 10;

      pdf.setFontSize(10);
      Object.entries(data.stats).forEach(([key, value]) => {
        pdf.text(`${key}: ${value}`, 20, yPosition);
        yPosition += 6;
      });

      yPosition += 10;
    }

    // Add data table
    if (data.data && Array.isArray(data.data) && data.data.length > 0) {
      const headers = Object.keys(data.data[0]).filter(
        (key) => !key.startsWith("_"),
      );
      const tableData = data.data.map((row) =>
        headers.map((header) => row[header] || ""),
      );

      // Simple table implementation
      pdf.setFontSize(8);
      const colWidth = (pageWidth - 40) / headers.length;

      // Headers
      headers.forEach((header, index) => {
        pdf.text(header, 20 + index * colWidth, yPosition);
      });
      yPosition += 8;

      // Data rows
      tableData.slice(0, 30).forEach((row) => {
        // Limit to 30 rows for simplicity
        if (yPosition > pageHeight - 20) {
          pdf.addPage();
          yPosition = 20;
        }

        row.forEach((cell, index) => {
          const cellText = String(cell).substring(0, 15); // Truncate long text
          pdf.text(cellText, 20 + index * colWidth, yPosition);
        });
        yPosition += 6;
      });
    }

    return pdf.output("blob");
  }

  // Calculate attendance statistics
  private calculateAttendanceStats(data: any[]) {
    if (!data.length) return {};

    const totalRecords = data.length;
    const pickups = data.filter((r) => r.type === "pickup").length;
    const dropoffs = data.filter((r) => r.type === "dropoff").length;
    const uniqueStudents = new Set(data.map((r) => r.student_id)).size;
    const uniqueDays = new Set(
      data.map((r) => new Date(r.recorded_at).toDateString()),
    ).size;

    return {
      totalRecords,
      pickups,
      dropoffs,
      uniqueStudents,
      uniqueDays,
      averagePerDay: totalRecords / Math.max(uniqueDays, 1),
    };
  }

  // Calculate utilization statistics
  private calculateUtilizationStats(data: any[]) {
    if (!data.length) return {};

    const avgUtilization =
      data.reduce((sum, item) => sum + (item.utilizationPercentage || 0), 0) /
      data.length;
    const totalTrips = data.reduce(
      (sum, item) => sum + (item.totalRides || 0),
      0,
    );
    const avgRidesPerDay =
      data.reduce((sum, item) => sum + (item.averageRidesPerDay || 0), 0) /
      data.length;

    return {
      avgUtilization: Math.round(avgUtilization * 100) / 100,
      totalTrips,
      avgRidesPerDay: Math.round(avgRidesPerDay * 100) / 100,
      totalBuses: data.length,
    };
  }

  // Calculate driver performance statistics
  private calculateDriverPerformanceStats(data: any[]) {
    if (!data.length) return {};

    const avgOnTimeRate =
      data.reduce((sum, item) => {
        const rate =
          (item.on_time_arrivals || 0) / Math.max(item.total_trips || 1, 1);
        return sum + rate;
      }, 0) / data.length;

    const avgSafetyScore =
      data.reduce((sum, item) => sum + (item.safety_score || 0), 0) /
      data.length;
    const avgParentRating =
      data.reduce((sum, item) => sum + (item.parent_rating || 0), 0) /
      data.length;
    const totalTrips = data.reduce(
      (sum, item) => sum + (item.total_trips || 0),
      0,
    );

    return {
      avgOnTimeRate: Math.round(avgOnTimeRate * 10000) / 100, // Convert to percentage
      avgSafetyScore: Math.round(avgSafetyScore * 100) / 100,
      avgParentRating: Math.round(avgParentRating * 100) / 100,
      totalTrips,
      totalDrivers: data.length,
    };
  }

  // Calculate delay statistics
  private calculateDelayStats(data: any[]) {
    if (!data.length) return {};

    const totalDelays = data.length;
    const delayedTrips = data.filter((item) => item.delay_minutes > 0);
    const avgDelayMinutes =
      delayedTrips.length > 0
        ? delayedTrips.reduce((sum, item) => sum + item.delay_minutes, 0) /
          delayedTrips.length
        : 0;
    const maxDelayMinutes = data.reduce(
      (max, item) => Math.max(max, item.delay_minutes),
      0,
    );
    const onTimePercentage =
      totalDelays > 0
        ? ((totalDelays - delayedTrips.length) / totalDelays) * 100
        : 100;

    return {
      totalDelays,
      delayedTrips: delayedTrips.length,
      avgDelayMinutes: Math.round(avgDelayMinutes * 100) / 100,
      maxDelayMinutes,
      onTimePercentage: Math.round(onTimePercentage * 100) / 100,
    };
  }

  // Analyze delay patterns
  private analyzeDelayPatterns(data: any[]) {
    if (!data.length) return {};

    // Group by route
    const routeDelays = data.reduce((acc, item) => {
      const routeId = item.route_id;
      if (!acc[routeId]) {
        acc[routeId] = {
          routeName: item.route?.name || "Unknown",
          delays: [],
          totalDelayMinutes: 0,
        };
      }
      acc[routeId].delays.push(item);
      acc[routeId].totalDelayMinutes += item.delay_minutes;
      return acc;
    }, {});

    // Find most problematic routes
    const problematicRoutes = Object.entries(routeDelays)
      .map(([routeId, data]: [string, any]) => ({
        routeId,
        routeName: data.routeName,
        delayCount: data.delays.length,
        avgDelay: data.totalDelayMinutes / data.delays.length,
        totalDelayMinutes: data.totalDelayMinutes,
      }))
      .sort((a, b) => b.totalDelayMinutes - a.totalDelayMinutes)
      .slice(0, 5);

    // Group by time of day
    const timePatterns = data.reduce((acc, item) => {
      const hour = parseInt(item.scheduled_time.split(":")[0]);
      const timeSlot =
        hour < 9 ? "morning" : hour < 15 ? "midday" : "afternoon";
      if (!acc[timeSlot]) acc[timeSlot] = 0;
      acc[timeSlot]++;
      return acc;
    }, {});

    // Group by day of week
    const dayPatterns = data.reduce((acc, item) => {
      const dayOfWeek = new Date(item.date).getDay();
      const dayName = [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
      ][dayOfWeek];
      if (!acc[dayName]) acc[dayName] = 0;
      acc[dayName]++;
      return acc;
    }, {});

    return {
      problematicRoutes,
      timePatterns,
      dayPatterns,
    };
  }

  // CSV conversion methods
  private attendanceToCSV(data: any[]): string {
    const headers = ["Date", "Time", "Student", "Bus", "Type", "Recorded By"];
    const rows = data.map((record) => [
      new Date(record.recorded_at).toLocaleDateString(),
      new Date(record.recorded_at).toLocaleTimeString(),
      record.student?.name || "Unknown",
      record.bus?.plate_number || "Unknown",
      record.type,
      record.recorded_by_user?.name || "System",
    ]);

    return [headers.join(","), ...rows.map((row) => row.join(","))].join("\n");
  }

  private utilizationToCSV(data: any[]): string {
    const headers = [
      "Bus",
      "Plate Number",
      "Capacity",
      "Total Rides",
      "Active Days",
      "Utilization %",
      "Avg Rides/Day",
    ];
    const rows = data.map((record) => [
      record.busId,
      record.plateNumber,
      record.capacity,
      record.totalRides,
      record.activeDays,
      record.utilizationPercentage,
      record.averageRidesPerDay,
    ]);

    return [headers.join(","), ...rows.map((row) => row.join(","))].join("\n");
  }

  private performanceToCSV(data: any[]): string {
    const headers = [
      "Date",
      "Driver",
      "On-Time Arrivals",
      "Total Trips",
      "Safety Score",
      "Parent Rating",
      "Fuel Efficiency",
    ];
    const rows = data.map((record) => [
      record.date,
      record.driver?.name || "Unknown",
      record.on_time_arrivals || 0,
      record.total_trips || 0,
      record.safety_score || 0,
      record.parent_rating || 0,
      record.fuel_efficiency || 0,
    ]);

    return [headers.join(","), ...rows.map((row) => row.join(","))].join("\n");
  }

  private delaysToCSV(data: any[]): string {
    const headers = [
      "Date",
      "Route",
      "Bus",
      "Scheduled Time",
      "Actual Time",
      "Delay (minutes)",
      "Reason",
    ];
    const rows = data.map((record) => [
      record.date,
      record.route?.name || "Unknown",
      record.bus?.plate_number || "Unknown",
      record.scheduled_time,
      record.actual_time,
      record.delay_minutes,
      record.reason || "On time",
    ]);

    return [headers.join(","), ...rows.map((row) => row.join(","))].join("\n");
  }

  private combinedToCSV(data: any): string {
    let csv = "COMBINED REPORT\n\n";

    if (data.attendance) {
      csv += "ATTENDANCE DATA\n";
      csv += this.attendanceToCSV(data.attendance.data) + "\n\n";
    }

    if (data.utilization) {
      csv += "BUS UTILIZATION DATA\n";
      csv += this.utilizationToCSV(data.utilization.data) + "\n\n";
    }

    if (data.performance) {
      csv += "DRIVER PERFORMANCE DATA\n";
      csv += this.performanceToCSV(data.performance.data) + "\n\n";
    }

    if (data.delays) {
      csv += "ROUTE DELAY DATA\n";
      csv += this.delaysToCSV(data.delays.data) + "\n\n";
    }

    return csv;
  }

  // Report template management
  async saveReportTemplate(
    template: Omit<ReportTemplate, "id" | "created_at" | "updated_at">,
  ): Promise<ReportTemplate> {
    const { data, error } = await supabase
      .from("report_templates")
      .insert({
        ...template,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getReportTemplates(
    tenantId: string,
    userId?: string,
  ): Promise<ReportTemplate[]> {
    let query = supabase
      .from("report_templates")
      .select("*")
      .eq("tenant_id", tenantId)
      .order("created_at", { ascending: false });

    if (userId) {
      query = query.or(`created_by.eq.${userId},is_public.eq.true`);
    }

    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  async deleteReportTemplate(templateId: string): Promise<void> {
    const { error } = await supabase
      .from("report_templates")
      .delete()
      .eq("id", templateId);

    if (error) throw error;
  }

  // Report scheduling
  async scheduleReport(
    schedule: Omit<
      ScheduledReport,
      "id" | "created_at" | "last_run" | "next_run"
    >,
  ): Promise<ScheduledReport> {
    const nextRun = this.calculateNextRun(schedule.schedule);

    const { data, error } = await supabase
      .from("scheduled_reports")
      .insert({
        ...schedule,
        next_run: nextRun,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getScheduledReports(tenantId: string): Promise<ScheduledReport[]> {
    const { data, error } = await supabase
      .from("scheduled_reports")
      .select("*")
      .eq("tenant_id", tenantId)
      .order("created_at", { ascending: false });

    if (error) throw error;
    return data || [];
  }

  private calculateNextRun(schedule: ScheduledReport["schedule"]): string {
    const now = new Date();
    const [hours, minutes] = schedule.time.split(":").map(Number);

    let nextRun = new Date();
    nextRun.setHours(hours, minutes, 0, 0);

    switch (schedule.frequency) {
      case "daily":
        if (nextRun <= now) {
          nextRun.setDate(nextRun.getDate() + 1);
        }
        break;
      case "weekly":
        const targetDay = schedule.dayOfWeek || 1; // Default to Monday
        const currentDay = nextRun.getDay();
        let daysUntilTarget = targetDay - currentDay;
        if (daysUntilTarget <= 0 || (daysUntilTarget === 0 && nextRun <= now)) {
          daysUntilTarget += 7;
        }
        nextRun.setDate(nextRun.getDate() + daysUntilTarget);
        break;
      case "monthly":
        const targetDate = schedule.dayOfMonth || 1;
        nextRun.setDate(targetDate);
        if (nextRun <= now) {
          nextRun.setMonth(nextRun.getMonth() + 1);
        }
        break;
    }

    return nextRun.toISOString();
  }
}

export const reportGeneratorService = new ReportGeneratorService();
export default reportGeneratorService;
