/**
 * Search Input Component
 * Reusable search input with debouncing and clear functionality
 * Phase 2: Application Structure Reorganization
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Search, X } from 'lucide-react';
import { cn } from '../../../utils/cn';

export interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  debounceMs?: number;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  autoFocus?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  onClear?: () => void;
}

/**
 * Search Input Component
 * Implements Single Responsibility Principle - handles search input with debouncing
 */
export const SearchInput: React.FC<SearchInputProps> = ({
  value,
  onChange,
  placeholder = 'Search...',
  debounceMs = 300,
  className,
  size = 'md',
  disabled = false,
  autoFocus = false,
  onFocus,
  onBlur,
  onClear,
}) => {
  const [internalValue, setInternalValue] = useState(value);

  // Sync internal value with external value
  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  // Debounced onChange
  useEffect(() => {
    const timer = setTimeout(() => {
      if (internalValue !== value) {
        onChange(internalValue);
      }
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [internalValue, debounceMs, onChange, value]);

  /**
   * Handle input change
   */
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setInternalValue(e.target.value);
  }, []);

  /**
   * Handle clear button click
   */
  const handleClear = useCallback(() => {
    setInternalValue('');
    onChange('');
    if (onClear) {
      onClear();
    }
  }, [onChange, onClear]);

  /**
   * Handle key press
   */
  const handleKeyPress = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Escape') {
      handleClear();
    }
  }, [handleClear]);

  /**
   * Size classes
   */
  const sizeClasses = {
    sm: 'h-8 text-sm',
    md: 'h-10 text-sm',
    lg: 'h-12 text-base',
  };

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const paddingClasses = {
    sm: 'pl-8 pr-8',
    md: 'pl-10 pr-10',
    lg: 'pl-12 pr-12',
  };

  return (
    <div className={cn('relative', className)}>
      {/* Search Icon */}
      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <Search className={cn('text-gray-400', iconSizeClasses[size])} />
      </div>

      {/* Input Field */}
      <input
        type="text"
        value={internalValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyPress}
        onFocus={onFocus}
        onBlur={onBlur}
        placeholder={placeholder}
        disabled={disabled}
        autoFocus={autoFocus}
        className={cn(
          'w-full border border-gray-300 rounded-md shadow-sm',
          'focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
          'disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed',
          'placeholder-gray-400',
          'transition-colors duration-200',
          sizeClasses[size],
          paddingClasses[size],
          className
        )}
      />

      {/* Clear Button */}
      {internalValue && !disabled && (
        <button
          type="button"
          onClick={handleClear}
          className={cn(
            'absolute inset-y-0 right-0 flex items-center pr-3',
            'text-gray-400 hover:text-gray-600',
            'transition-colors duration-200'
          )}
          tabIndex={-1}
        >
          <X className={iconSizeClasses[size]} />
        </button>
      )}
    </div>
  );
};

/**
 * Search Input with suggestions
 */
export interface SearchWithSuggestionsProps extends SearchInputProps {
  suggestions: string[];
  onSuggestionSelect: (suggestion: string) => void;
  maxSuggestions?: number;
  showSuggestions?: boolean;
}

export const SearchWithSuggestions: React.FC<SearchWithSuggestionsProps> = ({
  suggestions,
  onSuggestionSelect,
  maxSuggestions = 5,
  showSuggestions = true,
  ...searchProps
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);

  // Filter suggestions based on current value
  const filteredSuggestions = suggestions
    .filter(suggestion =>
      suggestion.toLowerCase().includes(searchProps.value.toLowerCase())
    )
    .slice(0, maxSuggestions);

  /**
   * Handle suggestion click
   */
  const handleSuggestionClick = useCallback((suggestion: string) => {
    onSuggestionSelect(suggestion);
    setIsOpen(false);
    setHighlightedIndex(-1);
  }, [onSuggestionSelect]);

  /**
   * Handle key navigation
   */
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!isOpen || filteredSuggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev =>
          prev < filteredSuggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev =>
          prev > 0 ? prev - 1 : filteredSuggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0) {
          handleSuggestionClick(filteredSuggestions[highlightedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setHighlightedIndex(-1);
        break;
    }
  }, [isOpen, filteredSuggestions, highlightedIndex, handleSuggestionClick]);

  /**
   * Handle focus
   */
  const handleFocus = useCallback(() => {
    if (showSuggestions && filteredSuggestions.length > 0) {
      setIsOpen(true);
    }
    searchProps.onFocus?.();
  }, [showSuggestions, filteredSuggestions.length, searchProps]);

  /**
   * Handle blur
   */
  const handleBlur = useCallback(() => {
    // Delay closing to allow suggestion clicks
    setTimeout(() => {
      setIsOpen(false);
      setHighlightedIndex(-1);
    }, 200);
    searchProps.onBlur?.();
  }, [searchProps]);

  return (
    <div className="relative">
      <SearchInput
        {...searchProps}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
      />

      {/* Suggestions Dropdown */}
      {isOpen && showSuggestions && filteredSuggestions.length > 0 && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {filteredSuggestions.map((suggestion, index) => (
            <button
              key={suggestion}
              type="button"
              className={cn(
                'w-full px-4 py-2 text-left hover:bg-gray-100',
                'focus:bg-gray-100 focus:outline-none',
                index === highlightedIndex && 'bg-gray-100'
              )}
              onClick={() => handleSuggestionClick(suggestion)}
            >
              {suggestion}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
