import { supabase } from "../lib/supabase";

// Debug utility to check database connection and data
export const debugDatabase = async () => {
  console.log("=== DATABASE DEBUG START ===");

  try {
    // Test basic connection
    console.log("1. Testing database connection...");
    const { data: connectionTest, error: connectionError } = await supabase
      .from("users")
      .select("count")
      .limit(1);

    console.log("Connection test result:", { connectionTest, connectionError });

    // Check if users table exists and has data
    console.log("2. Checking users table...");
    const {
      data: users,
      error: usersError,
      count,
    } = await supabase.from("users").select("*", { count: "exact" });

    console.log("Users query result:", {
      users: users,
      error: usersError,
      count: count,
      length: users?.length,
    });

    if (users && users.length > 0) {
      console.log("3. Users found:");
      users.forEach((user, index) => {
        console.log(`   User ${index + 1}:`, {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          tenant_id: user.tenant_id,
          is_active: user.is_active,
        });
      });
    } else {
      console.log("3. No users found!");
    }

    // Check current authenticated user
    console.log("4. Checking current auth session...");
    const { data: session, error: sessionError } =
      await supabase.auth.getSession();
    console.log("Current session:", {
      user: session?.user
        ? {
            id: session.user.id,
            email: session.user.email,
            role: session.user.user_metadata?.role,
          }
        : null,
      sessionError,
    });

    // Check if we can access the users table with current permissions
    console.log("5. Testing table permissions...");
    const { data: permissionTest, error: permissionError } = await supabase
      .from("users")
      .select("id, email, role")
      .limit(5);

    console.log("Permission test result:", {
      canAccess: !permissionError,
      recordCount: permissionTest?.length || 0,
      error: permissionError?.message,
    });
  } catch (error) {
    console.error("Database debug error:", error);
  }

  console.log("=== DATABASE DEBUG END ===");
};

// Function to manually create test users using the edge function
export const createTestUsers = async () => {
  console.log("Creating test users using edge function...");

  const testUsers = [
    {
      name: "Test School Manager",
      email: "<EMAIL>",
      password: "TestPassword123!",
      role: "school_manager",
      tenant_id: null,
      is_active: true,
    },
    {
      name: "Test Driver",
      email: "<EMAIL>",
      password: "TestPassword123!",
      role: "driver",
      tenant_id: null,
      is_active: true,
    },
    {
      name: "Test Parent",
      email: "<EMAIL>",
      password: "TestPassword123!",
      role: "parent",
      tenant_id: null,
      is_active: true,
    },
  ];

  for (const user of testUsers) {
    try {
      console.log(`Creating user: ${user.email}`);

      const { data, error } = await supabase.functions.invoke(
        "supabase-functions-create-user",
        {
          body: user,
        },
      );

      if (error) {
        console.error(`Error creating user ${user.email}:`, error);
      } else if (data?.success) {
        console.log(`✅ Created user ${user.email}:`, data.user);
      } else {
        console.error(`❌ Failed to create user ${user.email}:`, data?.error);
      }
    } catch (err) {
      console.error(`Exception creating user ${user.email}:`, err);
    }
  }

  console.log("Test user creation completed. Refreshing data...");
};
