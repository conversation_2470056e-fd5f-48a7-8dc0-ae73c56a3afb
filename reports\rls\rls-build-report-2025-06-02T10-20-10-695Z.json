{"build_info": {"timestamp": "2025-06-02T10-20-10-695Z", "total_tables": 8, "total_policies": 32, "total_functions": 6, "total_roles": 6, "total_migrations": 4}, "tables": [{"name": "tenants", "description": "جدول المستأجرين (المدارس)", "rls_required": true, "sensitive_data": ["settings", "contact_info"], "access_patterns": ["tenant_admin", "system_admin"]}, {"name": "users", "description": "جدول المستخدمين", "rls_required": true, "sensitive_data": ["email", "phone", "personal_info"], "access_patterns": ["self", "tenant_admin", "system_admin"]}, {"name": "buses", "description": "جدول الحافلات", "rls_required": true, "sensitive_data": ["gps_data", "maintenance_records"], "access_patterns": ["tenant_users", "drivers", "tenant_admin"]}, {"name": "students", "description": "جدول الطلاب", "rls_required": true, "sensitive_data": ["personal_info", "medical_info", "parent_contact"], "access_patterns": ["parents", "school_staff", "drivers", "tenant_admin"]}, {"name": "routes", "description": "جدول المسارات", "rls_required": true, "sensitive_data": ["gps_coordinates", "timing"], "access_patterns": ["tenant_users", "drivers", "parents"]}, {"name": "attendance", "description": "جدول الحضور", "rls_required": true, "sensitive_data": ["student_status", "location_data"], "access_patterns": ["parents", "school_staff", "drivers", "tenant_admin"]}, {"name": "notifications", "description": "جدول الإشعارات", "rls_required": true, "sensitive_data": ["message_content", "recipient_data"], "access_patterns": ["recipient", "sender", "tenant_admin"]}, {"name": "audit_logs", "description": "جدول سجلات المراجعة", "rls_required": true, "sensitive_data": ["user_actions", "data_changes"], "access_patterns": ["system_admin", "security_officer"]}], "policies": [{"table": "tenants", "name": "tenants_select_policy", "operation": "SELECT", "condition": "\n          (get_current_user_role() = 'system_admin') OR\n          (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n        ", "description": "سياسة قراءة جدول المستأجرين (المدارس)"}, {"table": "tenants", "name": "tenants_insert_policy", "operation": "INSERT", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n    ", "description": "سياسة إدراج جدول المستأجرين (المدارس)"}, {"table": "tenants", "name": "tenants_update_policy", "operation": "UPDATE", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n    ", "description": "سياسة تحديث جدول المستأجرين (المدارس)"}, {"table": "tenants", "name": "tenants_delete_policy", "operation": "DELETE", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin')\n    ", "description": "سياسة حذف جدو<PERSON> المستأجرين (المدارس)"}, {"table": "users", "name": "users_select_policy", "operation": "SELECT", "condition": "\n          (get_current_user_role() = 'system_admin') OR\n          (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')) OR\n          (id = auth.uid())\n        ", "description": "سياسة قراءة جدول المستخدمين"}, {"table": "users", "name": "users_insert_policy", "operation": "INSERT", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n    ", "description": "سياسة إدراج جدول المستخدمين"}, {"table": "users", "name": "users_update_policy", "operation": "UPDATE", "condition": "\n        (get_current_user_role() = 'system_admin') OR\n        (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')) OR\n        (id = auth.uid() AND get_current_user_role() IN ('parent', 'student', 'driver'))\n      ", "description": "سياسة تحديث جدول المستخدمين"}, {"table": "users", "name": "users_delete_policy", "operation": "DELETE", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin')\n    ", "description": "سياسة حذف جدو<PERSON> المستخدمين"}, {"table": "buses", "name": "buses_select_policy", "operation": "SELECT", "condition": "\n          (get_current_user_role() = 'system_admin') OR\n          (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')) OR\n          (can_access_bus(id))\n        ", "description": "سياسة قراءة جدول الحافلات"}, {"table": "buses", "name": "buses_insert_policy", "operation": "INSERT", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n    ", "description": "سياسة إدراج جدول الحافلات"}, {"table": "buses", "name": "buses_update_policy", "operation": "UPDATE", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n    ", "description": "سياسة تحديث جدول الحافلات"}, {"table": "buses", "name": "buses_delete_policy", "operation": "DELETE", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin')\n    ", "description": "سياسة حذف جدول الحافلات"}, {"table": "students", "name": "students_select_policy", "operation": "SELECT", "condition": "\n          (get_current_user_role() = 'system_admin') OR\n          (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')) OR\n          (can_access_student(id))\n        ", "description": "سياسة قراءة جدول الطلاب"}, {"table": "students", "name": "students_insert_policy", "operation": "INSERT", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n    ", "description": "سياسة إدراج جدول الطلاب"}, {"table": "students", "name": "students_update_policy", "operation": "UPDATE", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n    ", "description": "سياسة تحديث جدول الطلاب"}, {"table": "students", "name": "students_delete_policy", "operation": "DELETE", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin')\n    ", "description": "سياسة حذف جدول الطلاب"}, {"table": "routes", "name": "routes_select_policy", "operation": "SELECT", "condition": "\n          (get_current_user_role() = 'system_admin') OR\n          (tenant_id = get_current_tenant_id())\n        ", "description": "سياسة قراءة جدول المسارات"}, {"table": "routes", "name": "routes_insert_policy", "operation": "INSERT", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n    ", "description": "سياسة إدراج جدول المسارات"}, {"table": "routes", "name": "routes_update_policy", "operation": "UPDATE", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n    ", "description": "سياسة تحديث جدول المسارات"}, {"table": "routes", "name": "routes_delete_policy", "operation": "DELETE", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin')\n    ", "description": "سياسة حذف جدول المسارات"}, {"table": "attendance", "name": "attendance_select_policy", "operation": "SELECT", "condition": "\n          (get_current_user_role() = 'system_admin') OR\n          (tenant_id = get_current_tenant_id())\n        ", "description": "سياسة قراءة جدول الحضور"}, {"table": "attendance", "name": "attendance_insert_policy", "operation": "INSERT", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n    ", "description": "سياسة إدراج جدول الحضور"}, {"table": "attendance", "name": "attendance_update_policy", "operation": "UPDATE", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n    ", "description": "سياسة تحديث جدول الحضور"}, {"table": "attendance", "name": "attendance_delete_policy", "operation": "DELETE", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin')\n    ", "description": "سياسة حذف جدول الحضور"}, {"table": "notifications", "name": "notifications_select_policy", "operation": "SELECT", "condition": "\n          (get_current_user_role() = 'system_admin') OR\n          (tenant_id = get_current_tenant_id())\n        ", "description": "سياسة قراءة جدول الإشعارات"}, {"table": "notifications", "name": "notifications_insert_policy", "operation": "INSERT", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n    ", "description": "سياسة إدراج جدول الإشعارات"}, {"table": "notifications", "name": "notifications_update_policy", "operation": "UPDATE", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n    ", "description": "سياسة تحديث جدول الإشعارات"}, {"table": "notifications", "name": "notifications_delete_policy", "operation": "DELETE", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin')\n    ", "description": "سياسة حذف جدول الإشعارات"}, {"table": "audit_logs", "name": "audit_logs_select_policy", "operation": "SELECT", "condition": "\n          (get_current_user_role() = 'system_admin') OR\n          (tenant_id = get_current_tenant_id())\n        ", "description": "سياسة قراءة جدول سجلات المراجعة"}, {"table": "audit_logs", "name": "audit_logs_insert_policy", "operation": "INSERT", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n    ", "description": "سياسة إدراج جدول سجلات المراجعة"}, {"table": "audit_logs", "name": "audit_logs_update_policy", "operation": "UPDATE", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))\n    ", "description": "سياسة تحديث جدول سجلات المراجعة"}, {"table": "audit_logs", "name": "audit_logs_delete_policy", "operation": "DELETE", "condition": "\n      (get_current_user_role() = 'system_admin') OR\n      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin')\n    ", "description": "سياسة حذف جدول سجلات المراجعة"}], "functions": [{"name": "get_current_tenant_id", "description": "الحصول على معرف المستأجر الحالي", "returns": "uuid", "security_definer": true}, {"name": "get_current_user_role", "description": "الحصول على دور المستخدم الحالي", "returns": "text", "security_definer": true}, {"name": "is_tenant_admin", "description": "التحقق من كون المستخدم مدير مستأجر", "returns": "boolean", "security_definer": true}, {"name": "can_access_student", "description": "التحقق من إمكانية الوصول لبيانات الطالب", "parameters": ["student_id uuid"], "returns": "boolean", "security_definer": true}, {"name": "can_access_bus", "description": "التحقق من إمكانية الوصول لبيانات الحافلة", "parameters": ["bus_id uuid"], "returns": "boolean", "security_definer": true}, {"name": "audit_log_access", "description": "تسجيل محاولات الوصول", "parameters": ["table_name text", "operation text", "record_id uuid"], "returns": "void", "security_definer": true}], "roles": [{"name": "system_admin", "description": "مدير النظام العام", "permissions": ["all_tenants_read", "all_tenants_write", "system_config"], "rls_bypass": true}, {"name": "tenant_admin", "description": "مدير المدرسة", "permissions": ["tenant_full_access", "user_management", "reports"], "rls_bypass": false, "tenant_scoped": true}, {"name": "school_manager", "description": "مدير مدرسة", "permissions": ["tenant_read", "student_management", "bus_tracking"], "rls_bypass": false, "tenant_scoped": true}, {"name": "driver", "description": "سائق الحافلة", "permissions": ["bus_read", "attendance_write", "route_read"], "rls_bypass": false, "bus_scoped": true}, {"name": "parent", "description": "ولي أمر", "permissions": ["student_read", "attendance_read", "notifications_read"], "rls_bypass": false, "student_scoped": true}, {"name": "student", "description": "طالب", "permissions": ["self_read", "bus_location_read"], "rls_bypass": false, "self_scoped": true}], "migrations": [{"name": "2025-06-02_001_create_roles.sql", "type": "roles"}, {"name": "2025-06-02_002_create_security_functions.sql", "type": "functions"}, {"name": "2025-06-02_003_create_rls_policies.sql", "type": "policies"}, {"name": "2025-06-02_004_enable_rls.sql", "type": "enable_rls"}]}