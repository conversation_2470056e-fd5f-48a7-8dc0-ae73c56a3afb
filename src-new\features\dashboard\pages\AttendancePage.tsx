import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Calendar, Loader2, BarChart3 } from "lucide-react";
import { Navbar } from "../../components/layout/Navbar";
import { Sidebar } from "../../components/layout/Sidebar";
import { AttendanceList } from "../../components/students/AttendanceList";
import { AttendanceStats } from "../../components/students/AttendanceStats";
import { AttendanceDashboard } from "../../components/students/AttendanceDashboard";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";
import { Button } from "../../components/ui/Button";

export const AttendancePage: React.FC = () => {
  const { t } = useTranslation();
  const { students, loading, error } = useDatabase();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<"daily" | "dashboard">("daily");
  const [stats, setStats] = useState({
    total: 0,
    present: 0,
    absent: 0,
  });

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const startOfDay = new Date(selectedDate);
        startOfDay.setHours(0, 0, 0, 0);

        const endOfDay = new Date(selectedDate);
        endOfDay.setHours(23, 59, 59, 999);

        const { data, error } = await supabase
          .from("attendance")
          .select("student_id, type")
          .gte("recorded_at", startOfDay.toISOString())
          .lte("recorded_at", endOfDay.toISOString());

        if (error) throw error;

        const total = students.length;
        const present = new Set(data?.map((r) => r.student_id)).size;

        setStats({
          total,
          present,
          absent: total - present,
        });
      } catch (error) {
        console.error("Error fetching attendance stats:", error);
      }
    };

    fetchStats();
  }, [selectedDate, students]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin text-primary-500" />
          <span className="text-gray-600 dark:text-gray-300">
            {t("common.loading")}
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-error-600 dark:text-error-400">
          {error.message}
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />

        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {t("students.attendance")}
                </h1>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {viewMode === "daily"
                    ? selectedDate.toLocaleDateString()
                    : t("students.comprehensiveAttendanceAnalysis")}
                </p>
              </div>

              <div className="mt-4 md:mt-0 flex items-center space-x-4">
                <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                  <Button
                    variant={viewMode === "daily" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("daily")}
                    leftIcon={<Calendar size={16} />}
                  >
                    {t("students.dailyView")}
                  </Button>
                  <Button
                    variant={viewMode === "dashboard" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("dashboard")}
                    leftIcon={<BarChart3 size={16} />}
                  >
                    {t("students.dashboardView")}
                  </Button>
                </div>

                {viewMode === "daily" && (
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Calendar size={18} className="text-gray-400" />
                    </div>
                    <input
                      type="date"
                      className="block pl-10 pr-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                      value={selectedDate.toISOString().split("T")[0]}
                      onChange={(e) =>
                        setSelectedDate(new Date(e.target.value))
                      }
                    />
                  </div>
                )}
              </div>
            </div>

            {viewMode === "daily" ? (
              <>
                <AttendanceStats {...stats} />

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                  <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    {t("students.attendance")}
                  </h2>

                  <AttendanceList date={selectedDate} />
                </div>
              </>
            ) : (
              <AttendanceDashboard />
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AttendancePage;
