/**
 * Parent Dashboard Component
 * Provides parent-specific view with children information and bus tracking
 */

import React from "react";
import { useTranslation } from "react-i18next";
import {
  Users,
  Bus,
  MapPin,
  Clock,
  CheckCircle,
  AlertTriangle,
  Bell,
  Calendar,
  Navigation,
  Phone,
} from "lucide-react";
import { StatCard } from "../dashboard/StatCard";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { useRBACEnhancedSecurity } from "../../hooks/useRBACEnhancedSecurity";

interface ParentDashboardProps {
  className?: string;
}

export const ParentDashboard: React.FC<ParentDashboardProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { students, buses, routes, attendance } = useDatabase();
  const { filterDataByRole } = useRBACEnhancedSecurity();

  // Filter data to parent's children only
  const filteredStudents = filterDataByRole(students, "student", "parent_id");
  const myChildren = filteredStudents.filter((s) => s.parent_id === user?.id);

  // Get buses and routes for children
  const childrenBuses = buses.filter((bus) =>
    myChildren.some((child) => {
      const childRoute = routes.find((r) =>
        r.stops?.some((stop) => stop.id === child.route_stop_id),
      );
      return childRoute?.bus_id === bus.id;
    }),
  );

  const childrenRoutes = routes.filter((route) =>
    myChildren.some((child) =>
      route.stops?.some((stop) => stop.id === child.route_stop_id),
    ),
  );

  // Get recent attendance for children
  const recentAttendance = attendance
    .filter((a) => myChildren.some((child) => child.id === a.student_id))
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 5);

  const stats = {
    totalChildren: myChildren.length,
    activeChildren: myChildren.filter((s) => s.is_active).length,
    assignedBuses: childrenBuses.length,
    activeRoutes: childrenRoutes.filter((r) => r.is_active).length,
    todayAttendance: recentAttendance.filter(
      (a) => new Date(a.date).toDateString() === new Date().toDateString(),
    ).length,
    presentToday: recentAttendance.filter(
      (a) =>
        new Date(a.date).toDateString() === new Date().toDateString() &&
        a.status === "present",
    ).length,
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Parent Overview */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Welcome, {user?.name}
          </h2>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
              Parent Portal
            </span>
          </div>
        </div>
        <p className="text-gray-600 dark:text-gray-400">
          {myChildren.length > 0
            ? `You have ${myChildren.length} ${myChildren.length === 1 ? "child" : "children"} registered in the system.`
            : "No children registered. Please contact the school administration."}
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="My Children"
          value={stats.totalChildren}
          icon={<Users size={24} />}
          trend={{
            value: stats.activeChildren,
            isPositive: true,
            label: "Active",
          }}
          className="bg-gradient-to-r from-blue-500 to-blue-600 text-white"
        />
        <StatCard
          title="Assigned Buses"
          value={stats.assignedBuses}
          icon={<Bus size={24} />}
          className="bg-gradient-to-r from-green-500 to-green-600 text-white"
        />
        <StatCard
          title="Active Routes"
          value={stats.activeRoutes}
          icon={<Navigation size={24} />}
          className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white"
        />
        <StatCard
          title="Today's Attendance"
          value={`${stats.presentToday}/${stats.totalChildren}`}
          icon={<CheckCircle size={24} />}
          className="bg-gradient-to-r from-purple-500 to-purple-600 text-white"
        />
      </div>

      {/* Children Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            My Children
          </h3>
          {myChildren.length > 0 ? (
            <div className="space-y-4">
              {myChildren.map((child) => {
                const childRoute = childrenRoutes.find((route) =>
                  route.stops?.some((stop) => stop.id === child.route_stop_id),
                );
                const childBus = childRoute
                  ? childrenBuses.find((bus) => bus.id === childRoute.bus_id)
                  : null;
                const todayAttendance = recentAttendance.find(
                  (a) =>
                    a.student_id === child.id &&
                    new Date(a.date).toDateString() ===
                      new Date().toDateString(),
                );

                return (
                  <div
                    key={child.id}
                    className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                          <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {child.name}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Grade: {child.grade || "Not specified"}
                        </p>
                        {childBus && (
                          <p className="text-xs text-blue-600 dark:text-blue-400">
                            Bus: {childBus.plate_number}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {todayAttendance ? (
                        <span
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            todayAttendance.status === "present"
                              ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                              : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                          }`}
                        >
                          {todayAttendance.status === "present"
                            ? "Present"
                            : "Absent"}
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100">
                          No Record
                        </span>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Users size={32} className="mx-auto mb-2 opacity-50" />
              <p className="text-sm">No children registered</p>
            </div>
          )}
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Recent Activity
          </h3>
          {recentAttendance.length > 0 ? (
            <div className="space-y-3">
              {recentAttendance.map((record) => {
                const child = myChildren.find(
                  (c) => c.id === record.student_id,
                );
                return (
                  <div
                    key={`${record.student_id}-${record.date}`}
                    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className={`w-2 h-2 rounded-full ${
                          record.status === "present"
                            ? "bg-green-500"
                            : "bg-red-500"
                        }`}
                      ></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {child?.name}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {new Date(record.date).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <span
                      className={`text-xs font-medium ${
                        record.status === "present"
                          ? "text-green-600 dark:text-green-400"
                          : "text-red-600 dark:text-red-400"
                      }`}
                    >
                      {record.status === "present" ? "Present" : "Absent"}
                    </span>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Calendar size={32} className="mx-auto mb-2 opacity-50" />
              <p className="text-sm">No recent activity</p>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Quick Actions
          </h3>
          <div className="space-y-3">
            <button className="w-full flex items-center justify-between p-3 text-left bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
              <div className="flex items-center">
                <MapPin className="h-5 w-5 text-blue-500 mr-3" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  Track Bus Location
                </span>
              </div>
            </button>
            <button className="w-full flex items-center justify-between p-3 text-left bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
              <div className="flex items-center">
                <Calendar className="h-5 w-5 text-green-500 mr-3" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  View Attendance History
                </span>
              </div>
            </button>
            <button className="w-full flex items-center justify-between p-3 text-left bg-yellow-50 dark:bg-yellow-900/20 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors">
              <div className="flex items-center">
                <Bell className="h-5 w-5 text-yellow-500 mr-3" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  Notification Settings
                </span>
              </div>
            </button>
            <button className="w-full flex items-center justify-between p-3 text-left bg-red-50 dark:bg-red-900/20 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors">
              <div className="flex items-center">
                <Phone className="h-5 w-5 text-red-500 mr-3" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  Emergency Contact
                </span>
              </div>
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Bus Information
          </h3>
          {childrenBuses.length > 0 ? (
            <div className="space-y-3">
              {childrenBuses.map((bus) => {
                const busRoute = childrenRoutes.find(
                  (r) => r.bus_id === bus.id,
                );
                const assignedChildren = myChildren.filter((child) => {
                  return busRoute?.stops?.some(
                    (stop) => stop.id === child.route_stop_id,
                  );
                });

                return (
                  <div
                    key={bus.id}
                    className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <Bus className="h-5 w-5 text-blue-500" />
                        <span className="font-medium text-gray-900 dark:text-white">
                          {bus.plate_number}
                        </span>
                      </div>
                      <span
                        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          bus.is_active
                            ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                            : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100"
                        }`}
                      >
                        {bus.is_active ? "Active" : "Inactive"}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                      Route: {busRoute?.name || "Not assigned"}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Children on this bus:{" "}
                      {assignedChildren.map((c) => c.name).join(", ")}
                    </p>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Bus size={32} className="mx-auto mb-2 opacity-50" />
              <p className="text-sm">No bus assignments</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ParentDashboard;
