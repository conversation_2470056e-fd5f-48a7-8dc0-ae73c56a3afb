import React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "../../utils/cn";

const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-white transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 disabled:cursor-not-allowed dark:ring-offset-gray-950 relative overflow-hidden",
  {
    variants: {
      variant: {
        default:
          "bg-primary-600 text-white hover:bg-primary-700 hover:shadow-lg hover:scale-105 active:bg-primary-800 active:scale-95 disabled:bg-gray-400 disabled:hover:bg-gray-400 disabled:hover:scale-100 disabled:hover:shadow-none",
        secondary:
          "bg-secondary-600 text-white hover:bg-secondary-700 hover:shadow-lg hover:scale-105 active:bg-secondary-800 active:scale-95 disabled:bg-gray-400 disabled:hover:bg-gray-400 disabled:hover:scale-100 disabled:hover:shadow-none",
        success:
          "bg-accent-600 text-white hover:bg-accent-700 hover:shadow-lg hover:scale-105 active:bg-accent-800 active:scale-95 disabled:bg-gray-400 disabled:hover:bg-gray-400 disabled:hover:scale-100 disabled:hover:shadow-none",
        destructive:
          "bg-error-600 text-white hover:bg-error-700 hover:shadow-lg hover:scale-105 active:bg-error-800 active:scale-95 disabled:bg-gray-400 disabled:hover:bg-gray-400 disabled:hover:scale-100 disabled:hover:shadow-none",
        outline:
          "border border-gray-300 bg-transparent hover:bg-gray-100 hover:shadow-md hover:scale-105 active:bg-gray-200 active:scale-95 text-gray-800 dark:border-gray-700 dark:text-white dark:hover:bg-gray-800 dark:active:bg-gray-700 disabled:border-gray-200 disabled:text-gray-400 disabled:hover:bg-transparent disabled:hover:scale-100 disabled:hover:shadow-none",
        ghost:
          "hover:bg-gray-100 hover:text-gray-900 hover:shadow-md hover:scale-105 active:bg-gray-200 active:scale-95 dark:text-gray-100 dark:hover:bg-gray-800 dark:hover:text-white dark:active:bg-gray-700 disabled:text-gray-400 disabled:hover:bg-transparent disabled:hover:scale-100 disabled:hover:shadow-none",
        link: "text-primary-600 underline-offset-4 hover:underline hover:text-primary-700 dark:text-primary-500 dark:hover:text-primary-400 disabled:text-gray-400 disabled:no-underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-12 rounded-md px-6 text-base",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  loading?: boolean;
  loadingText?: string;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      leftIcon,
      rightIcon,
      children,
      loading = false,
      loadingText,
      disabled,
      ...props
    },
    ref,
  ) => {
    const Comp = asChild ? Slot : "button";
    const isDisabled = disabled || loading;

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        disabled={isDisabled}
        {...props}
      >
        {loading && (
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4 text-current"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        )}
        {!loading && leftIcon && <span className="mr-2">{leftIcon}</span>}
        {loading ? loadingText || children : children}
        {!loading && rightIcon && <span className="ml-2">{rightIcon}</span>}
      </Comp>
    );
  },
);

Button.displayName = "Button";

export { Button, buttonVariants };
