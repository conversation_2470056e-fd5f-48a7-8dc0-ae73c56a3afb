/**
 * Advanced Attendance Management Service
 * Phase 4: Core System Functionality Development
 */

import { supabase } from '../lib/supabase';
import { RealTimeTrackingService } from './RealTimeTrackingService';
import { SmartNotificationService } from './SmartNotificationService';

export interface AttendanceRecord {
  id: string;
  student_id: string;
  bus_id: string;
  tenant_id: string;
  type: 'pickup' | 'dropoff';
  status: 'present' | 'absent' | 'late' | 'early';
  recorded_at: string;
  recorded_by: string;
  location: {
    latitude: number;
    longitude: number;
  };
  route_stop_id?: string;
  notes?: string;
  photo_url?: string;
  verification_method: 'manual' | 'qr_code' | 'nfc' | 'facial_recognition';
}

export interface AttendanceSession {
  id: string;
  bus_id: string;
  route_id: string;
  driver_id: string;
  session_type: 'pickup' | 'dropoff';
  started_at: string;
  ended_at?: string;
  total_students: number;
  present_count: number;
  absent_count: number;
  status: 'active' | 'completed' | 'cancelled';
}

export interface AttendanceReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    totalStudents: number;
    averageAttendance: number;
    presentDays: number;
    absentDays: number;
    lateDays: number;
  };
  studentDetails: {
    studentId: string;
    studentName: string;
    attendanceRate: number;
    presentDays: number;
    absentDays: number;
    lateDays: number;
    trends: 'improving' | 'declining' | 'stable';
  }[];
  patterns: {
    dayOfWeek: Record<string, number>;
    timeOfDay: Record<string, number>;
    routePerformance: Record<string, number>;
  };
}

export interface AttendanceAlert {
  type: 'absent_student' | 'late_pickup' | 'missing_student' | 'route_delay';
  priority: 'low' | 'medium' | 'high' | 'critical';
  studentId?: string;
  busId?: string;
  routeId?: string;
  message: string;
  timestamp: string;
  autoResolved: boolean;
}

export class AdvancedAttendanceService {
  private static instance: AdvancedAttendanceService;
  private trackingService = RealTimeTrackingService.getInstance();
  private notificationService = SmartNotificationService.getInstance();
  private activeSessions = new Map<string, AttendanceSession>();

  private constructor() {}

  static getInstance(): AdvancedAttendanceService {
    if (!AdvancedAttendanceService.instance) {
      AdvancedAttendanceService.instance = new AdvancedAttendanceService();
    }
    return AdvancedAttendanceService.instance;
  }

  /**
   * Start attendance session
   */
  async startAttendanceSession(
    busId: string,
    routeId: string,
    driverId: string,
    sessionType: 'pickup' | 'dropoff',
    tenantId: string
  ): Promise<string> {
    try {
      // Get expected students for this route
      const { data: students, error: studentsError } = await supabase
        .from('students')
        .select(`
          id, name,
          route_stop:route_stops!route_stop_id(
            id, route_id
          )
        `)
        .eq('tenant_id', tenantId)
        .eq('is_active', true);

      if (studentsError) throw studentsError;

      const routeStudents = students?.filter(
        s => s.route_stop?.route_id === routeId
      ) || [];

      const session: Omit<AttendanceSession, 'id'> = {
        bus_id: busId,
        route_id: routeId,
        driver_id: driverId,
        session_type: sessionType,
        started_at: new Date().toISOString(),
        total_students: routeStudents.length,
        present_count: 0,
        absent_count: 0,
        status: 'active',
      };

      const { data, error } = await supabase
        .from('attendance_sessions')
        .insert([session])
        .select()
        .single();

      if (error) throw error;

      const sessionId = data.id;
      this.activeSessions.set(sessionId, { ...session, id: sessionId });

      // Create pending attendance records for all students
      const pendingRecords = routeStudents.map(student => ({
        student_id: student.id,
        bus_id: busId,
        tenant_id: tenantId,
        type: sessionType,
        status: 'absent', // Default to absent until marked present
        recorded_at: new Date().toISOString(),
        recorded_by: driverId,
        location: { latitude: 0, longitude: 0 }, // Will be updated when recorded
        route_stop_id: student.route_stop?.id,
        verification_method: 'manual',
        session_id: sessionId,
      }));

      if (pendingRecords.length > 0) {
        await supabase.from('attendance').insert(pendingRecords);
      }

      return sessionId;
    } catch (error) {
      console.error('Error starting attendance session:', error);
      throw error;
    }
  }

  /**
   * Record student attendance
   */
  async recordAttendance(
    sessionId: string,
    studentId: string,
    status: 'present' | 'absent' | 'late',
    location: { latitude: number; longitude: number },
    notes?: string,
    photoUrl?: string,
    verificationMethod: 'manual' | 'qr_code' | 'nfc' | 'facial_recognition' = 'manual'
  ): Promise<void> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        throw new Error('Session not found or not active');
      }

      // Update attendance record
      const { error } = await supabase
        .from('attendance')
        .update({
          status,
          location,
          notes,
          photo_url: photoUrl,
          verification_method: verificationMethod,
          recorded_at: new Date().toISOString(),
        })
        .eq('session_id', sessionId)
        .eq('student_id', studentId);

      if (error) throw error;

      // Update session counts
      await this.updateSessionCounts(sessionId);

      // Send notifications for important events
      if (status === 'absent') {
        await this.handleAbsentStudent(studentId, session);
      } else if (status === 'late') {
        await this.handleLateStudent(studentId, session);
      }

      // Check for completion
      await this.checkSessionCompletion(sessionId);
    } catch (error) {
      console.error('Error recording attendance:', error);
      throw error;
    }
  }

  /**
   * Bulk record attendance
   */
  async recordBulkAttendance(
    sessionId: string,
    attendanceData: Array<{
      studentId: string;
      status: 'present' | 'absent' | 'late';
      notes?: string;
    }>,
    location: { latitude: number; longitude: number }
  ): Promise<void> {
    try {
      for (const record of attendanceData) {
        await this.recordAttendance(
          sessionId,
          record.studentId,
          record.status,
          location,
          record.notes
        );
      }
    } catch (error) {
      console.error('Error recording bulk attendance:', error);
      throw error;
    }
  }

  /**
   * End attendance session
   */
  async endAttendanceSession(sessionId: string): Promise<void> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        throw new Error('Session not found');
      }

      // Update session status
      await supabase
        .from('attendance_sessions')
        .update({
          ended_at: new Date().toISOString(),
          status: 'completed',
        })
        .eq('id', sessionId);

      // Generate session summary
      await this.generateSessionSummary(sessionId);

      // Remove from active sessions
      this.activeSessions.delete(sessionId);

      // Send completion notifications
      await this.notifySessionCompletion(session);
    } catch (error) {
      console.error('Error ending attendance session:', error);
      throw error;
    }
  }

  /**
   * Get attendance report
   */
  async generateAttendanceReport(
    tenantId: string,
    startDate: string,
    endDate: string,
    filters?: {
      studentIds?: string[];
      routeIds?: string[];
      busIds?: string[];
    }
  ): Promise<AttendanceReport> {
    try {
      let query = supabase
        .from('attendance')
        .select(`
          *,
          student:students!student_id(id, name, grade),
          bus:buses!bus_id(id, plate_number),
          route_stop:route_stops!route_stop_id(
            id, name,
            route:routes!route_id(id, name)
          )
        `)
        .eq('tenant_id', tenantId)
        .gte('recorded_at', startDate)
        .lte('recorded_at', endDate);

      // Apply filters
      if (filters?.studentIds?.length) {
        query = query.in('student_id', filters.studentIds);
      }
      if (filters?.busIds?.length) {
        query = query.in('bus_id', filters.busIds);
      }

      const { data: attendanceData, error } = await query;
      if (error) throw error;

      // Calculate summary statistics
      const totalRecords = attendanceData?.length || 0;
      const presentRecords = attendanceData?.filter(r => r.status === 'present').length || 0;
      const absentRecords = attendanceData?.filter(r => r.status === 'absent').length || 0;
      const lateRecords = attendanceData?.filter(r => r.status === 'late').length || 0;

      // Calculate student-level details
      const studentMap = new Map<string, any>();
      attendanceData?.forEach(record => {
        const studentId = record.student_id;
        if (!studentMap.has(studentId)) {
          studentMap.set(studentId, {
            studentId,
            studentName: record.student?.name || 'Unknown',
            presentDays: 0,
            absentDays: 0,
            lateDays: 0,
            totalDays: 0,
          });
        }

        const student = studentMap.get(studentId);
        student.totalDays++;
        
        switch (record.status) {
          case 'present':
            student.presentDays++;
            break;
          case 'absent':
            student.absentDays++;
            break;
          case 'late':
            student.lateDays++;
            break;
        }
      });

      const studentDetails = Array.from(studentMap.values()).map(student => ({
        ...student,
        attendanceRate: student.totalDays > 0 
          ? (student.presentDays / student.totalDays) * 100 
          : 0,
        trends: this.calculateTrend(student) as 'improving' | 'declining' | 'stable',
      }));

      // Calculate patterns
      const patterns = this.calculateAttendancePatterns(attendanceData || []);

      return {
        period: { start: startDate, end: endDate },
        summary: {
          totalStudents: studentMap.size,
          averageAttendance: totalRecords > 0 ? (presentRecords / totalRecords) * 100 : 0,
          presentDays: presentRecords,
          absentDays: absentRecords,
          lateDays: lateRecords,
        },
        studentDetails,
        patterns,
      };
    } catch (error) {
      console.error('Error generating attendance report:', error);
      throw error;
    }
  }

  /**
   * Get real-time attendance alerts
   */
  async getAttendanceAlerts(tenantId: string): Promise<AttendanceAlert[]> {
    try {
      // Check for various alert conditions
      const alerts: AttendanceAlert[] = [];

      // Check for students who haven't been picked up
      const missedPickups = await this.checkMissedPickups(tenantId);
      alerts.push(...missedPickups);

      // Check for late routes
      const lateRoutes = await this.checkLateRoutes(tenantId);
      alerts.push(...lateRoutes);

      // Check for unusual absence patterns
      const absenceAlerts = await this.checkAbsencePatterns(tenantId);
      alerts.push(...absenceAlerts);

      return alerts;
    } catch (error) {
      console.error('Error getting attendance alerts:', error);
      return [];
    }
  }

  /**
   * Private helper methods
   */
  private async updateSessionCounts(sessionId: string): Promise<void> {
    const { data, error } = await supabase
      .from('attendance')
      .select('status')
      .eq('session_id', sessionId);

    if (error) return;

    const presentCount = data?.filter(r => r.status === 'present').length || 0;
    const absentCount = data?.filter(r => r.status === 'absent').length || 0;

    await supabase
      .from('attendance_sessions')
      .update({
        present_count: presentCount,
        absent_count: absentCount,
      })
      .eq('id', sessionId);

    // Update local session
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.present_count = presentCount;
      session.absent_count = absentCount;
    }
  }

  private async handleAbsentStudent(studentId: string, session: AttendanceSession): Promise<void> {
    // Get student and parent information
    const { data: student } = await supabase
      .from('students')
      .select(`
        *,
        parent:users!parent_id(id, name, email, phone)
      `)
      .eq('id', studentId)
      .single();

    if (student?.parent) {
      await this.notificationService.sendSmartNotification(
        'student_absent_template',
        [{ type: 'user', identifier: student.parent.id }],
        {
          studentName: student.name,
          sessionType: session.session_type,
          timestamp: new Date().toLocaleString(),
        },
        session.tenant_id || '',
        'high'
      );
    }
  }

  private async handleLateStudent(studentId: string, session: AttendanceSession): Promise<void> {
    // Similar to handleAbsentStudent but for late arrivals
  }

  private async checkSessionCompletion(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    const recordedCount = session.present_count + session.absent_count;
    if (recordedCount >= session.total_students) {
      await this.endAttendanceSession(sessionId);
    }
  }

  private async generateSessionSummary(sessionId: string): Promise<void> {
    // Generate and store session summary for reporting
  }

  private async notifySessionCompletion(session: AttendanceSession): Promise<void> {
    // Send completion notifications to relevant parties
  }

  private calculateTrend(student: any): string {
    // Simple trend calculation - can be enhanced
    const attendanceRate = student.attendanceRate;
    if (attendanceRate >= 90) return 'stable';
    if (attendanceRate >= 70) return 'declining';
    return 'improving';
  }

  private calculateAttendancePatterns(data: any[]): any {
    // Calculate day of week, time of day, and route performance patterns
    return {
      dayOfWeek: {},
      timeOfDay: {},
      routePerformance: {},
    };
  }

  private async checkMissedPickups(tenantId: string): Promise<AttendanceAlert[]> {
    // Check for students who should have been picked up but weren't
    return [];
  }

  private async checkLateRoutes(tenantId: string): Promise<AttendanceAlert[]> {
    // Check for routes running behind schedule
    return [];
  }

  private async checkAbsencePatterns(tenantId: string): Promise<AttendanceAlert[]> {
    // Check for unusual absence patterns
    return [];
  }
}

export default AdvancedAttendanceService;
