import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Navigation,
  AlertTriangle,
  Clock,
  MapPin,
  Gauge,
  Route,
  Users,
  Battery,
  Wifi,
  WifiOff,
} from "lucide-react";
import { LiveMap } from "../map/LiveMap";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/api";

interface BusTrackingData {
  bus: Tables<"buses">;
  route?: Tables<"routes">;
  currentSpeed: number;
  heading: number;
  distanceTraveled: number;
  estimatedArrival: string;
  nextStop?: string;
  studentsOnBoard: number;
  isOnline: boolean;
  lastUpdate: Date;
  alerts: Alert[];
}

interface Alert {
  id: string;
  type: "speed" | "route" | "maintenance" | "emergency";
  message: string;
  severity: "low" | "medium" | "high";
  timestamp: Date;
}

export const EnhancedBusTracking: React.FC = () => {
  const { t } = useTranslation();
  const { buses, routes, students } = useDatabase();
  const [selectedBus, setSelectedBus] = useState<string | null>(null);
  const [trackingData, setTrackingData] = useState<
    Map<string, BusTrackingData>
  >(new Map());
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    initializeTracking();
    const interval = setInterval(updateTrackingData, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, [buses, routes]);

  const initializeTracking = async () => {
    try {
      setLoading(true);
      const trackingMap = new Map<string, BusTrackingData>();

      for (const bus of buses) {
        const route = routes.find((r) => r.bus_id === bus.id);
        const busStudents = students.filter((s) => {
          if (!route?.stops) return false;
          return route.stops.some((stop) => stop.id === s.route_stop_id);
        });

        trackingMap.set(bus.id, {
          bus,
          route,
          currentSpeed: 0,
          heading: 0,
          distanceTraveled: 0,
          estimatedArrival: "N/A",
          nextStop: route?.stops?.[0]?.name,
          studentsOnBoard: busStudents.length,
          isOnline: bus.last_updated
            ? Date.now() - new Date(bus.last_updated).getTime() < 300000
            : false,
          lastUpdate: bus.last_updated
            ? new Date(bus.last_updated)
            : new Date(),
          alerts: [],
        });
      }

      setTrackingData(trackingMap);
    } catch (error) {
      console.error("Error initializing tracking:", error);
    } finally {
      setLoading(false);
    }
  };

  const updateTrackingData = async () => {
    try {
      // Fetch latest bus data
      const { data: latestBuses } = await supabase
        .from("buses")
        .select("*")
        .in(
          "id",
          buses.map((b) => b.id),
        );

      if (!latestBuses) return;

      const updatedTrackingData = new Map(trackingData);
      const newAlerts: Alert[] = [];

      for (const bus of latestBuses) {
        const existing = updatedTrackingData.get(bus.id);
        if (!existing) continue;

        const isOnline = bus.last_updated
          ? Date.now() - new Date(bus.last_updated).getTime() < 300000
          : false;

        // Calculate speed and distance if location data is available
        let currentSpeed = 0;
        let distanceTraveled = existing.distanceTraveled;

        if (bus.last_location && existing.bus.last_location) {
          const currentLocation = bus.last_location as any;
          const previousLocation = existing.bus.last_location as any;

          if (currentLocation.coordinates && previousLocation.coordinates) {
            const distance = calculateDistance(
              previousLocation.coordinates[1],
              previousLocation.coordinates[0],
              currentLocation.coordinates[1],
              currentLocation.coordinates[0],
            );

            const timeDiff =
              bus.last_updated && existing.bus.last_updated
                ? (new Date(bus.last_updated).getTime() -
                    new Date(existing.bus.last_updated).getTime()) /
                  1000
                : 0;

            if (timeDiff > 0) {
              currentSpeed = (distance / timeDiff) * 3.6; // Convert m/s to km/h
              distanceTraveled += distance;
            }

            // Check for speed alerts
            if (currentSpeed > 80) {
              newAlerts.push({
                id: `speed-${bus.id}-${Date.now()}`,
                type: "speed",
                message: `${bus.plate_number} تجاوز السرعة المسموحة (${currentSpeed.toFixed(0)} كم/س)`,
                severity: "high",
                timestamp: new Date(),
              });
            }
          }
        }

        // Check for offline alerts
        if (!isOnline && existing.isOnline) {
          newAlerts.push({
            id: `offline-${bus.id}-${Date.now()}`,
            type: "maintenance",
            message: `${bus.plate_number} غير متصل`,
            severity: "medium",
            timestamp: new Date(),
          });
        }

        updatedTrackingData.set(bus.id, {
          ...existing,
          bus,
          currentSpeed,
          distanceTraveled,
          isOnline,
          lastUpdate: bus.last_updated
            ? new Date(bus.last_updated)
            : existing.lastUpdate,
        });
      }

      setTrackingData(updatedTrackingData);

      if (newAlerts.length > 0) {
        setAlerts((prev) => [...newAlerts, ...prev].slice(0, 50)); // Keep last 50 alerts
      }
    } catch (error) {
      console.error("Error updating tracking data:", error);
    }
  };

  const calculateDistance = (
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number,
  ): number => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lng2 - lng1) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  };

  const dismissAlert = (alertId: string) => {
    setAlerts((prev) => prev.filter((alert) => alert.id !== alertId));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
      </div>
    );
  }

  const selectedBusData = selectedBus ? trackingData.get(selectedBus) : null;

  return (
    <div className="space-y-6">
      {/* Alerts Panel */}
      {alerts.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3 flex items-center">
            <AlertTriangle size={20} className="mr-2 text-orange-500" />
            {t("tracking.activeAlerts")} ({alerts.length})
          </h3>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {alerts.slice(0, 5).map((alert) => (
              <div
                key={alert.id}
                className={`flex items-center justify-between p-2 rounded-md ${
                  alert.severity === "high"
                    ? "bg-red-100 dark:bg-red-900/20"
                    : alert.severity === "medium"
                      ? "bg-orange-100 dark:bg-orange-900/20"
                      : "bg-yellow-100 dark:bg-yellow-900/20"
                }`}
              >
                <div className="flex items-center space-x-2">
                  <AlertTriangle
                    size={16}
                    className={`${
                      alert.severity === "high"
                        ? "text-red-600"
                        : alert.severity === "medium"
                          ? "text-orange-600"
                          : "text-yellow-600"
                    }`}
                  />
                  <span className="text-sm text-gray-900 dark:text-white">
                    {alert.message}
                  </span>
                </div>
                <button
                  onClick={() => dismissAlert(alert.id)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Bus List */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {t("tracking.activeBuses")} (
              {
                Array.from(trackingData.values()).filter((d) => d.isOnline)
                  .length
              }
              )
            </h3>
            <div className="space-y-2">
              {Array.from(trackingData.values()).map((data) => (
                <button
                  key={data.bus.id}
                  onClick={() => setSelectedBus(data.bus.id)}
                  className={`w-full p-3 rounded-lg text-left transition-colors ${
                    selectedBus === data.bus.id
                      ? "bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800"
                      : "hover:bg-gray-50 dark:hover:bg-gray-700"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div
                        className={`w-3 h-3 rounded-full ${
                          data.isOnline ? "bg-green-400" : "bg-red-400"
                        }`}
                      />
                      <span className="font-medium text-gray-900 dark:text-white">
                        {data.bus.plate_number}
                      </span>
                    </div>
                    {data.isOnline ? (
                      <Wifi size={16} className="text-green-500" />
                    ) : (
                      <WifiOff size={16} className="text-red-500" />
                    )}
                  </div>
                  <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    {data.route?.name || "No Route"} • {data.studentsOnBoard}{" "}
                    {t("students.students")}
                  </div>
                  <div className="mt-1 flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400">
                    <span className="flex items-center">
                      <Gauge size={12} className="mr-1" />
                      {data.currentSpeed.toFixed(0)} km/h
                    </span>
                    <span className="flex items-center">
                      <Clock size={12} className="mr-1" />
                      {data.lastUpdate.toLocaleTimeString()}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Map and Details */}
        <div className="lg:col-span-3">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <div className="h-[500px]">
              <LiveMap
                selectedBusId={selectedBus || undefined}
                onBusSelect={(bus) => setSelectedBus(bus.id)}
                showRoutes={true}
                showGeofences={true}
                showSpeedInfo={true}
                showETA={true}
              />
            </div>
          </div>

          {/* Bus Details */}
          {selectedBusData && (
            <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                {t("tracking.busDetails")} - {selectedBusData.bus.plate_number}
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <Gauge className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {t("tracking.currentSpeed")}
                    </p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {selectedBusData.currentSpeed.toFixed(0)} km/h
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                    <Route className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {t("tracking.distanceTraveled")}
                    </p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {(selectedBusData.distanceTraveled / 1000).toFixed(1)} km
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                    <Users className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {t("students.onBoard")}
                    </p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {selectedBusData.studentsOnBoard}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div
                    className={`p-2 rounded-lg ${
                      selectedBusData.isOnline
                        ? "bg-green-100 dark:bg-green-900/20"
                        : "bg-red-100 dark:bg-red-900/20"
                    }`}
                  >
                    {selectedBusData.isOnline ? (
                      <Wifi className="h-5 w-5 text-green-600 dark:text-green-400" />
                    ) : (
                      <WifiOff className="h-5 w-5 text-red-600 dark:text-red-400" />
                    )}
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {t("tracking.status")}
                    </p>
                    <p
                      className={`text-lg font-semibold ${
                        selectedBusData.isOnline
                          ? "text-green-600 dark:text-green-400"
                          : "text-red-600 dark:text-red-400"
                      }`}
                    >
                      {selectedBusData.isOnline
                        ? t("tracking.online")
                        : t("tracking.offline")}
                    </p>
                  </div>
                </div>
              </div>

              {selectedBusData.nextStop && (
                <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <MapPin size={16} className="text-gray-500" />
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {t("tracking.nextStop")}: {selectedBusData.nextStop}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2 mt-1">
                    <Clock size={16} className="text-gray-500" />
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {t("tracking.estimatedArrival")}:{" "}
                      {selectedBusData.estimatedArrival}
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedBusTracking;
