/**
 * نظام الإحصائيات الاحترافي - Professional Statistics System
 * يدعم النظام الجديد للصلاحيات مع تصميم احترافي وشامل
 */

import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDatabase } from "../../contexts/DatabaseContext";
import { useAuth } from "../../contexts/AuthContext";
import { usePermissions } from "../../hooks/usePermissions";
import { EnhancedStatCard as StatCard } from "./EnhancedStatCard";
import {
  Users,
  Bus,
  Route,
  GraduationCap,
  Activity,
  AlertTriangle,
  CheckCircle,
  Shield,
  School,
  UserCheck,
  Navigation,
  MessageSquare,
  Wrench,
  TrendingUp,
  TrendingDown,
  Clock,
  Settings,
  BarChart3,
  PieChart,
  Calendar,
  Target,
  Zap,
  Heart,
  Award,
  Globe,
} from "lucide-react";
import { Permission } from "../../lib/rbac";
import { UserRole } from "../../types";

interface ProfessionalStatsData {
  // إحصائيات أساسية
  schools: {
    total: number;
    active: number;
    inactive: number;
    growth: number;
  };
  users: {
    total: number;
    active: number;
    inactive: number;
    byRole: Record<string, number>;
    growth: number;
  };
  buses: {
    total: number;
    active: number;
    inService: number;
    inMaintenance: number;
    outOfService: number;
    utilizationRate: number;
  };
  routes: {
    total: number;
    active: number;
    inactive: number;
    coverage: number;
  };
  students: {
    total: number;
    active: number;
    suspended: number;
    growth: number;
  };
  // مقاييس الأداء
  performance: {
    systemHealth: number;
    responseTime: number;
    uptime: number;
    errorRate: number;
  };
  // مقاييس الأمان
  security: {
    score: number;
    compliance: string;
    threats: number;
    lastAudit: string;
  };
}

export const ProfessionalStats: React.FC = () => {
  const { t } = useTranslation();
  const { users, buses, routes, students, tenants, loading, error } = useDatabase();
  const { user } = useAuth();
  const {
    hasPermission,
    isAdmin,
    isSchoolManager,
    isSupervisor,
    isDriver,
    isParent,
    isStudent,
  } = usePermissions();

  // حساب الإحصائيات الشاملة
  const stats: ProfessionalStatsData = useMemo(() => {
    // تصفية البيانات حسب الصلاحيات
    let filteredUsers = users;
    let filteredBuses = buses;
    let filteredRoutes = routes;
    let filteredStudents = students;
    let filteredTenants = tenants;

    // تطبيق فلترة البيانات حسب الدور
    if (!isAdmin && user?.tenant_id) {
      filteredUsers = users.filter(u => u.tenant_id === user.tenant_id);
      filteredBuses = buses.filter(b => b.tenant_id === user.tenant_id);
      filteredRoutes = routes.filter(r => r.tenant_id === user.tenant_id);
      filteredStudents = students.filter(s => s.tenant_id === user.tenant_id);
      filteredTenants = tenants.filter(t => t.id === user.tenant_id);
    }

    // فلترة خاصة بالأدوار
    if (isParent) {
      filteredStudents = students.filter(s => s.parent_id === user?.id);
      filteredBuses = buses.filter(b => 
        filteredStudents.some(s => 
          routes.some(r => r.bus_id === b.id && 
            r.stops?.some(stop => stop.id === s.route_stop_id)
          )
        )
      );
    } else if (isDriver) {
      const myBus = buses.find(b => b.driver_id === user?.id);
      filteredBuses = myBus ? [myBus] : [];
      filteredRoutes = routes.filter(r => r.bus_id === myBus?.id);
      filteredStudents = students.filter(s => 
        filteredRoutes.some(route => 
          route.stops?.some(stop => stop.id === s.route_stop_id)
        )
      );
    }

    // حساب إحصائيات المدارس
    const schoolStats = {
      total: filteredTenants.length,
      active: filteredTenants.filter(t => t.is_active).length,
      inactive: filteredTenants.filter(t => !t.is_active).length,
      growth: 12.5, // نمو افتراضي - يمكن حسابه من البيانات التاريخية
    };

    // حساب إحصائيات المستخدمين
    const usersByRole: Record<string, number> = {};
    filteredUsers.forEach(user => {
      usersByRole[user.role] = (usersByRole[user.role] || 0) + 1;
    });

    const userStats = {
      total: filteredUsers.length,
      active: filteredUsers.filter(u => u.is_active).length,
      inactive: filteredUsers.filter(u => !u.is_active).length,
      byRole: usersByRole,
      growth: 8.3,
    };

    // حساب إحصائيات الحافلات المتقدمة
    const activeBuses = filteredBuses.filter(b => b.is_active);
    const busStats = {
      total: filteredBuses.length,
      active: activeBuses.length,
      inService: activeBuses.filter(b => b.status === 'in_service' || !b.status).length,
      inMaintenance: activeBuses.filter(b => b.status === 'maintenance').length,
      outOfService: filteredBuses.filter(b => !b.is_active).length,
      utilizationRate: filteredBuses.length > 0 ? (activeBuses.length / filteredBuses.length) * 100 : 0,
    };

    // حساب إحصائيات المسارات
    const routeStats = {
      total: filteredRoutes.length,
      active: filteredRoutes.filter(r => r.is_active).length,
      inactive: filteredRoutes.filter(r => !r.is_active).length,
      coverage: 85.7, // نسبة التغطية - يمكن حسابها من البيانات الجغرافية
    };

    // حساب إحصائيات الطلاب
    const studentStats = {
      total: filteredStudents.length,
      active: filteredStudents.filter(s => s.is_active).length,
      suspended: filteredStudents.filter(s => !s.is_active).length,
      growth: 15.2,
    };

    // مقاييس الأداء (محاكاة - يمكن ربطها بمقاييس حقيقية)
    const performance = {
      systemHealth: 98.5,
      responseTime: 245, // milliseconds
      uptime: 99.9,
      errorRate: 0.1,
    };

    // مقاييس الأمان
    const security = {
      score: isAdmin ? 95 : 0,
      compliance: isAdmin ? "excellent" : "unknown",
      threats: isAdmin ? 0 : 0,
      lastAudit: new Date().toISOString().split('T')[0],
    };

    return {
      schools: schoolStats,
      users: userStats,
      buses: busStats,
      routes: routeStats,
      students: studentStats,
      performance,
      security,
    };
  }, [users, buses, routes, students, tenants, user, isAdmin, isParent, isDriver]);

  // إظهار البيانات حتى لو كانت فارغة للتشخيص
  console.log("ProfessionalStats - Data:", {
    users: users.length,
    buses: buses.length,
    students: students.length,
    routes: routes.length,
    tenants: tenants.length,
    loading,
    error,
    stats
  });

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">جاري تحميل الإحصائيات...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2" />
          <p className="text-red-800 dark:text-red-200">
            خطأ في تحميل الإحصائيات: {error.message}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* معلومات تشخيصية */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">معلومات تشخيصية</h3>
        <div className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
          <p>المستخدمون: {users.length} | الحافلات: {buses.length} | الطلاب: {students.length} | المسارات: {routes.length}</p>
          <p>الدور: {user?.role} | المستأجر: {user?.tenant_id}</p>
          <p>الصلاحيات: Admin={isAdmin.toString()}, SchoolManager={isSchoolManager.toString()}</p>
        </div>
      </div>

      {/* الإحصائيات الأساسية الشاملة */}
      <div>
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
          <BarChart3 className="h-6 w-6 mr-2 text-primary-600" />
          الإحصائيات الأساسية
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* إحصائيات المدارس */}
          {isAdmin && (
            <StatCard
              title="المدارس المسجلة"
              value={stats.schools.total}
              icon={<School className="h-6 w-6" />}
              trend={{
                value: stats.schools.growth,
                label: "نمو شهري",
                isPositive: stats.schools.growth > 0,
              }}
              color="indigo"
              subtitle={`${stats.schools.active} نشطة • ${stats.schools.inactive} غير نشطة`}
            />
          )}

          {/* إجمالي المستخدمين */}
          <StatCard
            title={isAdmin ? "إجمالي المستخدمين" : "المستخدمون"}
            value={stats.users.total}
            icon={<Users className="h-6 w-6" />}
            trend={{
              value: stats.users.growth,
              label: "نمو شهري",
              isPositive: stats.users.growth > 0,
            }}
            color="blue"
            subtitle={`${stats.users.active} نشط • ${stats.users.inactive} غير نشط`}
          />

          {/* إحصائيات الحافلات */}
          <StatCard
            title={isAdmin ? "إجمالي الحافلات" : "الحافلات"}
            value={stats.buses.total}
            icon={<Bus className="h-6 w-6" />}
            trend={{
              value: stats.buses.utilizationRate,
              label: "معدل الاستخدام",
              isPositive: stats.buses.utilizationRate > 80,
            }}
            color="green"
            subtitle={`${stats.buses.inService} في الخدمة • ${stats.buses.inMaintenance} صيانة`}
          />

          {/* إحصائيات الطلاب */}
          <StatCard
            title={isAdmin ? "إجمالي الطلاب" : "الطلاب"}
            value={stats.students.total}
            icon={<GraduationCap className="h-6 w-6" />}
            trend={{
              value: stats.students.growth,
              label: "نمو شهري",
              isPositive: stats.students.growth > 0,
            }}
            color="orange"
            subtitle={`${stats.students.active} نشط • ${stats.students.suspended} متوقف`}
          />
        </div>
      </div>

      {/* إحصائيات متقدمة للحافلات */}
      {(isAdmin || isSchoolManager) && (
        <div>
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <Bus className="h-6 w-6 mr-2 text-green-600" />
            حالة الحافلات المتقدمة
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="في الخدمة"
              value={stats.buses.inService}
              icon={<CheckCircle className="h-6 w-6" />}
              color="green"
              subtitle="حافلات تعمل حالياً"
            />
            <StatCard
              title="في الصيانة"
              value={stats.buses.inMaintenance}
              icon={<Wrench className="h-6 w-6" />}
              color="yellow"
              subtitle="صيانة دورية وإصلاحات"
            />
            <StatCard
              title="خارج الخدمة"
              value={stats.buses.outOfService}
              icon={<AlertTriangle className="h-6 w-6" />}
              color="red"
              subtitle="حافلات متوقفة"
            />
            <StatCard
              title="معدل الاستخدام"
              value={`${stats.buses.utilizationRate.toFixed(1)}%`}
              icon={<Target className="h-6 w-6" />}
              color={stats.buses.utilizationRate > 80 ? "green" : "yellow"}
              subtitle="كفاءة استغلال الأسطول"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfessionalStats;
