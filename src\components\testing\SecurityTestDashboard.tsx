/**
 * لوحة اختبار الميزات الأمنية
 * Security Features Test Dashboard
 * 
 * مكون تفاعلي لاختبار جميع ميزات المرحلة الأولى
 */

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Shield, 
  Lock, 
  Eye, 
  AlertTriangle, 
  Monitor, 
  Play, 
  CheckCircle, 
  XCircle,
  RefreshCw,
  Download
} from 'lucide-react';
import { Button } from '../ui/Button';
import { PasswordStrengthIndicator } from '../auth/PasswordStrengthIndicator';
import { PasswordStrengthValidator } from '../../utils/passwordStrength';
import { BruteForceProtectionService } from '../../services/security/BruteForceProtection';
import { TwoFactorAuthService } from '../../services/security/TwoFactorAuth';
import { AnomalyDetectionService } from '../../services/security/AnomalyDetection';
import { SessionManagementService } from '../../services/security/SessionManagement';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  message: string;
  duration?: number;
}

export const SecurityTestDashboard: React.FC = () => {
  const { t } = useTranslation();
  const [testPassword, setTestPassword] = useState('');
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string | null>(null);

  // خدمات الأمان
  const bruteForceService = BruteForceProtectionService.getInstance();
  const twoFactorService = TwoFactorAuthService.getInstance();
  const anomalyService = AnomalyDetectionService.getInstance();
  const sessionService = SessionManagementService.getInstance();

  /**
   * تشغيل اختبار واحد
   */
  const runSingleTest = async (testName: string, testFunction: () => Promise<void>) => {
    const startTime = Date.now();
    setCurrentTest(testName);
    
    setTestResults(prev => prev.map(test => 
      test.name === testName 
        ? { ...test, status: 'running', message: 'جاري التشغيل...' }
        : test
    ));

    try {
      await testFunction();
      const duration = Date.now() - startTime;
      
      setTestResults(prev => prev.map(test => 
        test.name === testName 
          ? { ...test, status: 'passed', message: 'نجح الاختبار', duration }
          : test
      ));
    } catch (error) {
      const duration = Date.now() - startTime;
      
      setTestResults(prev => prev.map(test => 
        test.name === testName 
          ? { 
              ...test, 
              status: 'failed', 
              message: error instanceof Error ? error.message : 'خطأ غير معروف',
              duration 
            }
          : test
      ));
    }
  };

  /**
   * تشغيل جميع الاختبارات
   */
  const runAllTests = async () => {
    setIsRunning(true);
    setCurrentTest(null);

    // إعداد قائمة الاختبارات
    const tests: TestResult[] = [
      { name: 'فحص قوة كلمة المرور', status: 'pending', message: 'في الانتظار' },
      { name: 'حماية Brute-force', status: 'pending', message: 'في الانتظار' },
      { name: 'التحقق الثنائي', status: 'pending', message: 'في الانتظار' },
      { name: 'مراقبة السلوك الشاذ', status: 'pending', message: 'في الانتظار' },
      { name: 'إدارة الجلسات', status: 'pending', message: 'في الانتظار' }
    ];

    setTestResults(tests);

    // تشغيل الاختبارات
    await runSingleTest('فحص قوة كلمة المرور', testPasswordStrength);
    await runSingleTest('حماية Brute-force', testBruteForceProtection);
    await runSingleTest('التحقق الثنائي', testTwoFactorAuth);
    await runSingleTest('مراقبة السلوك الشاذ', testAnomalyDetection);
    await runSingleTest('إدارة الجلسات', testSessionManagement);

    setIsRunning(false);
    setCurrentTest(null);
  };

  /**
   * اختبار فحص قوة كلمة المرور
   */
  const testPasswordStrength = async () => {
    // اختبار كلمة مرور ضعيفة
    const weakResult = PasswordStrengthValidator.validatePassword('123');
    if (weakResult.isValid) {
      throw new Error('كلمة المرور الضعيفة لم يتم رفضها');
    }

    // اختبار كلمة مرور قوية
    const strongResult = PasswordStrengthValidator.validatePassword('MyStr0ng!P@ssw0rd2024');
    if (!strongResult.isValid || strongResult.score < 80) {
      throw new Error('كلمة المرور القوية لم يتم قبولها');
    }

    // اختبار كلمة مرور محظورة
    const bannedResult = PasswordStrengthValidator.validatePassword('password123');
    if (bannedResult.isValid) {
      throw new Error('كلمة المرور المحظورة لم يتم رفضها');
    }
  };

  /**
   * اختبار حماية Brute-force
   */
  const testBruteForceProtection = async () => {
    const testEmail = '<EMAIL>';
    const testIP = '***********00';
    const testUserAgent = 'Test Browser';

    // تسجيل محاولة ناجحة
    await bruteForceService.recordAttempt(testEmail, testIP, testUserAgent, true);

    // تسجيل محاولة فاشلة
    await bruteForceService.recordAttempt(testEmail, testIP, testUserAgent, false);

    // فحص الحظر
    const blockCheck = await bruteForceService.isBlocked(testEmail, testIP);
    
    // الحصول على إحصائيات
    const stats = await bruteForceService.getProtectionStats();
    if (typeof stats.totalAttempts !== 'number') {
      throw new Error('إحصائيات الحماية غير صحيحة');
    }
  };

  /**
   * اختبار التحقق الثنائي
   */
  const testTwoFactorAuth = async () => {
    const testUserId = 'test-user-' + Date.now();

    // إعداد التحقق الثنائي
    const setup = await twoFactorService.setupTwoFactor(testUserId);
    if (!setup.secret || !setup.qrCode || setup.backupCodes.length !== 10) {
      throw new Error('إعداد التحقق الثنائي غير مكتمل');
    }

    // اختبار التحقق من TOTP
    const isValid = twoFactorService.verifyTOTP(setup.secret, '123456');
    if (typeof isValid !== 'boolean') {
      throw new Error('دالة التحقق من TOTP لا تعمل');
    }
  };

  /**
   * اختبار مراقبة السلوك الشاذ
   */
  const testAnomalyDetection = async () => {
    const testUserId = 'test-user-' + Date.now();

    const context = {
      ip_address: '***********',
      user_agent: 'Mozilla/5.0 Test Browser',
      device_fingerprint: 'test-device',
      login_time: new Date()
    };

    const result = await anomalyService.analyzeLoginAttempt(testUserId, context);
    if (typeof result.isAnomalous !== 'boolean' || typeof result.riskScore !== 'number') {
      throw new Error('تحليل السلوك لا يعمل بشكل صحيح');
    }

    // اختبار الحصول على التنبيهات
    const alerts = await anomalyService.getUnresolvedAlerts();
    if (!Array.isArray(alerts)) {
      throw new Error('قائمة التنبيهات غير صحيحة');
    }
  };

  /**
   * اختبار إدارة الجلسات
   */
  const testSessionManagement = async () => {
    const testUserId = 'test-user-' + Date.now();
    const testToken = 'test-session-' + Date.now();

    // إنشاء جلسة
    const session = await sessionService.createSession(
      testUserId,
      testToken,
      '***********',
      'Test Browser'
    );

    if (!session.id || session.user_id !== testUserId) {
      throw new Error('فشل في إنشاء الجلسة');
    }

    // التحقق من صحة الجلسة
    const validation = await sessionService.validateSession(testToken);
    if (!validation.isValid) {
      throw new Error('فشل في التحقق من صحة الجلسة');
    }

    // إنهاء الجلسة
    await sessionService.terminateSession(testToken, 'Test termination');
  };

  /**
   * تصدير تقرير النتائج
   */
  const exportResults = () => {
    const report = {
      timestamp: new Date().toISOString(),
      results: testResults,
      summary: {
        total: testResults.length,
        passed: testResults.filter(t => t.status === 'passed').length,
        failed: testResults.filter(t => t.status === 'failed').length,
        successRate: (testResults.filter(t => t.status === 'passed').length / testResults.length) * 100
      }
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `security-test-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'running':
        return <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />;
      case 'passed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <div className="w-4 h-4 rounded-full bg-gray-300" />;
    }
  };

  const passedTests = testResults.filter(t => t.status === 'passed').length;
  const failedTests = testResults.filter(t => t.status === 'failed').length;
  const successRate = testResults.length > 0 ? (passedTests / testResults.length) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* العنوان */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 space-x-reverse">
          <Shield className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              لوحة اختبار الميزات الأمنية
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              اختبار شامل لجميع ميزات المرحلة الأولى
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3 space-x-reverse">
          <Button
            onClick={runAllTests}
            disabled={isRunning}
            className="flex items-center gap-2"
          >
            {isRunning ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Play className="w-4 h-4" />
            )}
            {isRunning ? 'جاري التشغيل...' : 'تشغيل جميع الاختبارات'}
          </Button>
          
          {testResults.length > 0 && (
            <Button
              onClick={exportResults}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              تصدير التقرير
            </Button>
          )}
        </div>
      </div>

      {/* إحصائيات سريعة */}
      {testResults.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  إجمالي الاختبارات
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {testResults.length}
                </p>
              </div>
              <Shield className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  نجح
                </p>
                <p className="text-2xl font-bold text-green-600">
                  {passedTests}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  فشل
                </p>
                <p className="text-2xl font-bold text-red-600">
                  {failedTests}
                </p>
              </div>
              <XCircle className="w-8 h-8 text-red-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  معدل النجاح
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {successRate.toFixed(1)}%
                </p>
              </div>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                successRate >= 90 ? 'bg-green-500' : 
                successRate >= 70 ? 'bg-yellow-500' : 'bg-red-500'
              }`}>
                <span className="text-white text-sm font-bold">
                  {successRate >= 90 ? '✓' : successRate >= 70 ? '!' : '✗'}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* اختبار فحص قوة كلمة المرور التفاعلي */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          اختبار فحص قوة كلمة المرور التفاعلي
        </h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              اختبر كلمة مرور:
            </label>
            <input
              type="password"
              value={testPassword}
              onChange={(e) => setTestPassword(e.target.value)}
              placeholder="أدخل كلمة مرور للاختبار"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
          {testPassword && (
            <PasswordStrengthIndicator password={testPassword} showDetails={true} />
          )}
        </div>
      </div>

      {/* نتائج الاختبارات */}
      {testResults.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              نتائج الاختبارات
            </h3>
          </div>
          
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {testResults.map((test, index) => (
              <div key={index} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    {getStatusIcon(test.status)}
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {test.name}
                      </p>
                      <p className={`text-xs ${
                        test.status === 'passed' ? 'text-green-600' :
                        test.status === 'failed' ? 'text-red-600' :
                        test.status === 'running' ? 'text-blue-600' :
                        'text-gray-500'
                      }`}>
                        {test.message}
                      </p>
                    </div>
                  </div>
                  
                  {test.duration && (
                    <span className="text-xs text-gray-500">
                      {test.duration}ms
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* حالة التشغيل */}
      {isRunning && currentTest && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
          <div className="flex items-center space-x-3 space-x-reverse">
            <RefreshCw className="w-5 h-5 text-blue-500 animate-spin" />
            <div>
              <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                جاري تشغيل: {currentTest}
              </p>
              <p className="text-xs text-blue-600 dark:text-blue-300">
                يرجى الانتظار...
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
