-- بيانات اختبار للنظام
-- Test Data for the System

-- مستأجرين إضافيين
INSERT INTO tenants (id, name, slug, settings) VALUES 
(
  'b1ffbc99-9c0b-4ef8-bb6d-6bb9bd380a22',
  'مدرسة النور الثانوية',
  'al-noor-secondary',
  '{"timezone": "Asia/Riyadh", "language": "ar"}'
),
(
  'c2ffbc99-9c0b-4ef8-bb6d-6bb9bd380a33',
  'مدرسة الفجر المتوسطة',
  'al-fajr-middle',
  '{"timezone": "Asia/Riyadh", "language": "ar"}'
) ON CONFLICT (slug) DO NOTHING;

-- حافلات اختبار
INSERT INTO buses (tenant_id, bus_number, license_plate, capacity, model, year, color, status) VALUES 
(
  'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  'BUS-001',
  'ABC-1234',
  45,
  'Mercedes Sprinter',
  2023,
  'أصفر',
  'active'
),
(
  'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  'BUS-002',
  'DEF-5678',
  35,
  'Toyota Coaster',
  2022,
  'أزرق',
  'active'
) ON CONFLICT (tenant_id, bus_number) DO NOTHING;

-- مسارات اختبار
INSERT INTO routes (tenant_id, name, description, start_location, end_location, estimated_duration, distance_km) VALUES 
(
  'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  'مسار الشمال',
  'مسار يخدم الأحياء الشمالية',
  'المدرسة',
  'حي الملك فهد',
  45,
  12.5
),
(
  'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  'مسار الجنوب',
  'مسار يخدم الأحياء الجنوبية',
  'المدرسة',
  'حي الملك عبدالله',
  35,
  8.3
);

-- إشعارات اختبار
INSERT INTO notifications (tenant_id, title, message, type, priority) VALUES 
(
  'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  'مرحباً بكم في النظام',
  'تم تفعيل نظام إدارة الحافلات المدرسية بنجاح',
  'success',
  'normal'
),
(
  'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  'تذكير مهم',
  'يرجى تحديث بيانات الطلاب قبل بداية العام الدراسي',
  'warning',
  'high'
);
