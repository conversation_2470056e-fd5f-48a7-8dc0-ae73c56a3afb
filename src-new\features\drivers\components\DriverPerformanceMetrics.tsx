import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  User,
  Star,
  Clock,
  Shield,
  TrendingUp,
  Award,
  Fuel,
  Target,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import * as api from "../../lib/api";

interface DriverPerformance {
  id: string;
  driver_id: string;
  date: string;
  on_time_arrivals?: number;
  total_trips?: number;
  safety_score?: number;
  parent_rating?: number;
  fuel_efficiency?: number;
  driver?: {
    id: string;
    name: string;
    email: string;
    avatar_url?: string;
  };
}

export const DriverPerformanceMetrics: React.FC = () => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const { users } = useDatabase();
  const [performanceData, setPerformanceData] = useState<DriverPerformance[]>(
    [],
  );
  const [loading, setLoading] = useState(true);
  const [selectedDriver, setSelectedDriver] = useState("");
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    end: new Date().toISOString().split("T")[0],
  });
  const [showDetailedView, setShowDetailedView] = useState(false);
  const [exportFormat, setExportFormat] = useState("csv");

  const drivers = users.filter((user) => user.role === "driver");

  const fetchPerformanceData = async () => {
    if (!tenant?.id) return;

    try {
      setLoading(true);
      if (selectedDriver) {
        const data = await api.getDriverPerformanceMetrics(
          selectedDriver,
          tenant.id,
          dateRange.start,
          dateRange.end,
        );
        setPerformanceData(data || []);
      } else {
        // Fetch data for all drivers
        const allData: DriverPerformance[] = [];
        for (const driver of drivers) {
          const data = await api.getDriverPerformanceMetrics(
            driver.id,
            tenant.id,
            dateRange.start,
            dateRange.end,
          );
          allData.push(...(data || []));
        }
        setPerformanceData(allData);
      }
    } catch (error) {
      console.error("Error fetching driver performance:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPerformanceData();
  }, [tenant?.id, selectedDriver, dateRange]);

  const calculateAverages = (data: DriverPerformance[]) => {
    if (data.length === 0) return null;

    const totals = data.reduce(
      (acc, record) => {
        const onTimeRate =
          (record.on_time_arrivals || 0) / Math.max(record.total_trips || 1, 1);
        acc.onTimeRate += onTimeRate;
        acc.safetyScore += record.safety_score || 0;
        acc.parentRating += record.parent_rating || 0;
        acc.fuelEfficiency += record.fuel_efficiency || 0;
        acc.totalTrips += record.total_trips || 0;
        acc.count += 1;
        return acc;
      },
      {
        onTimeRate: 0,
        safetyScore: 0,
        parentRating: 0,
        fuelEfficiency: 0,
        totalTrips: 0,
        count: 0,
      },
    );

    return {
      onTimeRate: (totals.onTimeRate / totals.count) * 100,
      safetyScore: totals.safetyScore / totals.count,
      parentRating: totals.parentRating / totals.count,
      fuelEfficiency: totals.fuelEfficiency / totals.count,
      totalTrips: totals.totalTrips,
    };
  };

  const getPerformanceGrade = (score: number) => {
    if (score >= 90)
      return { grade: "A", color: "text-green-600 bg-green-100" };
    if (score >= 80) return { grade: "B", color: "text-blue-600 bg-blue-100" };
    if (score >= 70)
      return { grade: "C", color: "text-yellow-600 bg-yellow-100" };
    if (score >= 60)
      return { grade: "D", color: "text-orange-600 bg-orange-100" };
    return { grade: "F", color: "text-red-600 bg-red-100" };
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={16}
        className={`${i < Math.floor(rating) ? "text-yellow-400 fill-current" : "text-gray-300"}`}
      />
    ));
  };

  const getDriversByPerformance = () => {
    const driverPerformance = drivers
      .map((driver) => {
        const driverData = performanceData.filter(
          (record) => record.driver_id === driver.id,
        );
        const averages = calculateAverages(driverData);

        return {
          driver,
          averages,
          recordCount: driverData.length,
        };
      })
      .filter((item) => item.averages !== null);

    return driverPerformance.sort((a, b) => {
      const scoreA =
        (a.averages!.onTimeRate +
          a.averages!.safetyScore +
          a.averages!.parentRating * 20) /
        3;
      const scoreB =
        (b.averages!.onTimeRate +
          b.averages!.safetyScore +
          b.averages!.parentRating * 20) /
        3;
      return scoreB - scoreA;
    });
  };

  const averages = calculateAverages(performanceData);
  const driverRankings = getDriversByPerformance();

  const exportPerformanceData = () => {
    if (performanceData.length === 0) return;

    const headers = [
      "Driver Name",
      "Date",
      "Total Trips",
      "On-Time Arrivals",
      "On-Time Rate (%)",
      "Safety Score",
      "Parent Rating",
      "Fuel Efficiency (km/L)",
    ];

    const csvContent = [
      headers.join(","),
      ...performanceData.map((record) => {
        const onTimeRate =
          ((record.on_time_arrivals || 0) /
            Math.max(record.total_trips || 1, 1)) *
          100;

        return [
          record.driver?.name || "Unknown",
          new Date(record.date).toLocaleDateString(),
          record.total_trips || 0,
          record.on_time_arrivals || 0,
          onTimeRate.toFixed(1),
          record.safety_score?.toFixed(1) || "N/A",
          record.parent_rating?.toFixed(1) || "N/A",
          record.fuel_efficiency?.toFixed(1) || "N/A",
        ].join(",");
      }),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `driver-performance-${dateRange.start}-to-${dateRange.end}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <TrendingUp className="mr-2 h-5 w-5 text-primary-500" />
          Driver Performance Metrics
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Track and analyze driver performance across multiple metrics.
        </p>
        <div className="mt-4 flex items-center gap-4">
          <Button
            onClick={exportPerformanceData}
            disabled={performanceData.length === 0}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <TrendingUp size={16} />
            Export Data
          </Button>
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={showDetailedView}
              onChange={(e) => setShowDetailedView(e.target.checked)}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <span className="text-gray-700 dark:text-gray-300">
              Detailed View
            </span>
          </label>
        </div>
      </div>

      <div className="p-6">
        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Driver
            </label>
            <select
              value={selectedDriver}
              onChange={(e) => setSelectedDriver(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Drivers</option>
              {drivers.map((driver) => (
                <option key={driver.id} value={driver.id}>
                  {driver.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Start Date
            </label>
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) =>
                setDateRange({ ...dateRange, start: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              End Date
            </label>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) =>
                setDateRange({ ...dateRange, end: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Summary Cards */}
            {averages && (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                        On-Time Rate
                      </p>
                      <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                        {averages.onTimeRate.toFixed(1)}%
                      </p>
                    </div>
                    <Clock className="h-8 w-8 text-blue-500" />
                  </div>
                </div>

                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-green-600 dark:text-green-400">
                        Safety Score
                      </p>
                      <div className="flex items-center gap-2">
                        <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                          {averages.safetyScore.toFixed(1)}
                        </p>
                        <span
                          className={`px-2 py-1 rounded text-xs font-medium ${getPerformanceGrade(averages.safetyScore).color}`}
                        >
                          {getPerformanceGrade(averages.safetyScore).grade}
                        </span>
                      </div>
                    </div>
                    <Shield className="h-8 w-8 text-green-500" />
                  </div>
                </div>

                <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">
                        Parent Rating
                      </p>
                      <div className="flex items-center gap-2">
                        <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                          {averages.parentRating.toFixed(1)}
                        </p>
                        <div className="flex">
                          {renderStars(averages.parentRating)}
                        </div>
                      </div>
                    </div>
                    <Star className="h-8 w-8 text-yellow-500" />
                  </div>
                </div>

                <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-purple-600 dark:text-purple-400">
                        Fuel Efficiency
                      </p>
                      <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                        {averages.fuelEfficiency.toFixed(1)}
                      </p>
                      <p className="text-xs text-purple-600 dark:text-purple-400">
                        km/L
                      </p>
                    </div>
                    <Fuel className="h-8 w-8 text-purple-500" />
                  </div>
                </div>
              </div>
            )}

            {/* Driver Rankings */}
            {!selectedDriver && driverRankings.length > 0 && (
              <div>
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                  <Award className="mr-2 h-5 w-5 text-yellow-500" />
                  Driver Rankings
                </h4>
                <div className="space-y-3">
                  {driverRankings.map((item, index) => {
                    const overallScore =
                      (item.averages!.onTimeRate +
                        item.averages!.safetyScore +
                        item.averages!.parentRating * 20) /
                      3;
                    const grade = getPerformanceGrade(overallScore);

                    return (
                      <div
                        key={item.driver.id}
                        className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className="flex items-center justify-center w-8 h-8 bg-primary-100 text-primary-800 rounded-full font-bold">
                              #{index + 1}
                            </div>
                            <div className="flex items-center gap-3">
                              {item.driver.avatar_url ? (
                                <img
                                  src={item.driver.avatar_url}
                                  alt={item.driver.name}
                                  className="w-10 h-10 rounded-full object-cover"
                                />
                              ) : (
                                <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                  <User size={20} className="text-gray-600" />
                                </div>
                              )}
                              <div>
                                <p className="font-medium text-gray-900 dark:text-white">
                                  {item.driver.name}
                                </p>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                  {item.recordCount} record
                                  {item.recordCount !== 1 ? "s" : ""}
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-4">
                            <div className="text-right">
                              <div className="flex items-center gap-2">
                                <span className="text-lg font-bold text-gray-900 dark:text-white">
                                  {overallScore.toFixed(1)}
                                </span>
                                <span
                                  className={`px-2 py-1 rounded text-xs font-medium ${grade.color}`}
                                >
                                  {grade.grade}
                                </span>
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                Overall Score
                              </div>
                            </div>

                            <div className="grid grid-cols-4 gap-2 text-center">
                              <div>
                                <div className="text-sm font-medium text-blue-600">
                                  {item.averages!.onTimeRate.toFixed(0)}%
                                </div>
                                <div className="text-xs text-gray-500">
                                  On-Time
                                </div>
                              </div>
                              <div>
                                <div className="text-sm font-medium text-green-600">
                                  {item.averages!.safetyScore.toFixed(0)}
                                </div>
                                <div className="text-xs text-gray-500">
                                  Safety
                                </div>
                              </div>
                              <div>
                                <div className="flex justify-center">
                                  {renderStars(
                                    item.averages!.parentRating,
                                  ).slice(0, 3)}
                                </div>
                                <div className="text-xs text-gray-500">
                                  Rating
                                </div>
                              </div>
                              <div>
                                <div className="text-sm font-medium text-purple-600">
                                  {item.averages!.fuelEfficiency.toFixed(1)}
                                </div>
                                <div className="text-xs text-gray-500">
                                  km/L
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Detailed Performance Data */}
            {selectedDriver && performanceData.length > 0 && (
              <div>
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Detailed Performance History
                </h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Trips
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          On-Time Rate
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Safety Score
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Parent Rating
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Fuel Efficiency
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                      {performanceData.map((record) => {
                        const onTimeRate =
                          ((record.on_time_arrivals || 0) /
                            Math.max(record.total_trips || 1, 1)) *
                          100;

                        return (
                          <tr
                            key={record.id}
                            className="hover:bg-gray-50 dark:hover:bg-gray-700"
                          >
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                              {new Date(record.date).toLocaleDateString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                              {record.on_time_arrivals || 0}/
                              {record.total_trips || 0}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">
                              <span
                                className={`px-2 py-1 rounded text-xs font-medium ${
                                  onTimeRate >= 90
                                    ? "bg-green-100 text-green-800"
                                    : onTimeRate >= 70
                                      ? "bg-yellow-100 text-yellow-800"
                                      : "bg-red-100 text-red-800"
                                }`}
                              >
                                {onTimeRate.toFixed(1)}%
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                              {record.safety_score?.toFixed(1) || "N/A"}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">
                              <div className="flex items-center gap-1">
                                {record.parent_rating
                                  ? renderStars(record.parent_rating)
                                  : "N/A"}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                              {record.fuel_efficiency?.toFixed(1) || "N/A"} km/L
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* No Data State */}
            {performanceData.length === 0 && (
              <div className="text-center py-8">
                <TrendingUp size={48} className="mx-auto mb-4 text-gray-300" />
                <p className="text-gray-500 dark:text-gray-400">
                  No performance data found for the selected criteria.
                </p>
                <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">
                  Performance metrics will appear here once drivers start
                  completing trips.
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default DriverPerformanceMetrics;
