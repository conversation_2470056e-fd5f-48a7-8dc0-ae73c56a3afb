import { UserRole } from "../types";

/**
 * نظام الصلاحيات المبسط والموحد
 * Simplified and Unified Permission System
 */

// الصلاحيات الأساسية - مبسطة وواضحة
export enum Permission {
  // صلاحيات المدارس
  TENANTS_VIEW = 'tenants:view',
  TENANTS_CREATE = 'tenants:create',
  TENANTS_EDIT = 'tenants:edit',
  TENANTS_DELETE = 'tenants:delete',

  // صلاحيات المستخدمين
  USERS_VIEW = 'users:view',
  USERS_CREATE = 'users:create',
  USERS_EDIT = 'users:edit',
  USERS_DELETE = 'users:delete',

  // صلاحيات الحافلات
  BUSES_VIEW = 'buses:view',
  BUSES_CREATE = 'buses:create',
  BUSES_EDIT = 'buses:edit',
  BUSES_DELETE = 'buses:delete',

  // صلاحيات الطلاب
  STUDENTS_VIEW = 'students:view',
  STUDENTS_CREATE = 'students:create',
  STUDENTS_EDIT = 'students:edit',
  STUDENTS_DELETE = 'students:delete',

  // صلاحيات المسارات
  ROUTES_VIEW = 'routes:view',
  ROUTES_CREATE = 'routes:create',
  ROUTES_EDIT = 'routes:edit',
  ROUTES_DELETE = 'routes:delete',

  // صلاحيات الحضور
  ATTENDANCE_VIEW = 'attendance:view',
  ATTENDANCE_CREATE = 'attendance:create',
  ATTENDANCE_EDIT = 'attendance:edit',
  ATTENDANCE_DELETE = 'attendance:delete',

  // صلاحيات الإشعارات
  NOTIFICATIONS_VIEW = 'notifications:view',
  NOTIFICATIONS_CREATE = 'notifications:create',
  NOTIFICATIONS_EDIT = 'notifications:edit',
  NOTIFICATIONS_DELETE = 'notifications:delete',

  // صلاحيات التقارير
  REPORTS_VIEW = 'reports:view',
  REPORTS_EXPORT = 'reports:export',

  // صلاحيات الصيانة
  MAINTENANCE_VIEW = 'maintenance:view',
  MAINTENANCE_CREATE = 'maintenance:create',
  MAINTENANCE_EDIT = 'maintenance:edit',
  MAINTENANCE_DELETE = 'maintenance:delete',

  // صلاحيات النظام
  SYSTEM_SETTINGS = 'system:settings',
  SYSTEM_BACKUP = 'system:backup',
}

// نطاقات البيانات المبسطة
export enum DataScope {
  GLOBAL = "global",     // وصول شامل للنظام (الأدمن فقط)
  TENANT = "tenant",     // وصول خاص بالمدرسة
  PERSONAL = "personal", // البيانات الشخصية فقط
  ASSIGNED = "assigned", // البيانات المخصصة للمستخدم
  CHILDREN = "children", // بيانات الأطفال (لأولياء الأمور)
}

// أنواع الموارد الأساسية
export enum ResourceType {
  TENANT = "tenant",
  USER = "user",
  BUS = "bus",
  ROUTE = "route",
  STUDENT = "student",
  ATTENDANCE = "attendance",
  NOTIFICATION = "notification",
  REPORT = "report",
  MAINTENANCE = "maintenance",
  SYSTEM = "system",
}

// العمليات الأساسية
export enum Action {
  CREATE = "create",
  READ = "read",
  UPDATE = "update",
  DELETE = "delete",
  EXPORT = "export",
}

// مصفوفة الصلاحيات المبسطة لكل دور
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [
    // الأدمن له جميع الصلاحيات
    Permission.TENANTS_VIEW,
    Permission.TENANTS_CREATE,
    Permission.TENANTS_EDIT,
    Permission.TENANTS_DELETE,
    Permission.USERS_VIEW,
    Permission.USERS_CREATE,
    Permission.USERS_EDIT,
    Permission.USERS_DELETE,
    Permission.BUSES_VIEW,
    Permission.BUSES_CREATE,
    Permission.BUSES_EDIT,
    Permission.BUSES_DELETE,
    Permission.STUDENTS_VIEW,
    Permission.STUDENTS_CREATE,
    Permission.STUDENTS_EDIT,
    Permission.STUDENTS_DELETE,
    Permission.ROUTES_VIEW,
    Permission.ROUTES_CREATE,
    Permission.ROUTES_EDIT,
    Permission.ROUTES_DELETE,
    Permission.ATTENDANCE_VIEW,
    Permission.ATTENDANCE_CREATE,
    Permission.ATTENDANCE_EDIT,
    Permission.ATTENDANCE_DELETE,
    Permission.NOTIFICATIONS_VIEW,
    Permission.NOTIFICATIONS_CREATE,
    Permission.NOTIFICATIONS_EDIT,
    Permission.NOTIFICATIONS_DELETE,
    Permission.REPORTS_VIEW,
    Permission.REPORTS_EXPORT,
    Permission.MAINTENANCE_VIEW,
    Permission.MAINTENANCE_CREATE,
    Permission.MAINTENANCE_EDIT,
    Permission.MAINTENANCE_DELETE,
    Permission.SYSTEM_SETTINGS,
    Permission.SYSTEM_BACKUP,
  ],

  [UserRole.SCHOOL_MANAGER]: [
    // مدير المدرسة - إدارة مدرسته فقط
    Permission.TENANTS_VIEW,
    Permission.TENANTS_EDIT,
    Permission.USERS_VIEW,
    Permission.USERS_CREATE,
    Permission.USERS_EDIT,
    Permission.BUSES_VIEW,
    Permission.BUSES_CREATE,
    Permission.BUSES_EDIT,
    Permission.BUSES_DELETE,
    Permission.STUDENTS_VIEW,
    Permission.STUDENTS_CREATE,
    Permission.STUDENTS_EDIT,
    Permission.STUDENTS_DELETE,
    Permission.ROUTES_VIEW,
    Permission.ROUTES_CREATE,
    Permission.ROUTES_EDIT,
    Permission.ROUTES_DELETE,
    Permission.ATTENDANCE_VIEW,
    Permission.ATTENDANCE_CREATE,
    Permission.ATTENDANCE_EDIT,
    Permission.NOTIFICATIONS_VIEW,
    Permission.NOTIFICATIONS_CREATE,
    Permission.NOTIFICATIONS_EDIT,
    Permission.REPORTS_VIEW,
    Permission.REPORTS_EXPORT,
    Permission.MAINTENANCE_VIEW,
    Permission.MAINTENANCE_CREATE,
    Permission.MAINTENANCE_EDIT,
  ],

  [UserRole.SUPERVISOR]: [
    // المشرف - قراءة فقط لمدرسته
    Permission.TENANTS_VIEW,
    Permission.USERS_VIEW,
    Permission.BUSES_VIEW,
    Permission.STUDENTS_VIEW,
    Permission.ROUTES_VIEW,
    Permission.ATTENDANCE_VIEW,
    Permission.NOTIFICATIONS_VIEW,
    Permission.REPORTS_VIEW,
    Permission.MAINTENANCE_VIEW,
  ],

  [UserRole.DRIVER]: [
    // السائق - حافلته ومساره فقط
    Permission.BUSES_VIEW,
    Permission.STUDENTS_VIEW,
    Permission.ROUTES_VIEW,
    Permission.ATTENDANCE_VIEW,
    Permission.ATTENDANCE_CREATE,
    Permission.NOTIFICATIONS_VIEW,
    Permission.MAINTENANCE_VIEW,
  ],

  [UserRole.PARENT]: [
    // ولي الأمر - أطفاله فقط
    Permission.STUDENTS_VIEW,
    Permission.ATTENDANCE_VIEW,
    Permission.BUSES_VIEW,
    Permission.ROUTES_VIEW,
    Permission.NOTIFICATIONS_VIEW,
  ],

  [UserRole.STUDENT]: [
    // الطالب - بياناته الشخصية فقط
    Permission.ATTENDANCE_VIEW,
    Permission.BUSES_VIEW,
    Permission.ROUTES_VIEW,
    Permission.NOTIFICATIONS_VIEW,
  ],
};

// نطاق البيانات لكل دور - مبسط
export const ROLE_DATA_SCOPE: Record<UserRole, DataScope[]> = {
  [UserRole.ADMIN]: [DataScope.GLOBAL, DataScope.TENANT, DataScope.PERSONAL],
  [UserRole.SCHOOL_MANAGER]: [DataScope.TENANT, DataScope.PERSONAL],
  [UserRole.SUPERVISOR]: [DataScope.TENANT, DataScope.PERSONAL],
  [UserRole.DRIVER]: [DataScope.ASSIGNED, DataScope.PERSONAL],
  [UserRole.PARENT]: [DataScope.CHILDREN, DataScope.PERSONAL],
  [UserRole.STUDENT]: [DataScope.PERSONAL],
};

// دوال مساعدة بسيطة للصلاحيات
export class RBACHelper {
  /**
   * فحص ما إذا كان المستخدم لديه صلاحية معينة
   */
  static hasPermission(userRole: UserRole, permission: Permission): boolean {
    const rolePermissions = ROLE_PERMISSIONS[userRole] || [];
    return rolePermissions.includes(permission);
  }

  /**
   * فحص ما إذا كان المستخدم يمكنه الوصول لنطاق بيانات معين
   */
  static hasDataScope(userRole: UserRole, scope: DataScope): boolean {
    const roleScopes = ROLE_DATA_SCOPE[userRole] || [];
    return roleScopes.includes(scope);
  }

  /**
   * الحصول على جميع الصلاحيات لدور معين
   */
  static getRolePermissions(userRole: UserRole): Permission[] {
    return ROLE_PERMISSIONS[userRole] || [];
  }

  /**
   * الحصول على نطاقات البيانات لدور معين
   */
  static getRoleDataScopes(userRole: UserRole): DataScope[] {
    return ROLE_DATA_SCOPE[userRole] || [];
  }

  /**
   * فحص بسيط للصلاحيات حسب المورد والعملية
   */
  static canPerformAction(
    userRole: UserRole,
    resource: ResourceType,
    action: Action,
  ): boolean {
    // تحويل المورد والعملية إلى صلاحية
    const permission = this.getPermissionFromResourceAction(resource, action);
    return permission ? this.hasPermission(userRole, permission) : false;
  }

  /**
   * فحص ما إذا كان الدور يمكنه إدارة دور آخر
   */
  static canManageRole(managerRole: UserRole, targetRole: UserRole): boolean {
    // الأدمن يمكنه إدارة جميع الأدوار
    if (managerRole === 'admin') {
      return true;
    }

    // مدير المدرسة يمكنه إدارة الأدوار التالية فقط
    if (managerRole === 'school_manager') {
      return ['supervisor', 'driver', 'parent', 'student'].includes(targetRole);
    }

    // المشرف يمكنه إدارة السائقين والطلاب وأولياء الأمور
    if (managerRole === 'supervisor') {
      return ['driver', 'parent', 'student'].includes(targetRole);
    }

    // باقي الأدوار لا يمكنها إدارة أي دور آخر
    return false;
  }

  /**
   * تحويل المورد والعملية إلى صلاحية - مبسط
   */
  private static getPermissionFromResourceAction(
    resource: ResourceType,
    action: Action,
  ): Permission | null {
    const mapping: Record<string, Permission> = {
      [`${ResourceType.TENANT}_${Action.READ}`]: Permission.TENANTS_VIEW,
      [`${ResourceType.TENANT}_${Action.CREATE}`]: Permission.TENANTS_CREATE,
      [`${ResourceType.TENANT}_${Action.UPDATE}`]: Permission.TENANTS_EDIT,
      [`${ResourceType.TENANT}_${Action.DELETE}`]: Permission.TENANTS_DELETE,

      [`${ResourceType.USER}_${Action.READ}`]: Permission.USERS_VIEW,
      [`${ResourceType.USER}_${Action.CREATE}`]: Permission.USERS_CREATE,
      [`${ResourceType.USER}_${Action.UPDATE}`]: Permission.USERS_EDIT,
      [`${ResourceType.USER}_${Action.DELETE}`]: Permission.USERS_DELETE,

      [`${ResourceType.BUS}_${Action.READ}`]: Permission.BUSES_VIEW,
      [`${ResourceType.BUS}_${Action.CREATE}`]: Permission.BUSES_CREATE,
      [`${ResourceType.BUS}_${Action.UPDATE}`]: Permission.BUSES_EDIT,
      [`${ResourceType.BUS}_${Action.DELETE}`]: Permission.BUSES_DELETE,

      [`${ResourceType.STUDENT}_${Action.READ}`]: Permission.STUDENTS_VIEW,
      [`${ResourceType.STUDENT}_${Action.CREATE}`]: Permission.STUDENTS_CREATE,
      [`${ResourceType.STUDENT}_${Action.UPDATE}`]: Permission.STUDENTS_EDIT,
      [`${ResourceType.STUDENT}_${Action.DELETE}`]: Permission.STUDENTS_DELETE,

      [`${ResourceType.ROUTE}_${Action.READ}`]: Permission.ROUTES_VIEW,
      [`${ResourceType.ROUTE}_${Action.CREATE}`]: Permission.ROUTES_CREATE,
      [`${ResourceType.ROUTE}_${Action.UPDATE}`]: Permission.ROUTES_EDIT,
      [`${ResourceType.ROUTE}_${Action.DELETE}`]: Permission.ROUTES_DELETE,
    };

    return mapping[`${resource}_${action}`] || null;
  }
}

export default RBACHelper;
