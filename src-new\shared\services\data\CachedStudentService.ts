/**
 * Cached Student Service - خدمة الطلاب مع التخزين المؤقت
 * يحسن الأداء عبر استخدام Cache للبيانات المتكررة
 */

import { StudentService } from './StudentService';
import { CacheManager } from '../../lib/cache-manager';
import { APIResponse } from '../../api/types';
import { Student, CreateStudentRequest, UpdateStudentRequest } from '../../types/student';

export class CachedStudentService extends StudentService {
  
  private static readonly CACHE_TTL = {
    STUDENT_DETAILS: 2 * 60 * 1000, // 2 minutes
    STUDENT_LIST: 1 * 60 * 1000,    // 1 minute
    STUDENT_STATS: 5 * 60 * 1000,   // 5 minutes
  };

  /**
   * الحصول على طالب بالمعرف مع Cache
   */
  async getStudentById(id: string): Promise<APIResponse<Student>> {
    const cacheKey = `student:${id}`;
    
    return CacheManager.getOrFetch(
      cacheKey,
      () => super.getStudentById(id),
      CachedStudentService.CACHE_TTL.STUDENT_DETAILS
    );
  }

  /**
   * الحصول على قائمة الطلاب مع Cache
   */
  async getStudents(params?: {
    page?: number;
    limit?: number;
    search?: string;
    grade?: string;
    class?: string;
    status?: string;
  }): Promise<APIResponse<Student[]>> {
    
    // إنشاء مفتاح Cache بناءً على المعاملات
    const cacheKey = `students:${JSON.stringify(params || {})}`;
    
    return CacheManager.getOrFetch(
      cacheKey,
      () => super.getStudents(params),
      CachedStudentService.CACHE_TTL.STUDENT_LIST
    );
  }

  /**
   * إنشاء طالب جديد مع تنظيف Cache
   */
  async createStudent(studentData: CreateStudentRequest): Promise<APIResponse<Student>> {
    const result = await super.createStudent(studentData);
    
    if (result.success) {
      // تنظيف Cache المتعلق بالطلاب
      this.clearStudentCaches(studentData.tenant_id, studentData.grade, studentData.class);
    }
    
    return result;
  }

  /**
   * تحديث بيانات الطالب مع تنظيف Cache
   */
  async updateStudent(id: string, studentData: UpdateStudentRequest): Promise<APIResponse<Student>> {
    const result = await super.updateStudent(id, studentData);
    
    if (result.success) {
      // إزالة من Cache
      CacheManager.delete(`student:${id}`);
      
      // تنظيف Cache المتعلق بالطلاب
      if (studentData.tenant_id) {
        this.clearStudentCaches(studentData.tenant_id, studentData.grade, studentData.class);
      }
    }
    
    return result;
  }

  /**
   * حذف طالب مع تنظيف Cache
   */
  async deleteStudent(id: string): Promise<APIResponse<{ message: string }>> {
    // الحصول على بيانات الطالب قبل الحذف لتنظيف Cache
    const studentData = await this.getStudentById(id);
    
    const result = await super.deleteStudent(id);
    
    if (result.success && studentData.success) {
      // إزالة من Cache
      CacheManager.delete(`student:${id}`);
      
      // تنظيف Cache المتعلق بالطلاب
      const student = studentData.data;
      this.clearStudentCaches(student.tenant_id, student.grade, student.class);
    }
    
    return result;
  }

  /**
   * تعيين طالب لمحطة مع التحقق من السعة
   */
  async assignToRouteStop(studentId: string, routeStopId: string): Promise<APIResponse<Student>> {
    // التحقق من سعة الحافلة
    const { EnhancedValidationService } = await import('../../lib/validation-enhanced');
    
    // الحصول على معرف الحافلة من محطة المسار
    const busId = await this.getBusIdFromRouteStop(routeStopId);
    if (busId) {
      const capacityCheck = await EnhancedValidationService.validateBusCapacity(busId, 1);
      if (!capacityCheck.isValid) {
        return {
          success: false,
          error: {
            code: 'CAPACITY_EXCEEDED',
            message: capacityCheck.error || 'Bus capacity exceeded',
            details: {
              currentCapacity: capacityCheck.currentCapacity,
              maxCapacity: capacityCheck.maxCapacity
            }
          }
        };
      }
    }

    const result = await super.assignToRouteStop(studentId, routeStopId);
    
    if (result.success) {
      // إزالة من Cache
      CacheManager.delete(`student:${studentId}`);
      
      // تنظيف Cache المتعلق بالحافلات والمسارات
      if (busId) {
        CacheManager.deleteByPattern(new RegExp(`bus:${busId}|route.*${busId}`));
      }
    }
    
    return result;
  }

  /**
   * الحصول على إحصائيات الطلاب مع Cache
   */
  async getStudentStatistics(tenantId: string, filters?: {
    grade?: string;
    class?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<APIResponse<{
    totalStudents: number;
    activeStudents: number;
    inactiveStudents: number;
    byGrade: Record<string, number>;
    byClass: Record<string, number>;
    attendanceRate: number;
  }>> {
    
    const cacheKey = `student_stats:${tenantId}:${JSON.stringify(filters || {})}`;
    
    return CacheManager.getOrFetch(
      cacheKey,
      async () => {
        // تنفيذ الاستعلام الفعلي للإحصائيات
        const { data: students, error } = await this.supabase
          .from('students')
          .select('id, grade, class, is_active')
          .eq('tenant_id', tenantId)
          .eq('is_active', true);

        if (error) {
          return {
            success: false,
            error: {
              code: 'DATABASE_ERROR',
              message: 'Failed to fetch student statistics'
            }
          };
        }

        // حساب الإحصائيات
        const totalStudents = students?.length || 0;
        const activeStudents = students?.filter(s => s.is_active).length || 0;
        const inactiveStudents = totalStudents - activeStudents;

        const byGrade: Record<string, number> = {};
        const byClass: Record<string, number> = {};

        students?.forEach(student => {
          byGrade[student.grade] = (byGrade[student.grade] || 0) + 1;
          byClass[student.class] = (byClass[student.class] || 0) + 1;
        });

        // حساب معدل الحضور (يحتاج استعلام منفصل)
        const attendanceRate = await this.calculateAttendanceRate(tenantId, filters);

        return {
          success: true,
          data: {
            totalStudents,
            activeStudents,
            inactiveStudents,
            byGrade,
            byClass,
            attendanceRate
          }
        };
      },
      CachedStudentService.CACHE_TTL.STUDENT_STATS
    );
  }

  /**
   * البحث في الطلاب مع Cache ذكي
   */
  async searchStudents(query: string, filters?: {
    tenantId?: string;
    grade?: string;
    class?: string;
    limit?: number;
  }): Promise<APIResponse<Student[]>> {
    
    // للبحث القصير، استخدم Cache
    if (query.length >= 3) {
      const cacheKey = `search:${query}:${JSON.stringify(filters || {})}`;
      
      return CacheManager.getOrFetch(
        cacheKey,
        () => super.searchStudents(query, filters),
        30 * 1000 // 30 seconds cache for search results
      );
    }
    
    // للبحث الطويل، لا تستخدم Cache
    return super.searchStudents(query, filters);
  }

  /**
   * تنظيف Cache المتعلق بالطلاب
   */
  private clearStudentCaches(tenantId: string, grade?: string, className?: string): void {
    // تنظيف Cache العام للطلاب
    CacheManager.deleteByPattern(new RegExp(`students:`));
    
    // تنظيف Cache الإحصائيات
    CacheManager.deleteByPattern(new RegExp(`student_stats:${tenantId}`));
    
    // تنظيف Cache البحث
    CacheManager.deleteByPattern(new RegExp(`search:`));
    
    // تنظيف Cache المتعلق بالصف والفصل
    if (grade) {
      CacheManager.deleteByPattern(new RegExp(`grade:${grade}`));
    }
    
    if (className) {
      CacheManager.deleteByPattern(new RegExp(`class:${className}`));
    }
  }

  /**
   * الحصول على معرف الحافلة من محطة المسار
   */
  private async getBusIdFromRouteStop(routeStopId: string): Promise<string | null> {
    try {
      const { data, error } = await this.supabase
        .from('route_stops')
        .select('route_id, routes!inner(bus_id)')
        .eq('id', routeStopId)
        .single();

      if (error || !data) return null;
      
      return data.routes.bus_id;
    } catch (error) {
      console.error('Error getting bus ID from route stop:', error);
      return null;
    }
  }

  /**
   * حساب معدل الحضور
   */
  private async calculateAttendanceRate(tenantId: string, filters?: {
    dateFrom?: string;
    dateTo?: string;
  }): Promise<number> {
    try {
      let query = this.supabase
        .from('attendance')
        .select('status')
        .eq('tenant_id', tenantId);

      if (filters?.dateFrom) {
        query = query.gte('recorded_at', filters.dateFrom);
      }

      if (filters?.dateTo) {
        query = query.lte('recorded_at', filters.dateTo);
      }

      const { data, error } = await query;

      if (error || !data || data.length === 0) return 0;

      const presentCount = data.filter(record => record.status === 'present').length;
      return Math.round((presentCount / data.length) * 100);
    } catch (error) {
      console.error('Error calculating attendance rate:', error);
      return 0;
    }
  }

  /**
   * تحديث Cache بشكل استباقي
   */
  async preloadStudentData(studentIds: string[]): Promise<void> {
    const promises = studentIds.map(id => 
      this.getStudentById(id).catch(error => 
        console.error(`Failed to preload student ${id}:`, error)
      )
    );

    await Promise.allSettled(promises);
  }

  /**
   * الحصول على إحصائيات Cache
   */
  getCacheStatistics(): {
    totalItems: number;
    hitRate: number;
    memoryUsage: number;
  } {
    return CacheManager.getStats();
  }

  /**
   * تنظيف Cache يدوياً
   */
  clearAllCaches(): void {
    CacheManager.clear();
  }
}

export default CachedStudentService;
