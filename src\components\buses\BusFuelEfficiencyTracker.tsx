import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Fuel,
  TrendingUp,
  TrendingDown,
  Plus,
  Calendar,
  DollarSign,
  BarChart3,
  MapPin,
  Calculator,
  Download,
} from "lucide-react";
import { Button } from "../ui/Button";
import { supabase } from "../../lib/supabase";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";

interface FuelRecord {
  id: string;
  bus_id: string;
  date: string;
  fuel_amount: number;
  fuel_cost: number;
  odometer_reading: number;
  fuel_type: string;
  station_name?: string;
  notes?: string;
  created_at: string;
}

interface EfficiencyMetrics {
  currentEfficiency: number;
  previousEfficiency: number;
  trend: "up" | "down" | "stable";
  totalFuelCost: number;
  totalDistance: number;
  totalFuelAmount: number;
  averageCostPerLiter: number;
}

interface BusFuelEfficiencyTrackerProps {
  busId?: string;
  busPlateNumber?: string;
  className?: string;
}

export const BusFuelEfficiencyTracker: React.FC<
  BusFuelEfficiencyTrackerProps
> = ({ busId, busPlateNumber, className = "" }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { buses } = useDatabase();
  const [fuelRecords, setFuelRecords] = useState<FuelRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [metrics, setMetrics] = useState<EfficiencyMetrics | null>(null);
  const [selectedBus, setSelectedBus] = useState(busId || "");
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    end: new Date().toISOString().split("T")[0],
  });
  const [newRecord, setNewRecord] = useState({
    fuel_amount: "",
    fuel_cost: "",
    odometer_reading: "",
    fuel_type: "diesel",
    station_name: "",
    notes: "",
    date: new Date().toISOString().split("T")[0],
  });

  const availableBuses = busId
    ? buses.filter((bus) => bus.id === busId)
    : buses;
  const currentBus = buses.find((bus) => bus.id === selectedBus);

  useEffect(() => {
    if (selectedBus) {
      fetchFuelRecords();
    }
  }, [selectedBus, dateRange]);

  const fetchFuelRecords = async () => {
    if (!selectedBus || !user?.tenant_id) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("fuel_records")
        .select("*")
        .eq("bus_id", selectedBus)
        .eq("tenant_id", user.tenant_id)
        .gte("date", dateRange.start)
        .lte("date", dateRange.end)
        .order("date", { ascending: false });

      if (error) {
        console.error("Error fetching fuel records:", error);
        return;
      }

      setFuelRecords(data || []);
      calculateMetrics(data || []);
    } catch (error) {
      console.error("Error fetching fuel records:", error);
    } finally {
      setLoading(false);
    }
  };

  const calculateMetrics = (records: FuelRecord[]) => {
    if (records.length < 2) {
      setMetrics(null);
      return;
    }

    // Sort by odometer reading to calculate distances
    const sortedRecords = [...records].sort(
      (a, b) => a.odometer_reading - b.odometer_reading,
    );

    let totalDistance = 0;
    let totalFuelAmount = 0;
    let totalFuelCost = 0;
    const efficiencyReadings: number[] = [];

    for (let i = 1; i < sortedRecords.length; i++) {
      const currentRecord = sortedRecords[i];
      const previousRecord = sortedRecords[i - 1];

      const distance =
        currentRecord.odometer_reading - previousRecord.odometer_reading;
      const fuelUsed = currentRecord.fuel_amount;

      if (distance > 0 && fuelUsed > 0) {
        const efficiency = distance / fuelUsed; // km per liter
        efficiencyReadings.push(efficiency);
        totalDistance += distance;
        totalFuelAmount += fuelUsed;
      }

      totalFuelCost += currentRecord.fuel_cost;
    }

    const currentEfficiency =
      efficiencyReadings.length > 0
        ? efficiencyReadings[efficiencyReadings.length - 1]
        : 0;

    const previousEfficiency =
      efficiencyReadings.length > 1
        ? efficiencyReadings[efficiencyReadings.length - 2]
        : currentEfficiency;

    let trend: "up" | "down" | "stable" = "stable";
    if (currentEfficiency > previousEfficiency * 1.05) trend = "up";
    else if (currentEfficiency < previousEfficiency * 0.95) trend = "down";

    const averageCostPerLiter =
      totalFuelAmount > 0 ? totalFuelCost / totalFuelAmount : 0;

    setMetrics({
      currentEfficiency,
      previousEfficiency,
      trend,
      totalFuelCost,
      totalDistance,
      totalFuelAmount,
      averageCostPerLiter,
    });
  };

  const handleAddRecord = async () => {
    if (!selectedBus || !user?.tenant_id) return;

    try {
      const { error } = await supabase.from("fuel_records").insert({
        bus_id: selectedBus,
        tenant_id: user.tenant_id,
        date: newRecord.date,
        fuel_amount: parseFloat(newRecord.fuel_amount),
        fuel_cost: parseFloat(newRecord.fuel_cost),
        odometer_reading: parseInt(newRecord.odometer_reading),
        fuel_type: newRecord.fuel_type,
        station_name: newRecord.station_name || null,
        notes: newRecord.notes || null,
      });

      if (error) {
        console.error("Error adding fuel record:", error);
        return;
      }

      setShowAddModal(false);
      setNewRecord({
        fuel_amount: "",
        fuel_cost: "",
        odometer_reading: "",
        fuel_type: "diesel",
        station_name: "",
        notes: "",
        date: new Date().toISOString().split("T")[0],
      });
      fetchFuelRecords();
    } catch (error) {
      console.error("Error adding fuel record:", error);
    }
  };

  const exportToCSV = () => {
    if (fuelRecords.length === 0) return;

    const headers = [
      "Date",
      "Fuel Amount (L)",
      "Fuel Cost",
      "Odometer Reading",
      "Fuel Type",
      "Station",
      "Notes",
    ];

    const csvContent = [
      headers.join(","),
      ...fuelRecords.map((record) =>
        [
          record.date,
          record.fuel_amount,
          record.fuel_cost.toFixed(2),
          record.odometer_reading,
          record.fuel_type,
          record.station_name || "",
          `"${record.notes || ""}"`,
        ].join(","),
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `fuel-efficiency-${currentBus?.plate_number || "bus"}-${new Date().toISOString().split("T")[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
            <Fuel className="mr-2 h-5 w-5 text-primary-500" />
            Fuel Efficiency Tracker
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Monitor fuel consumption and efficiency trends over time
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={exportToCSV}
            disabled={fuelRecords.length === 0}
            variant="outline"
            leftIcon={<Download size={16} />}
          >
            Export
          </Button>
          <Button
            onClick={() => setShowAddModal(true)}
            disabled={!selectedBus}
            leftIcon={<Plus size={16} />}
          >
            Add Fuel Record
          </Button>
        </div>
      </div>

      {/* Bus and Date Selection */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {!busId && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Select Bus
              </label>
              <select
                value={selectedBus}
                onChange={(e) => setSelectedBus(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="">Choose a bus...</option>
                {availableBuses.map((bus) => (
                  <option key={bus.id} value={bus.id}>
                    {bus.plate_number}
                  </option>
                ))}
              </select>
            </div>
          )}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Start Date
            </label>
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) =>
                setDateRange({ ...dateRange, start: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              End Date
            </label>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) =>
                setDateRange({ ...dateRange, end: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>
      </div>

      {selectedBus && (
        <>
          {/* Efficiency Metrics */}
          {metrics && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Current Efficiency
                    </p>
                    <div className="flex items-center gap-2">
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {metrics.currentEfficiency.toFixed(2)}
                      </p>
                      <span className="text-sm text-gray-500">km/L</span>
                      {metrics.trend === "up" && (
                        <TrendingUp className="h-5 w-5 text-green-500" />
                      )}
                      {metrics.trend === "down" && (
                        <TrendingDown className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                  </div>
                  <Fuel className="h-8 w-8 text-blue-500" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Total Distance
                    </p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      {metrics.totalDistance.toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-500">km</p>
                  </div>
                  <MapPin className="h-8 w-8 text-green-500" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Total Fuel Cost
                    </p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      ${metrics.totalFuelCost.toFixed(2)}
                    </p>
                    <p className="text-sm text-gray-500">
                      ${metrics.averageCostPerLiter.toFixed(2)}/L avg
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-yellow-500" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Total Fuel Used
                    </p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      {metrics.totalFuelAmount.toFixed(1)}
                    </p>
                    <p className="text-sm text-gray-500">liters</p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-purple-500" />
                </div>
              </div>
            </div>
          )}

          {/* Fuel Records Table */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
            <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Fuel Records ({fuelRecords.length})
              </h3>
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
              </div>
            ) : fuelRecords.length === 0 ? (
              <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                <Fuel size={48} className="mx-auto mb-4 opacity-50" />
                <p>No fuel records found for the selected period.</p>
                <p className="text-sm mt-2">
                  Add your first fuel record to start tracking efficiency.
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Fuel Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Cost
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Odometer
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Station
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                    {fuelRecords.map((record) => (
                      <tr
                        key={record.id}
                        className="hover:bg-gray-50 dark:hover:bg-gray-700"
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {new Date(record.date).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {record.fuel_amount.toFixed(1)} L
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          ${record.fuel_cost.toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {record.odometer_reading.toLocaleString()} km
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {record.fuel_type.charAt(0).toUpperCase() +
                            record.fuel_type.slice(1)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {record.station_name || "-"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </>
      )}

      {/* Add Fuel Record Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Add Fuel Record
                </h3>
                <Button
                  onClick={() => setShowAddModal(false)}
                  variant="outline"
                  size="sm"
                >
                  Cancel
                </Button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Date
                  </label>
                  <input
                    type="date"
                    value={newRecord.date}
                    onChange={(e) =>
                      setNewRecord({ ...newRecord, date: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Fuel Amount (L)
                    </label>
                    <input
                      type="number"
                      step="0.1"
                      value={newRecord.fuel_amount}
                      onChange={(e) =>
                        setNewRecord({
                          ...newRecord,
                          fuel_amount: e.target.value,
                        })
                      }
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                      placeholder="50.0"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Cost ($)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      value={newRecord.fuel_cost}
                      onChange={(e) =>
                        setNewRecord({
                          ...newRecord,
                          fuel_cost: e.target.value,
                        })
                      }
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                      placeholder="75.00"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Odometer Reading (km)
                  </label>
                  <input
                    type="number"
                    value={newRecord.odometer_reading}
                    onChange={(e) =>
                      setNewRecord({
                        ...newRecord,
                        odometer_reading: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    placeholder="125000"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Fuel Type
                  </label>
                  <select
                    value={newRecord.fuel_type}
                    onChange={(e) =>
                      setNewRecord({ ...newRecord, fuel_type: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="diesel">Diesel</option>
                    <option value="gasoline">Gasoline</option>
                    <option value="cng">CNG</option>
                    <option value="electric">Electric</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Station Name (Optional)
                  </label>
                  <input
                    type="text"
                    value={newRecord.station_name}
                    onChange={(e) =>
                      setNewRecord({
                        ...newRecord,
                        station_name: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Shell Station"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Notes (Optional)
                  </label>
                  <textarea
                    value={newRecord.notes}
                    onChange={(e) =>
                      setNewRecord({ ...newRecord, notes: e.target.value })
                    }
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Additional notes..."
                  />
                </div>
              </div>

              <div className="flex justify-end gap-2 mt-6">
                <Button
                  onClick={() => setShowAddModal(false)}
                  variant="outline"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleAddRecord}
                  disabled={
                    !newRecord.fuel_amount ||
                    !newRecord.fuel_cost ||
                    !newRecord.odometer_reading
                  }
                >
                  Add Record
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BusFuelEfficiencyTracker;
