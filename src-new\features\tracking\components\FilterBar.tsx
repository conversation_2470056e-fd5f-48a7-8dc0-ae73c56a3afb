/**
 * Filter Bar Component
 * مكون شريط الفلاتر
 */

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Filter,
  X,
  ChevronDown,
  Settings,
  Eye,
  EyeOff,
  RefreshCw,
  Route as RouteIcon,
  User,
  Activity,
  MapPin,
  Navigation,
  Clock,
} from 'lucide-react';
import { FilterOptions } from './TrackingDashboard';

interface FilterBarProps {
  filters: FilterOptions;
  onFilterChange: (filters: Partial<FilterOptions>) => void;
  availableRoutes: string[];
  availableDrivers: string[];
}

export const FilterBar: React.FC<FilterBarProps> = ({
  filters,
  onFilterChange,
  availableRoutes,
  availableDrivers,
}) => {
  const { t } = useTranslation();
  const [showAdvanced, setShowAdvanced] = useState(false);

  const statusOptions = [
    { value: 'active', label: t('tracking.status.active'), color: 'green' },
    { value: 'stopped', label: t('tracking.status.stopped'), color: 'gray' },
    { value: 'maintenance', label: t('tracking.status.maintenance'), color: 'yellow' },
    { value: 'emergency', label: t('tracking.status.emergency'), color: 'red' },
  ];

  const refreshIntervals = [
    { value: 3000, label: '3s' },
    { value: 5000, label: '5s' },
    { value: 10000, label: '10s' },
    { value: 30000, label: '30s' },
    { value: 60000, label: '1m' },
  ];

  const handleStatusToggle = (status: string) => {
    const newStatus = filters.status.includes(status)
      ? filters.status.filter(s => s !== status)
      : [...filters.status, status];
    onFilterChange({ status: newStatus });
  };

  const handleRouteToggle = (route: string) => {
    const newRoutes = filters.routes.includes(route)
      ? filters.routes.filter(r => r !== route)
      : [...filters.routes, route];
    onFilterChange({ routes: newRoutes });
  };

  const handleDriverToggle = (driver: string) => {
    const newDrivers = filters.drivers.includes(driver)
      ? filters.drivers.filter(d => d !== driver)
      : [...filters.drivers, driver];
    onFilterChange({ drivers: newDrivers });
  };

  const clearAllFilters = () => {
    onFilterChange({
      status: [],
      routes: [],
      drivers: [],
      searchQuery: '',
    });
  };

  const getActiveFiltersCount = () => {
    return filters.status.length + filters.routes.length + filters.drivers.length +
           (filters.searchQuery ? 1 : 0);
  };

  const getStatusColor = (color: string) => {
    const colors = {
      green: 'bg-green-100 text-green-800 border-green-300',
      gray: 'bg-gray-100 text-gray-800 border-gray-300',
      yellow: 'bg-yellow-100 text-yellow-800 border-yellow-300',
      red: 'bg-red-100 text-red-800 border-red-300',
    };
    return colors[color as keyof typeof colors] || colors.gray;
  };

  return (
    <div className="px-6 py-3 bg-white border-b border-gray-200">
      {/* Main Filter Bar */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {/* Filter Toggle */}
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={`
              flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors
              ${showAdvanced 
                ? 'bg-blue-50 border-blue-300 text-blue-700' 
                : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }
            `}
          >
            <Filter className="w-4 h-4" />
            <span className="text-sm font-medium">{t('tracking.filters')}</span>
            {getActiveFiltersCount() > 0 && (
              <span className="bg-blue-600 text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center">
                {getActiveFiltersCount()}
              </span>
            )}
            <ChevronDown className={`w-4 h-4 transition-transform ${showAdvanced ? 'rotate-180' : ''}`} />
          </button>

          {/* Quick Status Filters */}
          <div className="flex items-center space-x-2">
            {statusOptions.map((status) => (
              <button
                key={status.value}
                onClick={() => handleStatusToggle(status.value)}
                className={`
                  px-3 py-1 text-sm rounded-full border transition-colors
                  ${filters.status.includes(status.value)
                    ? getStatusColor(status.color)
                    : 'bg-white border-gray-300 text-gray-600 hover:bg-gray-50'
                  }
                `}
              >
                {status.label}
              </button>
            ))}
          </div>

          {/* Clear Filters */}
          {getActiveFiltersCount() > 0 && (
            <button
              onClick={clearAllFilters}
              className="flex items-center space-x-1 px-2 py-1 text-sm text-gray-500 hover:text-gray-700 transition-colors"
            >
              <X className="w-4 h-4" />
              <span>{t('tracking.clearFilters')}</span>
            </button>
          )}
        </div>

        {/* Map Controls */}
        <div className="flex items-center space-x-2">
          {/* Layer Toggles */}
          <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => onFilterChange({ showTrails: !filters.showTrails })}
              className={`
                p-2 rounded transition-colors
                ${filters.showTrails ? 'bg-white shadow-sm text-blue-600' : 'text-gray-600 hover:text-gray-800'}
              `}
              title={t('tracking.toggleTrails')}
            >
              <Navigation className="w-4 h-4" />
            </button>
            <button
              onClick={() => onFilterChange({ showStops: !filters.showStops })}
              className={`
                p-2 rounded transition-colors
                ${filters.showStops ? 'bg-white shadow-sm text-blue-600' : 'text-gray-600 hover:text-gray-800'}
              `}
              title={t('tracking.toggleStops')}
            >
              <MapPin className="w-4 h-4" />
            </button>
            <button
              onClick={() => onFilterChange({ showTraffic: !filters.showTraffic })}
              className={`
                p-2 rounded transition-colors
                ${filters.showTraffic ? 'bg-white shadow-sm text-blue-600' : 'text-gray-600 hover:text-gray-800'}
              `}
              title={t('tracking.toggleTraffic')}
            >
              <Activity className="w-4 h-4" />
            </button>
          </div>

          {/* Auto Refresh */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onFilterChange({ autoRefresh: !filters.autoRefresh })}
              className={`
                flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors
                ${filters.autoRefresh
                  ? 'bg-green-50 border-green-300 text-green-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }
              `}
            >
              <RefreshCw className={`w-4 h-4 ${filters.autoRefresh ? 'animate-spin' : ''}`} />
              <span className="text-sm">{filters.autoRefresh ? 'ON' : 'OFF'}</span>
            </button>

            {filters.autoRefresh && (
              <select
                value={filters.refreshInterval}
                onChange={(e) => onFilterChange({ refreshInterval: parseInt(e.target.value) })}
                className="px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {refreshIntervals.map((interval) => (
                  <option key={interval.value} value={interval.value}>
                    {interval.label}
                  </option>
                ))}
              </select>
            )}
          </div>
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Routes Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <RouteIcon className="w-4 h-4 inline mr-1" />
                {t('tracking.routes')}
              </label>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {availableRoutes.map((route) => (
                  <label key={route} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.routes.includes(route)}
                      onChange={() => handleRouteToggle(route)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700 truncate">{route}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Drivers Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <User className="w-4 h-4 inline mr-1" />
                {t('tracking.drivers')}
              </label>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {availableDrivers.map((driver) => (
                  <label key={driver} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.drivers.includes(driver)}
                      onChange={() => handleDriverToggle(driver)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700 truncate">{driver}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Additional Settings */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Settings className="w-4 h-4 inline mr-1" />
                {t('tracking.settings')}
              </label>
              <div className="space-y-2">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.showTrails}
                    onChange={(e) => onFilterChange({ showTrails: e.target.checked })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">{t('tracking.showTrails')}</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.showStops}
                    onChange={(e) => onFilterChange({ showStops: e.target.checked })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">{t('tracking.showStops')}</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.showTraffic}
                    onChange={(e) => onFilterChange({ showTraffic: e.target.checked })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">{t('tracking.showTraffic')}</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterBar;
