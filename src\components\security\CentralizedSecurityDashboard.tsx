/**
 * Centralized Security Dashboard
 * Comprehensive security monitoring and management interface
 * Phase 1: RBAC Security Enhancement
 */

import React, { useState, useEffect, useMemo } from "react";
import { usePermissionService } from "../../hooks/usePermissionService";
import { SecurityAuditService } from "../../lib/securityAuditService";
import { PermissionService } from "../../lib/permissionService";
import { ResourceType, Action } from "../../lib/rbac";
import { Alert, AlertDescription, AlertTitle } from "../ui/Alert";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/Card";
import { Badge } from "../ui/Badge";
import { Button } from "../ui/Button";
import { 
  Shield, 
  AlertTriangle, 
  Users, 
  Activity, 
  TrendingUp,
  Eye,
  CheckCircle,
  XCircle,
  Clock
} from "lucide-react";

interface SecurityDashboardProps {
  className?: string;
}

export const CentralizedSecurityDashboard: React.FC<SecurityDashboardProps> = ({
  className
}) => {
  const { checkPermission, hasValidUser } = usePermissionService();
  const [securityMetrics, setSecurityMetrics] = useState<any>(null);
  const [securityAlerts, setSecurityAlerts] = useState<any[]>([]);
  const [auditLogs, setAuditLogs] = useState<any[]>([]);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  const securityAuditService = useMemo(() => SecurityAuditService.getInstance(), []);
  const permissionService = useMemo(() => PermissionService.getInstance(), []);

  // Check if user can access security dashboard
  const canViewSecurity = useMemo(() => {
    const result = checkPermission(ResourceType.REPORT, Action.READ);
    return result.allowed;
  }, [checkPermission]);

  // Load security data
  const loadSecurityData = React.useCallback(() => {
    if (!hasValidUser || !canViewSecurity) return;

    try {
      // Get security metrics
      const metrics = securityAuditService.getSecurityMetrics();
      setSecurityMetrics(metrics);

      // Get unacknowledged alerts
      const alerts = securityAuditService.getSecurityAlerts(true);
      setSecurityAlerts(alerts);

      // Get recent audit logs
      const logs = securityAuditService.getAuditLogs({ limit: 50 });
      setAuditLogs(logs);
    } catch (error) {
      console.error("Error loading security data:", error);
    }
  }, [hasValidUser, canViewSecurity, securityAuditService]);

  // Setup auto-refresh
  useEffect(() => {
    loadSecurityData();

    const interval = setInterval(loadSecurityData, 30000); // Refresh every 30 seconds
    setRefreshInterval(interval);

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [loadSecurityData]);

  // Handle alert acknowledgment
  const handleAcknowledgeAlert = (alertId: string) => {
    // TODO: Get current user ID
    const acknowledged = securityAuditService.acknowledgeAlert(alertId, "current-user");
    if (acknowledged) {
      loadSecurityData(); // Refresh data
    }
  };

  // Render security metrics cards
  const renderMetricsCards = () => {
    if (!securityMetrics) return null;

    const cards = [
      {
        title: "Total Events",
        value: securityMetrics.totalEvents,
        icon: Activity,
        color: "blue",
      },
      {
        title: "Permission Denials",
        value: securityMetrics.permissionDenials,
        icon: XCircle,
        color: securityMetrics.permissionDenials > 10 ? "red" : "yellow",
      },
      {
        title: "Tenant Violations",
        value: securityMetrics.tenantViolations,
        icon: AlertTriangle,
        color: securityMetrics.tenantViolations > 0 ? "red" : "green",
      },
      {
        title: "Critical Events",
        value: securityMetrics.criticalEvents,
        icon: Shield,
        color: securityMetrics.criticalEvents > 0 ? "red" : "green",
      },
      {
        title: "Average Risk Score",
        value: securityMetrics.averageRiskScore,
        icon: TrendingUp,
        color: securityMetrics.averageRiskScore > 50 ? "red" : 
               securityMetrics.averageRiskScore > 25 ? "yellow" : "green",
      },
    ];

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        {cards.map((card, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
              <card.icon className={`h-4 w-4 text-${card.color}-600`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold text-${card.color}-600`}>
                {card.value}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  // Render security alerts
  const renderSecurityAlerts = () => {
    if (!securityAlerts.length) {
      return (
        <Alert variant="success">
          <CheckCircle className="h-4 w-4" />
          <AlertTitle>All Clear</AlertTitle>
          <AlertDescription>No unacknowledged security alerts</AlertDescription>
        </Alert>
      );
    }

    return (
      <div className="space-y-4">
        {securityAlerts.map((alert) => (
          <Alert 
            key={alert.id} 
            variant={alert.severity === "critical" ? "destructive" : "warning"}
          >
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle className="flex items-center justify-between">
              <span>{alert.title}</span>
              <div className="flex items-center space-x-2">
                <Badge variant={alert.severity === "critical" ? "destructive" : "secondary"}>
                  {alert.severity}
                </Badge>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleAcknowledgeAlert(alert.id)}
                >
                  Acknowledge
                </Button>
              </div>
            </AlertTitle>
            <AlertDescription>
              <div className="mt-2">
                <p>{alert.description}</p>
                <div className="mt-2 text-xs text-gray-500">
                  <Clock className="inline h-3 w-3 mr-1" />
                  {alert.timestamp.toLocaleString()}
                  {alert.userId && (
                    <span className="ml-2">User: {alert.userId}</span>
                  )}
                </div>
              </div>
            </AlertDescription>
          </Alert>
        ))}
      </div>
    );
  };

  // Render audit logs table
  const renderAuditLogs = () => {
    if (!auditLogs.length) return null;

    return (
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Timestamp
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Event
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Resource
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Risk Level
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {auditLogs.slice(0, 20).map((log) => (
              <tr key={log.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {log.timestamp.toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {log.userId}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {log.eventType}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {log.resource ? `${log.resource}:${log.action}` : "-"}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <Badge 
                    variant={
                      log.riskLevel === "critical" ? "destructive" :
                      log.riskLevel === "high" ? "destructive" :
                      log.riskLevel === "medium" ? "secondary" : "default"
                    }
                  >
                    {log.riskLevel}
                  </Badge>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {log.success ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  if (!hasValidUser) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Authentication Required</AlertTitle>
        <AlertDescription>Please log in to access the security dashboard</AlertDescription>
      </Alert>
    );
  }

  if (!canViewSecurity) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Access Denied</AlertTitle>
        <AlertDescription>You don't have permission to view security information</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Security Dashboard</h1>
          <p className="text-gray-600">Monitor system security and access patterns</p>
        </div>
        <Button onClick={loadSecurityData} variant="outline">
          <Activity className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Metrics Cards */}
      {renderMetricsCards()}

      {/* Security Alerts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2" />
            Security Alerts
          </CardTitle>
        </CardHeader>
        <CardContent>
          {renderSecurityAlerts()}
        </CardContent>
      </Card>

      {/* Audit Logs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Eye className="h-5 w-5 mr-2" />
            Recent Audit Logs
          </CardTitle>
        </CardHeader>
        <CardContent>
          {renderAuditLogs()}
        </CardContent>
      </Card>
    </div>
  );
};
