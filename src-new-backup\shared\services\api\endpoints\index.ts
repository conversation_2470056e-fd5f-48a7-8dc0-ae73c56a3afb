/**
 * API Endpoints Registry
 * Centralized definition of all API endpoints
 * Phase 2: Application Structure Reorganization
 */

export interface APIEndpoint {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  description: string;
  requiredPermissions: string[];
  requiredRoles: string[];
  parameters?: {
    path?: Record<string, string>;
    query?: Record<string, string>;
    body?: Record<string, any>;
  };
  responses: {
    success: {
      status: number;
      schema: any;
    };
    error: {
      status: number;
      schema: any;
    }[];
  };
  examples: {
    request?: any;
    response?: any;
  };
}

/**
 * Authentication Endpoints
 */
export const AUTH_ENDPOINTS: Record<string, APIEndpoint> = {
  LOGIN: {
    path: '/auth/login',
    method: 'POST',
    description: 'User authentication',
    requiredPermissions: [],
    requiredRoles: [],
    parameters: {
      body: {
        email: 'string',
        password: 'string',
      },
    },
    responses: {
      success: {
        status: 200,
        schema: {
          user: 'User',
          token: 'string',
          refreshToken: 'string',
        },
      },
      error: [
        { status: 401, schema: { message: 'Invalid credentials' } },
        { status: 400, schema: { message: 'Validation error' } },
      ],
    },
    examples: {
      request: {
        email: '<EMAIL>',
        password: 'password123',
      },
      response: {
        user: { id: '1', email: '<EMAIL>', role: 'admin' },
        token: 'jwt_token_here',
        refreshToken: 'refresh_token_here',
      },
    },
  },
  
  LOGOUT: {
    path: '/auth/logout',
    method: 'POST',
    description: 'User logout',
    requiredPermissions: [],
    requiredRoles: ['*'],
    responses: {
      success: {
        status: 200,
        schema: { message: 'Logged out successfully' },
      },
      error: [
        { status: 401, schema: { message: 'Unauthorized' } },
      ],
    },
    examples: {
      response: { message: 'Logged out successfully' },
    },
  },
  
  REFRESH_TOKEN: {
    path: '/auth/refresh',
    method: 'POST',
    description: 'Refresh authentication token',
    requiredPermissions: [],
    requiredRoles: ['*'],
    parameters: {
      body: {
        refreshToken: 'string',
      },
    },
    responses: {
      success: {
        status: 200,
        schema: {
          token: 'string',
          refreshToken: 'string',
        },
      },
      error: [
        { status: 401, schema: { message: 'Invalid refresh token' } },
      ],
    },
    examples: {
      request: { refreshToken: 'refresh_token_here' },
      response: {
        token: 'new_jwt_token',
        refreshToken: 'new_refresh_token',
      },
    },
  },
};

/**
 * User Management Endpoints
 */
export const USER_ENDPOINTS: Record<string, APIEndpoint> = {
  GET_USERS: {
    path: '/users',
    method: 'GET',
    description: 'Get list of users',
    requiredPermissions: ['VIEW_ALL_USERS', 'VIEW_TENANT_USERS'],
    requiredRoles: ['admin', 'school_manager'],
    parameters: {
      query: {
        page: 'number',
        limit: 'number',
        role: 'string',
        tenant_id: 'string',
      },
    },
    responses: {
      success: {
        status: 200,
        schema: {
          users: 'User[]',
          pagination: 'PaginationInfo',
        },
      },
      error: [
        { status: 403, schema: { message: 'Insufficient permissions' } },
      ],
    },
    examples: {
      request: { page: 1, limit: 10, role: 'driver' },
      response: {
        users: [{ id: '1', name: 'John Doe', role: 'driver' }],
        pagination: { total: 50, page: 1, limit: 10 },
      },
    },
  },
  
  CREATE_USER: {
    path: '/users',
    method: 'POST',
    description: 'Create new user',
    requiredPermissions: ['USERS_CREATE'],
    requiredRoles: ['admin', 'school_manager'],
    parameters: {
      body: {
        name: 'string',
        email: 'string',
        role: 'string',
        tenant_id: 'string',
      },
    },
    responses: {
      success: {
        status: 201,
        schema: { user: 'User' },
      },
      error: [
        { status: 400, schema: { message: 'Validation error' } },
        { status: 403, schema: { message: 'Insufficient permissions' } },
      ],
    },
    examples: {
      request: {
        name: 'Jane Smith',
        email: '<EMAIL>',
        role: 'driver',
        tenant_id: 'school_1',
      },
      response: {
        user: { id: '2', name: 'Jane Smith', email: '<EMAIL>' },
      },
    },
  },
  
  UPDATE_USER: {
    path: '/users/:id',
    method: 'PUT',
    description: 'Update user information',
    requiredPermissions: ['USERS_UPDATE_ALL', 'USERS_UPDATE_TENANT', 'USERS_UPDATE_OWN'],
    requiredRoles: ['admin', 'school_manager', 'driver', 'parent'],
    parameters: {
      path: { id: 'string' },
      body: {
        name: 'string',
        email: 'string',
        is_active: 'boolean',
      },
    },
    responses: {
      success: {
        status: 200,
        schema: { user: 'User' },
      },
      error: [
        { status: 404, schema: { message: 'User not found' } },
        { status: 403, schema: { message: 'Insufficient permissions' } },
      ],
    },
    examples: {
      request: { name: 'Jane Smith Updated' },
      response: {
        user: { id: '2', name: 'Jane Smith Updated', email: '<EMAIL>' },
      },
    },
  },
  
  DELETE_USER: {
    path: '/users/:id',
    method: 'DELETE',
    description: 'Delete user',
    requiredPermissions: ['USERS_DELETE_ALL', 'USERS_DELETE_TENANT'],
    requiredRoles: ['admin', 'school_manager'],
    parameters: {
      path: { id: 'string' },
    },
    responses: {
      success: {
        status: 200,
        schema: { message: 'User deleted successfully' },
      },
      error: [
        { status: 404, schema: { message: 'User not found' } },
        { status: 403, schema: { message: 'Insufficient permissions' } },
      ],
    },
    examples: {
      response: { message: 'User deleted successfully' },
    },
  },
};

/**
 * All API Endpoints Registry
 */
export const API_ENDPOINTS = {
  AUTH: AUTH_ENDPOINTS,
  USERS: USER_ENDPOINTS,
  // More endpoint categories will be added here
} as const;

/**
 * Get endpoint by path and method
 */
export function getEndpoint(path: string, method: string): APIEndpoint | null {
  for (const category of Object.values(API_ENDPOINTS)) {
    for (const endpoint of Object.values(category)) {
      if (endpoint.path === path && endpoint.method === method) {
        return endpoint;
      }
    }
  }
  return null;
}

/**
 * Get all endpoints for a specific role
 */
export function getEndpointsForRole(role: string): APIEndpoint[] {
  const endpoints: APIEndpoint[] = [];
  
  for (const category of Object.values(API_ENDPOINTS)) {
    for (const endpoint of Object.values(category)) {
      if (endpoint.requiredRoles.includes(role) || endpoint.requiredRoles.includes('*')) {
        endpoints.push(endpoint);
      }
    }
  }
  
  return endpoints;
}
