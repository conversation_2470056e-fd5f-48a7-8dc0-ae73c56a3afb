/**
 * Theme Security Service
 * Security and permission management for theme customization
 * Phase 3: UI/UX Enhancement - Security Layer
 */

import { UserRole, User } from '../../api/types';
import { ThemePermission } from '../../lib/rbac';
import { TenantThemeConfig } from '../../design-system/themes/tenant/TenantTheme';

export interface ThemeSecurityPolicy {
  maxFileSize: number; // in bytes
  allowedImageTypes: string[];
  allowedFontTypes: string[];
  maxCustomThemes: number;
  requireApproval: boolean;
  auditChanges: boolean;
}

export interface ThemeAuditLog {
  id: string;
  userId: string;
  userName: string;
  schoolId: string;
  schoolName: string;
  action: 'create' | 'update' | 'delete' | 'approve' | 'reject';
  changes: Record<string, any>;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Theme Security Service
 */
export class ThemeSecurityService {
  private policies: Map<string, ThemeSecurityPolicy> = new Map();
  private auditLogs: ThemeAuditLog[] = [];

  constructor() {
    this.initializeDefaultPolicies();
  }

  /**
   * Initialize default security policies
   */
  private initializeDefaultPolicies(): void {
    // Default policy for school managers
    this.policies.set('school_manager', {
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedImageTypes: ['image/jpeg', 'image/png', 'image/svg+xml', 'image/webp'],
      allowedFontTypes: ['font/woff', 'font/woff2', 'font/ttf'],
      maxCustomThemes: 1,
      requireApproval: false,
      auditChanges: true,
    });

    // Policy for super admins
    this.policies.set('super_admin', {
      maxFileSize: 20 * 1024 * 1024, // 20MB
      allowedImageTypes: ['image/jpeg', 'image/png', 'image/svg+xml', 'image/webp', 'image/gif'],
      allowedFontTypes: ['font/woff', 'font/woff2', 'font/ttf', 'font/otf'],
      maxCustomThemes: -1, // unlimited
      requireApproval: false,
      auditChanges: true,
    });
  }

  /**
   * Check if user has permission for theme action
   */
  hasThemePermission(user: User, permission: ThemePermission, schoolId?: string): boolean {
    // Super admin has all permissions
    if (user.role === UserRole.SUPER_ADMIN) {
      return true;
    }

    // School manager can only manage their own school
    if (user.role === UserRole.SCHOOL_MANAGER) {
      if (schoolId && user.school_id !== schoolId) {
        return false;
      }

      const allowedPermissions = [
        ThemePermission.MANAGE_OWN_THEME,
        ThemePermission.CUSTOMIZE_COLORS,
        ThemePermission.UPLOAD_LOGO,
        ThemePermission.CHANGE_FONTS,
        ThemePermission.VIEW_THEME_SETTINGS,
      ];

      return allowedPermissions.includes(permission);
    }

    // Teachers can only view
    if (user.role === UserRole.TEACHER) {
      return permission === ThemePermission.VIEW_THEME_SETTINGS;
    }

    return false;
  }

  /**
   * Validate theme configuration
   */
  validateThemeConfig(
    config: TenantThemeConfig,
    user: User
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const policy = this.getSecurityPolicy(user.role);

    // Validate basic structure
    if (!config.id || !config.name || !config.branding) {
      errors.push('بيانات الثيم غير مكتملة');
    }

    // Validate school ownership
    if (user.role === UserRole.SCHOOL_MANAGER && config.id !== user.school_id) {
      errors.push('لا يمكنك تعديل ثيم مدرسة أخرى');
    }

    // Validate branding name
    if (config.branding.name && config.branding.name.length > 100) {
      errors.push('اسم المؤسسة طويل جداً (الحد الأقصى 100 حرف)');
    }

    // Validate colors
    if (config.branding.colors) {
      const colorErrors = this.validateColors(config.branding.colors);
      errors.push(...colorErrors);
    }

    // Validate custom CSS (if any)
    if (config.branding.customCSS) {
      const cssErrors = this.validateCustomCSS(config.branding.customCSS);
      errors.push(...cssErrors);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate uploaded file
   */
  validateUploadedFile(
    file: File,
    fileType: 'logo' | 'font' | 'asset',
    user: User
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const policy = this.getSecurityPolicy(user.role);

    // Check file size
    if (file.size > policy.maxFileSize) {
      errors.push(`حجم الملف كبير جداً (الحد الأقصى ${this.formatFileSize(policy.maxFileSize)})`);
    }

    // Check file type
    let allowedTypes: string[] = [];
    switch (fileType) {
      case 'logo':
        allowedTypes = policy.allowedImageTypes;
        break;
      case 'font':
        allowedTypes = policy.allowedFontTypes;
        break;
      case 'asset':
        allowedTypes = [...policy.allowedImageTypes, ...policy.allowedFontTypes];
        break;
    }

    if (!allowedTypes.includes(file.type)) {
      errors.push(`نوع الملف غير مدعوم (الأنواع المدعومة: ${allowedTypes.join(', ')})`);
    }

    // Check file name for security
    if (this.hasUnsafeFileName(file.name)) {
      errors.push('اسم الملف يحتوي على أحرف غير آمنة');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Log theme action for audit
   */
  logThemeAction(
    user: User,
    schoolId: string,
    action: ThemeAuditLog['action'],
    changes: Record<string, any>,
    metadata?: { ipAddress?: string; userAgent?: string }
  ): void {
    const log: ThemeAuditLog = {
      id: this.generateId(),
      userId: user.id,
      userName: user.name,
      schoolId,
      schoolName: user.school?.name || 'غير محدد',
      action,
      changes,
      timestamp: new Date().toISOString(),
      ipAddress: metadata?.ipAddress,
      userAgent: metadata?.userAgent,
    };

    this.auditLogs.push(log);

    // Send to backend for persistent storage
    this.sendAuditLog(log);
  }

  /**
   * Get audit logs for school
   */
  getAuditLogs(
    schoolId?: string,
    userId?: string,
    limit: number = 50
  ): ThemeAuditLog[] {
    let logs = [...this.auditLogs];

    if (schoolId) {
      logs = logs.filter(log => log.schoolId === schoolId);
    }

    if (userId) {
      logs = logs.filter(log => log.userId === userId);
    }

    return logs
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  /**
   * Check rate limiting for theme changes
   */
  checkRateLimit(userId: string, action: string): boolean {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    
    const recentActions = this.auditLogs.filter(
      log => 
        log.userId === userId &&
        log.action === action &&
        (now - new Date(log.timestamp).getTime()) < oneHour
    );

    // Allow max 10 changes per hour
    return recentActions.length < 10;
  }

  /**
   * Sanitize theme configuration
   */
  sanitizeThemeConfig(config: TenantThemeConfig): TenantThemeConfig {
    const sanitized = { ...config };

    // Sanitize branding name
    if (sanitized.branding.name) {
      sanitized.branding.name = this.sanitizeString(sanitized.branding.name);
    }

    // Remove any script tags from custom CSS
    if (sanitized.branding.customCSS) {
      sanitized.branding.customCSS = this.sanitizeCSS(sanitized.branding.customCSS);
    }

    // Validate and sanitize URLs
    if (sanitized.branding.logo.light) {
      sanitized.branding.logo.light = this.sanitizeURL(sanitized.branding.logo.light);
    }
    if (sanitized.branding.logo.dark) {
      sanitized.branding.logo.dark = this.sanitizeURL(sanitized.branding.logo.dark);
    }

    return sanitized;
  }

  /**
   * Private helper methods
   */
  private getSecurityPolicy(role: UserRole): ThemeSecurityPolicy {
    const policyKey = role === UserRole.SUPER_ADMIN ? 'super_admin' : 'school_manager';
    return this.policies.get(policyKey) || this.policies.get('school_manager')!;
  }

  private validateColors(colors: any): string[] {
    const errors: string[] = [];
    
    // Validate hex color format
    const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    
    const validateColorPalette = (palette: any, name: string) => {
      if (palette && typeof palette === 'object') {
        Object.entries(palette).forEach(([shade, color]) => {
          if (typeof color === 'string' && !hexColorRegex.test(color)) {
            errors.push(`لون ${name} ${shade} غير صحيح`);
          }
        });
      }
    };

    if (colors.primary) validateColorPalette(colors.primary, 'أساسي');
    if (colors.secondary) validateColorPalette(colors.secondary, 'ثانوي');

    return errors;
  }

  private validateCustomCSS(css: string): string[] {
    const errors: string[] = [];
    
    // Check for dangerous CSS
    const dangerousPatterns = [
      /javascript:/i,
      /expression\(/i,
      /behavior:/i,
      /@import/i,
      /url\(/i,
    ];

    dangerousPatterns.forEach(pattern => {
      if (pattern.test(css)) {
        errors.push('CSS يحتوي على كود غير آمن');
      }
    });

    return errors;
  }

  private hasUnsafeFileName(fileName: string): boolean {
    const unsafeChars = /[<>:"/\\|?*\x00-\x1f]/;
    return unsafeChars.test(fileName);
  }

  private sanitizeString(str: string): string {
    return str.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  }

  private sanitizeCSS(css: string): string {
    return css
      .replace(/javascript:/gi, '')
      .replace(/expression\(/gi, '')
      .replace(/behavior:/gi, '')
      .replace(/@import/gi, '');
  }

  private sanitizeURL(url: string): string {
    try {
      const parsed = new URL(url);
      // Only allow http, https, and data URLs
      if (['http:', 'https:', 'data:'].includes(parsed.protocol)) {
        return url;
      }
    } catch {
      // Invalid URL
    }
    return '';
  }

  private formatFileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private async sendAuditLog(log: ThemeAuditLog): Promise<void> {
    try {
      await fetch('/api/audit/theme', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(log),
      });
    } catch (error) {
      console.error('Failed to send audit log:', error);
    }
  }
}

// Export singleton instance
export const themeSecurityService = new ThemeSecurityService();
