import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  User,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  User<PERSON>he<PERSON>,
} from "lucide-react";
import { Button } from "../ui/Button";
import { Pagination, usePagination } from "../ui/Pagination";
import { StudentModal } from "./StudentModal";
import { BulkEditStudentModal } from "./BulkEditStudentModal";
import { AttendanceForm } from "./AttendanceForm";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/api";

interface StudentListProps {
  onRefresh: () => Promise<void>;
}

export const StudentList: React.FC<StudentListProps> = ({ onRefresh }) => {
  const { t } = useTranslation();
  const { students, users, routes } = useDatabase();

  // Unified state
  const [searchQuery, setSearchQuery] = useState("");
  const [filterGrade, setFilterGrade] = useState<string>("all");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAttendanceFormOpen, setIsAttendanceFormOpen] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Tables<"students"> | undefined>();
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedStudentIds, setSelectedStudentIds] = useState<string[]>([]);
  const [isBulkModalOpen, setIsBulkModalOpen] = useState(false);

  // Filtered students and pagination
  const filteredStudents = students
    .filter((student) => {
      if (filterGrade !== "all") return student.grade === filterGrade;
      return true;
    })
    .filter((student) => {
      if (!searchQuery) return true;
      return student.name.toLowerCase().includes(searchQuery.toLowerCase());
    });
  const { currentPage, totalPages, startIndex, endIndex, goToPage } = usePagination(filteredStudents.length, 10);
  const paginatedStudents = filteredStudents.slice(startIndex, endIndex);

  // Helpers for selection
  const isAllSelected = selectedStudentIds.length > 0 && paginatedStudents.every((s) => selectedStudentIds.includes(s.id));
  const toggleSelectAll = () => {
    if (isAllSelected) setSelectedStudentIds([]);
    else setSelectedStudentIds(paginatedStudents.map((s) => s.id));
  };
  const toggleSelectOne = (id: string) => {
    setSelectedStudentIds((prev) => prev.includes(id) ? prev.filter((sid) => sid !== id) : [...prev, id]);
  };

  // Helpers
  const handleSaveStudent = async (data: Partial<Tables<"students">>, id?: string) => {
    try {
      if (id) {
        const { error } = await supabase.from("students").update(data).eq("id", id);
        if (error) throw error;
      } else {
        const { error } = await supabase.from("students").insert([
          {
            ...data,
            parent_id: data.parent_id || null,
            route_stop_id: data.route_stop_id || null,
          },
        ]);
        if (error) throw error;
      }
      await onRefresh();
      handleCloseModal();
    } catch (error) {
      console.error("Error saving student:", error);
      throw error;
    }
  };

  const handleOpenModal = (student?: Tables<"students">) => {
    setSelectedStudent(student);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setSelectedStudent(undefined);
    setIsModalOpen(false);
  };

  const handleOpenAttendanceForm = (student: Tables<"students">) => {
    setSelectedStudent(student);
    setIsAttendanceFormOpen(true);
  };

  const handleCloseAttendanceForm = () => {
    setSelectedStudent(undefined);
    setIsAttendanceFormOpen(false);
  };

  const handleDelete = async (id: string) => {
    if (!confirm(t("students.deleteConfirmation"))) return;

    setIsDeleting(true);
    try {
      const { error } = await supabase.from("students").delete().eq("id", id);

      if (error) throw error;

      await onRefresh();
    } catch (error) {
      console.error("Error deleting student:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  // Get parent name from ID
  const getParentName = (parentId?: string) => {
    if (!parentId) return "-";
    const parent = users.find((u) => u.id === parentId);
    return parent ? parent.name : "-";
  };

  // Get stop name from ID
  const getStopName = (stopId?: string) => {
    if (!stopId) return "-";
    for (const route of routes) {
      if (!route.stops || !Array.isArray(route.stops)) {
        continue;
      }
      const stop = route.stops.find((s) => s.id === stopId);
      if (stop) {
        const arrivalTime = stop.arrival_time ? ` (${stop.arrival_time})` : "";
        return `${route.name} - ${stop.name}${arrivalTime}`;
      }
    }
    return "-";
  };

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="relative max-w-xs w-full">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={18} className="text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder={t("common.search")}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex items-center space-x-2">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Filter size={18} className="text-gray-400" />
                </div>
                <select
                  className="block pl-10 pr-8 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                  value={filterGrade}
                  onChange={(e) => setFilterGrade(e.target.value)}
                >
                  <option value="all">
                    {t("common.filter")}: {t("common.all")}
                  </option>
                  <option value="1">Grade 1</option>
                  <option value="2">Grade 2</option>
                  <option value="3">Grade 3</option>
                  <option value="4">Grade 4</option>
                  <option value="5">Grade 5</option>
                  <option value="6">Grade 6</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          {/* Bulk Actions Bar */}
          {selectedStudentIds.length > 0 && (
            <div className="flex items-center justify-between p-2 bg-primary-50 dark:bg-primary-900/20 border-b border-primary-200 dark:border-primary-700">
              <span className="text-sm font-medium text-primary-700 dark:text-primary-200">
                {t("common.selectedCount", { count: selectedStudentIds.length })}
              </span>
              <Button size="sm" variant="default" onClick={() => setIsBulkModalOpen(true)}>
                {t("common.bulkActions")}
              </Button>
              <Button size="sm" variant="outline" onClick={() => setSelectedStudentIds([])}>
                {t("common.clearSelection")}
              </Button>
            </div>
          )}
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-4 py-3">
                  <input
                    type="checkbox"
                    checked={isAllSelected}
                    onChange={toggleSelectAll}
                    aria-label={t("common.selectAll")}
                  />
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  {t("students.name")}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  {t("students.grade")}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  {t("students.parent")}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  {t("routes.stop")}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  {t("common.actions")}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {paginatedStudents.length > 0 ? (
                paginatedStudents.map((student) => (
                  <tr
                    key={student.id}
                    className={`hover:bg-gray-50 dark:hover:bg-gray-700 ${selectedStudentIds.includes(student.id) ? 'bg-primary-50 dark:bg-primary-900/10' : ''}`}
                  >
                    <td className="px-4 py-4 text-center">
                      <input
                        type="checkbox"
                        checked={selectedStudentIds.includes(student.id)}
                        onChange={() => toggleSelectOne(student.id)}
                        aria-label={t("common.selectRow")}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          {student.photo_url ? (
                            <img
                              src={student.photo_url}
                              alt={student.name}
                              className="h-10 w-10 rounded-full object-cover"
                            />
                          ) : (
                            <div className="h-10 w-10 bg-primary-100 dark:bg-primary-800/20 rounded-full flex items-center justify-center text-primary-600 dark:text-primary-400">
                              <User size={20} />
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {student.name}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {t("students.grade")} {student.grade}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {getParentName(student.parent_id)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {getStopName(student.route_stop_id)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 p-1"
                          title={t("common.view")}
                          onClick={() => handleOpenModal(student)}
                        >
                          <Eye size={18} />
                        </button>
                        <button
                          className="text-warning-600 hover:text-warning-900 dark:text-warning-400 dark:hover:text-warning-300 p-1"
                          title={t("common.edit")}
                          onClick={() => handleOpenModal(student)}
                        >
                          <Edit size={18} />
                        </button>
                        <button
                          className="text-error-600 hover:text-error-900 dark:text-error-400 dark:hover:text-error-300 p-1"
                          title={t("common.delete")}
                          onClick={() => handleDelete(student.id)}
                          disabled={isDeleting}
                        >
                          <Trash2 size={18} />
                        </button>
                        <Button
                          size="sm"
                          variant="success"
                          onClick={() => handleOpenAttendanceForm(student)}
                          className="flex items-center gap-1"
                        >
                          <UserCheck size={16} />
                          {t("students.attendance")}
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={5}
                    className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400"
                  >
                    {searchQuery || filterGrade !== "all"
                      ? t("common.noResultsFound")
                      : t("students.noStudentsFound")}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {t("common.showing")} {startIndex + 1} - {endIndex}{" "}
                {t("common.of")} {filteredStudents.length}{" "}
                {t("students.manageStudents").toLowerCase()}
              </div>
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={goToPage}
              />
            </div>
          </div>
        )}
      </div>

      {isModalOpen && (
        <StudentModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          onSubmit={async (data) => {
            await handleSaveStudent(data, selectedStudent?.id);
          }}
          student={selectedStudent}
        />
      )}

      {/* Bulk Edit Modal */}
      {isBulkModalOpen && (
        <BulkEditStudentModal
          isOpen={isBulkModalOpen}
          onClose={() => setIsBulkModalOpen(false)}
          selectedIds={selectedStudentIds}
          onSubmit={async (data, ids) => {
            if (!ids.length) return;
            const { error } = await supabase.from("students").update(data).in("id", ids);
            if (error) throw error;
            await onRefresh();
            setSelectedStudentIds([]);
            setIsBulkModalOpen(false);
          }}
        />
      )}


      {isAttendanceFormOpen && selectedStudent && (
        <AttendanceForm
          isOpen={isAttendanceFormOpen}
          onClose={handleCloseAttendanceForm}
          onSubmit={async (data) => {
            try {
              const { error } = await supabase
                .from("attendance")
                .insert([data]);

              if (error) throw error;

              handleCloseAttendanceForm();
            } catch (error) {
              console.error("Error recording attendance:", error);
              throw error;
            }
          }}
          student={selectedStudent}
        />
      )}
    </>
  );
};
