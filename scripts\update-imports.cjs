/**
 * تحديث مسارات الاستيراد للبنية الجديدة
 * Update Import Paths for New Structure
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 بدء تحديث مسارات الاستيراد...\n');

class ImportUpdater {
  constructor() {
    this.importMappings = {
      // الملفات الأساسية
      'src/types': 'src-new/core/types',
      'src/utils': 'src-new/core/utils',
      'src/hooks': 'src-new/core/hooks',
      'src/contexts': 'src-new/core/contexts',
      'src/config': 'src-new/core/constants',
      
      // الخدمات
      'src/services': 'src-new/shared/services',
      'src/lib': 'src-new/shared/services/lib',
      'src/middleware': 'src-new/shared/services/middleware',
      
      // المكونات المشتركة
      'src/components/ui': 'src-new/shared/components/ui',
      'src/components/layout': 'src-new/shared/layouts',
      'src/components/common': 'src-new/shared/components/common',
      'src/design-system': 'src-new/shared/components/design-system',
      
      // الميزات
      'src/components/auth': 'src-new/features/auth/components',
      'src/pages/auth': 'src-new/features/auth/pages',
      'src/pages/login': 'src-new/features/auth/pages/login',
      'src/components/security': 'src-new/features/auth/security',
      'src/components/dashboard': 'src-new/features/dashboard/components',
      'src/components/dashboards': 'src-new/features/dashboard/variants',
      'src/pages/dashboard': 'src-new/features/dashboard/pages',
      'src/components/admin': 'src-new/features/dashboard/admin',
      'src/components/buses': 'src-new/features/buses/components',
      'src/components/routes': 'src-new/features/routes/components',
      'src/components/drivers': 'src-new/features/drivers/components',
      'src/components/tracking': 'src-new/features/tracking/components',
      'src/components/students': 'src-new/features/students/components',
      'src/components/schools': 'src-new/features/schools/components',
      'src/pages/school': 'src-new/features/schools/pages',
      'src/components/notifications': 'src-new/features/notifications/components',
      'src/components/reports': 'src-new/features/reports/components',
      'src/components/maintenance': 'src-new/features/maintenance/components',
      'src/components/maps': 'src-new/features/tracking/maps',
      'src/components/map': 'src-new/features/tracking/map',
      
      // الملفات الثابتة
      'src/themes': 'src-new/assets/themes',
      'src/i18n': 'src-new/assets/locales'
    };
    
    this.updatedFiles = [];
    this.errors = [];
  }

  /**
   * تحديث مسارات الاستيراد في ملف واحد
   */
  updateFileImports(filePath) {
    try {
      if (!fs.existsSync(filePath)) {
        return false;
      }

      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;

      // تحديث جميع أنواع الاستيراد
      for (const [oldPath, newPath] of Object.entries(this.importMappings)) {
        // استيراد عادي
        const importRegex = new RegExp(`from\\s+['"\`]${oldPath.replace(/\//g, '\\/')}`, 'g');
        if (importRegex.test(content)) {
          content = content.replace(importRegex, `from '${newPath}`);
          hasChanges = true;
        }

        // استيراد ديناميكي
        const dynamicImportRegex = new RegExp(`import\\s*\\(\\s*['"\`]${oldPath.replace(/\//g, '\\/')}`, 'g');
        if (dynamicImportRegex.test(content)) {
          content = content.replace(dynamicImportRegex, `import('${newPath}`);
          hasChanges = true;
        }

        // require
        const requireRegex = new RegExp(`require\\s*\\(\\s*['"\`]${oldPath.replace(/\//g, '\\/')}`, 'g');
        if (requireRegex.test(content)) {
          content = content.replace(requireRegex, `require('${newPath}`);
          hasChanges = true;
        }
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, content);
        this.updatedFiles.push(filePath);
        return true;
      }

      return false;
    } catch (error) {
      this.errors.push(`خطأ في تحديث ${filePath}: ${error.message}`);
      return false;
    }
  }

  /**
   * تحديث مسارات الاستيراد في مجلد بشكل تكراري
   */
  updateDirectoryImports(dirPath) {
    if (!fs.existsSync(dirPath)) {
      return;
    }

    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // تجاهل مجلدات معينة
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          this.updateDirectoryImports(fullPath);
        }
      } else if (stat.isFile()) {
        // تحديث الملفات ذات الامتدادات المناسبة
        const ext = path.extname(item);
        if (['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
          this.updateFileImports(fullPath);
        }
      }
    }
  }

  /**
   * تحديث جميع مسارات الاستيراد في البنية الجديدة
   */
  updateAllImports() {
    console.log('🔄 تحديث مسارات الاستيراد في البنية الجديدة...');
    
    // تحديث الملفات في البنية الجديدة
    this.updateDirectoryImports('src-new');
    
    // تحديث ملفات التكوين الجذرية
    const rootFiles = [
      'vite.config.ts',
      'tsconfig.json'
    ];
    
    rootFiles.forEach(file => {
      if (fs.existsSync(file)) {
        this.updateFileImports(file);
      }
    });

    console.log(`✅ تم تحديث ${this.updatedFiles.length} ملف`);
    
    if (this.errors.length > 0) {
      console.log(`⚠️ ${this.errors.length} خطأ في التحديث`);
      this.errors.forEach(error => console.log(`  • ${error}`));
    }
  }

  /**
   * إنشاء ملف index.ts محدث للبنية الجديدة
   */
  createUpdatedIndexFiles() {
    console.log('📄 إنشاء ملفات index.ts محدثة...');

    // ملف index.ts رئيسي للـ core
    const coreIndexContent = `/**
 * تصدير الملفات الأساسية
 * Core Exports
 */

// Types
export * from './types';

// Utils
export * from './utils';

// Hooks
export * from './hooks';

// Contexts
export * from './contexts';

// Constants
export * from './constants';
`;

    fs.writeFileSync('src-new/core/index.ts', coreIndexContent);

    // ملف index.ts للخدمات المشتركة
    const sharedServicesIndexContent = `/**
 * تصدير الخدمات المشتركة
 * Shared Services Exports
 */

// Main Services
export * from './CentralizedPermissionService';
export * from './DatabaseService';
export * from './TenantService';

// Data Services
export * from './data';

// Security Services
export * from './security';

// Lib Services
export * from './lib';
`;

    fs.writeFileSync('src-new/shared/services/index.ts', sharedServicesIndexContent);

    // ملف index.ts للمكونات المشتركة
    const sharedComponentsIndexContent = `/**
 * تصدير المكونات المشتركة
 * Shared Components Exports
 */

// UI Components
export * from './ui';

// Common Components
export * from './common';

// Design System
export * from './design-system';
`;

    fs.writeFileSync('src-new/shared/components/index.ts', sharedComponentsIndexContent);

    // ملف index.ts للميزات
    const featuresIndexContent = `/**
 * تصدير جميع الميزات
 * Features Exports
 */

// Auth Feature
export * from './auth';

// Dashboard Feature
export * from './dashboard';

// Bus Management
export * from './buses';
export * from './routes';
export * from './drivers';

// Student Management
export * from './students';
export * from './schools';

// Advanced Features
export * from './tracking';
export * from './notifications';
export * from './reports';
export * from './maintenance';
`;

    fs.writeFileSync('src-new/features/index.ts', featuresIndexContent);

    console.log('✅ تم إنشاء ملفات index.ts محدثة');
  }

  /**
   * إنشاء تقرير التحديث
   */
  generateUpdateReport() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportDir = path.join(process.cwd(), 'reports', 'import-updates');
    
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const report = {
      update_info: {
        timestamp: timestamp,
        total_mappings: Object.keys(this.importMappings).length,
        updated_files: this.updatedFiles.length,
        errors: this.errors.length
      },
      import_mappings: this.importMappings,
      updated_files: this.updatedFiles,
      errors: this.errors,
      next_steps: [
        'اختبار التطبيق للتأكد من عمل جميع الاستيرادات',
        'إصلاح أي مسارات مفقودة يدوياً',
        'تحديث ملفات التكوين إذا لزم الأمر',
        'حذف البنية القديمة بعد التأكد'
      ]
    };

    const reportPath = path.join(reportDir, `import-update-report-${timestamp}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log(`📊 تم إنشاء تقرير التحديث: ${reportPath}`);
  }
}

/**
 * الدالة الرئيسية
 */
async function main() {
  try {
    const updater = new ImportUpdater();
    
    // تحديث جميع مسارات الاستيراد
    updater.updateAllImports();
    
    // إنشاء ملفات index.ts محدثة
    updater.createUpdatedIndexFiles();
    
    // إنشاء تقرير التحديث
    updater.generateUpdateReport();
    
    console.log('\n🎉 تم إكمال تحديث مسارات الاستيراد بنجاح!');
    console.log('\n📋 الخطوات التالية:');
    console.log('1. اختبار التطبيق: npm run dev');
    console.log('2. إصلاح أي أخطاء استيراد متبقية');
    console.log('3. تحديث ملفات التكوين إذا لزم الأمر');
    console.log('4. حذف البنية القديمة بعد التأكد');
    
  } catch (error) {
    console.error('💥 خطأ في تحديث مسارات الاستيراد:', error);
    process.exit(1);
  }
}

// تشغيل النظام
main();
