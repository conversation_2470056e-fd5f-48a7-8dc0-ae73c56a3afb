-- المرحلة 1: إنشاء جداول الأمان الأساسية
-- Phase 1: Create basic security tables

-- جدول محاولات تسجيل الدخول
-- Login attempts table
CREATE TABLE IF NOT EXISTS login_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL,
    ip_address INET NOT NULL,
    user_agent TEXT,
    success BOOLEAN NOT NULL DEFAULT false,
    attempted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_login_attempts_email ON login_attempts(email);
CREATE INDEX IF NOT EXISTS idx_login_attempts_ip ON login_attempts(ip_address);
CREATE INDEX IF NOT EXISTS idx_login_attempts_attempted_at ON login_attempts(attempted_at);
CREATE INDEX IF NOT EXISTS idx_login_attempts_success ON login_attempts(success);

-- جدول حظر المستخدمين
-- User blocks table
CREATE TABLE IF NOT EXISTS user_blocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL,
    blocked_until TIMESTAMP WITH TIME ZONE NOT NULL,
    reason TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس لجدول حظر المستخدمين
CREATE INDEX IF NOT EXISTS idx_user_blocks_email ON user_blocks(email);
CREATE INDEX IF NOT EXISTS idx_user_blocks_active ON user_blocks(is_active);
CREATE INDEX IF NOT EXISTS idx_user_blocks_until ON user_blocks(blocked_until);

-- جدول حظر عناوين IP
-- IP blocks table
CREATE TABLE IF NOT EXISTS ip_blocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ip_address INET NOT NULL,
    blocked_until TIMESTAMP WITH TIME ZONE NOT NULL,
    reason TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس لجدول حظر IP
CREATE INDEX IF NOT EXISTS idx_ip_blocks_ip ON ip_blocks(ip_address);
CREATE INDEX IF NOT EXISTS idx_ip_blocks_active ON ip_blocks(is_active);
CREATE INDEX IF NOT EXISTS idx_ip_blocks_until ON ip_blocks(blocked_until);

-- جدول الأحداث الأمنية
-- Security events table
CREATE TABLE IF NOT EXISTS security_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('INFO', 'WARNING', 'ERROR')),
    description TEXT NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    tenant_id UUID REFERENCES tenants(id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس لجدول الأحداث الأمنية
CREATE INDEX IF NOT EXISTS idx_security_events_type ON security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON security_events(severity);
CREATE INDEX IF NOT EXISTS idx_security_events_user ON security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_tenant ON security_events(tenant_id);
CREATE INDEX IF NOT EXISTS idx_security_events_created_at ON security_events(created_at);

-- جدول جلسات المستخدمين
-- User sessions table
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    ip_address INET,
    user_agent TEXT,
    device_info JSONB DEFAULT '{}',
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس لجدول الجلسات
CREATE INDEX IF NOT EXISTS idx_user_sessions_user ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at);

-- دالة تنظيف البيانات القديمة
-- Function to clean old data
CREATE OR REPLACE FUNCTION cleanup_old_security_data()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- حذف محاولات تسجيل الدخول الأقدم من 30 يوم
    DELETE FROM login_attempts 
    WHERE attempted_at < NOW() - INTERVAL '30 days';
    
    -- حذف الأحداث الأمنية الأقدم من 90 يوم
    DELETE FROM security_events 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    -- إلغاء تفعيل الحظر المنتهي الصلاحية
    UPDATE user_blocks 
    SET is_active = false 
    WHERE blocked_until < NOW() AND is_active = true;
    
    UPDATE ip_blocks 
    SET is_active = false 
    WHERE blocked_until < NOW() AND is_active = true;
    
    -- حذف الجلسات المنتهية الصلاحية
    DELETE FROM user_sessions 
    WHERE expires_at < NOW() OR last_activity < NOW() - INTERVAL '7 days';
    
    RAISE NOTICE 'Security data cleanup completed';
END;
$$;

-- دالة تسجيل حدث أمني
-- Function to log security event
CREATE OR REPLACE FUNCTION log_security_event(
    event_type TEXT,
    severity TEXT,
    description TEXT,
    user_id UUID DEFAULT NULL,
    tenant_id UUID DEFAULT NULL,
    metadata JSONB DEFAULT '{}'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    event_id UUID;
BEGIN
    INSERT INTO security_events (
        event_type,
        severity,
        description,
        user_id,
        tenant_id,
        metadata
    ) VALUES (
        event_type,
        severity,
        description,
        user_id,
        tenant_id,
        metadata
    ) RETURNING id INTO event_id;
    
    RETURN event_id;
END;
$$;

-- دالة فحص محاولات تسجيل الدخول
-- Function to check login attempts
CREATE OR REPLACE FUNCTION check_login_attempts(
    email_input TEXT,
    ip_input INET,
    time_window_minutes INTEGER DEFAULT 15,
    max_attempts INTEGER DEFAULT 5
)
RETURNS TABLE (
    attempt_count INTEGER,
    is_blocked BOOLEAN,
    blocked_until TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    attempt_count_result INTEGER;
    user_block_until TIMESTAMP WITH TIME ZONE;
    ip_block_until TIMESTAMP WITH TIME ZONE;
BEGIN
    -- عد المحاولات الفاشلة في النافة الزمنية المحددة
    SELECT COUNT(*) INTO attempt_count_result
    FROM login_attempts
    WHERE (email = email_input OR ip_address = ip_input)
    AND success = false
    AND attempted_at > NOW() - (time_window_minutes || ' minutes')::INTERVAL;
    
    -- فحص حظر المستخدم
    SELECT blocked_until INTO user_block_until
    FROM user_blocks
    WHERE email = email_input
    AND is_active = true
    AND blocked_until > NOW()
    ORDER BY blocked_until DESC
    LIMIT 1;
    
    -- فحص حظر IP
    SELECT blocked_until INTO ip_block_until
    FROM ip_blocks
    WHERE ip_address = ip_input
    AND is_active = true
    AND blocked_until > NOW()
    ORDER BY blocked_until DESC
    LIMIT 1;
    
    -- إرجاع النتائج
    RETURN QUERY SELECT 
        attempt_count_result,
        (user_block_until IS NOT NULL OR ip_block_until IS NOT NULL OR attempt_count_result >= max_attempts)::BOOLEAN,
        GREATEST(user_block_until, ip_block_until);
END;
$$;

-- تفعيل RLS على الجداول الجديدة
ALTER TABLE login_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE ip_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;

-- سياسات RLS للأدمن فقط
CREATE POLICY "Admin only access" ON login_attempts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

CREATE POLICY "Admin only access" ON user_blocks
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

CREATE POLICY "Admin only access" ON ip_blocks
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

CREATE POLICY "Admin only access" ON security_events
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

CREATE POLICY "Users can view own sessions" ON user_sessions
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admin can view all sessions" ON user_sessions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

-- إنشاء مهمة تنظيف دورية (يتطلب تفعيل pg_cron)
-- Create periodic cleanup job (requires pg_cron extension)
-- SELECT cron.schedule('security-cleanup', '0 2 * * *', 'SELECT cleanup_old_security_data();');

COMMENT ON TABLE login_attempts IS 'جدول محاولات تسجيل الدخول لحماية من هجمات Brute-force';
COMMENT ON TABLE user_blocks IS 'جدول حظر المستخدمين المؤقت';
COMMENT ON TABLE ip_blocks IS 'جدول حظر عناوين IP المؤقت';
COMMENT ON TABLE security_events IS 'جدول الأحداث الأمنية والتدقيق';
COMMENT ON TABLE user_sessions IS 'جدول جلسات المستخدمين النشطة';
