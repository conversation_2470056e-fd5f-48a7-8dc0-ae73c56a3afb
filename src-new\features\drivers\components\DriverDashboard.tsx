import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  User,
  Star,
  Clock,
  Shield,
  TrendingUp,
  Award,
  Fuel,
  Target,
  Calendar,
  MapPin,
  AlertTriangle,
  CheckCircle,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import * as api from "../../lib/api";

interface DriverPerformance {
  id: string;
  driver_id: string;
  date: string;
  on_time_arrivals?: number;
  total_trips?: number;
  safety_score?: number;
  parent_rating?: number;
  fuel_efficiency?: number;
}

interface PerformanceMetrics {
  onTimeRate: number;
  safetyScore: number;
  parentRating: number;
  fuelEfficiency: number;
  totalTrips: number;
  grade: string;
  gradeColor: string;
}

export const DriverDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const { buses } = useDatabase();
  const [performanceData, setPerformanceData] = useState<DriverPerformance[]>(
    [],
  );
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    end: new Date().toISOString().split("T")[0],
  });

  const myBus = buses.find((bus) => bus.driver_id === user?.id);

  const fetchPerformanceData = async () => {
    if (!user?.id || !tenant?.id) return;

    try {
      setLoading(true);
      const data = await api.getDriverPerformanceMetrics(
        user.id,
        tenant.id,
        dateRange.start,
        dateRange.end,
      );
      setPerformanceData(data || []);
      calculateMetrics(data || []);
    } catch (error) {
      console.error("Error fetching driver performance:", error);
    } finally {
      setLoading(false);
    }
  };

  const calculateMetrics = (data: DriverPerformance[]) => {
    if (data.length === 0) {
      setMetrics(null);
      return;
    }

    const totals = data.reduce(
      (acc, record) => {
        const onTimeRate =
          (record.on_time_arrivals || 0) / Math.max(record.total_trips || 1, 1);
        acc.onTimeRate += onTimeRate;
        acc.safetyScore += record.safety_score || 0;
        acc.parentRating += record.parent_rating || 0;
        acc.fuelEfficiency += record.fuel_efficiency || 0;
        acc.totalTrips += record.total_trips || 0;
        acc.count += 1;
        return acc;
      },
      {
        onTimeRate: 0,
        safetyScore: 0,
        parentRating: 0,
        fuelEfficiency: 0,
        totalTrips: 0,
        count: 0,
      },
    );

    const avgOnTimeRate = (totals.onTimeRate / totals.count) * 100;
    const avgSafetyScore = totals.safetyScore / totals.count;
    const avgParentRating = totals.parentRating / totals.count;
    const avgFuelEfficiency = totals.fuelEfficiency / totals.count;

    const overallScore =
      (avgOnTimeRate + avgSafetyScore + avgParentRating * 20) / 3;
    const grade = getPerformanceGrade(overallScore);

    setMetrics({
      onTimeRate: avgOnTimeRate,
      safetyScore: avgSafetyScore,
      parentRating: avgParentRating,
      fuelEfficiency: avgFuelEfficiency,
      totalTrips: totals.totalTrips,
      grade: grade.grade,
      gradeColor: grade.color,
    });
  };

  const getPerformanceGrade = (score: number) => {
    if (score >= 90)
      return { grade: "A", color: "text-green-600 bg-green-100" };
    if (score >= 80) return { grade: "B", color: "text-blue-600 bg-blue-100" };
    if (score >= 70)
      return { grade: "C", color: "text-yellow-600 bg-yellow-100" };
    if (score >= 60)
      return { grade: "D", color: "text-orange-600 bg-orange-100" };
    return { grade: "F", color: "text-red-600 bg-red-100" };
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={16}
        className={`${i < Math.floor(rating) ? "text-yellow-400 fill-current" : "text-gray-300"}`}
      />
    ));
  };

  const getPerformanceIndicator = (
    value: number,
    type: "percentage" | "score" | "rating",
  ) => {
    let threshold = 0;
    if (type === "percentage") threshold = 85;
    else if (type === "score") threshold = 80;
    else threshold = 4;

    const isGood = value >= threshold;
    return (
      <div
        className={`flex items-center gap-1 ${isGood ? "text-green-600" : "text-red-600"}`}
      >
        {isGood ? <CheckCircle size={16} /> : <AlertTriangle size={16} />}
        <span className="text-sm font-medium">
          {isGood ? "Excellent" : "Needs Improvement"}
        </span>
      </div>
    );
  };

  useEffect(() => {
    fetchPerformanceData();
  }, [user?.id, tenant?.id, dateRange]);

  if (user?.role !== "driver") {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">
          This dashboard is only available for drivers.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
              <User className="mr-3 h-6 w-6 text-primary-500" />
              Driver Performance Dashboard
            </h2>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Track your performance metrics and improve your driving excellence
            </p>
            {myBus && (
              <div className="mt-2 flex items-center text-sm text-gray-600 dark:text-gray-300">
                <MapPin size={14} className="mr-1" />
                Assigned Bus:{" "}
                <span className="font-medium ml-1">{myBus.plate_number}</span>
              </div>
            )}
          </div>
          {metrics && (
            <div className="text-center">
              <div
                className={`inline-flex items-center px-4 py-2 rounded-full text-lg font-bold ${metrics.gradeColor}`}
              >
                <Award className="mr-2 h-5 w-5" />
                Grade: {metrics.grade}
              </div>
            </div>
          )}
        </div>

        {/* Date Range Selector */}
        <div className="mt-4 flex items-center gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Start Date
            </label>
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) =>
                setDateRange({ ...dateRange, start: e.target.value })
              }
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              End Date
            </label>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) =>
                setDateRange({ ...dateRange, end: e.target.value })
              }
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <Button
            onClick={fetchPerformanceData}
            disabled={loading}
            className="mt-6"
          >
            Update
          </Button>
        </div>
      </div>

      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
        ) : metrics ? (
          <div className="space-y-6">
            {/* Performance Metrics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                      On-Time Performance
                    </p>
                    <p className="text-3xl font-bold text-blue-900 dark:text-blue-100">
                      {metrics.onTimeRate.toFixed(1)}%
                    </p>
                    {getPerformanceIndicator(metrics.onTimeRate, "percentage")}
                  </div>
                  <Clock className="h-8 w-8 text-blue-500" />
                </div>
              </div>

              <div className="p-6 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-600 dark:text-green-400">
                      Safety Score
                    </p>
                    <p className="text-3xl font-bold text-green-900 dark:text-green-100">
                      {metrics.safetyScore.toFixed(1)}
                    </p>
                    {getPerformanceIndicator(metrics.safetyScore, "score")}
                  </div>
                  <Shield className="h-8 w-8 text-green-500" />
                </div>
              </div>

              <div className="p-6 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">
                      Parent Rating
                    </p>
                    <div className="flex items-center gap-2">
                      <p className="text-3xl font-bold text-yellow-900 dark:text-yellow-100">
                        {metrics.parentRating.toFixed(1)}
                      </p>
                      <div className="flex">
                        {renderStars(metrics.parentRating)}
                      </div>
                    </div>
                    {getPerformanceIndicator(metrics.parentRating, "rating")}
                  </div>
                  <Star className="h-8 w-8 text-yellow-500" />
                </div>
              </div>

              <div className="p-6 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-600 dark:text-purple-400">
                      Fuel Efficiency
                    </p>
                    <p className="text-3xl font-bold text-purple-900 dark:text-purple-100">
                      {metrics.fuelEfficiency.toFixed(1)}
                    </p>
                    <p className="text-xs text-purple-600 dark:text-purple-400">
                      km/L
                    </p>
                  </div>
                  <Fuel className="h-8 w-8 text-purple-500" />
                </div>
              </div>
            </div>

            {/* Performance Summary */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <TrendingUp className="mr-2 h-5 w-5 text-primary-500" />
                Performance Summary
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {metrics.totalTrips}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Total Trips Completed
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {performanceData.length}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Active Days
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {(
                      metrics.totalTrips / Math.max(performanceData.length, 1)
                    ).toFixed(1)}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Avg Trips/Day
                  </p>
                </div>
              </div>
            </div>

            {/* Performance Tips */}
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4 flex items-center">
                <Target className="mr-2 h-5 w-5" />
                Performance Tips
              </h3>
              <div className="space-y-3">
                {metrics.onTimeRate < 85 && (
                  <div className="flex items-start gap-3">
                    <Clock className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <p className="font-medium text-blue-900 dark:text-blue-100">
                        Improve On-Time Performance
                      </p>
                      <p className="text-sm text-blue-700 dark:text-blue-200">
                        Plan your routes better and account for traffic
                        conditions. Aim for 90%+ on-time arrivals.
                      </p>
                    </div>
                  </div>
                )}
                {metrics.safetyScore < 80 && (
                  <div className="flex items-start gap-3">
                    <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <p className="font-medium text-blue-900 dark:text-blue-100">
                        Enhance Safety Practices
                      </p>
                      <p className="text-sm text-blue-700 dark:text-blue-200">
                        Follow all safety protocols, maintain safe speeds, and
                        ensure proper vehicle inspections.
                      </p>
                    </div>
                  </div>
                )}
                {metrics.parentRating < 4 && (
                  <div className="flex items-start gap-3">
                    <Star className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <p className="font-medium text-blue-900 dark:text-blue-100">
                        Boost Parent Satisfaction
                      </p>
                      <p className="text-sm text-blue-700 dark:text-blue-200">
                        Maintain friendly communication, ensure student safety,
                        and keep the bus clean and comfortable.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <TrendingUp size={48} className="mx-auto mb-4 text-gray-300" />
            <p className="text-gray-500 dark:text-gray-400">
              No performance data found for the selected period.
            </p>
            <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">
              Complete some trips to see your performance metrics here.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DriverDashboard;
