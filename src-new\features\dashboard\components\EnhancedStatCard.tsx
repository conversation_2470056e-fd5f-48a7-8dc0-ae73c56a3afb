/**
 * مكون بطاقة الإحصائيات المحسن - Enhanced Stat Card Component
 * يدعم المزيد من الخيارات والتخصيصات
 */

import React from 'react';
import { cn } from '../../utils/cn';
import { TrendingUp, TrendingDown } from 'lucide-react';

interface EnhancedStatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
    label?: string;
  };
  color?: "blue" | "green" | "yellow" | "red" | "purple" | "indigo" | "orange" | "pink" | "gray";
  className?: string;
  subtitle?: string;
  size?: "sm" | "md" | "lg";
  showBorder?: boolean;
  animated?: boolean;
  onClick?: () => void;
}

const colorVariants = {
  blue: {
    bg: "bg-blue-50 dark:bg-blue-900/20",
    icon: "text-blue-600 dark:text-blue-400",
    border: "border-blue-200 dark:border-blue-800",
    accent: "text-blue-600 dark:text-blue-400"
  },
  green: {
    bg: "bg-green-50 dark:bg-green-900/20",
    icon: "text-green-600 dark:text-green-400",
    border: "border-green-200 dark:border-green-800",
    accent: "text-green-600 dark:text-green-400"
  },
  yellow: {
    bg: "bg-yellow-50 dark:bg-yellow-900/20",
    icon: "text-yellow-600 dark:text-yellow-400",
    border: "border-yellow-200 dark:border-yellow-800",
    accent: "text-yellow-600 dark:text-yellow-400"
  },
  red: {
    bg: "bg-red-50 dark:bg-red-900/20",
    icon: "text-red-600 dark:text-red-400",
    border: "border-red-200 dark:border-red-800",
    accent: "text-red-600 dark:text-red-400"
  },
  purple: {
    bg: "bg-purple-50 dark:bg-purple-900/20",
    icon: "text-purple-600 dark:text-purple-400",
    border: "border-purple-200 dark:border-purple-800",
    accent: "text-purple-600 dark:text-purple-400"
  },
  indigo: {
    bg: "bg-indigo-50 dark:bg-indigo-900/20",
    icon: "text-indigo-600 dark:text-indigo-400",
    border: "border-indigo-200 dark:border-indigo-800",
    accent: "text-indigo-600 dark:text-indigo-400"
  },
  orange: {
    bg: "bg-orange-50 dark:bg-orange-900/20",
    icon: "text-orange-600 dark:text-orange-400",
    border: "border-orange-200 dark:border-orange-800",
    accent: "text-orange-600 dark:text-orange-400"
  },
  pink: {
    bg: "bg-pink-50 dark:bg-pink-900/20",
    icon: "text-pink-600 dark:text-pink-400",
    border: "border-pink-200 dark:border-pink-800",
    accent: "text-pink-600 dark:text-pink-400"
  },
  gray: {
    bg: "bg-gray-50 dark:bg-gray-700",
    icon: "text-gray-600 dark:text-gray-400",
    border: "border-gray-200 dark:border-gray-600",
    accent: "text-gray-600 dark:text-gray-400"
  }
};

const sizeVariants = {
  sm: {
    container: "p-4",
    title: "text-xs",
    value: "text-lg",
    icon: "p-1.5",
    iconSize: "h-4 w-4"
  },
  md: {
    container: "p-5",
    title: "text-sm",
    value: "text-2xl",
    icon: "p-2",
    iconSize: "h-5 w-5"
  },
  lg: {
    container: "p-6",
    title: "text-base",
    value: "text-3xl",
    icon: "p-3",
    iconSize: "h-6 w-6"
  }
};

export const EnhancedStatCard: React.FC<EnhancedStatCardProps> = ({ 
  title, 
  value, 
  icon, 
  trend, 
  color = "blue",
  className,
  subtitle,
  size = "md",
  showBorder = true,
  animated = true,
  onClick
}) => {
  const colorClasses = colorVariants[color];
  const sizeClasses = sizeVariants[size];

  return (
    <div 
      className={cn(
        'bg-white dark:bg-gray-800 rounded-lg shadow-sm transition-all duration-200',
        showBorder && `border ${colorClasses.border}`,
        animated && 'hover:shadow-md hover:scale-[1.02] transform',
        onClick && 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750',
        sizeClasses.container,
        className
      )}
      onClick={onClick}
    >
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <h3 className={cn(
            'font-medium text-gray-500 dark:text-gray-400 mb-1',
            sizeClasses.title
          )}>
            {title}
          </h3>
          <p className={cn(
            'font-bold text-gray-900 dark:text-white',
            sizeClasses.value
          )}>
            {value}
          </p>
          {subtitle && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {subtitle}
            </p>
          )}
        </div>
        
        <div className={cn(
          'rounded-lg flex-shrink-0',
          colorClasses.bg,
          colorClasses.icon,
          sizeClasses.icon
        )}>
          <div className={sizeClasses.iconSize}>
            {icon}
          </div>
        </div>
      </div>
      
      {trend && (
        <div className="mt-4 flex items-center">
          <div className={cn(
            'inline-flex items-center text-xs font-medium',
            trend.isPositive 
              ? 'text-green-600 dark:text-green-400' 
              : 'text-red-600 dark:text-red-400'
          )}>
            {trend.isPositive ? (
              <TrendingUp className="mr-1 h-3 w-3" />
            ) : (
              <TrendingDown className="mr-1 h-3 w-3" />
            )}
            {typeof trend.value === 'number' ? `${trend.value}%` : trend.value}
          </div>
          {trend.label && (
            <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
              {trend.label}
            </span>
          )}
        </div>
      )}

      {/* شريط تقدم اختياري */}
      {color && animated && (
        <div className="mt-3 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 overflow-hidden">
          <div 
            className={cn(
              'h-1 rounded-full transition-all duration-1000 ease-out',
              colorClasses.accent.replace('text-', 'bg-')
            )}
            style={{ 
              width: trend?.isPositive ? '75%' : '45%',
              animationDelay: '0.5s'
            }}
          />
        </div>
      )}
    </div>
  );
};

// إعادة تصدير StatCard الأصلي للتوافق مع الكود الموجود
export const StatCard = EnhancedStatCard;

export default EnhancedStatCard;
