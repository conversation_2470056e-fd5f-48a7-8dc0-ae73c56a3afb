import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { MapPin, Clock, User, Bus, Calendar, Bell } from "lucide-react";
import { ResponsiveLayout } from "../../components/layout/ResponsiveLayout";
import { LiveMap } from "../../components/map/LiveMap";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/api";

interface ChildInfo {
  id: string;
  name: string;
  grade: string;
  routeName?: string;
  stopName?: string;
  busPlateNumber?: string;
  nextPickupTime?: string;
  lastAttendance?: {
    date: string;
    status: "present" | "absent";
    type: "pickup" | "dropoff";
  };
}

export const ParentDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { students, buses, routes } = useDatabase();
  const [children, setChildren] = useState<ChildInfo[]>([]);
  const [selectedBusId, setSelectedBusId] = useState<string>("");
  const [notifications, setNotifications] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user && students.length > 0) {
      fetchChildrenInfo();
      fetchNotifications();
    }
  }, [user, students, buses, routes]);

  const fetchChildrenInfo = async () => {
    try {
      setIsLoading(true);

      // Get children for this parent
      const parentChildren = students.filter(
        (student) => student.parent_id === user?.id,
      );

      const childrenInfo: ChildInfo[] = await Promise.all(
        parentChildren.map(async (child) => {
          // Find route and stop information
          let routeName, stopName, busPlateNumber, nextPickupTime;

          if (child.route_stop_id) {
            for (const route of routes) {
              const stop = route.stops?.find(
                (s) => s.id === child.route_stop_id,
              );
              if (stop) {
                routeName = route.name;
                stopName = stop.name;
                nextPickupTime = stop.arrival_time;

                // Find bus for this route
                const bus = buses.find((b) => b.id === route.bus_id);
                if (bus) {
                  busPlateNumber = bus.plate_number;
                  if (!selectedBusId) {
                    setSelectedBusId(bus.id);
                  }
                }
                break;
              }
            }
          }

          // Get last attendance record
          const { data: attendanceData } = await supabase
            .from("attendance")
            .select("*")
            .eq("student_id", child.id)
            .order("recorded_at", { ascending: false })
            .limit(1);

          let lastAttendance;
          if (attendanceData && attendanceData.length > 0) {
            const record = attendanceData[0];
            lastAttendance = {
              date: new Date(record.recorded_at).toLocaleDateString(),
              status: "present" as "present" | "absent",
              type: record.type as "pickup" | "dropoff",
            };
          }

          return {
            id: child.id,
            name: child.name,
            grade: child.grade,
            routeName,
            stopName,
            busPlateNumber,
            nextPickupTime,
            lastAttendance,
          };
        }),
      );

      setChildren(childrenInfo);
    } catch (error) {
      console.error("Error fetching children info:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchNotifications = async () => {
    try {
      const { data, error } = await supabase
        .from("notifications")
        .select("*")
        .eq("user_id", user?.id)
        .eq("read", false)
        .order("created_at", { ascending: false })
        .limit(5);

      if (error) throw error;

      setNotifications(data || []);
    } catch (error) {
      console.error("Error fetching notifications:", error);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-pulse">
          <div className="bg-primary-500 h-12 w-12 rounded-xl flex items-center justify-center shadow-lg">
            <Bus className="text-white" size={30} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <ResponsiveLayout>
      <div className="p-4 md:p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t("dashboard.welcome")}, {user?.name}
            </h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {t("dashboard.parentOverview")}
            </p>
          </div>

          {/* Children Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            {children.map((child) => (
              <div
                key={child.id}
                className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <div className="h-10 w-10 bg-primary-100 dark:bg-primary-800/20 rounded-full flex items-center justify-center text-primary-600 dark:text-primary-400 mr-3">
                      <User size={20} />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {child.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {t("students.grade")} {child.grade}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  {child.routeName && (
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                      <MapPin size={16} className="mr-2 text-gray-400" />
                      <span>
                        {child.routeName} - {child.stopName}
                      </span>
                    </div>
                  )}

                  {child.busPlateNumber && (
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                      <Bus size={16} className="mr-2 text-gray-400" />
                      <span>
                        {t("buses.plateNumber")}: {child.busPlateNumber}
                      </span>
                    </div>
                  )}

                  {child.nextPickupTime && (
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                      <Clock size={16} className="mr-2 text-gray-400" />
                      <span>
                        {t("tracking.nextPickup")}: {child.nextPickupTime}
                      </span>
                    </div>
                  )}

                  {child.lastAttendance && (
                    <div className="flex items-center text-sm">
                      <Calendar size={16} className="mr-2 text-gray-400" />
                      <span className="text-gray-600 dark:text-gray-300 mr-2">
                        {t("students.lastAttendance")}:
                      </span>
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          child.lastAttendance.status === "present"
                            ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                            : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                        }`}
                      >
                        {child.lastAttendance.status === "present"
                          ? t("students.present")
                          : t("students.absent")}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Live Tracking Map */}
            <div className="lg:col-span-2">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                    <MapPin size={20} className="mr-2" />
                    {t("tracking.liveTracking")}
                  </h2>
                </div>
                <div className="h-96">
                  <LiveMap
                    selectedBusId={selectedBusId}
                    onBusSelect={(bus) => setSelectedBusId(bus.id)}
                  />
                </div>
              </div>
            </div>

            {/* Notifications and Quick Info */}
            <div className="space-y-6">
              {/* Recent Notifications */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                    <Bell size={20} className="mr-2" />
                    {t("notifications.new")}
                  </h2>
                </div>
                <div className="p-4">
                  {notifications.length > 0 ? (
                    <div className="space-y-3">
                      {notifications.map((notification) => (
                        <div
                          key={notification.id}
                          className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md"
                        >
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {notification.title}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {notification.message}
                          </p>
                          <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                            {new Date(notification.created_at).toLocaleString()}
                          </p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {t("notifications.empty")}
                    </p>
                  )}
                </div>
              </div>

              {/* Quick Stats */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {t("dashboard.quickStats")}
                  </h2>
                </div>
                <div className="p-4 space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      {t("dashboard.totalChildren")}
                    </span>
                    <span className="text-lg font-semibold text-gray-900 dark:text-white">
                      {children.length}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      {t("dashboard.activeRoutes")}
                    </span>
                    <span className="text-lg font-semibold text-gray-900 dark:text-white">
                      {
                        new Set(
                          children.map((c) => c.routeName).filter(Boolean),
                        ).size
                      }
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      {t("notifications.unread")}
                    </span>
                    <span className="text-lg font-semibold text-gray-900 dark:text-white">
                      {notifications.length}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ResponsiveLayout>
  );
};
