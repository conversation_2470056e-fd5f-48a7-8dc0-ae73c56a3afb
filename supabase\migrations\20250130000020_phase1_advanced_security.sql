-- المرحلة 1: جداول الأمان المتقدمة
-- Phase 1: Advanced Security Tables

-- جدول إعدادات التحقق الثنائي
-- Two-factor authentication configuration table
CREATE TABLE IF NOT EXISTS user_2fa_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    secret_key TEXT NOT NULL,
    backup_codes TEXT[] NOT NULL DEFAULT '{}',
    is_enabled BOOLEAN NOT NULL DEFAULT false,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- فهارس لجدول التحقق الثنائي
CREATE INDEX IF NOT EXISTS idx_user_2fa_config_user_id ON user_2fa_config(user_id);
CREATE INDEX IF NOT EXISTS idx_user_2fa_config_enabled ON user_2fa_config(is_enabled);

-- جدول رموز التحقق عبر البريد الإلكتروني
-- Email verification codes table
CREATE TABLE IF NOT EXISTS email_verification_codes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    code VARCHAR(6) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    attempts INTEGER NOT NULL DEFAULT 0,
    is_used BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس لجدول رموز التحقق
CREATE INDEX IF NOT EXISTS idx_email_verification_codes_user_id ON email_verification_codes(user_id);
CREATE INDEX IF NOT EXISTS idx_email_verification_codes_code ON email_verification_codes(code);
CREATE INDEX IF NOT EXISTS idx_email_verification_codes_expires ON email_verification_codes(expires_at);

-- جدول أنماط سلوك المستخدمين
-- User behavior patterns table
CREATE TABLE IF NOT EXISTS user_behavior_patterns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    typical_login_hours INTEGER[] NOT NULL DEFAULT '{}',
    typical_locations TEXT[] NOT NULL DEFAULT '{}',
    typical_devices TEXT[] NOT NULL DEFAULT '{}',
    average_session_duration INTEGER NOT NULL DEFAULT 3600,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- فهارس لجدول أنماط السلوك
CREATE INDEX IF NOT EXISTS idx_user_behavior_patterns_user_id ON user_behavior_patterns(user_id);
CREATE INDEX IF NOT EXISTS idx_user_behavior_patterns_updated ON user_behavior_patterns(last_updated);

-- جدول تنبيهات الشذوذ
-- Anomaly alerts table
CREATE TABLE IF NOT EXISTS anomaly_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    anomaly_type VARCHAR(50) NOT NULL CHECK (anomaly_type IN ('unusual_location', 'unusual_time', 'unusual_device', 'suspicious_activity')),
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high')),
    description TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    is_resolved BOOLEAN NOT NULL DEFAULT false,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهارس لجدول تنبيهات الشذوذ
CREATE INDEX IF NOT EXISTS idx_anomaly_alerts_user_id ON anomaly_alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_anomaly_alerts_type ON anomaly_alerts(anomaly_type);
CREATE INDEX IF NOT EXISTS idx_anomaly_alerts_severity ON anomaly_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_anomaly_alerts_resolved ON anomaly_alerts(is_resolved);
CREATE INDEX IF NOT EXISTS idx_anomaly_alerts_created_at ON anomaly_alerts(created_at);

-- تحديث جدول جلسات المستخدمين لإضافة معلومات إضافية
-- Update user sessions table with additional information
ALTER TABLE user_sessions 
ADD COLUMN IF NOT EXISTS device_info JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS location JSONB DEFAULT '{}';

-- دالة تنظيف رموز التحقق المنتهية الصلاحية
-- Function to cleanup expired verification codes
CREATE OR REPLACE FUNCTION cleanup_expired_verification_codes()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- حذف رموز التحقق المنتهية الصلاحية
    DELETE FROM email_verification_codes 
    WHERE expires_at < NOW() OR is_used = true;
    
    RAISE NOTICE 'Expired verification codes cleaned up';
END;
$$;

-- دالة فحص التحقق الثنائي للمستخدم
-- Function to check if 2FA is required for user
CREATE OR REPLACE FUNCTION is_2fa_required(user_id_input UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_role TEXT;
    is_enabled BOOLEAN;
BEGIN
    -- الحصول على دور المستخدم
    SELECT role INTO user_role
    FROM users
    WHERE id = user_id_input;
    
    -- فحص إذا كان التحقق الثنائي مفعل
    SELECT COALESCE(is_enabled, false) INTO is_enabled
    FROM user_2fa_config
    WHERE user_id = user_id_input;
    
    -- التحقق الثنائي مطلوب للأدمن والمديرين أو إذا كان مفعل
    RETURN is_enabled OR user_role IN ('admin', 'school_manager');
END;
$$;

-- دالة تحليل أنماط سلوك المستخدم
-- Function to analyze user behavior patterns
CREATE OR REPLACE FUNCTION analyze_user_behavior(
    user_id_input UUID,
    login_hour INTEGER,
    location_info TEXT,
    device_signature TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    pattern_record user_behavior_patterns%ROWTYPE;
    anomalies JSONB := '[]'::JSONB;
    risk_score INTEGER := 0;
BEGIN
    -- الحصول على نمط سلوك المستخدم
    SELECT * INTO pattern_record
    FROM user_behavior_patterns
    WHERE user_id = user_id_input;
    
    -- إذا لم يوجد نمط، إنشاء نمط جديد
    IF NOT FOUND THEN
        INSERT INTO user_behavior_patterns (
            user_id,
            typical_login_hours,
            typical_locations,
            typical_devices
        ) VALUES (
            user_id_input,
            ARRAY[login_hour],
            ARRAY[location_info],
            ARRAY[device_signature]
        );
        
        RETURN jsonb_build_object(
            'is_anomalous', false,
            'risk_score', 0,
            'anomalies', '[]'::JSONB
        );
    END IF;
    
    -- فحص شذوذ التوقيت
    IF NOT (login_hour = ANY(pattern_record.typical_login_hours)) THEN
        anomalies := anomalies || jsonb_build_object(
            'type', 'unusual_time',
            'description', 'تسجيل دخول في توقيت غير مألوف',
            'hour', login_hour
        );
        risk_score := risk_score + 20;
    END IF;
    
    -- فحص شذوذ الموقع
    IF NOT (location_info = ANY(pattern_record.typical_locations)) THEN
        anomalies := anomalies || jsonb_build_object(
            'type', 'unusual_location',
            'description', 'تسجيل دخول من موقع غير مألوف',
            'location', location_info
        );
        risk_score := risk_score + 30;
    END IF;
    
    -- فحص شذوذ الجهاز
    IF NOT (device_signature = ANY(pattern_record.typical_devices)) THEN
        anomalies := anomalies || jsonb_build_object(
            'type', 'unusual_device',
            'description', 'تسجيل دخول من جهاز غير مألوف',
            'device', device_signature
        );
        risk_score := risk_score + 25;
    END IF;
    
    -- تحديث نمط السلوك
    UPDATE user_behavior_patterns SET
        typical_login_hours = array_append(
            CASE WHEN array_length(typical_login_hours, 1) >= 10 
                 THEN typical_login_hours[2:] 
                 ELSE typical_login_hours 
            END, 
            login_hour
        ),
        typical_locations = array_append(
            CASE WHEN array_length(typical_locations, 1) >= 5 
                 THEN typical_locations[2:] 
                 ELSE typical_locations 
            END, 
            location_info
        ),
        typical_devices = array_append(
            CASE WHEN array_length(typical_devices, 1) >= 5 
                 THEN typical_devices[2:] 
                 ELSE typical_devices 
            END, 
            device_signature
        ),
        last_updated = NOW()
    WHERE user_id = user_id_input;
    
    RETURN jsonb_build_object(
        'is_anomalous', risk_score >= 50,
        'risk_score', LEAST(risk_score, 100),
        'anomalies', anomalies
    );
END;
$$;

-- دالة إنشاء تنبيه شذوذ
-- Function to create anomaly alert
CREATE OR REPLACE FUNCTION create_anomaly_alert(
    user_id_input UUID,
    anomaly_type_input TEXT,
    severity_input TEXT,
    description_input TEXT,
    metadata_input JSONB DEFAULT '{}'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    alert_id UUID;
BEGIN
    INSERT INTO anomaly_alerts (
        user_id,
        anomaly_type,
        severity,
        description,
        metadata
    ) VALUES (
        user_id_input,
        anomaly_type_input,
        severity_input,
        description_input,
        metadata_input
    ) RETURNING id INTO alert_id;
    
    RETURN alert_id;
END;
$$;

-- دالة الحصول على إحصائيات الجلسات
-- Function to get session statistics
CREATE OR REPLACE FUNCTION get_session_statistics()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    stats JSONB;
    active_sessions INTEGER;
    unique_users INTEGER;
    avg_duration INTERVAL;
BEGIN
    -- عد الجلسات النشطة
    SELECT COUNT(*) INTO active_sessions
    FROM user_sessions
    WHERE is_active = true AND expires_at > NOW();
    
    -- عد المستخدمين الفريدين
    SELECT COUNT(DISTINCT user_id) INTO unique_users
    FROM user_sessions
    WHERE is_active = true AND expires_at > NOW();
    
    -- حساب متوسط مدة الجلسة
    SELECT AVG(NOW() - created_at) INTO avg_duration
    FROM user_sessions
    WHERE is_active = true AND expires_at > NOW();
    
    stats := jsonb_build_object(
        'active_sessions', active_sessions,
        'unique_users', unique_users,
        'average_duration_minutes', EXTRACT(EPOCH FROM COALESCE(avg_duration, INTERVAL '0')) / 60
    );
    
    RETURN stats;
END;
$$;

-- تفعيل RLS على الجداول الجديدة
ALTER TABLE user_2fa_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_verification_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_behavior_patterns ENABLE ROW LEVEL SECURITY;
ALTER TABLE anomaly_alerts ENABLE ROW LEVEL SECURITY;

-- سياسات RLS للتحقق الثنائي
CREATE POLICY "Users can manage own 2FA config" ON user_2fa_config
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Admin can view all 2FA configs" ON user_2fa_config
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

-- سياسات RLS لرموز التحقق
CREATE POLICY "Users can manage own verification codes" ON email_verification_codes
    FOR ALL USING (user_id = auth.uid());

-- سياسات RLS لأنماط السلوك
CREATE POLICY "Users can view own behavior patterns" ON user_behavior_patterns
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admin can view all behavior patterns" ON user_behavior_patterns
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

-- سياسات RLS لتنبيهات الشذوذ
CREATE POLICY "Users can view own anomaly alerts" ON anomaly_alerts
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admin can manage all anomaly alerts" ON anomaly_alerts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

-- إنشاء مهام تنظيف دورية
-- Create periodic cleanup jobs
-- SELECT cron.schedule('cleanup-verification-codes', '*/15 * * * *', 'SELECT cleanup_expired_verification_codes();');

COMMENT ON TABLE user_2fa_config IS 'جدول إعدادات التحقق الثنائي للمستخدمين';
COMMENT ON TABLE email_verification_codes IS 'جدول رموز التحقق المرسلة عبر البريد الإلكتروني';
COMMENT ON TABLE user_behavior_patterns IS 'جدول أنماط سلوك المستخدمين لكشف الشذوذ';
COMMENT ON TABLE anomaly_alerts IS 'جدول تنبيهات السلوك الشاذ والأنشطة المشبوهة';
