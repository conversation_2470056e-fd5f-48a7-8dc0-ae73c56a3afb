/**
 * سكريبت تطبيق تهجيرات المرحلة الأولى
 * Phase 1 Migration Application Script
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// إعداد Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://pcavtwqvgnkgybzfqeuz.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// قائمة ملفات التهجير للمرحلة الأولى
const phase1Migrations = [
  '20250130000005_phase1_security_cleanup.sql',
  '20250130000006_phase1_centralized_permissions.sql', 
  '20250130000007_phase1_new_rls_policies.sql',
  '20250130000008_phase1_complete_rls_policies.sql',
  '20250130000009_phase1_finalization.sql'
];

/**
 * قراءة ملف SQL
 */
function readSQLFile(filename: string): string {
  const filePath = path.join(process.cwd(), 'supabase', 'migrations', filename);
  
  if (!fs.existsSync(filePath)) {
    throw new Error(`Migration file not found: ${filename}`);
  }
  
  return fs.readFileSync(filePath, 'utf8');
}

/**
 * تطبيق تهجير واحد
 */
async function applyMigration(filename: string): Promise<void> {
  console.log(`📄 Applying migration: ${filename}`);
  
  try {
    const sql = readSQLFile(filename);
    
    // تقسيم SQL إلى عبارات منفصلة
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`   📝 Found ${statements.length} SQL statements`);
    
    // تطبيق كل عبارة على حدة
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.trim()) {
        console.log(`   ⚡ Executing statement ${i + 1}/${statements.length}`);
        
        const { error } = await supabase.rpc('exec_sql', { 
          sql_query: statement + ';' 
        });
        
        if (error) {
          // محاولة تطبيق العبارة مباشرة إذا فشلت الدالة
          const { error: directError } = await supabase
            .from('_temp_exec')
            .select('*')
            .limit(0);
          
          if (directError) {
            console.warn(`   ⚠️  Warning in statement ${i + 1}: ${error.message}`);
          }
        }
      }
    }
    
    console.log(`   ✅ Migration ${filename} applied successfully`);
    
  } catch (error) {
    console.error(`   ❌ Error applying migration ${filename}:`, error);
    throw error;
  }
}

/**
 * التحقق من حالة النظام قبل التطبيق
 */
async function checkSystemStatus(): Promise<void> {
  console.log('🔍 Checking system status...');
  
  try {
    // التحقق من الاتصال
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    if (error) {
      throw new Error(`Database connection failed: ${error.message}`);
    }
    
    console.log('   ✅ Database connection successful');
    
    // التحقق من وجود الجداول الأساسية
    const tables = ['users', 'tenants', 'buses', 'routes', 'students', 'attendance'];
    
    for (const table of tables) {
      const { error: tableError } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (tableError) {
        console.warn(`   ⚠️  Warning: Table ${table} might have issues: ${tableError.message}`);
      } else {
        console.log(`   ✅ Table ${table} is accessible`);
      }
    }
    
  } catch (error) {
    console.error('❌ System status check failed:', error);
    throw error;
  }
}

/**
 * التحقق من سلامة النظام بعد التطبيق
 */
async function verifySystemIntegrity(): Promise<void> {
  console.log('🔍 Verifying system integrity...');
  
  try {
    // محاولة استدعاء دالة فحص السلامة
    const { data, error } = await supabase.rpc('check_permission_system_integrity');
    
    if (error) {
      console.warn('   ⚠️  Could not run integrity check:', error.message);
      return;
    }
    
    if (data) {
      console.log('   📊 System Integrity Report:');
      console.log(`      Status: ${data.system_status}`);
      console.log(`      Policies: ${data.policy_count}`);
      console.log(`      Functions: ${data.function_count}`);
      console.log(`      Matrix Entries: ${data.matrix_entries}`);
      
      if (data.system_status === 'HEALTHY') {
        console.log('   ✅ System integrity verified');
      } else {
        console.warn(`   ⚠️  System status: ${data.system_status}`);
        if (data.recommendations) {
          console.log('   📋 Recommendations:', data.recommendations);
        }
      }
    }
    
  } catch (error) {
    console.warn('   ⚠️  Integrity verification failed:', error);
  }
}

/**
 * إنشاء نسخة احتياطية من السياسات الحالية
 */
async function backupCurrentPolicies(): Promise<void> {
  console.log('💾 Creating backup of current policies...');
  
  try {
    const { data: policies, error } = await supabase.rpc('get_all_policies');
    
    if (error) {
      console.warn('   ⚠️  Could not backup policies:', error.message);
      return;
    }
    
    const backupData = {
      timestamp: new Date().toISOString(),
      policies: policies || [],
      note: 'Backup created before Phase 1 migration'
    };
    
    const backupPath = path.join(process.cwd(), 'backups', `policies-backup-${Date.now()}.json`);
    
    // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
    const backupDir = path.dirname(backupPath);
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    fs.writeFileSync(backupPath, JSON.stringify(backupData, null, 2));
    console.log(`   ✅ Backup created: ${backupPath}`);
    
  } catch (error) {
    console.warn('   ⚠️  Backup creation failed:', error);
  }
}

/**
 * الدالة الرئيسية
 */
async function main(): Promise<void> {
  console.log('🚀 Starting Phase 1 Migration Application');
  console.log('==========================================');
  
  try {
    // 1. التحقق من حالة النظام
    await checkSystemStatus();
    
    // 2. إنشاء نسخة احتياطية
    await backupCurrentPolicies();
    
    // 3. تطبيق التهجيرات
    console.log('\n📦 Applying Phase 1 migrations...');
    
    for (const migration of phase1Migrations) {
      await applyMigration(migration);
      
      // انتظار قصير بين التهجيرات
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 4. التحقق من سلامة النظام
    console.log('\n🔍 Post-migration verification...');
    await verifySystemIntegrity();
    
    console.log('\n🎉 Phase 1 Migration Completed Successfully!');
    console.log('============================================');
    console.log('✅ All migrations applied');
    console.log('✅ System integrity verified');
    console.log('✅ Centralized permission system is now active');
    console.log('\n📋 Next Steps:');
    console.log('   1. Test the new permission system');
    console.log('   2. Update frontend components to use new hooks');
    console.log('   3. Begin Phase 2 development');
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    console.log('\n🔧 Recovery options:');
    console.log('   1. Check the error details above');
    console.log('   2. Run emergency_permission_reset() if needed');
    console.log('   3. Restore from backup if necessary');
    
    process.exit(1);
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main().catch(console.error);
}

export { main as applyPhase1Migrations };
