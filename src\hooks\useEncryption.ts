import { useState, useEffect, useCallback } from "react";
import {
  generateKey,
  export<PERSON>ey,
  importKey,
  encryptSensitiveFields,
  decryptSensitiveFields,
} from "../utils/encryption";

/**
 * Hook for managing encryption keys and encrypting/decrypting sensitive data
 */
export const useEncryption = () => {
  const [encryptionKey, setEncryptionKey] = useState<CryptoKey | null>(null);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize encryption key
  useEffect(() => {
    const initializeKey = async () => {
      try {
        // Try to load existing key from localStorage
        const storedKey = localStorage.getItem("encryption_key");

        let key: CryptoKey;
        if (storedKey) {
          key = await importKey(storedKey);
        } else {
          // Generate new key and store it
          key = await generateKey();
          const exportedKey = await exportKey(key);
          localStorage.setItem("encryption_key", exportedKey);
        }

        setEncryptionKey(key);
        setIsReady(true);
      } catch (err) {
        setError("Failed to initialize encryption key");
        console.error("Encryption initialization error:", err);
      }
    };

    initializeKey();
  }, []);

  // Encrypt sensitive fields in an object
  const encryptObject = useCallback(
    async (obj: Record<string, any>, sensitiveFields: string[]) => {
      if (!encryptionKey) {
        throw new Error("Encryption key not ready");
      }

      return await encryptSensitiveFields(obj, sensitiveFields, encryptionKey);
    },
    [encryptionKey],
  );

  // Decrypt sensitive fields in an object
  const decryptObject = useCallback(
    async (obj: Record<string, any>, sensitiveFields: string[]) => {
      if (!encryptionKey) {
        throw new Error("Encryption key not ready");
      }

      return await decryptSensitiveFields(obj, sensitiveFields, encryptionKey);
    },
    [encryptionKey],
  );

  // Reset encryption key (generate new one)
  const resetKey = useCallback(async () => {
    try {
      const newKey = await generateKey();
      const exportedKey = await exportKey(newKey);
      localStorage.setItem("encryption_key", exportedKey);
      setEncryptionKey(newKey);
      setError(null);
    } catch (err) {
      setError("Failed to reset encryption key");
      console.error("Key reset error:", err);
    }
  }, []);

  return {
    isReady,
    error,
    encryptObject,
    decryptObject,
    resetKey,
  };
};

/**
 * Hook for encrypting sensitive data before sending to API
 */
export const useSecureData = () => {
  const { isReady, encryptObject, decryptObject } = useEncryption();

  // Define which fields should be encrypted for different data types
  const sensitiveFields = {
    user: ["email", "phone_number", "address"],
    student: ["parent_contact", "emergency_contact", "medical_info"],
    bus: ["license_plate", "insurance_number"],
    tenant: ["contact_number", "address", "domain"],
  };

  const encryptUserData = useCallback(
    (userData: Record<string, any>) => {
      return encryptObject(userData, sensitiveFields.user);
    },
    [encryptObject],
  );

  const decryptUserData = useCallback(
    (userData: Record<string, any>) => {
      return decryptObject(userData, sensitiveFields.user);
    },
    [decryptObject],
  );

  const encryptStudentData = useCallback(
    (studentData: Record<string, any>) => {
      return encryptObject(studentData, sensitiveFields.student);
    },
    [encryptObject],
  );

  const decryptStudentData = useCallback(
    (studentData: Record<string, any>) => {
      return decryptObject(studentData, sensitiveFields.student);
    },
    [decryptObject],
  );

  const encryptBusData = useCallback(
    (busData: Record<string, any>) => {
      return encryptObject(busData, sensitiveFields.bus);
    },
    [encryptObject],
  );

  const decryptBusData = useCallback(
    (busData: Record<string, any>) => {
      return decryptObject(busData, sensitiveFields.bus);
    },
    [decryptObject],
  );

  const encryptTenantData = useCallback(
    (tenantData: Record<string, any>) => {
      return encryptObject(tenantData, sensitiveFields.tenant);
    },
    [encryptObject],
  );

  const decryptTenantData = useCallback(
    (tenantData: Record<string, any>) => {
      return decryptObject(tenantData, sensitiveFields.tenant);
    },
    [decryptObject],
  );

  return {
    isReady,
    encryptUserData,
    decryptUserData,
    encryptStudentData,
    decryptStudentData,
    encryptBusData,
    decryptBusData,
    encryptTenantData,
    decryptTenantData,
  };
};
