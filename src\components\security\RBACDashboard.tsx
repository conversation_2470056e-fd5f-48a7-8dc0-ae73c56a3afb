import React, { useState, useEffect } from "react";
import {
  Shield,
  Users,
  Key,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  Database,
} from "lucide-react";
import { Button } from "../ui/Button";
import {
  RBACSecurityAuditSystem,
  RBACSecurityAudit,
} from "../../lib/rbacAuditSystem";
import { RBACDiagramGenerator } from "../../lib/rbacDiagramGenerator";
import { UserRole } from "../../types";

interface RBACDashboardProps {
  className?: string;
}

export const RBACDashboard: React.FC<RBACDashboardProps> = ({
  className = "",
}) => {
  const [audit, setAudit] = useState<RBACSecurityAudit | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<
    "overview" | "roles" | "vulnerabilities" | "compliance"
  >("overview");

  useEffect(() => {
    // Load latest audit on component mount
    const latestAudit = RBACSecurityAuditSystem.getLatestAudit();
    if (latestAudit) {
      setAudit(latestAudit);
    }
  }, []);

  const runAudit = async () => {
    setLoading(true);
    try {
      const newAudit = RBACSecurityAuditSystem.generateComprehensiveAudit();
      setAudit(newAudit);
    } catch (error) {
      console.error("Failed to run RBAC audit:", error);
    } finally {
      setLoading(false);
    }
  };

  const exportAudit = () => {
    if (!audit) return;

    const auditJson = RBACSecurityAuditSystem.exportAuditReport(audit);
    const blob = new Blob([auditJson], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `rbac-audit-${audit.auditId}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportDiagram = () => {
    const mermaidDiagram = RBACDiagramGenerator.generateMermaidDiagram();
    const blob = new Blob([mermaidDiagram], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "rbac-diagram.mmd";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case "CRITICAL":
        return "text-red-600 bg-red-100";
      case "HIGH":
        return "text-orange-600 bg-orange-100";
      case "MEDIUM":
        return "text-yellow-600 bg-yellow-100";
      case "LOW":
        return "text-green-600 bg-green-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 80) return "text-yellow-600";
    if (score >= 70) return "text-orange-600";
    return "text-red-600";
  };

  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Shield className="h-8 w-8 text-blue-600" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              RBAC Security Dashboard
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Role-Based Access Control Monitoring & Audit
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button
            onClick={runAudit}
            disabled={loading}
            className="flex items-center space-x-2"
          >
            <Shield size={16} />
            <span>{loading ? "Running Audit..." : "Run Security Audit"}</span>
          </Button>
          {audit && (
            <>
              <Button
                onClick={exportAudit}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <Database size={16} />
                <span>Export Audit</span>
              </Button>
              <Button
                onClick={exportDiagram}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <TrendingUp size={16} />
                <span>Export Diagram</span>
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">
            Running comprehensive security audit...
          </span>
        </div>
      )}

      {/* No Audit State */}
      {!loading && !audit && (
        <div className="text-center py-12">
          <Shield className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No Security Audit Available
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Run a comprehensive RBAC security audit to analyze your system's
            access controls.
          </p>
          <Button
            onClick={runAudit}
            className="flex items-center space-x-2 mx-auto"
          >
            <Shield size={16} />
            <span>Run First Audit</span>
          </Button>
        </div>
      )}

      {/* Audit Results */}
      {!loading && audit && (
        <>
          {/* Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">Overall Score</p>
                  <p className="text-2xl font-bold">{audit.overallScore}/100</p>
                </div>
                <Shield className="h-8 w-8 text-blue-200" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm">Total Roles</p>
                  <p className="text-2xl font-bold">
                    {audit.roleAudits.length}
                  </p>
                </div>
                <Users className="h-8 w-8 text-green-200" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm">Vulnerabilities</p>
                  <p className="text-2xl font-bold">
                    {audit.vulnerabilities.length}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-purple-200" />
              </div>
            </div>

            <div
              className={`rounded-lg p-4 text-white ${
                audit.riskLevel === "LOW"
                  ? "bg-gradient-to-r from-green-500 to-green-600"
                  : audit.riskLevel === "MEDIUM"
                    ? "bg-gradient-to-r from-yellow-500 to-yellow-600"
                    : audit.riskLevel === "HIGH"
                      ? "bg-gradient-to-r from-orange-500 to-orange-600"
                      : "bg-gradient-to-r from-red-500 to-red-600"
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/80 text-sm">Risk Level</p>
                  <p className="text-xl font-bold">{audit.riskLevel}</p>
                </div>
                <Key className="h-8 w-8 text-white/80" />
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: "overview", label: "Overview", icon: Shield },
                { id: "roles", label: "Role Analysis", icon: Users },
                {
                  id: "vulnerabilities",
                  label: "Vulnerabilities",
                  icon: AlertTriangle,
                },
                { id: "compliance", label: "Compliance", icon: CheckCircle },
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? "border-blue-500 text-blue-600"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                    }`}
                  >
                    <Icon size={16} />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          {activeTab === "overview" && (
            <div className="space-y-6">
              {/* Audit Summary */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                  Audit Summary
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Audit ID
                    </p>
                    <p className="font-mono text-sm">{audit.auditId}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Timestamp
                    </p>
                    <p className="text-sm">
                      {new Date(audit.timestamp).toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Version
                    </p>
                    <p className="text-sm">{audit.version}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Performance
                    </p>
                    <p className="text-sm">
                      {audit.performanceMetrics.averagePermissionCheckTime}ms
                      avg
                    </p>
                  </div>
                </div>
              </div>

              {/* Recommendations */}
              {audit.recommendations.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                    Top Recommendations
                  </h3>
                  <div className="space-y-3">
                    {audit.recommendations.slice(0, 3).map((rec) => (
                      <div
                        key={rec.id}
                        className="border border-gray-200 dark:border-gray-600 rounded-lg p-4"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <span
                                className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  rec.priority === "CRITICAL"
                                    ? "bg-red-100 text-red-800"
                                    : rec.priority === "HIGH"
                                      ? "bg-orange-100 text-orange-800"
                                      : rec.priority === "MEDIUM"
                                        ? "bg-yellow-100 text-yellow-800"
                                        : "bg-green-100 text-green-800"
                                }`}
                              >
                                {rec.priority}
                              </span>
                              <span className="text-xs text-gray-500">
                                {rec.category}
                              </span>
                            </div>
                            <h4 className="font-medium text-gray-900 dark:text-white">
                              {rec.title}
                            </h4>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                              {rec.description}
                            </p>
                          </div>
                          <div className="text-right text-sm text-gray-500">
                            <p>Impact: {rec.securityImpact}/10</p>
                            <p>Effort: {rec.estimatedEffort}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === "roles" && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Role Security Analysis
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {audit.roleAudits.map((roleAudit) => (
                  <div
                    key={roleAudit.role}
                    className="border border-gray-200 dark:border-gray-600 rounded-lg p-4"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {roleAudit.role.replace("_", " ").toUpperCase()}
                      </h4>
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(roleAudit.riskLevel)}`}
                      >
                        {roleAudit.riskLevel}
                      </span>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Security Score
                        </span>
                        <span
                          className={`font-medium ${getScoreColor(roleAudit.securityScore)}`}
                        >
                          {roleAudit.securityScore}/100
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Permissions
                        </span>
                        <span className="font-medium">
                          {roleAudit.permissionCount}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          High Risk
                        </span>
                        <span className="font-medium text-red-600">
                          {roleAudit.permissionBreakdown.highRisk}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Medium Risk
                        </span>
                        <span className="font-medium text-yellow-600">
                          {roleAudit.permissionBreakdown.mediumRisk}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Low Risk
                        </span>
                        <span className="font-medium text-green-600">
                          {roleAudit.permissionBreakdown.lowRisk}
                        </span>
                      </div>
                    </div>

                    {roleAudit.vulnerabilities.length > 0 && (
                      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                        <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">
                          Issues:
                        </p>
                        <ul className="text-xs text-red-600 space-y-1">
                          {roleAudit.vulnerabilities
                            .slice(0, 2)
                            .map((vuln, index) => (
                              <li key={index}>• {vuln}</li>
                            ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === "vulnerabilities" && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Security Vulnerabilities
              </h3>
              {audit.vulnerabilities.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No Vulnerabilities Found
                  </h4>
                  <p className="text-gray-600 dark:text-gray-400">
                    Your RBAC system appears to be secure with no identified
                    vulnerabilities.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {audit.vulnerabilities.map((vuln) => (
                    <div
                      key={vuln.id}
                      className="border border-gray-200 dark:border-gray-600 rounded-lg p-4"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <span
                            className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(vuln.severity)}`}
                          >
                            {vuln.severity}
                          </span>
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {vuln.title}
                          </h4>
                        </div>
                        {vuln.cveScore && (
                          <span className="text-sm text-gray-500">
                            CVE: {vuln.cveScore}
                          </span>
                        )}
                      </div>

                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        {vuln.description}
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white mb-1">
                            Impact:
                          </p>
                          <p className="text-gray-600 dark:text-gray-400">
                            {vuln.impact}
                          </p>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white mb-1">
                            Remediation:
                          </p>
                          <p className="text-gray-600 dark:text-gray-400">
                            {vuln.remediation}
                          </p>
                        </div>
                      </div>

                      {vuln.affectedRoles.length > 0 && (
                        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                          <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                            Affected Roles:
                          </p>
                          <div className="flex flex-wrap gap-2">
                            {vuln.affectedRoles.map((role) => (
                              <span
                                key={role}
                                className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs"
                              >
                                {role}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === "compliance" && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Compliance Status
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Object.entries(audit.complianceStatus).map(
                  ([standard, status]) => (
                    <div
                      key={standard}
                      className="border border-gray-200 dark:border-gray-600 rounded-lg p-4"
                    >
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {standard.toUpperCase()}
                        </h4>
                        <div className="flex items-center space-x-2">
                          {status.compliant ? (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          ) : (
                            <XCircle className="h-5 w-5 text-red-500" />
                          )}
                          <span
                            className={`text-sm font-medium ${
                              status.compliant
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {status.compliant ? "Compliant" : "Non-Compliant"}
                          </span>
                        </div>
                      </div>

                      <div className="mb-3">
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-gray-600 dark:text-gray-400">
                            Score
                          </span>
                          <span
                            className={`font-medium ${getScoreColor(status.score)}`}
                          >
                            {status.score}/100
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              status.score >= 90
                                ? "bg-green-500"
                                : status.score >= 80
                                  ? "bg-yellow-500"
                                  : status.score >= 70
                                    ? "bg-orange-500"
                                    : "bg-red-500"
                            }`}
                            style={{ width: `${status.score}%` }}
                          ></div>
                        </div>
                      </div>

                      {status.issues.length > 0 && (
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                            Issues:
                          </p>
                          <ul className="text-sm text-red-600 space-y-1">
                            {status.issues.map((issue, index) => (
                              <li key={index}>• {issue}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ),
                )}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default RBACDashboard;
