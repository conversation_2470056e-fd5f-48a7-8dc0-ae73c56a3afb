/**
 * Admin Dashboard Component
 * Provides comprehensive system-wide view for administrators with enhanced RBAC
 */

import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import {
  Building2,
  Users,
  Bus,
  Route,
  TrendingUp,
  AlertTriangle,
  Activity,
  BarChart3,
  Shield,
  Database,
  Settings,
} from "lucide-react";
import { StatCard } from "../dashboard/StatCard";
import { AdminStats } from "../dashboard/AdminStats";
import { useDatabase } from "../../contexts/DatabaseContext";
import { useRBACEnhancedSecurity } from "../../hooks/useRBACEnhancedSecurity";
import { EnhancedPermissionGuard } from "../auth/EnhancedPermissionGuard";
import { UserRole } from "../../types";
import { Permission, ResourceType } from "../../lib/rbac";

interface AdminDashboardProps {
  className?: string;
}

export const AdminDashboard: React.FC<AdminDashboardProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { tenants, users, buses, routes, students } = useDatabase();
  const { filterDataByRole, logSecurityEvent } = useRBACEnhancedSecurity();

  // Log admin dashboard access for security audit
  React.useEffect(() => {
    logSecurityEvent("admin_dashboard_access", {
      timestamp: new Date().toISOString(),
      component: "AdminDashboard",
    });
  }, [logSecurityEvent]);

  // Admin sees all data across all tenants with enhanced filtering
  const stats = useMemo(() => {
    const filteredUsers = filterDataByRole(
      users,
      ResourceType.USER,
      "id",
      "strict",
    );
    const filteredBuses = filterDataByRole(
      buses,
      ResourceType.BUS,
      "driver_id",
      "strict",
    );
    const filteredRoutes = filterDataByRole(
      routes,
      ResourceType.ROUTE,
      undefined,
      "strict",
    );
    const filteredStudents = filterDataByRole(
      students,
      ResourceType.STUDENT,
      "parent_id",
      "strict",
    );

    return {
      totalTenants: tenants.length,
      activeTenants: tenants.filter((t) => t.is_active).length,
      totalUsers: filteredUsers.length,
      activeUsers: filteredUsers.filter((u) => u.is_active).length,
      totalBuses: filteredBuses.length,
      activeBuses: filteredBuses.filter((b) => b.is_active).length,
      totalRoutes: filteredRoutes.length,
      activeRoutes: filteredRoutes.filter((r) => r.is_active).length,
      totalStudents: filteredStudents.length,
      activeStudents: filteredStudents.filter((s) => s.is_active).length,
      adminUsers: filteredUsers.filter((u) => u.role === UserRole.ADMIN).length,
      schoolManagers: filteredUsers.filter(
        (u) => u.role === UserRole.SCHOOL_MANAGER,
      ).length,
      drivers: filteredUsers.filter((u) => u.role === UserRole.DRIVER).length,
      parents: filteredUsers.filter((u) => u.role === UserRole.PARENT).length,
      supervisors: filteredUsers.filter((u) => u.role === UserRole.SUPERVISOR)
        .length,
      students: filteredUsers.filter((u) => u.role === UserRole.STUDENT).length,
    };
  }, [tenants, users, buses, routes, students, filterDataByRole]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Admin Header with Security Badge */}
      <div className="bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Shield className="h-8 w-8" />
            <div>
              <h1 className="text-2xl font-bold">System Administrator</h1>
              <p className="text-red-100">
                Full system access across all tenants
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-700 text-red-100">
              <Database className="h-3 w-3 mr-1" />
              Global Access
            </span>
          </div>
        </div>
      </div>

      {/* System Overview - Enhanced with Security Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <EnhancedPermissionGuard
          permissions={[Permission.SCHOOLS_VIEW_ALL]}
          componentKey="SchoolModal.view"
          auditAccess={true}
        >
          <StatCard
            title="Total Schools"
            value={stats.totalTenants}
            icon={<Building2 size={24} />}
            trend={{
              value: stats.activeTenants,
              isPositive: true,
              label: "Active",
            }}
            className="bg-gradient-to-r from-blue-500 to-blue-600 text-white"
          />
        </EnhancedPermissionGuard>

        <EnhancedPermissionGuard
          permissions={[Permission.USERS_VIEW_ALL]}
          componentKey="UserModal.view"
          auditAccess={true}
        >
          <StatCard
            title="Total Users"
            value={stats.totalUsers}
            icon={<Users size={24} />}
            trend={{
              value: stats.activeUsers,
              isPositive: true,
              label: "Active",
            }}
            className="bg-gradient-to-r from-green-500 to-green-600 text-white"
          />
        </EnhancedPermissionGuard>

        <EnhancedPermissionGuard
          permissions={[Permission.BUSES_VIEW_ALL]}
          componentKey="BusModal.view"
          auditAccess={true}
        >
          <StatCard
            title="Total Buses"
            value={stats.totalBuses}
            icon={<Bus size={24} />}
            trend={{
              value: stats.activeBuses,
              isPositive: true,
              label: "Active",
            }}
            className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white"
          />
        </EnhancedPermissionGuard>

        <EnhancedPermissionGuard
          permissions={[Permission.STUDENTS_VIEW_ALL]}
          componentKey="StudentModal.view"
          auditAccess={true}
        >
          <StatCard
            title="Total Students"
            value={stats.totalStudents}
            icon={<Users size={24} />}
            trend={{
              value: stats.activeStudents,
              isPositive: true,
              label: "Active",
            }}
            className="bg-gradient-to-r from-purple-500 to-purple-600 text-white"
          />
        </EnhancedPermissionGuard>
      </div>

      {/* Enhanced User Role Distribution */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <StatCard
          title="Administrators"
          value={stats.adminUsers}
          icon={<Shield size={24} />}
          className="border-red-200 bg-red-50 dark:bg-red-900/20"
        />
        <StatCard
          title="School Managers"
          value={stats.schoolManagers}
          icon={<BarChart3 size={24} />}
          className="border-blue-200 bg-blue-50 dark:bg-blue-900/20"
        />
        <StatCard
          title="Supervisors"
          value={stats.supervisors}
          icon={<Settings size={24} />}
          className="border-indigo-200 bg-indigo-50 dark:bg-indigo-900/20"
        />
        <StatCard
          title="Drivers"
          value={stats.drivers}
          icon={<Bus size={24} />}
          className="border-green-200 bg-green-50 dark:bg-green-900/20"
        />
        <StatCard
          title="Parents"
          value={stats.parents}
          icon={<Users size={24} />}
          className="border-purple-200 bg-purple-50 dark:bg-purple-900/20"
        />
        <StatCard
          title="Students"
          value={stats.students}
          icon={<Users size={24} />}
          className="border-orange-200 bg-orange-50 dark:bg-orange-900/20"
        />
      </div>

      {/* Enhanced System Health Indicators */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            System Health & Performance
          </h3>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
              <Activity className="h-3 w-3 mr-1" />
              All Systems Operational
            </span>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div>
              <p className="text-sm text-green-600 dark:text-green-400">
                Active Schools
              </p>
              <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                {stats.totalTenants > 0
                  ? Math.round((stats.activeTenants / stats.totalTenants) * 100)
                  : 0}
                %
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-green-500" />
          </div>
          <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div>
              <p className="text-sm text-blue-600 dark:text-blue-400">
                Active Users
              </p>
              <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                {stats.totalUsers > 0
                  ? Math.round((stats.activeUsers / stats.totalUsers) * 100)
                  : 0}
                %
              </p>
            </div>
            <Activity className="h-8 w-8 text-blue-500" />
          </div>
          <div className="flex items-center justify-between p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <div>
              <p className="text-sm text-yellow-600 dark:text-yellow-400">
                Active Buses
              </p>
              <p className="text-2xl font-bold text-yellow-700 dark:text-yellow-300">
                {stats.totalBuses > 0
                  ? Math.round((stats.activeBuses / stats.totalBuses) * 100)
                  : 0}
                %
              </p>
            </div>
            <Bus className="h-8 w-8 text-yellow-500" />
          </div>
          <div className="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div>
              <p className="text-sm text-purple-600 dark:text-purple-400">
                Active Routes
              </p>
              <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                {stats.totalRoutes > 0
                  ? Math.round((stats.activeRoutes / stats.totalRoutes) * 100)
                  : 0}
                %
              </p>
            </div>
            <Route className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Enhanced Admin Stats Component with RBAC */}
      <EnhancedPermissionGuard
        permissions={[Permission.REPORTS_VIEW_SYSTEM]}
        componentKey="ReportExport"
        auditAccess={true}
        fallback={
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2" />
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                Advanced analytics require additional permissions.
              </p>
            </div>
          </div>
        }
      >
        <AdminStats />
      </EnhancedPermissionGuard>
    </div>
  );
};

export default AdminDashboard;
