import { UserRole, type Bus, type Notification, type Route, type School, type Student, type User } from '../types';

export const mockSchools: School[] = [
  {
    id: '1',
    name: 'International Academy',
    logo: 'https://images.pexels.com/photos/5676744/pexels-photo-5676744.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    address: 'Cairo, Egypt',
    contactNumber: '+201234567890',
    isActive: true,
    createdAt: '2023-01-15T10:00:00Z',
  },
  {
    id: '2',
    name: 'Future Schools',
    logo: 'https://images.pexels.com/photos/301926/pexels-photo-301926.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    address: 'Alexandria, Egypt',
    contactNumber: '+201234567891',
    isActive: true,
    createdAt: '2023-02-20T10:00:00Z',
  },
  {
    id: '3',
    name: 'Modern Education Academy',
    logo: 'https://images.pexels.com/photos/256520/pexels-photo-256520.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    address: 'Giza, Egypt',
    contactNumber: '+201234567892',
    isActive: false,
    createdAt: '2023-03-10T10:00:00Z',
  },
];

export const mockUsers: User[] = [
  {
    id: '1',
    name: 'System Admin',
    email: '<EMAIL>',
    role: UserRole.ADMIN,
    avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
  {
    id: '2',
    name: 'Ahmed Manager',
    email: '<EMAIL>',
    role: UserRole.SCHOOL_MANAGER,
    schoolId: '1',
    avatar: 'https://images.pexels.com/photos/2379005/pexels-photo-2379005.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
  {
    id: '3',
    name: 'Fatima Supervisor',
    email: '<EMAIL>',
    role: UserRole.SUPERVISOR,
    schoolId: '1',
    avatar: 'https://images.pexels.com/photos/3586798/pexels-photo-3586798.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
  {
    id: '4',
    name: 'Mohamed Driver',
    email: '<EMAIL>',
    role: UserRole.DRIVER,
    schoolId: '1',
    avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
  {
    id: '5',
    name: 'Layla Parent',
    email: '<EMAIL>',
    role: UserRole.PARENT,
    schoolId: '1',
    avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
];

export const mockBuses: Bus[] = [
  {
    id: '1',
    plateNumber: 'ABC 123',
    capacity: 40,
    schoolId: '1',
    driverId: '4',
    isActive: true,
    lastLocation: {
      lat: 30.0444,
      lng: 31.2357,
      updatedAt: new Date().toISOString(),
    },
    route: {
      id: '1',
      name: 'North Route',
      schoolId: '1',
      busId: '1',
      stops: [
        {
          id: '1',
          name: 'Heliopolis Stop',
          routeId: '1',
          order: 1,
          location: { lat: 30.0911, lng: 31.322 },
          students: [],
          arrivalTime: '07:30',
        },
        {
          id: '2',
          name: 'Nasr City Stop',
          routeId: '1',
          order: 2,
          location: { lat: 30.0511, lng: 31.3422 },
          students: [],
          arrivalTime: '07:45',
        },
      ],
      isActive: true,
    },
  },
  {
    id: '2',
    plateNumber: 'DEF 456',
    capacity: 30,
    schoolId: '1',
    isActive: true,
    lastLocation: {
      lat: 30.0565,
      lng: 31.2262,
      updatedAt: new Date().toISOString(),
    },
  },
  {
    id: '3',
    plateNumber: 'GHI 789',
    capacity: 35,
    schoolId: '2',
    isActive: true,
    lastLocation: {
      lat: 31.2156,
      lng: 29.9553,
      updatedAt: new Date().toISOString(),
    },
  },
];

export const mockRoutes: Route[] = [
  {
    id: '1',
    name: 'North Route',
    schoolId: '1',
    busId: '1',
    stops: [
      {
        id: '1',
        name: 'Heliopolis Stop',
        routeId: '1',
        order: 1,
        location: { lat: 30.0911, lng: 31.322 },
        students: [
          {
            id: '1',
            name: 'Omar Student',
            schoolId: '1',
            grade: '6',
            parentId: '5',
            routeStopId: '1',
            isActive: true,
            photoUrl: 'https://images.pexels.com/photos/3755832/pexels-photo-3755832.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
          }
        ],
        arrivalTime: '07:30',
      },
      {
        id: '2',
        name: 'Nasr City Stop',
        routeId: '1',
        order: 2,
        location: { lat: 30.0511, lng: 31.3422 },
        students: [
          {
            id: '2',
            name: 'Noor Student',
            schoolId: '1',
            grade: '8',
            parentId: '5',
            routeStopId: '2',
            isActive: true,
            photoUrl: 'https://images.pexels.com/photos/4946604/pexels-photo-4946604.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
          }
        ],
        arrivalTime: '07:45',
      },
    ],
    isActive: true,
  },
  {
    id: '2',
    name: 'East Route',
    schoolId: '1',
    busId: '2',
    stops: [
      {
        id: '3',
        name: 'New Cairo Stop',
        routeId: '2',
        order: 1,
        location: { lat: 30.0284, lng: 31.4179 },
        students: [],
        arrivalTime: '07:20',
      },
    ],
    isActive: true,
  },
];

export const mockStudents: Student[] = [
  {
    id: '1',
    name: 'Omar Student',
    schoolId: '1',
    grade: '6',
    parentId: '5',
    routeStopId: '1',
    isActive: true,
    photoUrl: 'https://images.pexels.com/photos/3755832/pexels-photo-3755832.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
  {
    id: '2',
    name: 'Noor Student',
    schoolId: '1',
    grade: '8',
    parentId: '5',
    routeStopId: '2',
    isActive: true,
    photoUrl: 'https://images.pexels.com/photos/4946604/pexels-photo-4946604.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
];

export const mockNotifications: Notification[] = [
  {
    id: '1',
    userId: '5',
    title: 'Bus Arrived',
    message: 'The bus has arrived at Heliopolis Stop.',
    type: 'info',
    read: false,
    createdAt: new Date(Date.now() - 5 * 60000).toISOString(),
  },
  {
    id: '2',
    userId: '5',
    title: 'Student Boarded',
    message: 'Omar has boarded the bus.',
    type: 'success',
    read: true,
    createdAt: new Date(Date.now() - 4 * 60000).toISOString(),
  },
  {
    id: '3',
    userId: '5',
    title: 'Bus Delayed',
    message: 'The bus is running 5 minutes late today.',
    type: 'warning',
    read: false,
    createdAt: new Date(Date.now() - 60 * 60000).toISOString(),
  },
  {
    id: '4',
    userId: '2',
    title: 'New Parent Registered',
    message: 'A new parent has registered on the platform.',
    type: 'info',
    read: false,
    createdAt: new Date(Date.now() - 120 * 60000).toISOString(),
  },
  {
    id: '5',
    userId: '4',
    title: 'Route Changed',
    message: 'Your route has been updated. Please check the new stops.',
    type: 'warning',
    read: false,
    createdAt: new Date(Date.now() - 180 * 60000).toISOString(),
  },
];