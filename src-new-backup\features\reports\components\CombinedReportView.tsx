import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Grid3X3,
  Download,
  FileText,
  Users,
  Bus,
  TrendingUp,
  Clock,
  Calendar,
  Filter,
  RefreshCw,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import {
  reportGeneratorService,
  ReportConfig,
} from "../../lib/reportGeneratorService";
import { AttendanceReport } from "./AttendanceReport";
import { BusUtilizationReport } from "./BusUtilizationReport";
import { DriverPerformanceReport } from "./DriverPerformanceReport";
import { RouteDelayReport } from "./RouteDelayReport";
import { BusUtilizationChart } from "./BusUtilizationChart";

interface CombinedReportViewProps {
  className?: string;
}

export const CombinedReportView: React.FC<CombinedReportViewProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const { buses, routes } = useDatabase();
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState<any>(null);
  const [filters, setFilters] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
    busId: "",
    routeId: "",
    driverId: "",
  });
  const [activeSection, setActiveSection] = useState<string>("overview");
  const [exportFormat, setExportFormat] = useState<"csv" | "pdf">("pdf");

  useEffect(() => {
    if (tenant?.id) {
      generateCombinedReport();
    }
  }, [tenant?.id, filters]);

  const generateCombinedReport = async () => {
    if (!tenant?.id) return;

    setLoading(true);
    try {
      const config: ReportConfig = {
        type: "combined",
        format: "json",
        filters: {
          startDate: filters.startDate,
          endDate: filters.endDate,
          tenantId: tenant.id,
          busId: filters.busId || undefined,
          routeId: filters.routeId || undefined,
          driverId: filters.driverId || undefined,
        },
        options: {
          includeCharts: true,
          includeStats: true,
          customTitle: "Combined Transportation Report",
        },
      };

      const data = await reportGeneratorService.generateReportData(config);
      setReportData(data);
    } catch (error) {
      console.error("Error generating combined report:", error);
    } finally {
      setLoading(false);
    }
  };

  const exportCombinedReport = async () => {
    if (!tenant?.id || !reportData) return;

    try {
      const config: ReportConfig = {
        type: "combined",
        format: exportFormat,
        filters: {
          startDate: filters.startDate,
          endDate: filters.endDate,
          tenantId: tenant.id,
          busId: filters.busId || undefined,
          routeId: filters.routeId || undefined,
          driverId: filters.driverId || undefined,
        },
        options: {
          includeCharts: true,
          includeStats: true,
          customTitle: "Combined Transportation Report",
        },
      };

      if (exportFormat === "csv") {
        const csvData = await reportGeneratorService.exportToCSV(config);
        const blob = new Blob([csvData], { type: "text/csv" });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `combined_report_${filters.startDate}_to_${filters.endDate}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
      } else {
        const pdfBlob = await reportGeneratorService.exportToPDF(config);
        const url = window.URL.createObjectURL(pdfBlob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `combined_report_${filters.startDate}_to_${filters.endDate}.pdf`;
        a.click();
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Error exporting combined report:", error);
      alert("Failed to export report. Please try again.");
    }
  };

  const renderOverviewStats = () => {
    if (!reportData) return null;

    const stats = [
      {
        title: "Total Attendance Records",
        value: reportData.attendance?.stats?.totalRecords || 0,
        icon: Users,
        color: "blue",
        change: "+12%",
      },
      {
        title: "Average Bus Utilization",
        value: `${reportData.utilization?.stats?.avgUtilization?.toFixed(1) || 0}%`,
        icon: Bus,
        color: "green",
        change: "+5%",
      },
      {
        title: "Driver On-Time Rate",
        value: `${reportData.performance?.stats?.avgOnTimeRate?.toFixed(1) || 0}%`,
        icon: TrendingUp,
        color: "yellow",
        change: "-2%",
      },
      {
        title: "Route Punctuality",
        value: `${reportData.delays?.stats?.onTimePercentage?.toFixed(1) || 100}%`,
        icon: Clock,
        color: "purple",
        change: "+8%",
      },
    ];

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div
              key={index}
              className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stat.value}
                  </p>
                  <p
                    className={`text-sm ${
                      stat.change.startsWith("+")
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  >
                    {stat.change} from last period
                  </p>
                </div>
                <Icon className={`h-8 w-8 text-${stat.color}-500`} />
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderSectionContent = () => {
    switch (activeSection) {
      case "overview":
        return (
          <div className="space-y-8">
            {renderOverviewStats()}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Key Insights
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        High Bus Utilization
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Average utilization is above 85%, indicating efficient
                        resource usage.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        Driver Performance Variation
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Some drivers show lower on-time rates, requiring
                        attention.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        Attendance Tracking Effective
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Consistent attendance recording across all routes.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Recommendations
                </h3>
                <div className="space-y-4">
                  <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                      Route Optimization
                    </p>
                    <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                      Consider adjusting routes with frequent delays during peak
                      hours.
                    </p>
                  </div>
                  <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <p className="text-sm font-medium text-green-900 dark:text-green-100">
                      Driver Training
                    </p>
                    <p className="text-xs text-green-700 dark:text-green-300 mt-1">
                      Provide additional training for drivers with lower
                      performance scores.
                    </p>
                  </div>
                  <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <p className="text-sm font-medium text-purple-900 dark:text-purple-100">
                      Capacity Planning
                    </p>
                    <p className="text-xs text-purple-700 dark:text-purple-300 mt-1">
                      High utilization rates suggest potential need for
                      additional buses.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case "attendance":
        return <AttendanceReport />;
      case "utilization":
        return (
          <div className="space-y-6">
            <BusUtilizationReport />
            <BusUtilizationChart />
          </div>
        );
      case "performance":
        return <DriverPerformanceReport />;
      case "delays":
        return <RouteDelayReport />;
      default:
        return null;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
            <Grid3X3 className="mr-2 h-5 w-5 text-primary-500" />
            Combined Report View
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Comprehensive analysis of all transportation metrics in one view.
          </p>
        </div>

        <div className="flex items-center gap-2">
          <select
            value={exportFormat}
            onChange={(e) => setExportFormat(e.target.value as "csv" | "pdf")}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white"
          >
            <option value="pdf">PDF</option>
            <option value="csv">CSV</option>
          </select>
          <Button
            onClick={exportCombinedReport}
            disabled={loading || !reportData}
            className="flex items-center gap-2"
            size="sm"
          >
            <Download size={16} />
            Export
          </Button>
          <Button
            onClick={generateCombinedReport}
            disabled={loading}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
            ) : (
              <RefreshCw size={16} />
            )}
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Start Date
            </label>
            <input
              type="date"
              value={filters.startDate}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, startDate: e.target.value }))
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              End Date
            </label>
            <input
              type="date"
              value={filters.endDate}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, endDate: e.target.value }))
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Bus
            </label>
            <select
              value={filters.busId}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, busId: e.target.value }))
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
            >
              <option value="">All Buses</option>
              {buses.map((bus) => (
                <option key={bus.id} value={bus.id}>
                  {bus.plate_number}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Route
            </label>
            <select
              value={filters.routeId}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, routeId: e.target.value }))
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
            >
              <option value="">All Routes</option>
              {routes.map((route) => (
                <option key={route.id} value={route.id}>
                  {route.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-end">
            <Button
              onClick={generateCombinedReport}
              disabled={loading}
              className="w-full flex items-center justify-center gap-2"
              size="sm"
            >
              <Filter size={16} />
              Apply Filters
            </Button>
          </div>
        </div>
      </div>

      {/* Section Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {[
              { key: "overview", label: "Overview", icon: Grid3X3 },
              { key: "attendance", label: "Attendance", icon: Users },
              { key: "utilization", label: "Bus Utilization", icon: Bus },
              {
                key: "performance",
                label: "Driver Performance",
                icon: TrendingUp,
              },
              { key: "delays", label: "Route Delays", icon: Clock },
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setActiveSection(key)}
                className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                  activeSection === key
                    ? "border-primary-500 text-primary-600 dark:text-primary-400"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <Icon size={16} className="mr-2" />
                {label}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
            </div>
          ) : (
            renderSectionContent()
          )}
        </div>
      </div>
    </div>
  );
};

export default CombinedReportView;
