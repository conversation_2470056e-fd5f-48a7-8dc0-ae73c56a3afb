import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useParams, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Bus,
  User,
  MapPin,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  Settings,
} from "lucide-react";
import { Navbar } from "../../../shared/layouts/Navbar";
import { Sidebar } from "../../components/layout/Sidebar";
import { Button } from "../../components/ui/Button";
import { AttendanceList } from "../../components/students/AttendanceList";
import { BusMaintenanceSchedule } from "../../components/buses/BusMaintenanceSchedule";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/api";

interface BusDetails extends Tables<"buses"> {
  driver?: Tables<"users">;
  route?: Tables<"routes"> & {
    stops?: Tables<"route_stops">[];
  };
  maintenance_records?: MaintenanceRecord[];
}

interface MaintenanceRecord {
  id: string;
  bus_id: string;
  type: "routine" | "repair" | "inspection";
  description: string;
  cost?: number;
  scheduled_date: string;
  completed_date?: string;
  status: "scheduled" | "in_progress" | "completed" | "overdue";
  notes?: string;
  created_at: string;
}

export const BusDetailsPage: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [bus, setBus] = useState<BusDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<
    "overview" | "attendance" | "maintenance" | "route"
  >("overview");
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      fetchBusDetails(id);
    }
  }, [id]);

  const fetchBusDetails = async (busId: string) => {
    try {
      setLoading(true);
      setError(null);

      // Fetch bus details with related data
      const { data: busData, error: busError } = await supabase
        .from("buses")
        .select(
          `
          *,
          driver:users(*),
          route:routes(
            *,
            stops:route_stops(*)
          )
        `,
        )
        .eq("id", busId)
        .single();

      if (busError) throw busError;

      setBus(busData);
    } catch (err) {
      console.error("Error fetching bus details:", err);
      setError(
        err instanceof Error ? err.message : "Failed to fetch bus details",
      );
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive
      ? "bg-accent-100 text-accent-800 dark:bg-accent-800 dark:text-accent-100"
      : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
  };

  const formatLocation = (location: any) => {
    if (!location || !location.coordinates) return "Unknown";
    const [lng, lat] = location.coordinates;
    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
  };

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar />
          <main className="flex-1 overflow-y-auto p-4 md:p-6">
            <div className="max-w-7xl mx-auto">
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  if (error || !bus) {
    return (
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar />
          <main className="flex-1 overflow-y-auto p-4 md:p-6">
            <div className="max-w-7xl mx-auto">
              <div className="text-center py-12">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  {error || t("buses.busNotFound")}
                </h2>
                <Button
                  onClick={() => navigate("/buses")}
                  leftIcon={<ArrowLeft size={16} />}
                >
                  {t("common.back")}
                </Button>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />

        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  onClick={() => navigate("/buses")}
                  leftIcon={<ArrowLeft size={16} />}
                >
                  {t("common.back")}
                </Button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {t("buses.busDetails")} - {bus.plate_number}
                  </h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {t("buses.capacity")}: {bus.capacity} {t("common.seats")}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                    bus.is_active,
                  )}`}
                >
                  {bus.is_active ? (
                    <>
                      <CheckCircle size={12} className="mr-1" />{" "}
                      {t("buses.active")}
                    </>
                  ) : (
                    <>
                      <AlertTriangle size={12} className="mr-1" />{" "}
                      {t("buses.inactive")}
                    </>
                  )}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  leftIcon={<Settings size={16} />}
                >
                  {t("common.edit")}
                </Button>
              </div>
            </div>

            {/* Tabs */}
            <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
              <nav className="-mb-px flex space-x-8">
                {[
                  {
                    key: "overview",
                    label: t("common.overview"),
                    icon: <Bus size={16} />,
                  },
                  {
                    key: "attendance",
                    label: t("students.attendance"),
                    icon: <User size={16} />,
                  },
                  {
                    key: "route",
                    label: t("nav.routes"),
                    icon: <MapPin size={16} />,
                  },
                  {
                    key: "maintenance",
                    label: t("buses.maintenance"),
                    icon: <Settings size={16} />,
                  },
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setActiveTab(tab.key as any)}
                    className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.key
                        ? "border-primary-500 text-primary-600 dark:text-primary-400"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                    }`}
                  >
                    {tab.icon}
                    <span>{tab.label}</span>
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="space-y-6">
              {activeTab === "overview" && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* Basic Information */}
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      {t("buses.basicInformation")}
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          {t("buses.plateNumber")}
                        </label>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {bus.plate_number}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          {t("buses.capacity")}
                        </label>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {bus.capacity} {t("common.seats")}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          {t("buses.status")}
                        </label>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {bus.is_active
                            ? t("buses.active")
                            : t("buses.inactive")}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Driver Information */}
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      {t("buses.driverInformation")}
                    </h3>
                    {bus.driver ? (
                      <div className="space-y-3">
                        <div className="flex items-center space-x-3">
                          <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                            {bus.driver.avatar_url ? (
                              <img
                                src={bus.driver.avatar_url}
                                alt={bus.driver.name}
                                className="h-full w-full object-cover"
                              />
                            ) : (
                              <User size={20} className="text-gray-500" />
                            )}
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {bus.driver.name}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {bus.driver.email}
                            </p>
                          </div>
                        </div>
                        {bus.driver.phone && (
                          <div>
                            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                              {t("users.phone")}
                            </label>
                            <p className="text-sm text-gray-900 dark:text-white">
                              {bus.driver.phone}
                            </p>
                          </div>
                        )}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {t("buses.noDriverAssigned")}
                      </p>
                    )}
                  </div>

                  {/* Location Information */}
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      {t("buses.locationInformation")}
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          {t("buses.lastLocation")}
                        </label>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {formatLocation(bus.last_location)}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          {t("buses.lastUpdated")}
                        </label>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {bus.last_updated
                            ? new Date(bus.last_updated).toLocaleString()
                            : t("common.never")}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === "attendance" && (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    {t("students.attendanceRecords")}
                  </h3>
                  <AttendanceList showDateFilter={true} />
                </div>
              )}

              {activeTab === "route" && (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    {t("routes.routeInformation")}
                  </h3>
                  {bus.route ? (
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          {t("routes.name")}
                        </label>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {bus.route.name}
                        </p>
                      </div>
                      {bus.route.stops && bus.route.stops.length > 0 && (
                        <div>
                          <label className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2 block">
                            {t("routes.stops")} ({bus.route.stops.length})
                          </label>
                          <div className="space-y-2">
                            {bus.route.stops
                              .sort((a, b) => a.order - b.order)
                              .map((stop) => (
                                <div
                                  key={stop.id}
                                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md"
                                >
                                  <div className="flex items-center space-x-3">
                                    <span className="flex items-center justify-center w-6 h-6 bg-primary-100 dark:bg-primary-800 text-primary-600 dark:text-primary-400 rounded-full text-xs font-medium">
                                      {stop.order}
                                    </span>
                                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                                      {stop.name}
                                    </span>
                                  </div>
                                  {stop.arrival_time && (
                                    <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                                      <Clock size={12} />
                                      <span>{stop.arrival_time}</span>
                                    </div>
                                  )}
                                </div>
                              ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {t("routes.noRouteAssigned")}
                    </p>
                  )}
                </div>
              )}

              {activeTab === "maintenance" && (
                <BusMaintenanceSchedule
                  busId={bus.id}
                  busPlateNumber={bus.plate_number}
                  showAddButton={true}
                />
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};
