/**
 * Two Factor Authentication Component
 * مكون التحقق الثنائي - المرحلة الأولى
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Shield, 
  Smartphone, 
  Mail, 
  Key, 
  Copy, 
  Check, 
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import { Button } from '../ui/Button';
import { EnhancedSecurityService, User2FASettings } from '../../services/security/EnhancedSecurityService';
import { useAuth } from '../../contexts/AuthContext';

interface TwoFactorAuthProps {
  onComplete?: () => void;
  onCancel?: () => void;
  mode?: 'setup' | 'verify' | 'manage';
}

export const TwoFactorAuth: React.FC<TwoFactorAuthProps> = ({
  onComplete,
  onCancel,
  mode = 'setup'
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState<'method' | 'setup' | 'verify'>('method');
  const [selectedMethod, setSelectedMethod] = useState<'email' | 'sms' | 'totp'>('email');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [secretKey, setSecretKey] = useState('');
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  const [existing2FA, setExisting2FA] = useState<User2FASettings[]>([]);

  const securityService = EnhancedSecurityService.getInstance();

  useEffect(() => {
    if (user && mode === 'manage') {
      loadExisting2FA();
    }
  }, [user, mode]);

  const loadExisting2FA = async () => {
    if (!user) return;
    
    try {
      setIsLoading(true);
      const settings = await securityService.get2FASettings(user.id);
      setExisting2FA(settings);
    } catch (error) {
      console.error('Error loading 2FA settings:', error);
      setError('فشل في تحميل إعدادات التحقق الثنائي');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMethodSelect = (method: 'email' | 'sms' | 'totp') => {
    setSelectedMethod(method);
    setCurrentStep('setup');
    setError('');
    setSuccess('');
  };

  const handleSetup2FA = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError('');

      const settings = await securityService.enable2FA(
        user.id,
        selectedMethod,
        selectedMethod === 'sms' ? phoneNumber : undefined
      );

      if (selectedMethod === 'totp') {
        setSecretKey(settings.secret_key || '');
        setBackupCodes(settings.backup_codes || []);
        generateQRCode(settings.secret_key || '');
      }

      setCurrentStep('verify');
      setSuccess('تم إعداد التحقق الثنائي بنجاح');
    } catch (error) {
      console.error('Error setting up 2FA:', error);
      setError('فشل في إعداد التحقق الثنائي');
    } finally {
      setIsLoading(false);
    }
  };

  const generateQRCode = (secret: string) => {
    if (!user) return;
    
    const issuer = 'School Bus Management';
    const accountName = user.email;
    const otpAuthUrl = `otpauth://totp/${encodeURIComponent(issuer)}:${encodeURIComponent(accountName)}?secret=${secret}&issuer=${encodeURIComponent(issuer)}`;
    
    // يمكن استخدام مكتبة qrcode لإنشاء QR code
    setQrCodeUrl(`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(otpAuthUrl)}`);
  };

  const handleVerification = async () => {
    if (!verificationCode.trim()) {
      setError('يرجى إدخال رمز التحقق');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // هنا يجب إضافة منطق التحقق من الرمز
      // للتبسيط، سنعتبر أي رمز مكون من 6 أرقام صحيح
      if (verificationCode.length === 6 && /^\d+$/.test(verificationCode)) {
        setSuccess('تم التحقق بنجاح! تم تفعيل التحقق الثنائي');
        setTimeout(() => {
          onComplete?.();
        }, 2000);
      } else {
        setError('رمز التحقق غير صحيح');
      }
    } catch (error) {
      console.error('Error verifying 2FA:', error);
      setError('فشل في التحقق من الرمز');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisable2FA = async (method: 'email' | 'sms' | 'totp') => {
    if (!user) return;

    try {
      setIsLoading(true);
      await securityService.disable2FA(user.id, method);
      setSuccess('تم إلغاء تفعيل التحقق الثنائي');
      await loadExisting2FA();
    } catch (error) {
      console.error('Error disabling 2FA:', error);
      setError('فشل في إلغاء تفعيل التحقق الثنائي');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedCode(type);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const renderMethodSelection = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        اختر طريقة التحقق الثنائي
      </h3>
      
      <div className="grid gap-4">
        <button
          onClick={() => handleMethodSelect('email')}
          className="p-4 border-2 border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors text-right"
        >
          <div className="flex items-center space-x-3 space-x-reverse">
            <Mail className="w-6 h-6 text-blue-500" />
            <div>
              <div className="font-medium text-gray-900 dark:text-white">البريد الإلكتروني</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                إرسال رمز التحقق عبر البريد الإلكتروني
              </div>
            </div>
          </div>
        </button>

        <button
          onClick={() => handleMethodSelect('sms')}
          className="p-4 border-2 border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors text-right"
        >
          <div className="flex items-center space-x-3 space-x-reverse">
            <Smartphone className="w-6 h-6 text-green-500" />
            <div>
              <div className="font-medium text-gray-900 dark:text-white">رسالة نصية (SMS)</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                إرسال رمز التحقق عبر الرسائل النصية
              </div>
            </div>
          </div>
        </button>

        <button
          onClick={() => handleMethodSelect('totp')}
          className="p-4 border-2 border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors text-right"
        >
          <div className="flex items-center space-x-3 space-x-reverse">
            <Key className="w-6 h-6 text-purple-500" />
            <div>
              <div className="font-medium text-gray-900 dark:text-white">تطبيق المصادقة</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                استخدام تطبيق مثل Google Authenticator
              </div>
            </div>
          </div>
        </button>
      </div>
    </div>
  );

  const renderSetup = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        إعداد التحقق الثنائي - {selectedMethod === 'email' ? 'البريد الإلكتروني' : 
                                   selectedMethod === 'sms' ? 'الرسائل النصية' : 
                                   'تطبيق المصادقة'}
      </h3>

      {selectedMethod === 'sms' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            رقم الهاتف
          </label>
          <input
            type="tel"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
            placeholder="+966xxxxxxxxx"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            dir="ltr"
          />
        </div>
      )}

      {selectedMethod === 'totp' && (
        <div className="space-y-4">
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div className="flex items-start space-x-2 space-x-reverse">
              <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
              <div className="text-sm text-yellow-800 dark:text-yellow-200">
                ستحتاج إلى تطبيق مصادقة مثل Google Authenticator أو Authy لإكمال الإعداد
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex space-x-3 space-x-reverse">
        <Button
          onClick={handleSetup2FA}
          disabled={isLoading || (selectedMethod === 'sms' && !phoneNumber.trim())}
          className="flex-1"
        >
          {isLoading ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              جاري الإعداد...
            </>
          ) : (
            'متابعة'
          )}
        </Button>
        <Button
          variant="outline"
          onClick={() => setCurrentStep('method')}
          className="flex-1"
        >
          رجوع
        </Button>
      </div>
    </div>
  );

  const renderVerification = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        التحقق من الإعداد
      </h3>

      {selectedMethod === 'totp' && qrCodeUrl && (
        <div className="space-y-4">
          <div className="text-center">
            <img src={qrCodeUrl} alt="QR Code" className="mx-auto mb-4" />
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              امسح الرمز باستخدام تطبيق المصادقة
            </p>
          </div>

          {secretKey && (
            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  المفتاح السري (للإدخال اليدوي):
                </span>
                <button
                  onClick={() => copyToClipboard(secretKey, 'secret')}
                  className="text-blue-500 hover:text-blue-600 dark:text-blue-400"
                >
                  {copiedCode === 'secret' ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                </button>
              </div>
              <code className="text-sm font-mono bg-white dark:bg-gray-900 p-2 rounded border block">
                {secretKey}
              </code>
            </div>
          )}

          {backupCodes.length > 0 && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-red-800 dark:text-red-200">
                  رموز الطوارئ (احفظها في مكان آمن):
                </span>
                <button
                  onClick={() => copyToClipboard(backupCodes.join('\n'), 'backup')}
                  className="text-red-600 hover:text-red-700 dark:text-red-400"
                >
                  {copiedCode === 'backup' ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                </button>
              </div>
              <div className="grid grid-cols-2 gap-2">
                {backupCodes.map((code, index) => (
                  <code key={index} className="text-xs font-mono bg-white dark:bg-gray-900 p-1 rounded border block text-center">
                    {code}
                  </code>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          أدخل رمز التحقق
        </label>
        <input
          type="text"
          value={verificationCode}
          onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
          placeholder="123456"
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-center text-lg font-mono"
          dir="ltr"
          maxLength={6}
        />
      </div>

      <div className="flex space-x-3 space-x-reverse">
        <Button
          onClick={handleVerification}
          disabled={isLoading || verificationCode.length !== 6}
          className="flex-1"
        >
          {isLoading ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              جاري التحقق...
            </>
          ) : (
            'تأكيد'
          )}
        </Button>
        <Button
          variant="outline"
          onClick={() => setCurrentStep('setup')}
          className="flex-1"
        >
          رجوع
        </Button>
      </div>
    </div>
  );

  return (
    <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div className="flex items-center space-x-3 space-x-reverse mb-6">
        <Shield className="w-6 h-6 text-blue-500" />
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          التحقق الثنائي
        </h2>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
        </div>
      )}

      {success && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <p className="text-sm text-green-800 dark:text-green-200">{success}</p>
        </div>
      )}

      {currentStep === 'method' && renderMethodSelection()}
      {currentStep === 'setup' && renderSetup()}
      {currentStep === 'verify' && renderVerification()}

      {onCancel && (
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <Button
            variant="outline"
            onClick={onCancel}
            className="w-full"
          >
            إلغاء
          </Button>
        </div>
      )}
    </div>
  );
};
