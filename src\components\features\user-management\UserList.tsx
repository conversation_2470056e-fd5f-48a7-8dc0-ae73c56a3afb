/**
 * User List Component
 * Displays paginated list of users with filtering and actions
 * Phase 2: Application Structure Reorganization
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { User, UserRole, UserListParams } from '../../../api/types';
import { userService } from '../../../services/data/UserService';
import { usePermissionService } from '../../../hooks/usePermissionService';
import { ResourceType, Action } from '../../../lib/rbac';

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '../../common/ui/Card';
import { Button } from '../../common/ui/Button';
import { Badge } from '../../common/ui/Badge';
import { Alert, AlertDescription } from '../../common/ui/Alert';
import { DataTable } from '../../common/data-display/DataTable';
import { SearchInput } from '../../common/forms/SearchInput';
import { FilterSelect } from '../../common/forms/FilterSelect';
import { Pagination } from '../../common/data-display/Pagination';

// Icons
import { Plus, Edit, Trash2, Eye, UserCheck, UserX } from 'lucide-react';

interface UserListProps {
  tenantId?: string;
  onUserSelect?: (user: User) => void;
  onUserCreate?: () => void;
  onUserEdit?: (user: User) => void;
  onUserDelete?: (user: User) => void;
  className?: string;
}

interface UserListState {
  users: User[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filters: {
    search: string;
    role: UserRole | '';
    isActive: boolean | '';
  };
}

/**
 * User List Component
 * Implements Single Responsibility Principle - only handles user list display
 */
export const UserList: React.FC<UserListProps> = ({
  tenantId,
  onUserSelect,
  onUserCreate,
  onUserEdit,
  onUserDelete,
  className,
}) => {
  const { t } = useTranslation();

  // Permissions
  const { canRead, canCreate, canUpdate, canDelete } = usePermissionService();

  // State
  const [state, setState] = useState<UserListState>({
    users: [],
    loading: false,
    error: null,
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
    },
    filters: {
      search: '',
      role: '',
      isActive: '',
    },
  });

  // Check permissions
  const canViewUsers = canRead(ResourceType.USER);
  const canCreateUsers = canCreate(ResourceType.USER);
  const canUpdateUsers = canUpdate(ResourceType.USER);
  const canDeleteUsers = canDelete(ResourceType.USER);

  /**
   * Load users with current filters and pagination
   */
  const loadUsers = useCallback(async () => {
    if (!canViewUsers) {
      setState(prev => ({
        ...prev,
        error: 'You do not have permission to view users',
      }));
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const params: UserListParams = {
        page: state.pagination.page,
        limit: state.pagination.limit,
        ...(state.filters.search && { search: state.filters.search }),
        ...(state.filters.role && { role: state.filters.role }),
        ...(state.filters.isActive !== '' && { is_active: state.filters.isActive }),
        ...(tenantId && { tenant_id: tenantId }),
      };

      const response = await userService.getUsers(params);

      if (response.success && response.data) {
        setState(prev => ({
          ...prev,
          users: response.data!.data,
          pagination: {
            ...prev.pagination,
            total: response.data!.pagination.total,
            totalPages: response.data!.pagination.totalPages,
          },
          loading: false,
        }));
      } else {
        setState(prev => ({
          ...prev,
          error: response.error?.message || 'Failed to load users',
          loading: false,
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'An unexpected error occurred',
        loading: false,
      }));
    }
  }, [state.pagination.page, state.pagination.limit, state.filters, tenantId, canViewUsers]);

  // Load users on mount and when dependencies change
  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  /**
   * Handle search input change
   */
  const handleSearchChange = useCallback((search: string) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, search },
      pagination: { ...prev.pagination, page: 1 },
    }));
  }, []);

  /**
   * Handle role filter change
   */
  const handleRoleFilterChange = useCallback((role: UserRole | '') => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, role },
      pagination: { ...prev.pagination, page: 1 },
    }));
  }, []);

  /**
   * Handle active status filter change
   */
  const handleActiveFilterChange = useCallback((isActive: boolean | '') => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, isActive },
      pagination: { ...prev.pagination, page: 1 },
    }));
  }, []);

  /**
   * Handle page change
   */
  const handlePageChange = useCallback((page: number) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page },
    }));
  }, []);

  /**
   * Handle page size change
   */
  const handlePageSizeChange = useCallback((limit: number) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, limit, page: 1 },
    }));
  }, []);

  /**
   * Handle user status toggle
   */
  const handleToggleUserStatus = useCallback(async (user: User) => {
    if (!canUpdateUsers) return;

    try {
      const response = await userService.toggleUserStatus(user.id, !user.is_active);
      if (response.success) {
        // Refresh the list
        loadUsers();
      } else {
        setState(prev => ({
          ...prev,
          error: response.error?.message || 'Failed to update user status',
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'Failed to update user status',
      }));
    }
  }, [canUpdateUsers, loadUsers]);

  /**
   * Table columns configuration
   */
  const columns = useMemo(() => [
    {
      key: 'name',
      title: 'Name',
      render: (user: User) => (
        <div className="flex items-center space-x-3">
          {user.avatar_url ? (
            <img
              src={user.avatar_url}
              alt={user.name}
              className="w-8 h-8 rounded-full"
            />
          ) : (
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              {user.name.charAt(0).toUpperCase()}
            </div>
          )}
          <div>
            <div className="font-medium">{user.name}</div>
            <div className="text-sm text-gray-500">{user.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'role',
      title: 'Role',
      render: (user: User) => (
        <Badge variant="secondary">
          {user.role.replace('_', ' ').toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'status',
      title: 'Status',
      render: (user: User) => (
        <Badge variant={user.is_active ? 'default' : 'destructive'}>
          {user.is_active ? 'Active' : 'Inactive'}
        </Badge>
      ),
    },
    {
      key: 'last_login',
      title: 'Last Login',
      render: (user: User) => (
        <span className="text-sm text-gray-500">
          {user.last_login
            ? new Date(user.last_login).toLocaleDateString()
            : 'Never'}
        </span>
      ),
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (user: User) => (
        <div className="flex items-center space-x-2">
          {onUserSelect && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onUserSelect(user)}
              title="View Details"
            >
              <Eye className="w-4 h-4" />
            </Button>
          )}
          
          {canUpdateUsers && onUserEdit && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onUserEdit(user)}
              title="Edit User"
            >
              <Edit className="w-4 h-4" />
            </Button>
          )}
          
          {canUpdateUsers && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleToggleUserStatus(user)}
              title={user.is_active ? 'Deactivate' : 'Activate'}
            >
              {user.is_active ? (
                <UserX className="w-4 h-4 text-red-500" />
              ) : (
                <UserCheck className="w-4 h-4 text-green-500" />
              )}
            </Button>
          )}
          
          {canDeleteUsers && onUserDelete && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onUserDelete(user)}
              title="Delete User"
            >
              <Trash2 className="w-4 h-4 text-red-500" />
            </Button>
          )}
        </div>
      ),
    },
  ], [canUpdateUsers, canDeleteUsers, onUserSelect, onUserEdit, onUserDelete, handleToggleUserStatus]);

  // Role options for filter
  const roleOptions = [
    { value: '', label: 'All Roles' },
    { value: UserRole.ADMIN, label: 'Admin' },
    { value: UserRole.SCHOOL_MANAGER, label: 'School Manager' },
    { value: UserRole.SUPERVISOR, label: 'Supervisor' },
    { value: UserRole.DRIVER, label: 'Driver' },
    { value: UserRole.PARENT, label: 'Parent' },
    { value: UserRole.STUDENT, label: 'Student' },
  ];

  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: true, label: 'Active' },
    { value: false, label: 'Inactive' },
  ];

  if (!canViewUsers) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          You do not have permission to view users.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Users</CardTitle>
          {/* زر إضافة مستخدم - مؤقت بدون فحص الصلاحيات */}
          {onUserCreate && (
            <Button onClick={onUserCreate}>
              <Plus className="w-4 h-4 mr-2" />
              {t("users.addUser")}
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Filters */}
        <div className="flex items-center space-x-4 mb-6">
          <SearchInput
            placeholder="Search users..."
            value={state.filters.search}
            onChange={handleSearchChange}
            className="flex-1"
          />
          
          <FilterSelect
            options={roleOptions}
            value={state.filters.role}
            onChange={handleRoleFilterChange}
            placeholder="Filter by role"
          />
          
          <FilterSelect
            options={statusOptions}
            value={state.filters.isActive}
            onChange={handleActiveFilterChange}
            placeholder="Filter by status"
          />
        </div>

        {/* Error Display */}
        {state.error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{state.error}</AlertDescription>
          </Alert>
        )}

        {/* Data Table */}
        <DataTable
          data={state.users}
          columns={columns}
          loading={state.loading}
          emptyMessage="No users found"
        />

        {/* Pagination */}
        <div className="mt-6">
          <Pagination
            currentPage={state.pagination.page}
            totalPages={state.pagination.totalPages}
            pageSize={state.pagination.limit}
            totalItems={state.pagination.total}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        </div>
      </CardContent>
    </Card>
  );
};
