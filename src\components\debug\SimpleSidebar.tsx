/**
 * Simple Sidebar for Testing
 * Simplified sidebar to test theme links and reports
 * Phase 3: UI/UX Enhancement - Debug
 */

import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole } from '../../types';
import {
  LayoutDashboard,
  Users,
  Bus,
  Route,
  ClipboardCheck,
  MapPin,
  FileBarChart,
  Bell,
  Star,
  Palette,
  Settings,
  User,
  LogOut,
} from 'lucide-react';

export const SimpleSidebar: React.FC = () => {
  const { user, logout } = useAuth();
  const location = useLocation();

  if (!user) return null;

  // Simple navigation items
  const navItems = [
    {
      key: "dashboard",
      to: "/dashboard",
      icon: <LayoutDashboard size={20} />,
      label: "لوحة التحكم",
      show: true,
    },
    {
      key: "users",
      to: "/dashboard/users",
      icon: <Users size={20} />,
      label: "المستخدمين",
      show: true,
    },
    {
      key: "buses",
      to: "/dashboard/buses",
      icon: <Bus size={20} />,
      label: "الحافلات",
      show: true,
    },
    {
      key: "routes",
      to: "/dashboard/routes",
      icon: <Route size={20} />,
      label: "المسارات",
      show: true,
    },
    {
      key: "students",
      to: "/dashboard/students",
      icon: <Users size={20} />,
      label: "الطلاب",
      show: true,
    },
    {
      key: "attendance",
      to: "/dashboard/attendance",
      icon: <ClipboardCheck size={20} />,
      label: "الحضور",
      show: true,
    },
    {
      key: "tracking",
      to: "/dashboard/tracking",
      icon: <MapPin size={20} />,
      label: "التتبع المباشر",
      show: true,
    },
    {
      key: "reports",
      to: "/dashboard/reports",
      icon: <FileBarChart size={20} />,
      label: "التقارير",
      show: true, // Always show reports
    },
    {
      key: "notifications",
      to: "/dashboard/notifications",
      icon: <Bell size={20} />,
      label: "الإشعارات",
      show: true,
    },
    {
      key: "evaluation",
      to: "/dashboard/evaluation",
      icon: <Star size={20} />,
      label: "التقييم",
      show: !["student", "driver"].includes(user.role),
    },
    // Theme options
    {
      key: "admin-themes",
      to: "/admin/themes",
      icon: <Palette size={20} />,
      label: "إدارة الثيمات",
      show: user.role === UserRole.ADMIN,
    },
    {
      key: "school-theme",
      to: "/school/theme",
      icon: <Palette size={20} />,
      label: "ثيم المدرسة",
      show: user.role === UserRole.SCHOOL_MANAGER,
    },
    {
      key: "settings",
      to: "/dashboard/settings",
      icon: <Settings size={20} />,
      label: "الإعدادات",
      show: true,
    },
    {
      key: "profile",
      to: "/dashboard/profile",
      icon: <User size={20} />,
      label: "الملف الشخصي",
      show: true,
    },
  ];

  // Filter items to show
  const visibleItems = navItems.filter(item => item.show);

  return (
    <aside className="w-64 bg-white dark:bg-gray-800 h-screen fixed top-0 left-0 z-40 overflow-y-auto border-r border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <div className="h-8 w-8 bg-blue-600 text-white rounded flex items-center justify-center">
            <Bus size={20} />
          </div>
          <div>
            <span className="text-lg font-bold text-gray-900 dark:text-white">
              إدارة الحافلات
            </span>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="mt-4 px-2 space-y-1">
        {visibleItems.map((item) => (
          <Link
            key={item.key}
            to={item.to}
            className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              location.pathname === item.to ||
              location.pathname.startsWith(`${item.to}/`)
                ? "text-blue-700 bg-blue-50"
                : "text-gray-700 hover:text-blue-600 hover:bg-gray-100"
            }`}
          >
            <span className="ml-3">{item.icon}</span>
            {item.label}
          </Link>
        ))}
      </nav>

      {/* User Info & Logout */}
      <div className="absolute bottom-0 w-full p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="mb-2 text-sm text-gray-600">
          <div>المستخدم: {user.name}</div>
          <div>الدور: {user.role}</div>
        </div>
        <button
          type="button"
          onClick={logout}
          className="w-full flex items-center justify-start px-3 py-2 text-sm font-medium text-gray-700 hover:text-red-600 hover:bg-gray-100 rounded-md transition-colors"
        >
          <LogOut size={16} className="ml-2" />
          تسجيل الخروج
        </button>
      </div>
    </aside>
  );
};
