# 🔧 دليل تطبيق الهجرات على Supabase

## 📋 نظرة عامة

هذا الدليل يوضح كيفية تطبيق هجرات Row Level Security (RLS) على قاعدة بيانات Supabase بشكل يدوي.

## ✅ المتطلبات المسبقة

### 1. حساب Supabase
- إنشاء مشروع جديد على [supabase.com](https://supabase.com)
- الحصول على URL و API Keys

### 2. متغيرات البيئة
```bash
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## 📦 ملفات الهجرة (بالترتيب)

### 1. الجداول الأساسية
**ملف**: `2025-06-02_000_create_base_tables.sql`
- ✅ 13 جدول أساسي
- ✅ 23 فهرس
- ✅ 6 محفز
- ✅ تفعيل RLS

### 2. الأدوار والصلاحيات
**ملف**: `2025-06-02_001_create_roles.sql`
- ✅ جدول user_roles
- ✅ جدول role_permissions
- ✅ البيانات الأساسية للأدوار

### 3. الدوال الأمنية
**ملف**: `2025-06-02_002_create_security_functions.sql`
- ✅ 8 دوال أمنية
- ✅ دوال التحقق من الصلاحيات
- ✅ دوال التدقيق

### 4. سياسات RLS
**ملف**: `2025-06-02_003_create_rls_policies.sql`
- ✅ 32 سياسة RLS
- ✅ حماية جميع الجداول
- ✅ عزل المستأجرين

### 5. تفعيل RLS
**ملف**: `2025-06-02_004_enable_rls.sql`
- ✅ تفعيل RLS على جميع الجداول

## 🚀 خطوات التطبيق

### الطريقة الأولى: Supabase Dashboard

1. **الدخول إلى Dashboard**
   - اذهب إلى [app.supabase.com](https://app.supabase.com)
   - اختر مشروعك

2. **فتح SQL Editor**
   - اذهب إلى SQL Editor في الشريط الجانبي
   - انقر على "New Query"

3. **تطبيق الهجرات بالترتيب**
   ```sql
   -- نسخ محتوى كل ملف وتشغيله بالترتيب
   -- 1. 2025-06-02_000_create_base_tables.sql
   -- 2. 2025-06-02_001_create_roles.sql
   -- 3. 2025-06-02_002_create_security_functions.sql
   -- 4. 2025-06-02_003_create_rls_policies.sql
   -- 5. 2025-06-02_004_enable_rls.sql
   ```

### الطريقة الثانية: Supabase CLI

1. **تثبيت CLI**
   ```bash
   npm install -g supabase
   ```

2. **تسجيل الدخول**
   ```bash
   supabase login
   ```

3. **ربط المشروع**
   ```bash
   supabase link --project-ref your-project-ref
   ```

4. **تطبيق الهجرات**
   ```bash
   supabase db push
   ```

### الطريقة الثالثة: API مباشر

```javascript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

// قراءة وتطبيق كل ملف هجرة
const applyMigration = async (sqlContent) => {
  const { error } = await supabase.rpc('exec_sql', {
    sql: sqlContent
  })
  
  if (error) {
    console.error('خطأ في تطبيق الهجرة:', error)
  } else {
    console.log('تم تطبيق الهجرة بنجاح')
  }
}
```

## 🧪 التحقق من التطبيق

### 1. فحص الجداول
```sql
-- التحقق من وجود الجداول
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public';
```

### 2. فحص RLS
```sql
-- التحقق من تفعيل RLS
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';
```

### 3. فحص السياسات
```sql
-- التحقق من السياسات
SELECT schemaname, tablename, policyname, cmd 
FROM pg_policies 
WHERE schemaname = 'public';
```

### 4. فحص الدوال
```sql
-- التحقق من الدوال
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_schema = 'public';
```

## 📊 بيانات الاختبار

### إنشاء مستأجر اختبار
```sql
INSERT INTO tenants (name, slug) VALUES 
('مدرسة الأمل الابتدائية', 'al-amal-primary'),
('مدرسة النور الثانوية', 'al-noor-secondary');
```

### إنشاء مستخدم اختبار
```sql
-- يجب إنشاء المستخدم في auth.users أولاً عبر Supabase Auth
-- ثم إضافة البيانات الإضافية
INSERT INTO users (id, email, full_name, tenant_id) VALUES 
('user-uuid-here', '<EMAIL>', 'أحمد محمد', 'tenant-uuid-here');

-- إضافة دور للمستخدم
INSERT INTO user_roles (user_id, role, tenant_id) VALUES 
('user-uuid-here', 'tenant_admin', 'tenant-uuid-here');
```

## 🔍 اختبار السياسات

### اختبار عزل المستأجرين
```sql
-- تسجيل الدخول كمستخدم من مستأجر معين
-- يجب أن يرى بيانات مستأجره فقط
SELECT * FROM students; -- يجب أن يعرض طلاب المدرسة فقط
SELECT * FROM buses;    -- يجب أن يعرض حافلات المدرسة فقط
```

### اختبار صلاحيات الأدوار
```sql
-- اختبار دور السائق
-- يجب أن يرى حافلته فقط
SELECT * FROM buses WHERE driver_id = auth.uid();

-- اختبار دور ولي الأمر
-- يجب أن يرى أطفاله فقط
SELECT * FROM students WHERE parent_id = auth.uid();
```

## ⚠️ استكشاف الأخطاء

### خطأ: "relation does not exist"
```sql
-- التحقق من وجود الجدول
SELECT EXISTS (
  SELECT FROM information_schema.tables 
  WHERE table_schema = 'public' 
  AND table_name = 'table_name'
);
```

### خطأ: "function does not exist"
```sql
-- التحقق من وجود الدالة
SELECT EXISTS (
  SELECT FROM information_schema.routines 
  WHERE routine_schema = 'public' 
  AND routine_name = 'function_name'
);
```

### خطأ: "permission denied"
```sql
-- التحقق من سياسات RLS
SELECT * FROM pg_policies WHERE tablename = 'table_name';
```

## 📈 مراقبة الأداء

### فحص استخدام الفهارس
```sql
SELECT 
  schemaname,
  tablename,
  indexname,
  idx_scan,
  idx_tup_read,
  idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

### فحص أداء الاستعلامات
```sql
SELECT 
  query,
  calls,
  total_time,
  mean_time
FROM pg_stat_statements
WHERE query LIKE '%your_table%'
ORDER BY total_time DESC;
```

## 🔒 أفضل الممارسات الأمنية

### 1. استخدام Service Role Key بحذر
- لا تعرض Service Role Key في الكود العام
- استخدمها فقط في البيئات الآمنة

### 2. اختبار السياسات بانتظام
- اختبر جميع السيناريوهات
- تأكد من عزل البيانات

### 3. مراقبة السجلات
- راقب جدول audit_logs
- راقب security_incidents

### 4. النسخ الاحتياطية
- أنشئ نسخ احتياطية منتظمة
- اختبر استعادة البيانات

## 📞 الدعم

### في حالة المشاكل:
1. راجع سجلات Supabase
2. تحقق من متغيرات البيئة
3. راجع تقارير التحقق في `reports/validation/`
4. راجع الوثائق الرسمية لـ Supabase

### الموارد المفيدة:
- [Supabase Documentation](https://supabase.com/docs)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [PostgreSQL RLS Documentation](https://www.postgresql.org/docs/current/ddl-rowsecurity.html)

---

## ✅ قائمة التحقق النهائية

- [ ] تطبيق جميع ملفات الهجرة بالترتيب
- [ ] التحقق من تفعيل RLS على جميع الجداول
- [ ] اختبار السياسات مع مستخدمين مختلفين
- [ ] إنشاء بيانات اختبار
- [ ] التحقق من عمل الدوال الأمنية
- [ ] مراقبة الأداء والسجلات
- [ ] إعداد النسخ الاحتياطية

**🎉 تهانينا! نظام RLS جاهز للاستخدام!**
