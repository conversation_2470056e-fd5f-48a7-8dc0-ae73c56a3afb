import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Calendar,
  Download,
  Filter,
  FileText,
  Clock,
  AlertTriangle,
  TrendingUp,
  Route,
} from "lucide-react";
import { Button } from "../ui/Button";
import { supabase } from "../../lib/supabase";
import { useDatabase } from "../../contexts/DatabaseContext";
import { useAuth } from "../../contexts/AuthContext";
import { getRouteDelays } from "../../lib/api";
import type { Tables } from "../../lib/api";

interface RouteDelayData {
  id: string;
  route_id: string;
  bus_id: string;
  date: string;
  scheduled_time: string;
  actual_time: string;
  delay_minutes: number;
  reason: string;
  route?: {
    id: string;
    name: string;
  };
  bus?: {
    id: string;
    plate_number: string;
  };
}

interface RouteDelayReportProps {
  className?: string;
}

export const RouteDelayReport: React.FC<RouteDelayReportProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { buses, routes } = useDatabase();
  const [reportData, setReportData] = useState<RouteDelayData[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
    routeId: "",
    busId: "",
  });
  const [stats, setStats] = useState({
    totalDelays: 0,
    avgDelayMinutes: 0,
    maxDelayMinutes: 0,
    onTimePercentage: 0,
    analytics: null as any,
  });

  useEffect(() => {
    generateReport();
  }, [filters]);

  const generateReport = async () => {
    setLoading(true);
    try {
      // Fetch real data from the API
      const data = await getRouteDelays(
        user?.tenant_id || "",
        filters.routeId || undefined,
        filters.startDate,
        filters.endDate,
      );

      if (data && data.length > 0) {
        setReportData(data);
        // Calculate stats from real data
        const totalDelays = data.length;
        const delayedRoutes = data.filter((item) => item.delay_minutes > 0);
        const avgDelayMinutes =
          delayedRoutes.length > 0
            ? delayedRoutes.reduce((sum, item) => sum + item.delay_minutes, 0) /
              delayedRoutes.length
            : 0;
        const maxDelayMinutes = data.reduce(
          (max, item) => Math.max(max, item.delay_minutes),
          0,
        );
        const onTimePercentage =
          totalDelays > 0
            ? ((totalDelays - delayedRoutes.length) / totalDelays) * 100
            : 100;

        setStats({
          totalDelays,
          avgDelayMinutes,
          maxDelayMinutes,
          onTimePercentage,
          analytics: analyzeDelayPatterns(data),
        });
        return;
      }

      // Fallback to sample data if no real data available
      const sampleData: RouteDelayData[] = [];

      // Get dates between start and end
      const startDate = new Date(filters.startDate);
      const endDate = new Date(filters.endDate);
      const dateArray = [];
      let currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        dateArray.push(new Date(currentDate));
        currentDate.setDate(currentDate.getDate() + 1);
      }

      // Filter routes based on selection
      const relevantRoutes = filters.routeId
        ? routes.filter((r) => r.id === filters.routeId)
        : routes;

      // Generate sample data for each route and date
      relevantRoutes.forEach((route) => {
        const bus = buses.find((b) => b.id === route.bus_id);
        if (filters.busId && bus?.id !== filters.busId) return;

        dateArray.forEach((date) => {
          // Skip weekends
          if (date.getDay() === 0 || date.getDay() === 6) return;

          // Generate 2-4 trips per day
          const tripsCount = Math.floor(Math.random() * 3) + 2;

          for (let i = 0; i < tripsCount; i++) {
            const scheduledHour = 7 + i * 2; // 7 AM, 9 AM, 11 AM, 1 PM
            const scheduledTime = `${scheduledHour.toString().padStart(2, "0")}:00`;

            // 70% chance of being on time, 30% chance of delay
            const isDelayed = Math.random() > 0.7;
            const delayMinutes = isDelayed
              ? Math.floor(Math.random() * 30) + 5
              : 0; // 5-35 minutes delay

            const actualHour = scheduledHour + Math.floor(delayMinutes / 60);
            const actualMinutes = delayMinutes % 60;
            const actualTime = `${actualHour.toString().padStart(2, "0")}:${actualMinutes.toString().padStart(2, "0")}`;

            const reasons = [
              "Traffic congestion",
              "Weather conditions",
              "Vehicle maintenance",
              "Student pickup delay",
              "Road construction",
              "Emergency stop",
            ];

            sampleData.push({
              id: `${route.id}-${date.toISOString().split("T")[0]}-${i}`,
              route_id: route.id,
              bus_id: bus?.id || "",
              date: date.toISOString().split("T")[0],
              scheduled_time: scheduledTime,
              actual_time: actualTime,
              delay_minutes: delayMinutes,
              reason: isDelayed
                ? reasons[Math.floor(Math.random() * reasons.length)]
                : "",
              route: {
                id: route.id,
                name: route.name,
              },
              bus: bus
                ? {
                    id: bus.id,
                    plate_number: bus.plate_number,
                  }
                : undefined,
            });
          }
        });
      });

      setReportData(sampleData);

      // Calculate stats
      if (sampleData.length > 0) {
        const totalDelays = sampleData.length;
        const delayedRoutes = sampleData.filter(
          (item) => item.delay_minutes > 0,
        );
        const avgDelayMinutes =
          delayedRoutes.length > 0
            ? delayedRoutes.reduce((sum, item) => sum + item.delay_minutes, 0) /
              delayedRoutes.length
            : 0;
        const maxDelayMinutes = sampleData.reduce(
          (max, item) => Math.max(max, item.delay_minutes),
          0,
        );
        const onTimePercentage =
          totalDelays > 0
            ? ((totalDelays - delayedRoutes.length) / totalDelays) * 100
            : 100;

        setStats({
          totalDelays,
          avgDelayMinutes,
          maxDelayMinutes,
          onTimePercentage,
          analytics: analyzeDelayPatterns(sampleData),
        });
      }
    } catch (error) {
      console.error("Error generating route delay report:", error);
    } finally {
      setLoading(false);
    }
  };

  const exportToCSV = () => {
    const headers = [
      "Date",
      "Route",
      "Bus",
      "Scheduled Time",
      "Actual Time",
      "Delay (minutes)",
      "Reason",
    ];

    const csvContent = [
      headers.join(","),
      ...reportData.map((row) =>
        [
          row.date,
          `"${row.route?.name || "Unknown"}"`,
          `"${row.bus?.plate_number || "Unknown"}"`,
          row.scheduled_time,
          row.actual_time,
          row.delay_minutes,
          `"${row.reason || "On time"}"`,
        ].join(","),
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `route_delays_${filters.startDate}_to_${filters.endDate}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const analyzeDelayPatterns = (data: RouteDelayData[]) => {
    if (!data.length) return null;

    // Group by route
    const routeDelays = data.reduce((acc, item) => {
      const routeId = item.route_id;
      if (!acc[routeId]) {
        acc[routeId] = {
          routeName: item.route?.name || "Unknown",
          delays: [],
          totalDelayMinutes: 0,
        };
      }
      acc[routeId].delays.push(item);
      acc[routeId].totalDelayMinutes += item.delay_minutes;
      return acc;
    }, {} as any);

    // Find most problematic routes
    const problematicRoutes = Object.entries(routeDelays)
      .map(([routeId, data]: [string, any]) => ({
        routeId,
        routeName: data.routeName,
        delayCount: data.delays.length,
        avgDelay: data.totalDelayMinutes / data.delays.length,
        totalDelayMinutes: data.totalDelayMinutes,
      }))
      .sort((a, b) => b.totalDelayMinutes - a.totalDelayMinutes)
      .slice(0, 5);

    // Group by time of day
    const timePatterns = data.reduce((acc, item) => {
      const hour = parseInt(item.scheduled_time.split(":")[0]);
      const timeSlot =
        hour < 9
          ? "Morning (6-9 AM)"
          : hour < 15
            ? "Midday (9 AM-3 PM)"
            : "Afternoon (3-6 PM)";
      if (!acc[timeSlot]) acc[timeSlot] = 0;
      if (item.delay_minutes > 0) acc[timeSlot]++;
      return acc;
    }, {} as any);

    // Group by day of week
    const dayPatterns = data.reduce((acc, item) => {
      const dayOfWeek = new Date(item.date).getDay();
      const dayName = [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
      ][dayOfWeek];
      if (!acc[dayName]) acc[dayName] = 0;
      if (item.delay_minutes > 0) acc[dayName]++;
      return acc;
    }, {} as any);

    // Common delay reasons
    const reasonPatterns = data
      .filter((item) => item.delay_minutes > 0 && item.reason)
      .reduce((acc, item) => {
        if (!acc[item.reason]) acc[item.reason] = 0;
        acc[item.reason]++;
        return acc;
      }, {} as any);

    return {
      problematicRoutes,
      timePatterns,
      dayPatterns,
      reasonPatterns,
    };
  };

  const exportToPDF = () => {
    const printContent = `
      <html>
        <head>
          <title>Route Delay Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            .header { text-align: center; margin-bottom: 20px; }
            .stats { display: flex; justify-content: space-around; margin: 20px 0; }
            .stat-card { text-align: center; padding: 10px; border: 1px solid #ddd; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${t("reports.routeDelayReport")}</h1>
            <p>Period: ${filters.startDate} to ${filters.endDate}</p>
          </div>
          
          <div class="stats">
            <div class="stat-card">
              <h3>${stats.totalDelays}</h3>
              <p>${t("reports.totalTrips")}</p>
            </div>
            <div class="stat-card">
              <h3>${stats.avgDelayMinutes.toFixed(1)} min</h3>
              <p>${t("reports.avgDelay")}</p>
            </div>
            <div class="stat-card">
              <h3>${stats.maxDelayMinutes} min</h3>
              <p>${t("reports.maxDelay")}</p>
            </div>
            <div class="stat-card">
              <h3>${stats.onTimePercentage.toFixed(1)}%</h3>
              <p>${t("reports.onTimePercentage")}</p>
            </div>
          </div>
          
          <table>
            <thead>
              <tr>
                <th>${t("common.date")}</th>
                <th>${t("nav.routes")}</th>
                <th>${t("reports.bus")}</th>
                <th>${t("reports.scheduledTime")}</th>
                <th>${t("reports.actualTime")}</th>
                <th>${t("reports.delay")} (min)</th>
                <th>${t("reports.reason")}</th>
              </tr>
            </thead>
            <tbody>
              ${reportData
                .map(
                  (row) => `
                <tr>
                  <td>${new Date(row.date).toLocaleDateString()}</td>
                  <td>${row.route?.name || "Unknown"}</td>
                  <td>${row.bus?.plate_number || "Unknown"}</td>
                  <td>${row.scheduled_time}</td>
                  <td>${row.actual_time}</td>
                  <td>${row.delay_minutes}</td>
                  <td>${row.reason || "On time"}</td>
                </tr>
              `,
                )
                .join("")}
            </tbody>
          </table>
        </body>
      </html>
    `;

    const printWindow = window.open("", "_blank");
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t("reports.routeDelayReport")}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t("reports.analyzeRouteDelays")}
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={exportToCSV}
            variant="outline"
            size="sm"
            leftIcon={<Download size={16} />}
            disabled={reportData.length === 0}
          >
            {t("common.exportCSV")}
          </Button>
          <Button
            onClick={exportToPDF}
            variant="outline"
            size="sm"
            leftIcon={<FileText size={16} />}
            disabled={reportData.length === 0}
          >
            {t("common.exportPDF")}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("common.startDate")}
            </label>
            <input
              type="date"
              value={filters.startDate}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, startDate: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("common.endDate")}
            </label>
            <input
              type="date"
              value={filters.endDate}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, endDate: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("nav.routes")}
            </label>
            <select
              value={filters.routeId}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, routeId: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="">{t("common.all")}</option>
              {routes.map((route) => (
                <option key={route.id} value={route.id}>
                  {route.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("reports.bus")}
            </label>
            <select
              value={filters.busId}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, busId: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="">{t("common.all")}</option>
              {buses.map((bus) => (
                <option key={bus.id} value={bus.id}>
                  {bus.plate_number}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-primary-100 dark:bg-primary-800/20 rounded-lg">
              <Route className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("reports.totalTrips")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {stats.totalDelays}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 dark:bg-yellow-800/20 rounded-lg">
              <Clock className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("reports.avgDelay")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {stats.avgDelayMinutes.toFixed(1)} min
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 dark:bg-red-800/20 rounded-lg">
              <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("reports.maxDelay")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {stats.maxDelayMinutes} min
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-800/20 rounded-lg">
              <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("reports.onTimePercentage")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {stats.onTimePercentage.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Report Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t("reports.routeDelayData")} ({reportData.length}{" "}
            {t("common.records")})
          </h3>
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
          </div>
        ) : reportData.length > 0 ? (
          <div className="overflow-x-auto max-h-96">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700 sticky top-0">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("common.date")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("nav.routes")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("reports.bus")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("reports.scheduledTime")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("reports.actualTime")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("reports.delay")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("reports.reason")}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {reportData.map((row) => (
                  <tr
                    key={row.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {new Date(row.date).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {row.route?.name || "Unknown"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {row.bus?.plate_number || "Unknown"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {row.scheduled_time}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {row.actual_time}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          row.delay_minutes === 0
                            ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                            : row.delay_minutes <= 10
                              ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100"
                              : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                        }`}
                      >
                        {row.delay_minutes === 0
                          ? "On time"
                          : `${row.delay_minutes} min`}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {row.reason || "-"}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <FileText size={48} className="mx-auto mb-4 opacity-50" />
            <p>{t("reports.noDataFound")}</p>
            <p className="text-sm mt-2">{t("reports.adjustFilters")}</p>
          </div>
        )}
      </div>

      {/* Analytics Section */}
      {stats.analytics && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden mt-6">
          <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Delay Pattern Analytics
            </h3>
          </div>

          <div className="p-6 space-y-6">
            {/* Problematic Routes */}
            {stats.analytics.problematicRoutes?.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                  Most Problematic Routes
                </h4>
                <div className="space-y-2">
                  {stats.analytics.problematicRoutes.map(
                    (route: any, index: number) => (
                      <div
                        key={route.routeId}
                        className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                      >
                        <div className="flex items-center">
                          <span className="flex items-center justify-center w-6 h-6 bg-red-100 text-red-800 rounded-full text-xs font-bold mr-3">
                            {index + 1}
                          </span>
                          <span className="font-medium text-gray-900 dark:text-white">
                            {route.routeName}
                          </span>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {route.delayCount} delays
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Avg: {route.avgDelay.toFixed(1)} min
                          </div>
                        </div>
                      </div>
                    ),
                  )}
                </div>
              </div>
            )}

            {/* Time Patterns */}
            {Object.keys(stats.analytics.timePatterns || {}).length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                  Delays by Time of Day
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {Object.entries(stats.analytics.timePatterns).map(
                    ([timeSlot, count]: [string, any]) => (
                      <div
                        key={timeSlot}
                        className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg text-center"
                      >
                        <div className="text-lg font-bold text-gray-900 dark:text-white">
                          {count}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {timeSlot}
                        </div>
                      </div>
                    ),
                  )}
                </div>
              </div>
            )}

            {/* Day Patterns */}
            {Object.keys(stats.analytics.dayPatterns || {}).length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                  Delays by Day of Week
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">
                  {Object.entries(stats.analytics.dayPatterns).map(
                    ([day, count]: [string, any]) => (
                      <div
                        key={day}
                        className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg text-center"
                      >
                        <div className="text-lg font-bold text-gray-900 dark:text-white">
                          {count}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {day}
                        </div>
                      </div>
                    ),
                  )}
                </div>
              </div>
            )}

            {/* Reason Patterns */}
            {Object.keys(stats.analytics.reasonPatterns || {}).length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                  Common Delay Reasons
                </h4>
                <div className="space-y-2">
                  {Object.entries(stats.analytics.reasonPatterns)
                    .sort(([, a], [, b]) => (b as number) - (a as number))
                    .slice(0, 5)
                    .map(([reason, count]: [string, any]) => (
                      <div
                        key={reason}
                        className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                      >
                        <span className="text-gray-900 dark:text-white">
                          {reason}
                        </span>
                        <span className="font-medium text-gray-900 dark:text-white">
                          {count} times
                        </span>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default RouteDelayReport;
