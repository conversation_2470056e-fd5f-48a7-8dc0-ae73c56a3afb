{"test_info": {"timestamp": "2025-06-02T09-35-19-140Z", "total_tests": 38, "passed_tests": 36, "failed_tests": 1, "warning_tests": 1, "skipped_tests": 0, "success_rate": "94.7"}, "detailed_results": [{"test": "البنية الأساسية - src/components", "status": "passed", "message": "المجلد موجود"}, {"test": "البنية الأساسية - src/pages", "status": "passed", "message": "المجلد موجود"}, {"test": "البنية الأساسية - src/contexts", "status": "passed", "message": "المجلد موجود"}, {"test": "البنية الأساسية - src/services", "status": "passed", "message": "المجلد موجود"}, {"test": "البنية الأساسية - src/lib", "status": "passed", "message": "المجلد موجود"}, {"test": "البنية الأساسية - src/hooks", "status": "passed", "message": "المجلد موجود"}, {"test": "البنية الأساسية - src/types", "status": "passed", "message": "المجلد موجود"}, {"test": "البنية الأساسية - src/utils", "status": "passed", "message": "المجلد موجود"}, {"test": "البنية الأساسية - src/config", "status": "passed", "message": "المجلد موجود"}, {"test": "البنية الأساسية - src/i18n", "status": "passed", "message": "المجلد موجود"}, {"test": "ملفات التكوين - package.json", "status": "passed", "message": "المل<PERSON> موجود"}, {"test": "ملفات التكوين - vite.config.ts", "status": "passed", "message": "المل<PERSON> موجود"}, {"test": "ملفات التكوين - tsconfig.json", "status": "passed", "message": "المل<PERSON> موجود"}, {"test": "ملفات التكوين - tailwind.config.js", "status": "passed", "message": "المل<PERSON> موجود"}, {"test": "ملفات التكوين - .env.example", "status": "warning", "message": "ملف اختياري مفقود"}, {"test": "ملفات التكوين - src/main.tsx", "status": "passed", "message": "المل<PERSON> موجود"}, {"test": "ملفات التكوين - src/App.tsx", "status": "passed", "message": "المل<PERSON> موجود"}, {"test": "ملفات التكوين - src/index.css", "status": "passed", "message": "المل<PERSON> موجود"}, {"test": "مسارات الاستيراد - src/main.tsx", "status": "passed", "message": "جميع المسارات صحيحة (12)"}, {"test": "مسارات الاستيراد - src/App.tsx", "status": "failed", "message": "2 مسار غير صحيح من أصل 39"}, {"test": "المكونات الأساسية - src/contexts/AuthContext.tsx", "status": "passed", "message": "المكون يحتوي على Context صحيح"}, {"test": "المكونات الأساسية - src/contexts/ThemeContext.tsx", "status": "passed", "message": "المكون يحتوي على Context صحيح"}, {"test": "المكونات الأساسية - src/contexts/DatabaseContext.tsx", "status": "passed", "message": "المكون يحتوي على Context صحيح"}, {"test": "المكونات الأساسية - src/contexts/NotificationsContext.tsx", "status": "passed", "message": "المكون يحتوي على Context صحيح"}, {"test": "المكونات الأساسية - src/contexts/CustomThemeContext.tsx", "status": "passed", "message": "المكون يحتوي على Context صحيح"}, {"test": "الصفحات الرئيسية - src/pages/login/LoginPage.tsx", "status": "passed", "message": "الصفحة تحتوي على مكون صحيح"}, {"test": "الصفحات الرئيسية - src/pages/dashboard/DashboardPage.tsx", "status": "passed", "message": "الصفحة تحتوي على مكون صحيح"}, {"test": "الصفحات الرئيسية - src/pages/dashboard/BusesPage.tsx", "status": "passed", "message": "الصفحة تحتوي على مكون صحيح"}, {"test": "الصفحات الرئيسية - src/pages/dashboard/StudentsPage.tsx", "status": "passed", "message": "الصفحة تحتوي على مكون صحيح"}, {"test": "الصفحات الرئيسية - src/pages/dashboard/RoutesPage.tsx", "status": "passed", "message": "الصفحة تحتوي على مكون صحيح"}, {"test": "الخدمات - src/lib/supabase.ts", "status": "passed", "message": "الخدمة موجودة"}, {"test": "الخدمات - src/services/CentralizedPermissionService.ts", "status": "passed", "message": "الخدمة موجودة"}, {"test": "الخدمات - src/services/DatabaseService.ts", "status": "passed", "message": "الخدمة موجودة"}, {"test": "الخدمات - src/services/TenantService.ts", "status": "passed", "message": "الخدمة موجودة"}, {"test": "اتصال قاعدة البيانات - Supabase", "status": "passed", "message": "إعداد Supabase صحيح"}, {"test": "ميزات الأمان - src/services/security", "status": "passed", "message": "مكون الأمان موجود"}, {"test": "ميزات الأمان - src/components/auth", "status": "passed", "message": "مكون الأمان موجود"}, {"test": "ميزات الأمان - src/middleware/authMiddleware.ts", "status": "passed", "message": "مكون الأمان موجود"}], "errors": [], "warnings": [], "recommendations": ["إصلاح الاختبارات الفاشلة أولاً", "مراجعة التحذيرات وإصلاحها إن أمكن"]}