/**
 * لوحة التحكم الاحترافية - Professional Dashboard
 * تجمع جميع مكونات الإحصائيات المتقدمة
 */

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useAuth } from "../../contexts/AuthContext";
import { usePermissions } from "../../hooks/usePermissions";
import { ProfessionalStats } from "./ProfessionalStats";
import { SimpleStats } from "./SimpleStats";
import { SystemStatusPanel } from "./SystemStatusPanel";
import { UserDistributionChart } from "./UserDistributionChart";
import { LiveMap } from "../map/LiveMap";
import {
  BarChart3,
  PieChart,
  Activity,
  Shield,
  Users,
  MapPin,
  Download,
  RefreshCw,
  Calendar,
  Filter,
  Settings,
  Bell,
  TrendingUp,
} from "lucide-react";

interface DashboardTab {
  id: string;
  name: string;
  icon: React.ReactNode;
  component: React.ReactNode;
  permission?: string;
}

export const ProfessionalDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const { isAdmin, isSchoolManager, isSupervisor, isDriver, isParent } = usePermissions();
  const [activeTab, setActiveTab] = useState("overview");
  const [isRefreshing, setIsRefreshing] = useState(false);

  // تحديد التبويبات المتاحة حسب الدور
  const availableTabs: DashboardTab[] = [
    {
      id: "overview",
      name: "نظرة عامة",
      icon: <BarChart3 className="h-5 w-5" />,
      component: <SimpleStats />,
    },
    {
      id: "users",
      name: "توزيع المستخدمين",
      icon: <Users className="h-5 w-5" />,
      component: <UserDistributionChart />,
    },
    {
      id: "system",
      name: "حالة النظام",
      icon: <Shield className="h-5 w-5" />,
      component: <SystemStatusPanel />,
    },
    {
      id: "tracking",
      name: "التتبع المباشر",
      icon: <MapPin className="h-5 w-5" />,
      component: (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              التتبع المباشر للحافلات
            </h2>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
              مباشر
            </span>
          </div>
          <LiveMap />
        </div>
      ),
    },
  ];

  // تصفية التبويبات حسب الصلاحيات
  const filteredTabs = availableTabs.filter(tab => {
    switch (tab.id) {
      case "overview":
        return true; // متاح للجميع
      case "users":
        return isAdmin || isSchoolManager;
      case "system":
        return isAdmin || isSchoolManager;
      case "tracking":
        return !isParent; // متاح لجميع الأدوار عدا أولياء الأمور
      default:
        return true;
    }
  });

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // محاكاة تحديث البيانات
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRefreshing(false);
  };

  const handleExport = () => {
    // محاكاة تصدير البيانات
    console.log("Exporting dashboard data...");
  };

  return (
    <div className="space-y-6">
      {/* رأس لوحة التحكم */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {tenant?.settings?.branding?.schoolName || tenant?.name || "لوحة التحكم"}
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            مرحباً، {user?.name} - {new Date().toLocaleDateString('ar')}
          </p>
          {tenant?.settings?.branding?.tagline && (
            <p className="mt-1 text-xs text-gray-400 dark:text-gray-500">
              {tenant.settings.branding.tagline}
            </p>
          )}
        </div>

        {/* أدوات التحكم */}
        <div className="mt-4 md:mt-0 flex items-center space-x-3 space-x-reverse">
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
            تحديث
          </button>

          {(isAdmin || isSchoolManager) && (
            <button
              onClick={handleExport}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 border border-transparent rounded-md shadow-sm transition-colors"
            >
              <Download className="mr-2 h-4 w-4" />
              تصدير
            </button>
          )}

          <div className="inline-flex rounded-md shadow-sm">
            <span className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md">
              <TrendingUp className="mr-2 h-4 w-4 text-green-500" />
              {new Date().toLocaleDateString('ar')}
            </span>
          </div>
        </div>
      </div>

      {/* تبويبات لوحة التحكم */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8 space-x-reverse">
          {filteredTabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? "border-primary-500 text-primary-600 dark:text-primary-400"
                  : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
              }`}
            >
              {tab.icon}
              <span className="mr-2">{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* محتوى التبويب النشط */}
      <div className="min-h-[600px]">
        {filteredTabs.find(tab => tab.id === activeTab)?.component}
      </div>

      {/* تنبيهات سريعة */}
      {(isAdmin || isSchoolManager) && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-start">
            <Bell className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                تنبيهات سريعة
              </h3>
              <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
                <ul className="list-disc list-inside space-y-1">
                  <li>جميع الأنظمة تعمل بشكل طبيعي</li>
                  <li>لا توجد تنبيهات أمنية حرجة</li>
                  <li>معدل الأداء ممتاز (98.5%)</li>
                  {isAdmin && <li>آخر نسخة احتياطية: اليوم الساعة 3:00 ص</li>}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* معلومات إضافية للسائقين */}
      {isDriver && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-start">
            <Activity className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                معلومات السائق
              </h3>
              <div className="mt-2 text-sm text-green-700 dark:text-green-300">
                <p>حالة الحافلة: جاهزة للخدمة</p>
                <p>المسار التالي: يبدأ في 30 دقيقة</p>
                <p>عدد الطلاب المتوقع: 25 طالب</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* معلومات إضافية لأولياء الأمور */}
      {isParent && (
        <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
          <div className="flex items-start">
            <Users className="h-5 w-5 text-purple-600 dark:text-purple-400 mt-0.5 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-purple-800 dark:text-purple-200">
                معلومات ولي الأمر
              </h3>
              <div className="mt-2 text-sm text-purple-700 dark:text-purple-300">
                <p>حالة الأطفال: جميعهم في المدرسة</p>
                <p>الحافلة القادمة: تصل في 45 دقيقة</p>
                <p>لا توجد تنبيهات جديدة</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// دالة مساعدة للـ className
function cn(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ');
}

export default ProfessionalDashboard;
