import React, { useRef, useEffect, useState, useCallback } from "react";
import Map, { <PERSON>er, NavigationControl, Source, Layer } from "react-map-gl";
import type { MapRef, ViewState } from "react-map-gl";
import "mapbox-gl/dist/mapbox-gl.css";

const MAPBOX_TOKEN =
  import.meta.env.VITE_MAPBOX_TOKEN ||
  "pk.eyJ1IjoidGVtcG8tZGV2IiwiYSI6ImNsejBxbzBqZjBhcWsya3M4ZGZqZGZqZGYifQ.example";

interface MapboxMapProps {
  initialViewState?: Partial<ViewState>;
  onMapLoad?: (map: MapRef) => void;
  onViewStateChange?: (viewState: ViewState) => void;
  children?: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
  showNavigation?: boolean;
  mapStyle?: string;
  onClick?: (event: any) => void;
  onMove?: (event: any) => void;
}

const defaultViewState: ViewState = {
  latitude: 30.0444,
  longitude: 31.2357,
  zoom: 11,
  bearing: 0,
  pitch: 0,
  padding: { top: 0, bottom: 0, left: 0, right: 0 },
};

export const MapboxMap: React.FC<MapboxMapProps> = ({
  initialViewState = {},
  onMapLoad,
  onViewStateChange,
  children,
  style = { width: "100%", height: "100%" },
  className = "",
  showNavigation = true,
  mapStyle = "mapbox://styles/mapbox/streets-v12",
  onClick,
  onMove,
}) => {
  const mapRef = useRef<MapRef>(null);
  const [viewState, setViewState] = useState<ViewState>({
    ...defaultViewState,
    ...initialViewState,
  });
  const [isLoaded, setIsLoaded] = useState(false);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    if (onMapLoad && mapRef.current) {
      onMapLoad(mapRef.current);
    }
  }, [onMapLoad]);

  const handleMove = useCallback(
    (evt: any) => {
      const newViewState = evt.viewState;
      setViewState(newViewState);
      if (onViewStateChange) {
        onViewStateChange(newViewState);
      }
      if (onMove) {
        onMove(evt);
      }
    },
    [onViewStateChange, onMove],
  );

  const handleClick = useCallback(
    (evt: any) => {
      if (onClick) {
        onClick(evt);
      }
    },
    [onClick],
  );

  if (!MAPBOX_TOKEN || MAPBOX_TOKEN.includes("example")) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-lg ${className}`}
        style={style}
      >
        <div className="text-center p-4">
          <div className="w-full h-full bg-gradient-to-br from-blue-100 to-green-100 dark:from-blue-900 dark:to-green-900 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <p className="text-gray-600 dark:text-gray-300 mb-2 font-medium">
                خريطة تفاعلية
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                سيتم عرض الخريطة هنا عند تكوين Mapbox
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className} style={style}>
      <Map
        ref={mapRef}
        {...viewState}
        onMove={handleMove}
        onClick={handleClick}
        onLoad={handleLoad}
        mapStyle={mapStyle}
        mapboxAccessToken={MAPBOX_TOKEN}
        style={{ width: "100%", height: "100%" }}
        attributionControl={false}
      >
        {showNavigation && <NavigationControl position="top-right" />}
        {isLoaded && children}
      </Map>
    </div>
  );
};

export default MapboxMap;
