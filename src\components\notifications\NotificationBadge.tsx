import React, { useEffect, useState, useMemo } from "react";
import { Bell } from "lucide-react";
import { useNotifications } from "../../contexts/NotificationsContext";

interface NotificationBadgeProps {
  onClick?: () => void;
  className?: string;
  size?: "sm" | "md" | "lg";
  showCount?: boolean;
}

export const NotificationBadge: React.FC<NotificationBadgeProps> = ({
  onClick,
  className = "",
  size = "md",
  showCount = true,
}) => {
  const { unreadCount, notificationIndicators, clearIndicator, notifications } =
    useNotifications();

  // Sync badge count with actual unread notifications
  const actualUnreadCount = React.useMemo(() => {
    return notifications.filter((n) => !n.read).length;
  }, [notifications]);
  const [isAnimating, setIsAnimating] = useState(false);
  const [hasNewNotification, setHasNewNotification] = useState(false);
  const [pulseAnimation, setPulseAnimation] = useState(false);

  // Check for new notifications
  useEffect(() => {
    if (!notificationIndicators) return;

    const hasNew = notificationIndicators.get("global") || false;
    if (hasNew && !hasNewNotification) {
      setHasNewNotification(true);
      setIsAnimating(true);
      setPulseAnimation(true);

      // Stop bounce animation after 2 seconds
      setTimeout(() => {
        setIsAnimating(false);
      }, 2000);

      // Stop pulse animation after 5 seconds
      setTimeout(() => {
        setPulseAnimation(false);
      }, 5000);
    }
  }, [notificationIndicators, hasNewNotification]);

  const handleClick = () => {
    if (hasNewNotification && clearIndicator) {
      setHasNewNotification(false);
      clearIndicator("global");
    }
    onClick?.();
  };

  const sizeClasses = {
    sm: "p-1",
    md: "p-2",
    lg: "p-3",
  };

  const iconSizes = {
    sm: 16,
    md: 20,
    lg: 24,
  };

  const badgeSizes = {
    sm: "h-3 w-3 text-xs",
    md: "h-4 w-4 text-xs",
    lg: "h-5 w-5 text-sm",
  };

  return (
    <button
      onClick={handleClick}
      className={`relative text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-all duration-200 ${sizeClasses[size]} ${className} ${
        isAnimating ? "animate-bounce" : ""
      } ${hasNewNotification ? "text-primary-600 dark:text-primary-400" : ""} ${
        pulseAnimation ? "animate-pulse-slow" : ""
      }`}
      aria-label={`Notifications${actualUnreadCount > 0 ? ` (${actualUnreadCount} unread)` : ""}`}
    >
      <Bell
        size={iconSizes[size]}
        className={`transition-all duration-200 ${
          hasNewNotification ? "fill-current" : ""
        } ${pulseAnimation ? "drop-shadow-lg" : ""}`}
      />
      {showCount && actualUnreadCount > 0 && (
        <span
          className={`absolute -top-1 -right-1 bg-error-500 text-white rounded-full flex items-center justify-center font-medium ${badgeSizes[size]} ${
            isAnimating ? "animate-pulse scale-110" : "animate-pulse"
          } transition-transform duration-200 shadow-lg ring-2 ring-white dark:ring-gray-800`}
        >
          {actualUnreadCount > 99 ? "99+" : actualUnreadCount}
        </span>
      )}
      {hasNewNotification && actualUnreadCount === 0 && (
        <>
          <span className="absolute -top-1 -right-1 w-3 h-3 bg-primary-500 rounded-full animate-ping" />
          <span className="absolute -top-1 -right-1 w-3 h-3 bg-primary-500 rounded-full" />
        </>
      )}
    </button>
  );
};

export default NotificationBadge;
