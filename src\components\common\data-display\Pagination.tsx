/**
 * Pagination Component
 * Reusable pagination component with customizable options
 * Phase 2: Application Structure Reorganization
 */

import React, { useMemo } from 'react';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';
import { cn } from '../../../utils/cn';

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showPageSizeSelector?: boolean;
  showItemsInfo?: boolean;
  showFirstLast?: boolean;
  maxVisiblePages?: number;
  pageSizeOptions?: number[];
}

/**
 * Pagination Component
 * Implements Single Responsibility Principle - handles pagination logic only
 */
export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  pageSize,
  totalItems,
  onPageChange,
  onPageSizeChange,
  className,
  size = 'md',
  showPageSizeSelector = true,
  showItemsInfo = true,
  showFirstLast = true,
  maxVisiblePages = 7,
  pageSizeOptions = [10, 25, 50, 100],
}) => {
  /**
   * Calculate visible page numbers
   */
  const visiblePages = useMemo(() => {
    const pages: (number | 'ellipsis')[] = [];
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Calculate start and end of visible range
      const halfVisible = Math.floor(maxVisiblePages / 2);
      let start = Math.max(1, currentPage - halfVisible);
      let end = Math.min(totalPages, currentPage + halfVisible);
      
      // Adjust if we're near the beginning or end
      if (currentPage <= halfVisible) {
        end = Math.min(totalPages, maxVisiblePages);
      } else if (currentPage > totalPages - halfVisible) {
        start = Math.max(1, totalPages - maxVisiblePages + 1);
      }
      
      // Add first page and ellipsis if needed
      if (start > 1) {
        pages.push(1);
        if (start > 2) {
          pages.push('ellipsis');
        }
      }
      
      // Add visible pages
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      
      // Add ellipsis and last page if needed
      if (end < totalPages) {
        if (end < totalPages - 1) {
          pages.push('ellipsis');
        }
        pages.push(totalPages);
      }
    }
    
    return pages;
  }, [currentPage, totalPages, maxVisiblePages]);

  /**
   * Calculate items range
   */
  const itemsRange = useMemo(() => {
    const start = Math.min((currentPage - 1) * pageSize + 1, totalItems);
    const end = Math.min(currentPage * pageSize, totalItems);
    return { start, end };
  }, [currentPage, pageSize, totalItems]);

  /**
   * Size classes
   */
  const sizeClasses = {
    sm: {
      button: 'px-2 py-1 text-xs',
      select: 'px-2 py-1 text-xs',
      text: 'text-xs',
    },
    md: {
      button: 'px-3 py-2 text-sm',
      select: 'px-3 py-2 text-sm',
      text: 'text-sm',
    },
    lg: {
      button: 'px-4 py-2 text-base',
      select: 'px-4 py-2 text-base',
      text: 'text-base',
    },
  };

  /**
   * Handle page change with validation
   */
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page);
    }
  };

  /**
   * Handle page size change
   */
  const handlePageSizeChange = (newSize: number) => {
    if (newSize !== pageSize) {
      onPageSizeChange(newSize);
    }
  };

  if (totalPages <= 1 && !showPageSizeSelector && !showItemsInfo) {
    return null;
  }

  return (
    <div className={cn('flex items-center justify-between', className)}>
      {/* Items Info */}
      {showItemsInfo && (
        <div className={cn('text-gray-700 dark:text-gray-300', sizeClasses[size].text)}>
          {totalItems > 0 ? (
            <>
              Showing {itemsRange.start} to {itemsRange.end} of {totalItems} results
            </>
          ) : (
            'No results found'
          )}
        </div>
      )}

      {/* Pagination Controls */}
      <div className="flex items-center space-x-2">
        {/* Page Size Selector */}
        {showPageSizeSelector && (
          <div className="flex items-center space-x-2">
            <span className={cn('text-gray-700 dark:text-gray-300', sizeClasses[size].text)}>
              Show
            </span>
            <select
              value={pageSize}
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
              className={cn(
                'border border-gray-300 rounded-md bg-white dark:bg-gray-800',
                'focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'dark:border-gray-600 dark:text-gray-300',
                sizeClasses[size].select
              )}
            >
              {pageSizeOptions.map(option => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Page Navigation */}
        {totalPages > 1 && (
          <div className="flex items-center space-x-1">
            {/* First Page */}
            {showFirstLast && currentPage > 1 && (
              <button
                onClick={() => handlePageChange(1)}
                className={cn(
                  'border border-gray-300 rounded-md bg-white hover:bg-gray-50',
                  'dark:bg-gray-800 dark:border-gray-600 dark:hover:bg-gray-700',
                  'disabled:opacity-50 disabled:cursor-not-allowed',
                  'transition-colors duration-200',
                  sizeClasses[size].button
                )}
              >
                First
              </button>
            )}

            {/* Previous Page */}
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage <= 1}
              className={cn(
                'border border-gray-300 rounded-md bg-white hover:bg-gray-50',
                'dark:bg-gray-800 dark:border-gray-600 dark:hover:bg-gray-700',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                'transition-colors duration-200',
                sizeClasses[size].button
              )}
            >
              <ChevronLeft className="w-4 h-4" />
            </button>

            {/* Page Numbers */}
            {visiblePages.map((page, index) => (
              <React.Fragment key={index}>
                {page === 'ellipsis' ? (
                  <span className={cn(
                    'flex items-center justify-center',
                    sizeClasses[size].button
                  )}>
                    <MoreHorizontal className="w-4 h-4 text-gray-400" />
                  </span>
                ) : (
                  <button
                    onClick={() => handlePageChange(page)}
                    className={cn(
                      'border rounded-md transition-colors duration-200',
                      sizeClasses[size].button,
                      page === currentPage
                        ? 'bg-blue-600 border-blue-600 text-white'
                        : 'border-gray-300 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:hover:bg-gray-700'
                    )}
                  >
                    {page}
                  </button>
                )}
              </React.Fragment>
            ))}

            {/* Next Page */}
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage >= totalPages}
              className={cn(
                'border border-gray-300 rounded-md bg-white hover:bg-gray-50',
                'dark:bg-gray-800 dark:border-gray-600 dark:hover:bg-gray-700',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                'transition-colors duration-200',
                sizeClasses[size].button
              )}
            >
              <ChevronRight className="w-4 h-4" />
            </button>

            {/* Last Page */}
            {showFirstLast && currentPage < totalPages && (
              <button
                onClick={() => handlePageChange(totalPages)}
                className={cn(
                  'border border-gray-300 rounded-md bg-white hover:bg-gray-50',
                  'dark:bg-gray-800 dark:border-gray-600 dark:hover:bg-gray-700',
                  'disabled:opacity-50 disabled:cursor-not-allowed',
                  'transition-colors duration-200',
                  sizeClasses[size].button
                )}
              >
                Last
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Simple Pagination Component
 * Minimal version with just prev/next buttons
 */
export interface SimplePaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const SimplePagination: React.FC<SimplePaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  className,
  size = 'md',
}) => {
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-2 text-base',
  };

  if (totalPages <= 1) return null;

  return (
    <div className={cn('flex items-center justify-center space-x-2', className)}>
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage <= 1}
        className={cn(
          'flex items-center space-x-1 border border-gray-300 rounded-md',
          'bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed',
          'transition-colors duration-200',
          sizeClasses[size]
        )}
      >
        <ChevronLeft className="w-4 h-4" />
        <span>Previous</span>
      </button>

      <span className={cn('text-gray-700', sizeClasses[size])}>
        Page {currentPage} of {totalPages}
      </span>

      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage >= totalPages}
        className={cn(
          'flex items-center space-x-1 border border-gray-300 rounded-md',
          'bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed',
          'transition-colors duration-200',
          sizeClasses[size]
        )}
      >
        <span>Next</span>
        <ChevronRight className="w-4 h-4" />
      </button>
    </div>
  );
};
