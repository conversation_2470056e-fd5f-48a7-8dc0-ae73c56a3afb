import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { X, Bus, User, Hash, Users, MapPin } from "lucide-react";
import { Button } from "../ui/Button";
import { supabase } from "../../lib/supabase";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import type { Tables } from "../../lib/api";

interface BusModalProps {
  isOpen: boolean;
  onClose: (refreshData?: boolean) => void;
  bus?: Tables<"buses"> | null;
  mode: "add" | "edit";
}

export const BusModal: React.FC<BusModalProps> = ({
  isOpen,
  onClose,
  bus,
  mode,
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { users } = useDatabase();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    plate_number: "",
    capacity: "",
    driver_id: "",
    is_active: true,
    notes: "",
  });
  const [error, setError] = useState("");

  // Get available drivers (users with driver role)
  const availableDrivers = users.filter((u) => u.role === "driver");

  useEffect(() => {
    if (mode === "edit" && bus) {
      setFormData({
        plate_number: bus.plate_number || "",
        capacity: bus.capacity?.toString() || "",
        driver_id: bus.driver_id || "",
        is_active: bus.is_active ?? true,
        notes: bus.notes || "",
      });
    } else {
      setFormData({
        plate_number: "",
        capacity: "",
        driver_id: "",
        is_active: true,
        notes: "",
      });
    }
    setError("");
  }, [mode, bus, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.plate_number || !formData.capacity) {
      setError(t("common.requiredFieldsMissing"));
      return;
    }

    // For admin users, we need to get tenant_id from form or use first available tenant
    let targetTenantId = user?.tenant_id;

    if (!targetTenantId && user?.role === 'admin') {
      // For admin, we should get tenant_id from a form field or use first available
      // For now, let's get the first available tenant
      try {
        const { data: tenants, error } = await supabase
          .from('tenants')
          .select('id')
          .eq('is_active', true)
          .limit(1);

        if (error) throw error;

        if (tenants && tenants.length > 0) {
          targetTenantId = tenants[0].id;
        } else {
          setError("No active school found. Please create a school first.");
          return;
        }
      } catch (err) {
        console.error("Error fetching tenants:", err);
        setError("Failed to get school information");
        return;
      }
    }

    if (!targetTenantId) {
      setError("No school assignment found");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const busData = {
        plate_number: formData.plate_number.trim(),
        capacity: parseInt(formData.capacity),
        driver_id: formData.driver_id || null,
        is_active: formData.is_active,
        notes: formData.notes.trim() || null,
        tenant_id: targetTenantId,
      };

      console.log("🔄 BusModal: Creating/updating bus with data:", {
        ...busData,
        user_role: user?.role,
        user_tenant: user?.tenant_id
      });

      if (mode === "add") {
        const { error: insertError } = await supabase
          .from("buses")
          .insert([busData]);

        if (insertError) throw insertError;
      } else if (mode === "edit" && bus) {
        const { error: updateError } = await supabase
          .from("buses")
          .update(busData)
          .eq("id", bus.id);

        if (updateError) throw updateError;
      }

      onClose(true);
    } catch (err) {
      console.error("Error saving bus:", err);
      setError(
        err instanceof Error
          ? err.message
          : mode === "add"
            ? t("buses.addBusError")
            : t("buses.updateBusError"),
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md">
        <div className="flex justify-between items-center border-b border-gray-200 dark:border-gray-700 p-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {mode === "add" ? t("buses.addBus") : t("buses.editBus")}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onClose()}
            aria-label="Close"
          >
            <X size={20} />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="p-4">
          {error && (
            <div className="bg-error-50 dark:bg-error-900/20 border border-error-200 dark:border-error-800 rounded-md p-3 mb-4">
              <p className="text-sm text-error-600 dark:text-error-400">
                {error}
              </p>
            </div>
          )}

          <div className="space-y-4">
            <div>
              <label
                htmlFor="plate_number"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                {t("buses.plateNumber")} *
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Hash size={16} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  id="plate_number"
                  value={formData.plate_number}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      plate_number: e.target.value,
                    }))
                  }
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  placeholder={t("buses.plateNumberPlaceholder")}
                  required
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="capacity"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                {t("buses.capacity")} *
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Users size={16} className="text-gray-400" />
                </div>
                <input
                  type="number"
                  id="capacity"
                  value={formData.capacity}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      capacity: e.target.value,
                    }))
                  }
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  placeholder="50"
                  min="1"
                  max="100"
                  required
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="driver_id"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                {t("buses.driver")} ({t("common.optional")})
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User size={16} className="text-gray-400" />
                </div>
                <select
                  id="driver_id"
                  value={formData.driver_id}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      driver_id: e.target.value,
                    }))
                  }
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">{t("buses.selectDriver")}</option>
                  {availableDrivers.map((driver) => (
                    <option key={driver.id} value={driver.id}>
                      {driver.name} - {driver.email}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      is_active: e.target.checked,
                    }))
                  }
                  className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  {t("buses.isActive")}
                </span>
              </label>
            </div>

            <div>
              <label
                htmlFor="notes"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                {t("common.notes")} ({t("common.optional")})
              </label>
              <textarea
                id="notes"
                value={formData.notes}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, notes: e.target.value }))
                }
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                rows={3}
                placeholder={t("buses.notesPlaceholder")}
              />
            </div>
          </div>

          <div className="mt-6 flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => onClose()}
              disabled={isLoading}
            >
              {t("common.cancel")}
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin mr-2 h-4 w-4 border-b-2 border-white rounded-full"></div>
                  {mode === "add" ? t("common.adding") : t("common.updating")}
                </div>
              ) : mode === "add" ? (
                t("buses.addBus")
              ) : (
                t("buses.updateBus")
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
