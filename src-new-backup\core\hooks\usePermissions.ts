/**
 * Hook للصلاحيات - متوافق مع النظام القديم والجديد
 * Permissions Hook - Compatible with old and new systems
 */

import { useMemo } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { UserRole } from '../types';

// تعريف الصلاحيات للتوافق مع النظام القديم
export enum Permission {
  // صلاحيات المستخدمين
  USERS_VIEW = 'users:view',
  USERS_CREATE = 'users:create',
  USERS_EDIT = 'users:edit',
  USERS_DELETE = 'users:delete',
  
  // صلاحيات الحافلات
  BUSES_VIEW = 'buses:view',
  BUSES_CREATE = 'buses:create',
  BUSES_EDIT = 'buses:edit',
  BUSES_DELETE = 'buses:delete',
  
  // صلاحيات الطلاب
  STUDENTS_VIEW = 'students:view',
  STUDENTS_CREATE = 'students:create',
  STUDENTS_EDIT = 'students:edit',
  STUDENTS_DELETE = 'students:delete',
  
  // صلاحيات المسارات
  ROUTES_VIEW = 'routes:view',
  ROUTES_CREATE = 'routes:create',
  ROUTES_EDIT = 'routes:edit',
  ROUTES_DELETE = 'routes:delete',
  
  // صلاحيات الحضور
  ATTENDANCE_VIEW = 'attendance:view',
  ATTENDANCE_CREATE = 'attendance:create',
  ATTENDANCE_EDIT = 'attendance:edit',
  ATTENDANCE_DELETE = 'attendance:delete',
  
  // صلاحيات الإشعارات
  NOTIFICATIONS_VIEW = 'notifications:view',
  NOTIFICATIONS_CREATE = 'notifications:create',
  NOTIFICATIONS_EDIT = 'notifications:edit',
  NOTIFICATIONS_DELETE = 'notifications:delete',
  NOTIFICATIONS_MANAGE_TEMPLATES = 'notifications:manage_templates',
  
  // صلاحيات التقارير
  REPORTS_VIEW = 'reports:view',
  REPORTS_EXPORT = 'reports:export',
  
  // صلاحيات النظام
  SYSTEM_SETTINGS = 'system:settings',
  SYSTEM_BACKUP = 'system:backup',
  
  // صلاحيات المدارس
  TENANTS_VIEW = 'tenants:view',
  TENANTS_CREATE = 'tenants:create',
  TENANTS_EDIT = 'tenants:edit',
  TENANTS_DELETE = 'tenants:delete',
}

// مصفوفة الصلاحيات حسب الدور
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  admin: [
    // الأدمن له جميع الصلاحيات
    Permission.USERS_VIEW,
    Permission.USERS_CREATE,
    Permission.USERS_EDIT,
    Permission.USERS_DELETE,
    Permission.BUSES_VIEW,
    Permission.BUSES_CREATE,
    Permission.BUSES_EDIT,
    Permission.BUSES_DELETE,
    Permission.STUDENTS_VIEW,
    Permission.STUDENTS_CREATE,
    Permission.STUDENTS_EDIT,
    Permission.STUDENTS_DELETE,
    Permission.ROUTES_VIEW,
    Permission.ROUTES_CREATE,
    Permission.ROUTES_EDIT,
    Permission.ROUTES_DELETE,
    Permission.ATTENDANCE_VIEW,
    Permission.ATTENDANCE_CREATE,
    Permission.ATTENDANCE_EDIT,
    Permission.ATTENDANCE_DELETE,
    Permission.NOTIFICATIONS_VIEW,
    Permission.NOTIFICATIONS_CREATE,
    Permission.NOTIFICATIONS_EDIT,
    Permission.NOTIFICATIONS_DELETE,
    Permission.NOTIFICATIONS_MANAGE_TEMPLATES,
    Permission.REPORTS_VIEW,
    Permission.REPORTS_EXPORT,
    Permission.SYSTEM_SETTINGS,
    Permission.SYSTEM_BACKUP,
    Permission.TENANTS_VIEW,
    Permission.TENANTS_CREATE,
    Permission.TENANTS_EDIT,
    Permission.TENANTS_DELETE,
  ],
  
  school_manager: [
    // مدير المدرسة
    Permission.USERS_VIEW,
    Permission.USERS_CREATE,
    Permission.USERS_EDIT,
    Permission.BUSES_VIEW,
    Permission.BUSES_CREATE,
    Permission.BUSES_EDIT,
    Permission.BUSES_DELETE,
    Permission.STUDENTS_VIEW,
    Permission.STUDENTS_CREATE,
    Permission.STUDENTS_EDIT,
    Permission.STUDENTS_DELETE,
    Permission.ROUTES_VIEW,
    Permission.ROUTES_CREATE,
    Permission.ROUTES_EDIT,
    Permission.ROUTES_DELETE,
    Permission.ATTENDANCE_VIEW,
    Permission.ATTENDANCE_CREATE,
    Permission.ATTENDANCE_EDIT,
    Permission.NOTIFICATIONS_VIEW,
    Permission.NOTIFICATIONS_CREATE,
    Permission.NOTIFICATIONS_EDIT,
    Permission.NOTIFICATIONS_MANAGE_TEMPLATES,
    Permission.REPORTS_VIEW,
    Permission.REPORTS_EXPORT,
    Permission.TENANTS_VIEW,
    Permission.TENANTS_EDIT,
  ],
  
  supervisor: [
    // المشرف
    Permission.USERS_VIEW,
    Permission.BUSES_VIEW,
    Permission.BUSES_EDIT,
    Permission.STUDENTS_VIEW,
    Permission.STUDENTS_EDIT,
    Permission.ROUTES_VIEW,
    Permission.ROUTES_EDIT,
    Permission.ATTENDANCE_VIEW,
    Permission.ATTENDANCE_CREATE,
    Permission.ATTENDANCE_EDIT,
    Permission.NOTIFICATIONS_VIEW,
    Permission.REPORTS_VIEW,
  ],
  
  driver: [
    // السائق
    Permission.BUSES_VIEW,
    Permission.STUDENTS_VIEW,
    Permission.ROUTES_VIEW,
    Permission.ATTENDANCE_VIEW,
    Permission.ATTENDANCE_CREATE,
    Permission.NOTIFICATIONS_VIEW,
  ],
  
  parent: [
    // ولي الأمر
    Permission.STUDENTS_VIEW,
    Permission.ATTENDANCE_VIEW,
    Permission.NOTIFICATIONS_VIEW,
    Permission.BUSES_VIEW,
    Permission.ROUTES_VIEW,
  ],
  
  student: [
    // الطالب
    Permission.ATTENDANCE_VIEW,
    Permission.NOTIFICATIONS_VIEW,
    Permission.BUSES_VIEW,
    Permission.ROUTES_VIEW,
  ],
};

/**
 * Hook للتعامل مع الصلاحيات
 */
export const usePermissions = () => {
  const { user } = useAuth();

  const permissions = useMemo(() => {
    if (!user?.role) {
      return {
        hasPermission: () => false,
        hasDataScope: () => false,
        canPerformAction: () => false,
        canManageRole: () => false,
        getRolePermissions: () => [],
        getRoleDataScopes: () => [],
        isAdmin: false,
        isSchoolManager: false,
        isSupervisor: false,
        isDriver: false,
        isParent: false,
        isStudent: false,
      };
    }

    const userPermissions = ROLE_PERMISSIONS[user.role] || [];

    return {
      /**
       * التحقق من وجود صلاحية معينة
       */
      hasPermission: (permission: Permission): boolean => {
        return userPermissions.includes(permission);
      },

      /**
       * التحقق من نطاق البيانات (للتوافق مع النظام القديم)
       */
      hasDataScope: (scope: string): boolean => {
        // تنفيذ مبسط للتوافق
        switch (user.role) {
          case 'admin':
            return true;
          case 'school_manager':
            return scope === 'tenant' || scope === 'own';
          case 'supervisor':
            return scope === 'tenant' || scope === 'own';
          case 'driver':
            return scope === 'assigned' || scope === 'own';
          case 'parent':
            return scope === 'children' || scope === 'own';
          case 'student':
            return scope === 'own';
          default:
            return false;
        }
      },

      /**
       * التحقق من إمكانية تنفيذ عملية معينة
       */
      canPerformAction: (resource: string, action: string): boolean => {
        const permission = `${resource}:${action}` as Permission;
        return userPermissions.includes(permission);
      },

      /**
       * التحقق من إمكانية إدارة دور معين
       */
      canManageRole: (targetRole: UserRole): boolean => {
        if (user.role === 'admin') return true;
        if (user.role === 'school_manager') {
          return !['admin'].includes(targetRole);
        }
        return false;
      },

      /**
       * الحصول على صلاحيات الدور
       */
      getRolePermissions: (): Permission[] => {
        return userPermissions;
      },

      /**
       * الحصول على نطاقات البيانات للدور
       */
      getRoleDataScopes: (): string[] => {
        switch (user.role) {
          case 'admin':
            return ['global', 'tenant', 'own'];
          case 'school_manager':
            return ['tenant', 'own'];
          case 'supervisor':
            return ['tenant', 'own'];
          case 'driver':
            return ['assigned', 'own'];
          case 'parent':
            return ['children', 'own'];
          case 'student':
            return ['own'];
          default:
            return [];
        }
      },

      // خصائص سريعة للأدوار
      isAdmin: user.role === 'admin',
      isSchoolManager: user.role === 'school_manager',
      isSupervisor: user.role === 'supervisor',
      isDriver: user.role === 'driver',
      isParent: user.role === 'parent',
      isStudent: user.role === 'student',
    };
  }, [user]);

  return permissions;
};

export default usePermissions;
