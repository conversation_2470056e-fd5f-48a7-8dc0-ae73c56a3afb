/**
 * Enhanced Button Component
 * Design system button with full token support and RTL compatibility
 * Phase 3: UI/UX Enhancement
 */

import React, { forwardRef, ButtonHTMLAttributes } from 'react';
import { cn } from '../../../../utils/cn';
import { useRTL } from '../../../../i18n/rtl/RTLProvider';

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  rounded?: boolean;
  children: React.ReactNode;
}

/**
 * Enhanced Button Component with Design Tokens
 */
export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      loading = false,
      leftIcon,
      rightIcon,
      fullWidth = false,
      rounded = false,
      disabled,
      className,
      children,
      ...props
    },
    ref
  ) => {
    const { isRTL, getDirectionalValue } = useRTL();

    // Base button styles using design tokens
    const baseStyles = [
      // Layout
      'inline-flex items-center justify-center',
      'font-medium transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'disabled:pointer-events-none',
      
      // Full width
      fullWidth && 'w-full',
      
      // Loading state
      loading && 'cursor-wait',
    ];

    // Variant styles using CSS custom properties (design tokens)
    const variantStyles = {
      primary: [
        'bg-[var(--color-interactive-primary)]',
        'text-white',
        'border border-transparent',
        'hover:bg-[var(--color-interactive-primaryHover)]',
        'active:bg-[var(--color-interactive-primaryActive)]',
        'focus:ring-[var(--color-border-focus)]',
        'shadow-[var(--shadow-sm)]',
        'hover:shadow-[var(--shadow-md)]',
      ],
      secondary: [
        'bg-[var(--color-interactive-secondary)]',
        'text-[var(--color-text-primary)]',
        'border border-[var(--color-border-primary)]',
        'hover:bg-[var(--color-interactive-secondaryHover)]',
        'active:bg-[var(--color-interactive-secondaryActive)]',
        'focus:ring-[var(--color-border-focus)]',
        'shadow-[var(--shadow-sm)]',
      ],
      outline: [
        'bg-transparent',
        'text-[var(--color-interactive-primary)]',
        'border border-[var(--color-interactive-primary)]',
        'hover:bg-[var(--color-interactive-primary)]',
        'hover:text-white',
        'active:bg-[var(--color-interactive-primaryActive)]',
        'focus:ring-[var(--color-border-focus)]',
      ],
      ghost: [
        'bg-transparent',
        'text-[var(--color-text-primary)]',
        'border border-transparent',
        'hover:bg-[var(--color-interactive-secondary)]',
        'active:bg-[var(--color-interactive-secondaryActive)]',
        'focus:ring-[var(--color-border-focus)]',
      ],
      destructive: [
        'bg-[var(--color-error-600)]',
        'text-white',
        'border border-transparent',
        'hover:bg-[var(--color-error-700)]',
        'active:bg-[var(--color-error-800)]',
        'focus:ring-[var(--color-border-error)]',
        'shadow-[var(--shadow-sm)]',
        'hover:shadow-[var(--shadow-md)]',
      ],
    };

    // Size styles using design tokens
    const sizeStyles = {
      xs: [
        'h-6',
        'px-[var(--spacing-2)]',
        'text-xs',
        'gap-[var(--spacing-1)]',
        rounded ? 'rounded-[var(--radius-full)]' : 'rounded-[var(--radius-sm)]',
      ],
      sm: [
        'h-8',
        'px-[var(--spacing-3)]',
        'text-sm',
        'gap-[var(--spacing-1-5)]',
        rounded ? 'rounded-[var(--radius-full)]' : 'rounded-[var(--radius-md)]',
      ],
      md: [
        'h-10',
        'px-[var(--spacing-4)]',
        'text-sm',
        'gap-[var(--spacing-2)]',
        rounded ? 'rounded-[var(--radius-full)]' : 'rounded-[var(--radius-md)]',
      ],
      lg: [
        'h-12',
        'px-[var(--spacing-6)]',
        'text-base',
        'gap-[var(--spacing-2)]',
        rounded ? 'rounded-[var(--radius-full)]' : 'rounded-[var(--radius-lg)]',
      ],
      xl: [
        'h-14',
        'px-[var(--spacing-8)]',
        'text-lg',
        'gap-[var(--spacing-3)]',
        rounded ? 'rounded-[var(--radius-full)]' : 'rounded-[var(--radius-xl)]',
      ],
    };

    // Icon sizes based on button size
    const iconSizes = {
      xs: 'w-3 h-3',
      sm: 'w-4 h-4',
      md: 'w-4 h-4',
      lg: 'w-5 h-5',
      xl: 'w-6 h-6',
    };

    // Loading spinner component
    const LoadingSpinner = () => (
      <svg
        className={cn('animate-spin', iconSizes[size])}
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    );

    // RTL-aware icon positioning
    const getIconOrder = () => {
      if (isRTL) {
        return {
          startIcon: rightIcon,
          endIcon: leftIcon,
        };
      }
      return {
        startIcon: leftIcon,
        endIcon: rightIcon,
      };
    };

    const { startIcon, endIcon } = getIconOrder();

    return (
      <button
        ref={ref}
        disabled={disabled || loading}
        className={cn(
          baseStyles,
          variantStyles[variant],
          sizeStyles[size],
          className
        )}
        {...props}
      >
        {/* Loading spinner or start icon */}
        {loading ? (
          <LoadingSpinner />
        ) : startIcon ? (
          <span className={cn('flex-shrink-0', iconSizes[size])}>
            {startIcon}
          </span>
        ) : null}

        {/* Button content */}
        <span className={cn(
          'flex-1',
          (loading || startIcon || endIcon) && 'mx-1'
        )}>
          {children}
        </span>

        {/* End icon (only if not loading) */}
        {!loading && endIcon && (
          <span className={cn('flex-shrink-0', iconSizes[size])}>
            {endIcon}
          </span>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

/**
 * Button Group Component
 */
export interface ButtonGroupProps {
  children: React.ReactNode;
  orientation?: 'horizontal' | 'vertical';
  size?: ButtonProps['size'];
  variant?: ButtonProps['variant'];
  className?: string;
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  orientation = 'horizontal',
  size,
  variant,
  className,
}) => {
  const { isRTL } = useRTL();

  const groupStyles = [
    'inline-flex',
    orientation === 'vertical' ? 'flex-col' : 'flex-row',
    orientation === 'vertical' ? 'divide-y' : 'divide-x',
    'divide-[var(--color-border-primary)]',
    'rounded-[var(--radius-md)]',
    'overflow-hidden',
    'shadow-[var(--shadow-sm)]',
  ];

  // Clone children and apply group styles
  const enhancedChildren = React.Children.map(children, (child, index) => {
    if (React.isValidElement(child) && child.type === Button) {
      const isFirst = index === 0;
      const isLast = index === React.Children.count(children) - 1;

      let roundedClasses = '';
      if (orientation === 'horizontal') {
        if (isRTL) {
          if (isFirst) roundedClasses = 'rounded-r-none';
          else if (isLast) roundedClasses = 'rounded-l-none';
          else roundedClasses = 'rounded-none';
        } else {
          if (isFirst) roundedClasses = 'rounded-r-none';
          else if (isLast) roundedClasses = 'rounded-l-none';
          else roundedClasses = 'rounded-none';
        }
      } else {
        if (isFirst) roundedClasses = 'rounded-b-none';
        else if (isLast) roundedClasses = 'rounded-t-none';
        else roundedClasses = 'rounded-none';
      }

      return React.cloneElement(child, {
        ...child.props,
        size: size || child.props.size,
        variant: variant || child.props.variant,
        className: cn(
          child.props.className,
          roundedClasses,
          'shadow-none', // Remove individual shadows in group
          'border-0', // Remove individual borders in group
        ),
      });
    }
    return child;
  });

  return (
    <div className={cn(groupStyles, className)}>
      {enhancedChildren}
    </div>
  );
};

/**
 * Icon Button Component
 */
export interface IconButtonProps extends Omit<ButtonProps, 'leftIcon' | 'rightIcon' | 'children'> {
  icon: React.ReactNode;
  'aria-label': string;
}

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon, className, ...props }, ref) => {
    return (
      <Button
        ref={ref}
        className={cn('!px-0 aspect-square', className)}
        {...props}
      >
        {icon}
      </Button>
    );
  }
);

IconButton.displayName = 'IconButton';
