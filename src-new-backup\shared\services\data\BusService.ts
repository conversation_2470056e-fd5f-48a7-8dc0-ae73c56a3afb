/**
 * Bus Service
 * Handles all bus-related API operations
 * Phase 2: Application Structure Reorganization
 */

import { BaseService } from '../base/BaseService';
import {
  Bus,
  CreateBusRequest,
  UpdateBusRequest,
  Location,
  BusStatus,
  APIResponse,
  PaginatedResponse,
  PaginationParams,
} from '../../api/types';

export interface BusListParams extends PaginationParams {
  status?: BusStatus;
  driver_id?: string;
  route_id?: string;
  tenant_id?: string;
  is_active?: boolean;
  search?: string;
  search_fields?: string[];
}

/**
 * Bus Service Class
 * Implements comprehensive bus management and tracking operations
 */
export class BusService extends BaseService {
  private readonly endpoint = '/buses';

  /**
   * Get paginated list of buses
   */
  async getBuses(params: BusListParams = {}): Promise<APIResponse<PaginatedResponse<Bus>>> {
    return this.getPaginated<Bus>(this.endpoint, params);
  }

  /**
   * Get bus by ID
   */
  async getBusById(id: string): Promise<APIResponse<Bus>> {
    return this.get<Bus>(`${this.endpoint}/${id}`);
  }

  /**
   * Get buses assigned to current user (driver)
   */
  async getMyBuses(): Promise<APIResponse<Bus[]>> {
    const response = await this.get<PaginatedResponse<Bus>>(`${this.endpoint}/my-buses`);
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<Bus[]>;
  }

  /**
   * Create new bus
   */
  async createBus(busData: CreateBusRequest): Promise<APIResponse<Bus>> {
    const validation = this.validateCreateBusData(busData);
    if (!validation.isValid) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid bus data',
          details: validation.errors,
        },
      };
    }

    return this.post<Bus>(this.endpoint, busData);
  }

  /**
   * Update bus
   */
  async updateBus(id: string, busData: UpdateBusRequest): Promise<APIResponse<Bus>> {
    const validation = this.validateUpdateBusData(busData);
    if (!validation.isValid) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid update data',
          details: validation.errors,
        },
      };
    }

    return this.put<Bus>(`${this.endpoint}/${id}`, busData);
  }

  /**
   * Delete bus
   */
  async deleteBus(id: string): Promise<APIResponse<{ message: string }>> {
    return this.delete<{ message: string }>(`${this.endpoint}/${id}`);
  }

  /**
   * Update bus location
   */
  async updateBusLocation(id: string, location: Location): Promise<APIResponse<Bus>> {
    return this.patch<Bus>(`${this.endpoint}/${id}/location`, location);
  }

  /**
   * Update bus status
   */
  async updateBusStatus(id: string, status: BusStatus): Promise<APIResponse<Bus>> {
    return this.patch<Bus>(`${this.endpoint}/${id}/status`, { status });
  }

  /**
   * Assign driver to bus with validation
   */
  async assignDriver(busId: string, driverId: string): Promise<APIResponse<Bus>> {
    // التحقق من صحة تعيين السائق
    const { EnhancedValidationService } = await import('../../lib/validation-enhanced');

    const driverValidation = await EnhancedValidationService.validateDriverAssignment(driverId, busId);
    if (!driverValidation.isValid) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: driverValidation.error || 'Driver assignment validation failed',
          details: driverValidation.conflictingBusId ? {
            conflictingBusId: driverValidation.conflictingBusId
          } : undefined
        }
      };
    }

    return this.patch<Bus>(`${this.endpoint}/${busId}/driver`, { driver_id: driverId });
  }

  /**
   * Assign route to bus
   */
  async assignRoute(busId: string, routeId: string): Promise<APIResponse<Bus>> {
    return this.patch<Bus>(`${this.endpoint}/${busId}/route`, { route_id: routeId });
  }

  /**
   * Get bus location history
   */
  async getBusLocationHistory(
    id: string,
    startDate: string,
    endDate: string
  ): Promise<APIResponse<LocationHistory[]>> {
    const params = this.buildQueryString({ start_date: startDate, end_date: endDate });
    return this.get<LocationHistory[]>(`${this.endpoint}/${id}/location-history${params}`);
  }

  /**
   * Get bus statistics
   */
  async getBusStats(id?: string, tenantId?: string): Promise<APIResponse<BusStats>> {
    const params: any = {};
    if (tenantId) params.tenant_id = tenantId;
    
    const queryString = this.buildQueryString(params);
    const endpoint = id 
      ? `${this.endpoint}/${id}/stats${queryString}`
      : `${this.endpoint}/stats${queryString}`;
    
    return this.get<BusStats>(endpoint);
  }

  /**
   * Get buses by status
   */
  async getBusesByStatus(status: BusStatus, tenantId?: string): Promise<APIResponse<Bus[]>> {
    const params: BusListParams = { status };
    if (tenantId) params.tenant_id = tenantId;

    const response = await this.getBuses(params);
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<Bus[]>;
  }

  /**
   * Get available buses (no driver or route assigned)
   */
  async getAvailableBuses(tenantId?: string): Promise<APIResponse<Bus[]>> {
    const params = tenantId ? { tenant_id: tenantId } : {};
    const queryString = this.buildQueryString(params);
    return this.get<Bus[]>(`${this.endpoint}/available${queryString}`);
  }

  /**
   * Search buses
   */
  async searchBuses(
    query: string,
    fields: string[] = ['plate_number', 'model'],
    tenantId?: string
  ): Promise<APIResponse<Bus[]>> {
    const params: BusListParams = {
      search: query,
      search_fields: fields,
    };
    if (tenantId) params.tenant_id = tenantId;

    const response = await this.getBuses(params);
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<Bus[]>;
  }

  /**
   * Bulk operations
   */
  async bulkUpdateBusStatus(
    busIds: string[],
    status: BusStatus
  ): Promise<APIResponse<BulkOperationResult<Bus>>> {
    return this.patch<BulkOperationResult<Bus>>(`${this.endpoint}/bulk/status`, {
      bus_ids: busIds,
      status,
    });
  }

  async bulkAssignDriver(
    assignments: Array<{ bus_id: string; driver_id: string }>
  ): Promise<APIResponse<BulkOperationResult<Bus>>> {
    return this.patch<BulkOperationResult<Bus>>(`${this.endpoint}/bulk/assign-driver`, {
      assignments,
    });
  }

  /**
   * Maintenance operations
   */
  async scheduleMaintenance(
    busId: string,
    maintenanceData: MaintenanceSchedule
  ): Promise<APIResponse<Bus>> {
    return this.post<Bus>(`${this.endpoint}/${busId}/maintenance`, maintenanceData);
  }

  async getMaintenanceHistory(busId: string): Promise<APIResponse<MaintenanceRecord[]>> {
    return this.get<MaintenanceRecord[]>(`${this.endpoint}/${busId}/maintenance-history`);
  }

  /**
   * Validation methods
   */
  private validateCreateBusData(data: CreateBusRequest): ValidationResult {
    const errors: string[] = [];

    if (!data.plate_number || data.plate_number.trim().length < 3) {
      errors.push('Plate number must be at least 3 characters long');
    }

    if (!data.model || data.model.trim().length < 2) {
      errors.push('Bus model is required');
    }

    if (!data.year || data.year < 1990 || data.year > new Date().getFullYear() + 1) {
      errors.push('Valid year is required');
    }

    if (!data.capacity || data.capacity < 1 || data.capacity > 100) {
      errors.push('Capacity must be between 1 and 100');
    }

    if (!data.tenant_id || data.tenant_id.trim().length === 0) {
      errors.push('Tenant ID is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private validateUpdateBusData(data: UpdateBusRequest): ValidationResult {
    const errors: string[] = [];

    if (data.plate_number !== undefined && (!data.plate_number || data.plate_number.trim().length < 3)) {
      errors.push('Plate number must be at least 3 characters long');
    }

    if (data.model !== undefined && (!data.model || data.model.trim().length < 2)) {
      errors.push('Bus model is required');
    }

    if (data.year !== undefined && (data.year < 1990 || data.year > new Date().getFullYear() + 1)) {
      errors.push('Valid year is required');
    }

    if (data.capacity !== undefined && (data.capacity < 1 || data.capacity > 100)) {
      errors.push('Capacity must be between 1 and 100');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// ============================================================================
// Supporting Types
// ============================================================================

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

interface LocationHistory {
  id: string;
  bus_id: string;
  location: Location;
  recorded_at: string;
}

interface BusStats {
  total_buses: number;
  active_buses: number;
  in_route: number;
  maintenance: number;
  out_of_service: number;
  average_capacity: number;
  total_mileage: number;
  fuel_efficiency: number;
  last_updated: string;
}

interface MaintenanceSchedule {
  type: string;
  description: string;
  scheduled_date: string;
  estimated_cost?: number;
  mechanic?: string;
}

interface MaintenanceRecord {
  id: string;
  bus_id: string;
  type: string;
  description: string;
  date: string;
  cost?: number;
  mechanic?: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
}

interface BulkOperationResult<T> {
  successful: T[];
  failed: Array<{
    data: any;
    error: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

// ============================================================================
// Service Instance
// ============================================================================

export const busService = new BusService();
export { BusService };
