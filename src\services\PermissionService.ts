/**
 * خدمة الصلاحيات المركزية
 * Centralized Permission Service
 * 
 * هذه الخدمة توحد جميع منطق التحقق من الصلاحيات في مكان واحد
 * This service centralizes all permission checking logic in one place
 */

import { UserRole } from '../types';

// تعريف واجهة المستخدم المحسنة
interface User {
  id: string;
  role: UserRole;
  tenant_id?: string;
  email?: string;
  name?: string;
}

// تعريف أنواع الصلاحيات
export enum Permission {
  // إدارة المستخدمين
  CREATE_USER = 'create_user',
  READ_USER = 'read_user',
  UPDATE_USER = 'update_user',
  DELETE_USER = 'delete_user',
  
  // إدارة الطلاب
  CREATE_STUDENT = 'create_student',
  READ_STUDENT = 'read_student',
  UPDATE_STUDENT = 'update_student',
  DELETE_STUDENT = 'delete_student',
  
  // إدارة الحافلات
  CREATE_BUS = 'create_bus',
  READ_BUS = 'read_bus',
  UPDATE_BUS = 'update_bus',
  DELETE_BUS = 'delete_bus',
  
  // إدارة المسارات
  CREATE_ROUTE = 'create_route',
  READ_ROUTE = 'read_route',
  UPDATE_ROUTE = 'update_route',
  DELETE_ROUTE = 'delete_route',
  
  // إدارة المدارس
  CREATE_SCHOOL = 'create_school',
  READ_SCHOOL = 'read_school',
  UPDATE_SCHOOL = 'update_school',
  DELETE_SCHOOL = 'delete_school',
  
  // إدارة الحضور
  CREATE_ATTENDANCE = 'create_attendance',
  READ_ATTENDANCE = 'read_attendance',
  UPDATE_ATTENDANCE = 'update_attendance',
  
  // إدارة الصيانة
  CREATE_MAINTENANCE = 'create_maintenance',
  READ_MAINTENANCE = 'read_maintenance',
  UPDATE_MAINTENANCE = 'update_maintenance',
  
  // التقارير
  VIEW_REPORTS = 'view_reports',
  EXPORT_REPORTS = 'export_reports',
  
  // التتبع
  VIEW_TRACKING = 'view_tracking',
  MANAGE_TRACKING = 'manage_tracking',
  
  // الإعدادات
  MANAGE_SETTINGS = 'manage_settings',
  MANAGE_THEMES = 'manage_themes',
}

// مصفوفة الصلاحيات المركزية
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [
    // الأدمن له جميع الصلاحيات
    ...Object.values(Permission)
  ],
  
  [UserRole.SCHOOL_MANAGER]: [
    // إدارة المستخدمين (عدا الأدمن)
    Permission.CREATE_USER,
    Permission.READ_USER,
    Permission.UPDATE_USER,
    Permission.DELETE_USER,
    
    // إدارة الطلاب
    Permission.CREATE_STUDENT,
    Permission.READ_STUDENT,
    Permission.UPDATE_STUDENT,
    Permission.DELETE_STUDENT,
    
    // إدارة الحافلات
    Permission.CREATE_BUS,
    Permission.READ_BUS,
    Permission.UPDATE_BUS,
    Permission.DELETE_BUS,
    
    // إدارة المسارات
    Permission.CREATE_ROUTE,
    Permission.READ_ROUTE,
    Permission.UPDATE_ROUTE,
    Permission.DELETE_ROUTE,
    
    // قراءة معلومات المدرسة وتحديثها
    Permission.READ_SCHOOL,
    Permission.UPDATE_SCHOOL,
    
    // إدارة الحضور
    Permission.CREATE_ATTENDANCE,
    Permission.READ_ATTENDANCE,
    Permission.UPDATE_ATTENDANCE,
    
    // إدارة الصيانة
    Permission.CREATE_MAINTENANCE,
    Permission.READ_MAINTENANCE,
    Permission.UPDATE_MAINTENANCE,
    
    // التقارير
    Permission.VIEW_REPORTS,
    Permission.EXPORT_REPORTS,
    
    // التتبع
    Permission.VIEW_TRACKING,
    Permission.MANAGE_TRACKING,
    
    // الإعدادات
    Permission.MANAGE_SETTINGS,
    Permission.MANAGE_THEMES,
  ],
  
  [UserRole.SUPERVISOR]: [
    // قراءة المستخدمين
    Permission.READ_USER,
    
    // إدارة الطلاب
    Permission.CREATE_STUDENT,
    Permission.READ_STUDENT,
    Permission.UPDATE_STUDENT,
    
    // قراءة الحافلات والمسارات
    Permission.READ_BUS,
    Permission.READ_ROUTE,
    
    // إدارة الحضور
    Permission.CREATE_ATTENDANCE,
    Permission.READ_ATTENDANCE,
    Permission.UPDATE_ATTENDANCE,
    
    // قراءة الصيانة
    Permission.READ_MAINTENANCE,
    
    // التقارير
    Permission.VIEW_REPORTS,
    
    // التتبع
    Permission.VIEW_TRACKING,
  ],
  
  [UserRole.DRIVER]: [
    // قراءة المعلومات الأساسية
    Permission.READ_STUDENT,
    Permission.READ_BUS,
    Permission.READ_ROUTE,
    
    // إدارة الحضور
    Permission.CREATE_ATTENDANCE,
    Permission.READ_ATTENDANCE,
    Permission.UPDATE_ATTENDANCE,
    
    // التتبع
    Permission.VIEW_TRACKING,
  ],
  
  [UserRole.PARENT]: [
    // قراءة معلومات الأطفال فقط
    Permission.READ_STUDENT,
    Permission.READ_ATTENDANCE,
    Permission.VIEW_TRACKING,
  ],
  
  [UserRole.STUDENT]: [
    // قراءة المعلومات الشخصية فقط
    Permission.READ_STUDENT,
    Permission.READ_ATTENDANCE,
  ],
};

// واجهة المستخدم
interface User {
  id: string;
  role: UserRole;
  tenant_id?: string;
}

// واجهة السياق
interface PermissionContext {
  user: User;
  targetTenantId?: string;
  resourceOwnerId?: string;
}

export class PermissionService {
  private static instance: PermissionService;
  
  private constructor() {}
  
  public static getInstance(): PermissionService {
    if (!PermissionService.instance) {
      PermissionService.instance = new PermissionService();
    }
    return PermissionService.instance;
  }
  
  /**
   * التحقق من صلاحية واحدة
   * Check a single permission
   */
  public hasPermission(
    user: User,
    permission: Permission,
    context?: Partial<PermissionContext>
  ): boolean {
    try {
      // التحقق من وجود الصلاحية للدور
      const rolePermissions = ROLE_PERMISSIONS[user.role];
      if (!rolePermissions.includes(permission)) {
        return false;
      }
      
      // التحقق من السياق (المستأجر)
      if (context?.targetTenantId && user.role !== UserRole.ADMIN) {
        // المستخدمون غير الأدمن يمكنهم الوصول لمستأجرهم فقط
        if (user.tenant_id !== context.targetTenantId) {
          return false;
        }
      }
      
      // التحقق من ملكية المورد
      if (context?.resourceOwnerId && user.role !== UserRole.ADMIN) {
        // بعض الأدوار يمكنها الوصول لمواردها فقط
        if (user.role === UserRole.PARENT || user.role === UserRole.STUDENT) {
          if (user.id !== context.resourceOwnerId) {
            return false;
          }
        }
      }
      
      return true;
    } catch (error) {
      console.error('PermissionService: Error checking permission:', error);
      return false;
    }
  }
  
  /**
   * التحقق من عدة صلاحيات
   * Check multiple permissions
   */
  public hasPermissions(
    user: User,
    permissions: Permission[],
    context?: Partial<PermissionContext>
  ): boolean {
    return permissions.every(permission => 
      this.hasPermission(user, permission, context)
    );
  }
  
  /**
   * التحقق من أي صلاحية من مجموعة
   * Check if user has any of the permissions
   */
  public hasAnyPermission(
    user: User,
    permissions: Permission[],
    context?: Partial<PermissionContext>
  ): boolean {
    return permissions.some(permission => 
      this.hasPermission(user, permission, context)
    );
  }
  
  /**
   * الحصول على جميع صلاحيات المستخدم
   * Get all user permissions
   */
  public getUserPermissions(user: User): Permission[] {
    return ROLE_PERMISSIONS[user.role] || [];
  }
  
  /**
   * التحقق من إمكانية الوصول للمسار
   * Check route access
   */
  public canAccessRoute(user: User, route: string): boolean {
    const routePermissions: Record<string, Permission[]> = {
      '/dashboard/users': [Permission.READ_USER],
      '/dashboard/students': [Permission.READ_STUDENT],
      '/dashboard/buses': [Permission.READ_BUS],
      '/dashboard/routes': [Permission.READ_ROUTE],
      '/dashboard/schools': [Permission.READ_SCHOOL],
      '/dashboard/schools-management': [Permission.CREATE_SCHOOL, Permission.UPDATE_SCHOOL],
      '/dashboard/attendance': [Permission.READ_ATTENDANCE],
      '/dashboard/maintenance': [Permission.READ_MAINTENANCE],
      '/dashboard/tracking': [Permission.VIEW_TRACKING],
      '/dashboard/reports': [Permission.VIEW_REPORTS],
    };
    
    const requiredPermissions = routePermissions[route];
    if (!requiredPermissions) {
      return true; // المسارات غير المحددة متاحة للجميع
    }
    
    return this.hasAnyPermission(user, requiredPermissions);
  }
  
  /**
   * تسجيل حدث أمني
   * Log security event
   */
  public logSecurityEvent(
    user: User,
    action: string,
    resource: string,
    success: boolean,
    details?: any
  ): void {
    const event = {
      timestamp: new Date().toISOString(),
      userId: user.id,
      userRole: user.role,
      tenantId: user.tenant_id,
      action,
      resource,
      success,
      details,
    };
    
    console.log('🔒 Security Event:', event);
    
    // هنا يمكن إضافة منطق لحفظ الأحداث في قاعدة البيانات
    // Here you can add logic to save events to database
  }
}

// تصدير instance واحد
export const permissionService = PermissionService.getInstance();
