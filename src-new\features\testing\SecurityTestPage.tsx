/**
 * صفحة اختبار الميزات الأمنية
 * Security Features Test Page
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { SecurityTestDashboard } from '../../components/testing/SecurityTestDashboard';
import { useAuth } from '../../contexts/AuthContext';
import { Shield, AlertTriangle } from 'lucide-react';

export const SecurityTestPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();

  // التحقق من صلاحيات الوصول (للأدمن فقط في البيئة الإنتاجية)
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isAdmin = user?.role === 'admin';

  if (!isDevelopment && !isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="text-center">
            <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              غير مصرح بالوصول
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              هذه الصفحة متاحة للمطورين والمديرين فقط
            </p>
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-3">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                <strong>ملاحظة:</strong> في بيئة التطوير، هذه الصفحة متاحة لجميع المستخدمين لأغراض الاختبار.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* تحذير بيئة التطوير */}
        {isDevelopment && (
          <div className="mb-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
            <div className="flex items-start space-x-3 space-x-reverse">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  بيئة التطوير
                </h4>
                <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                  هذه الصفحة مخصصة لاختبار الميزات الأمنية في بيئة التطوير. 
                  في البيئة الإنتاجية، ستكون متاحة للمديرين فقط.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* معلومات الصفحة */}
        <div className="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center space-x-3 space-x-reverse mb-4">
            <Shield className="w-8 h-8 text-primary-500" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                اختبار الميزات الأمنية - المرحلة الأولى
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                اختبار شامل لجميع الميزات الأمنية المطبقة في المرحلة الأولى
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                🔒 فحص قوة كلمة المرور
              </h3>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                اختبار خوارزمية فحص قوة كلمة المرور والمؤشر البصري
              </p>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <h3 className="font-medium text-green-900 dark:text-green-100 mb-2">
                🛡️ حماية Brute-force
              </h3>
              <p className="text-sm text-green-700 dark:text-green-300">
                اختبار نظام الحماية من هجمات Brute-force والحظر التلقائي
              </p>
            </div>

            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
              <h3 className="font-medium text-purple-900 dark:text-purple-100 mb-2">
                🔐 التحقق الثنائي
              </h3>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                اختبار نظام التحقق الثنائي مع TOTP والبريد الإلكتروني
              </p>
            </div>

            <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
              <h3 className="font-medium text-orange-900 dark:text-orange-100 mb-2">
                🕵️ مراقبة السلوك الشاذ
              </h3>
              <p className="text-sm text-orange-700 dark:text-orange-300">
                اختبار نظام كشف السلوك الشاذ والأنشطة المشبوهة
              </p>
            </div>

            <div className="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg">
              <h3 className="font-medium text-indigo-900 dark:text-indigo-100 mb-2">
                💻 إدارة الجلسات
              </h3>
              <p className="text-sm text-indigo-700 dark:text-indigo-300">
                اختبار نظام إدارة الجلسات المتقدم وتتبع الأجهزة
              </p>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                🗄️ قاعدة البيانات
              </h3>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                اختبار الجداول والدوال الأمنية في قاعدة البيانات
              </p>
            </div>
          </div>
        </div>

        {/* لوحة الاختبار */}
        <SecurityTestDashboard />

        {/* معلومات إضافية */}
        <div className="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            معلومات مهمة حول الاختبارات
          </h3>
          
          <div className="space-y-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
              <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                📋 ما يتم اختباره:
              </h4>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                <li>• صحة عمل جميع الخدمات الأمنية</li>
                <li>• التكامل مع قاعدة البيانات</li>
                <li>• استجابة النظام للحالات المختلفة</li>
                <li>• دقة الخوارزميات الأمنية</li>
              </ul>
            </div>

            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
              <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">
                ⚠️ تحذيرات مهمة:
              </h4>
              <ul className="text-sm text-yellow-800 dark:text-yellow-200 space-y-1">
                <li>• الاختبارات تنشئ بيانات تجريبية في قاعدة البيانات</li>
                <li>• بعض الاختبارات قد تستغرق وقتاً أطول</li>
                <li>• لا تشغل الاختبارات في البيئة الإنتاجية</li>
                <li>• تأكد من وجود اتصال بقاعدة البيانات</li>
              </ul>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
              <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
                ✅ نصائح للاختبار:
              </h4>
              <ul className="text-sm text-green-800 dark:text-green-200 space-y-1">
                <li>• شغل الاختبارات بانتظام أثناء التطوير</li>
                <li>• راجع التفاصيل في حالة فشل أي اختبار</li>
                <li>• احفظ تقارير الاختبار للمراجعة اللاحقة</li>
                <li>• اختبر الميزات يدوياً أيضاً في الواجهة</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
