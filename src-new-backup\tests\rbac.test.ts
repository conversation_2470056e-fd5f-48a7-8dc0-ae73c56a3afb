/**
 * RBAC Security Test Suite
 * Comprehensive tests for role-based access control in the School Transport SaaS System
 *
 * This test suite validates:
 * - Permission validation across all user roles
 * - Data scope enforcement (global, tenant, assigned, personal, children)
 * - Resource access control with proper authorization
 * - Cross-tenant isolation and security
 * - Security middleware functionality
 * - Role hierarchy enforcement
 * - Data filtering based on user permissions
 * - Integration scenarios for complete workflows
 */

import { UserRole } from "../types";
import {
  RBACManager,
  Permission,
  DataScope,
  ResourceType,
  Action,
} from "../lib/rbac";
import { AuthMiddleware } from "../middleware/authMiddleware";
import { DatabaseSecurityAudit } from "../lib/databaseSecurityAudit";
import { RBACSecurityAudit } from "../lib/rbacAudit";

// Mock user data representing different roles in the system
const mockUsers = {
  admin: {
    id: "admin-1",
    role: UserRole.ADMIN,
    tenant_id: null, // System admin has no tenant restriction
    email: "<EMAIL>",
    name: "System Administrator",
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  schoolManager: {
    id: "manager-1",
    role: UserRole.SCHOOL_MANAGER,
    tenant_id: "school-1",
    email: "<EMAIL>",
    name: "Green Valley School Manager",
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  driver: {
    id: "driver-1",
    role: UserRole.DRIVER,
    tenant_id: "school-1",
    email: "<EMAIL>",
    name: "Ahmed Hassan - Bus Driver",
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  parent: {
    id: "parent-1",
    role: UserRole.PARENT,
    tenant_id: "school-1",
    email: "<EMAIL>",
    name: "Sarah Johnson - Parent",
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  student: {
    id: "student-1",
    role: UserRole.STUDENT,
    tenant_id: "school-1",
    email: "<EMAIL>",
    name: "Emma Johnson - Student",
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  inactiveUser: {
    id: "inactive-1",
    role: UserRole.DRIVER,
    tenant_id: "school-1",
    email: "<EMAIL>",
    name: "Inactive Driver",
    is_active: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
};

// Mock resource data for testing
const mockResources = {
  buses: [
    {
      id: "bus-1",
      tenant_id: "school-1",
      driver_id: "driver-1",
      license_plate: "ABC-123",
    },
    {
      id: "bus-2",
      tenant_id: "school-1",
      driver_id: "driver-2",
      license_plate: "DEF-456",
    },
    {
      id: "bus-3",
      tenant_id: "school-2",
      driver_id: "driver-3",
      license_plate: "GHI-789",
    },
  ],
  students: [
    {
      id: "student-1",
      tenant_id: "school-1",
      parent_id: "parent-1",
      name: "Emma Johnson",
    },
    {
      id: "student-2",
      tenant_id: "school-1",
      parent_id: "parent-2",
      name: "Liam Smith",
    },
    {
      id: "student-3",
      tenant_id: "school-2",
      parent_id: "parent-3",
      name: "Olivia Brown",
    },
    {
      id: "student-4",
      tenant_id: "school-1",
      parent_id: "parent-1",
      name: "Noah Johnson",
    },
  ],
  routes: [
    {
      id: "route-1",
      tenant_id: "school-1",
      bus_id: "bus-1",
      name: "Route A - Downtown",
    },
    {
      id: "route-2",
      tenant_id: "school-1",
      bus_id: "bus-2",
      name: "Route B - Suburbs",
    },
    {
      id: "route-3",
      tenant_id: "school-2",
      bus_id: "bus-3",
      name: "Route C - Uptown",
    },
  ],
};

describe("🔒 RBAC Security Test Suite", () => {
  beforeEach(() => {
    // Reset any cached permissions or rate limits before each test
    jest.clearAllMocks();
  });

  describe("👤 Permission Validation Tests", () => {
    describe("System Administrator Permissions", () => {
      test("Admin should have all system-level permissions", () => {
        const permissions = [
          Permission.SYSTEM_ADMIN,
          Permission.SYSTEM_SETTINGS,
          Permission.SYSTEM_AUDIT,
          Permission.SYSTEM_BACKUP,
          Permission.SYSTEM_ANALYTICS,
          Permission.SYSTEM_BILLING,
        ];

        permissions.forEach((permission) => {
          const hasPermission = RBACManager.hasPermission(
            UserRole.ADMIN,
            permission,
          );
          expect(hasPermission).toBe(true);
        });
      });

      test("Admin should have all user management permissions", () => {
        const permissions = [
          Permission.VIEW_ALL_USERS,
          Permission.MANAGE_ALL_USERS,
          Permission.ASSIGN_USER_ROLES,
          Permission.VIEW_ALL_SCHOOLS,
          Permission.MANAGE_SCHOOLS,
        ];

        permissions.forEach((permission) => {
          const hasPermission = RBACManager.hasPermission(
            UserRole.ADMIN,
            permission,
          );
          expect(hasPermission).toBe(true);
        });
      });

      test("Admin should have all resource management permissions", () => {
        const permissions = [
          Permission.VIEW_ALL_BUSES,
          Permission.MANAGE_BUSES,
          Permission.VIEW_ALL_STUDENTS,
          Permission.MANAGE_STUDENTS,
          Permission.VIEW_ALL_ROUTES,
          Permission.MANAGE_ROUTES,
        ];

        permissions.forEach((permission) => {
          const hasPermission = RBACManager.hasPermission(
            UserRole.ADMIN,
            permission,
          );
          expect(hasPermission).toBe(true);
        });
      });
    });

    describe("School Manager Permissions", () => {
      test("School Manager should have tenant-level permissions", () => {
        const allowedPermissions = [
          Permission.VIEW_TENANT_USERS,
          Permission.MANAGE_TENANT_USERS,
          Permission.VIEW_TENANT_BUSES,
          Permission.MANAGE_BUSES,
          Permission.VIEW_TENANT_STUDENTS,
          Permission.MANAGE_STUDENTS,
          Permission.VIEW_TENANT_ROUTES,
          Permission.MANAGE_ROUTES,
        ];

        allowedPermissions.forEach((permission) => {
          const hasPermission = RBACManager.hasPermission(
            UserRole.SCHOOL_MANAGER,
            permission,
          );
          expect(hasPermission).toBe(true);
        });
      });

      test("School Manager should NOT have system-level permissions", () => {
        const forbiddenPermissions = [
          Permission.SYSTEM_ADMIN,
          Permission.SYSTEM_SETTINGS,
          Permission.VIEW_ALL_USERS,
          Permission.MANAGE_ALL_USERS,
          Permission.VIEW_ALL_SCHOOLS,
          Permission.MANAGE_SCHOOLS,
        ];

        forbiddenPermissions.forEach((permission) => {
          const hasPermission = RBACManager.hasPermission(
            UserRole.SCHOOL_MANAGER,
            permission,
          );
          expect(hasPermission).toBe(false);
        });
      });
    });

    describe("Driver Permissions", () => {
      test("Driver should have limited operational permissions", () => {
        const allowedPermissions = [
          Permission.VIEW_ASSIGNED_BUSES,
          Permission.TRACK_BUS,
          Permission.MANAGE_ATTENDANCE,
          Permission.VIEW_ATTENDANCE,
          Permission.VIEW_ASSIGNED_ROUTE,
          Permission.VIEW_OWN_PROFILE,
          Permission.MANAGE_OWN_PROFILE,
        ];

        allowedPermissions.forEach((permission) => {
          const hasPermission = RBACManager.hasPermission(
            UserRole.DRIVER,
            permission,
          );
          expect(hasPermission).toBe(true);
        });
      });

      test("Driver should NOT have management permissions", () => {
        const forbiddenPermissions = [
          Permission.MANAGE_BUSES,
          Permission.MANAGE_STUDENTS,
          Permission.MANAGE_ROUTES,
          Permission.MANAGE_TENANT_USERS,
          Permission.ASSIGN_USER_ROLES,
          Permission.VIEW_ALL_BUSES,
        ];

        forbiddenPermissions.forEach((permission) => {
          const hasPermission = RBACManager.hasPermission(
            UserRole.DRIVER,
            permission,
          );
          expect(hasPermission).toBe(false);
        });
      });
    });

    describe("Parent Permissions", () => {
      test("Parent should have child-related permissions", () => {
        const allowedPermissions = [
          Permission.VIEW_OWN_CHILDREN,
          Permission.VIEW_ATTENDANCE,
          Permission.TRACK_BUS,
          Permission.CREATE_EVALUATION,
          Permission.VIEW_OWN_PROFILE,
          Permission.MANAGE_OWN_PROFILE,
        ];

        allowedPermissions.forEach((permission) => {
          const hasPermission = RBACManager.hasPermission(
            UserRole.PARENT,
            permission,
          );
          expect(hasPermission).toBe(true);
        });
      });

      test("Parent should NOT have administrative permissions", () => {
        const forbiddenPermissions = [
          Permission.MANAGE_STUDENTS,
          Permission.MANAGE_BUSES,
          Permission.MANAGE_ROUTES,
          Permission.MANAGE_TENANT_USERS,
          Permission.VIEW_ALL_STUDENTS,
        ];

        forbiddenPermissions.forEach((permission) => {
          const hasPermission = RBACManager.hasPermission(
            UserRole.PARENT,
            permission,
          );
          expect(hasPermission).toBe(false);
        });
      });
    });

    describe("Student Permissions", () => {
      test("Student should have minimal read-only permissions", () => {
        const allowedPermissions = [
          Permission.VIEW_OWN_PROFILE,
          Permission.MANAGE_OWN_PROFILE,
          Permission.TRACK_BUS,
          Permission.VIEW_ATTENDANCE,
        ];

        allowedPermissions.forEach((permission) => {
          const hasPermission = RBACManager.hasPermission(
            UserRole.STUDENT,
            permission,
          );
          expect(hasPermission).toBe(true);
        });
      });

      test("Student should NOT have any management permissions", () => {
        const forbiddenPermissions = [
          Permission.MANAGE_STUDENTS,
          Permission.MANAGE_BUSES,
          Permission.MANAGE_ROUTES,
          Permission.MANAGE_ATTENDANCE,
          Permission.CREATE_EVALUATION,
          Permission.VIEW_ALL_STUDENTS,
        ];

        forbiddenPermissions.forEach((permission) => {
          const hasPermission = RBACManager.hasPermission(
            UserRole.STUDENT,
            permission,
          );
          expect(hasPermission).toBe(false);
        });
      });
    });
  });

  describe("🎯 Data Scope Validation Tests", () => {
    test("Admin should have global data scope access", () => {
      const scopes = [
        DataScope.GLOBAL,
        DataScope.TENANT,
        DataScope.ASSIGNED,
        DataScope.PERSONAL,
      ];

      scopes.forEach((scope) => {
        const hasScope = RBACManager.hasDataScope(UserRole.ADMIN, scope);
        expect(hasScope).toBe(true);
      });
    });

    test("School Manager should have tenant and below scope access", () => {
      const allowedScopes = [
        DataScope.TENANT,
        DataScope.ASSIGNED,
        DataScope.PERSONAL,
      ];
      const forbiddenScopes = [DataScope.GLOBAL];

      allowedScopes.forEach((scope) => {
        const hasScope = RBACManager.hasDataScope(
          UserRole.SCHOOL_MANAGER,
          scope,
        );
        expect(hasScope).toBe(true);
      });

      forbiddenScopes.forEach((scope) => {
        const hasScope = RBACManager.hasDataScope(
          UserRole.SCHOOL_MANAGER,
          scope,
        );
        expect(hasScope).toBe(false);
      });
    });

    test("Driver should have assigned and personal scope only", () => {
      const allowedScopes = [DataScope.ASSIGNED, DataScope.PERSONAL];
      const forbiddenScopes = [DataScope.GLOBAL, DataScope.TENANT];

      allowedScopes.forEach((scope) => {
        const hasScope = RBACManager.hasDataScope(UserRole.DRIVER, scope);
        expect(hasScope).toBe(true);
      });

      forbiddenScopes.forEach((scope) => {
        const hasScope = RBACManager.hasDataScope(UserRole.DRIVER, scope);
        expect(hasScope).toBe(false);
      });
    });

    test("Parent should have children and personal scope", () => {
      const allowedScopes = [DataScope.CHILDREN, DataScope.PERSONAL];
      const forbiddenScopes = [
        DataScope.GLOBAL,
        DataScope.TENANT,
        DataScope.ASSIGNED,
      ];

      allowedScopes.forEach((scope) => {
        const hasScope = RBACManager.hasDataScope(UserRole.PARENT, scope);
        expect(hasScope).toBe(true);
      });

      forbiddenScopes.forEach((scope) => {
        const hasScope = RBACManager.hasDataScope(UserRole.PARENT, scope);
        expect(hasScope).toBe(false);
      });
    });

    test("Student should have personal scope only", () => {
      const allowedScopes = [DataScope.PERSONAL];
      const forbiddenScopes = [
        DataScope.GLOBAL,
        DataScope.TENANT,
        DataScope.ASSIGNED,
        DataScope.CHILDREN,
      ];

      allowedScopes.forEach((scope) => {
        const hasScope = RBACManager.hasDataScope(UserRole.STUDENT, scope);
        expect(hasScope).toBe(true);
      });

      forbiddenScopes.forEach((scope) => {
        const hasScope = RBACManager.hasDataScope(UserRole.STUDENT, scope);
        expect(hasScope).toBe(false);
      });
    });
  });

  describe("🔐 Resource Access Control Tests", () => {
    describe("User Resource Access", () => {
      test("Admin can perform all actions on user resources", () => {
        const actions = [
          Action.CREATE,
          Action.READ,
          Action.UPDATE,
          Action.DELETE,
          Action.MANAGE,
        ];

        actions.forEach((action) => {
          const canPerform = RBACManager.canPerformAction(
            UserRole.ADMIN,
            ResourceType.USER,
            action,
          );
          expect(canPerform).toBe(true);
        });
      });

      test("School Manager can manage users within their tenant", () => {
        const allowedActions = [
          Action.CREATE,
          Action.READ,
          Action.UPDATE,
          Action.MANAGE,
        ];
        const forbiddenActions = [Action.DELETE]; // Assuming delete is restricted

        allowedActions.forEach((action) => {
          const canPerform = RBACManager.canPerformAction(
            UserRole.SCHOOL_MANAGER,
            ResourceType.USER,
            action,
          );
          expect(canPerform).toBe(true);
        });
      });

      test("Driver cannot manage user resources", () => {
        const actions = [
          Action.CREATE,
          Action.UPDATE,
          Action.DELETE,
          Action.MANAGE,
        ];

        actions.forEach((action) => {
          const canPerform = RBACManager.canPerformAction(
            UserRole.DRIVER,
            ResourceType.USER,
            action,
          );
          expect(canPerform).toBe(false);
        });
      });
    });

    describe("Bus Resource Access", () => {
      test("Admin can perform all actions on bus resources", () => {
        const actions = [
          Action.CREATE,
          Action.READ,
          Action.UPDATE,
          Action.DELETE,
          Action.MANAGE,
        ];

        actions.forEach((action) => {
          const canPerform = RBACManager.canPerformAction(
            UserRole.ADMIN,
            ResourceType.BUS,
            action,
          );
          expect(canPerform).toBe(true);
        });
      });

      test("Driver can only read assigned buses", () => {
        const canRead = RBACManager.canPerformAction(
          UserRole.DRIVER,
          ResourceType.BUS,
          Action.READ,
        );
        const canManage = RBACManager.canPerformAction(
          UserRole.DRIVER,
          ResourceType.BUS,
          Action.MANAGE,
        );

        expect(canRead).toBe(true);
        expect(canManage).toBe(false);
      });

      test("Parent cannot manage bus resources", () => {
        const actions = [
          Action.CREATE,
          Action.UPDATE,
          Action.DELETE,
          Action.MANAGE,
        ];

        actions.forEach((action) => {
          const canPerform = RBACManager.canPerformAction(
            UserRole.PARENT,
            ResourceType.BUS,
            action,
          );
          expect(canPerform).toBe(false);
        });
      });
    });

    describe("Student Resource Access", () => {
      test("School Manager can manage all student resources in their tenant", () => {
        const actions = [
          Action.CREATE,
          Action.READ,
          Action.UPDATE,
          Action.MANAGE,
        ];

        actions.forEach((action) => {
          const canPerform = RBACManager.canPerformAction(
            UserRole.SCHOOL_MANAGER,
            ResourceType.STUDENT,
            action,
          );
          expect(canPerform).toBe(true);
        });
      });

      test("Parent can only read their own children", () => {
        const canRead = RBACManager.canPerformAction(
          UserRole.PARENT,
          ResourceType.STUDENT,
          Action.READ,
        );
        const canManage = RBACManager.canPerformAction(
          UserRole.PARENT,
          ResourceType.STUDENT,
          Action.MANAGE,
        );

        expect(canRead).toBe(true);
        expect(canManage).toBe(false);
      });
    });
  });

  describe("🏢 Cross-Tenant Access Control Tests", () => {
    test("School Manager cannot access resources from other schools", () => {
      const context = {
        userId: mockUsers.schoolManager.id,
        tenantId: "school-1",
        resourceTenantId: "school-2",
      };

      const canAccessStudents = RBACManager.canPerformAction(
        UserRole.SCHOOL_MANAGER,
        ResourceType.STUDENT,
        Action.READ,
        context,
      );

      const canAccessBuses = RBACManager.canPerformAction(
        UserRole.SCHOOL_MANAGER,
        ResourceType.BUS,
        Action.READ,
        context,
      );

      expect(canAccessStudents).toBe(false);
      expect(canAccessBuses).toBe(false);
    });

    test("Driver cannot access buses from other schools", () => {
      const context = {
        userId: mockUsers.driver.id,
        tenantId: "school-1",
        resourceTenantId: "school-2",
      };

      const canAccess = RBACManager.canPerformAction(
        UserRole.DRIVER,
        ResourceType.BUS,
        Action.READ,
        context,
      );

      expect(canAccess).toBe(false);
    });

    test("Parent cannot access students from other schools", () => {
      const context = {
        userId: mockUsers.parent.id,
        tenantId: "school-1",
        resourceTenantId: "school-2",
      };

      const canAccess = RBACManager.canPerformAction(
        UserRole.PARENT,
        ResourceType.STUDENT,
        Action.READ,
        context,
      );

      expect(canAccess).toBe(false);
    });

    test("Admin can access resources from any school", () => {
      const context = {
        userId: mockUsers.admin.id,
        tenantId: null,
        resourceTenantId: "school-2",
      };

      const canAccessStudents = RBACManager.canPerformAction(
        UserRole.ADMIN,
        ResourceType.STUDENT,
        Action.READ,
        context,
      );

      const canAccessBuses = RBACManager.canPerformAction(
        UserRole.ADMIN,
        ResourceType.BUS,
        Action.READ,
        context,
      );

      expect(canAccessStudents).toBe(true);
      expect(canAccessBuses).toBe(true);
    });
  });

  describe("🛡️ Security Middleware Tests", () => {
    test("Should block access for inactive users", () => {
      const result = AuthMiddleware.checkResourceAccess(
        mockUsers.inactiveUser,
        ResourceType.BUS,
        Action.READ,
      );

      expect(result.allowed).toBe(false);
      expect(result.error).toContain("deactivated");
    });

    test("Should enforce rate limiting for API calls", () => {
      const userId = mockUsers.driver.id;
      const action = "bus_tracking";
      const limit = 10;
      const windowMinutes = 1;

      // Simulate multiple requests within the rate limit
      for (let i = 0; i < limit; i++) {
        const result = AuthMiddleware.checkRateLimit(
          userId,
          action,
          limit,
          windowMinutes,
        );
        expect(result.allowed).toBe(true);
      }

      // The next request should be blocked
      const blockedResult = AuthMiddleware.checkRateLimit(
        userId,
        action,
        limit,
        windowMinutes,
      );
      expect(blockedResult.allowed).toBe(false);
      expect(blockedResult.error).toContain("rate limit");
    });

    test("Should validate user edit permissions within same tenant", () => {
      const canEdit = AuthMiddleware.checkUserEditPermission(
        mockUsers.schoolManager,
        mockUsers.driver,
      );

      expect(canEdit.allowed).toBe(true);
    });

    test("Should prevent cross-tenant user editing", () => {
      const otherSchoolDriver = {
        ...mockUsers.driver,
        tenant_id: "school-2",
      };

      const canEdit = AuthMiddleware.checkUserEditPermission(
        mockUsers.schoolManager,
        otherSchoolDriver,
      );

      expect(canEdit.allowed).toBe(false);
      expect(canEdit.error).toContain("different tenant");
    });

    test("Should log security violations", () => {
      const consoleSpy = jest.spyOn(console, "log").mockImplementation();

      AuthMiddleware.logSecurityViolation(
        mockUsers.driver.id,
        "unauthorized_access",
        "Attempted to access admin panel",
        { resource: "admin_panel" },
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining("Security Violation"),
      );

      consoleSpy.mockRestore();
    });
  });

  describe("👑 Role Hierarchy Tests", () => {
    test("Admin can manage all roles", () => {
      const roles = [
        UserRole.SCHOOL_MANAGER,
        UserRole.DRIVER,
        UserRole.PARENT,
        UserRole.STUDENT,
      ];

      roles.forEach((role) => {
        const canManage = RBACManager.canManageRole(UserRole.ADMIN, role);
        expect(canManage).toBe(true);
      });
    });

    test("School Manager can manage lower-level roles", () => {
      const allowedRoles = [UserRole.DRIVER, UserRole.PARENT, UserRole.STUDENT];
      const forbiddenRoles = [UserRole.ADMIN, UserRole.SCHOOL_MANAGER];

      allowedRoles.forEach((role) => {
        const canManage = RBACManager.canManageRole(
          UserRole.SCHOOL_MANAGER,
          role,
        );
        expect(canManage).toBe(true);
      });

      forbiddenRoles.forEach((role) => {
        const canManage = RBACManager.canManageRole(
          UserRole.SCHOOL_MANAGER,
          role,
        );
        expect(canManage).toBe(false);
      });
    });

    test("Driver cannot manage any roles", () => {
      const roles = [
        UserRole.ADMIN,
        UserRole.SCHOOL_MANAGER,
        UserRole.PARENT,
        UserRole.STUDENT,
      ];

      roles.forEach((role) => {
        const canManage = RBACManager.canManageRole(UserRole.DRIVER, role);
        expect(canManage).toBe(false);
      });
    });

    test("Parent cannot manage any roles", () => {
      const roles = [
        UserRole.ADMIN,
        UserRole.SCHOOL_MANAGER,
        UserRole.DRIVER,
        UserRole.STUDENT,
      ];

      roles.forEach((role) => {
        const canManage = RBACManager.canManageRole(UserRole.PARENT, role);
        expect(canManage).toBe(false);
      });
    });
  });

  describe("🔍 Data Filtering Tests", () => {
    test("Admin should see all students across all schools", () => {
      const filtered = AuthMiddleware.filterDataByPermissions(
        mockUsers.admin,
        mockResources.students,
        ResourceType.STUDENT,
      );

      expect(filtered).toHaveLength(mockResources.students.length);
      expect(filtered).toEqual(mockResources.students);
    });

    test("School Manager should only see students from their school", () => {
      const filtered = AuthMiddleware.filterDataByPermissions(
        mockUsers.schoolManager,
        mockResources.students,
        ResourceType.STUDENT,
      );

      const expectedStudents = mockResources.students.filter(
        (student) => student.tenant_id === mockUsers.schoolManager.tenant_id,
      );

      expect(filtered).toHaveLength(expectedStudents.length);
      expect(
        filtered.every((student) => student.tenant_id === "school-1"),
      ).toBe(true);
    });

    test("Parent should only see their own children", () => {
      const filtered = AuthMiddleware.filterDataByPermissions(
        mockUsers.parent,
        mockResources.students,
        ResourceType.STUDENT,
      );

      const expectedStudents = mockResources.students.filter(
        (student) => student.parent_id === mockUsers.parent.id,
      );

      expect(filtered).toHaveLength(expectedStudents.length);
      expect(
        filtered.every((student) => student.parent_id === mockUsers.parent.id),
      ).toBe(true);
    });

    test("Driver should see students on their assigned routes", () => {
      const filtered = AuthMiddleware.filterDataByPermissions(
        mockUsers.driver,
        mockResources.students,
        ResourceType.STUDENT,
        { assignedRoutes: ["route-1"] },
      );

      // This would depend on the actual implementation of route assignments
      expect(Array.isArray(filtered)).toBe(true);
    });

    test("Student should only see their own profile", () => {
      const studentData = [mockResources.students[0]];
      const filtered = AuthMiddleware.filterDataByPermissions(
        mockUsers.student,
        studentData,
        ResourceType.STUDENT,
      );

      expect(filtered).toHaveLength(1);
      expect(filtered[0].id).toBe(mockUsers.student.id);
    });
  });

  describe("🔗 Integration Tests", () => {
    test("Complete workflow: School Manager managing a bus", () => {
      const user = mockUsers.schoolManager;
      const busData = mockResources.buses[0];

      // Step 1: Check if user has permission to manage buses
      const hasPermission = RBACManager.hasPermission(
        user.role as UserRole,
        Permission.MANAGE_BUSES,
      );
      expect(hasPermission).toBe(true);

      // Step 2: Check if user can perform the specific action
      const canUpdate = RBACManager.canPerformAction(
        user.role as UserRole,
        ResourceType.BUS,
        Action.UPDATE,
      );
      expect(canUpdate).toBe(true);

      // Step 3: Validate tenant access
      const context = {
        userId: user.id,
        tenantId: user.tenant_id,
        resourceTenantId: busData.tenant_id,
      };

      const hasAccess = AuthMiddleware.checkResourceAccess(
        user,
        ResourceType.BUS,
        Action.UPDATE,
        context,
      );
      expect(hasAccess.allowed).toBe(true);
    });

    test("Complete workflow: Parent viewing child attendance", () => {
      const user = mockUsers.parent;
      const studentData = mockResources.students.filter(
        (student) => student.parent_id === user.id,
      );

      // Step 1: Check if parent has permission to view attendance
      const hasPermission = RBACManager.hasPermission(
        user.role as UserRole,
        Permission.VIEW_ATTENDANCE,
      );
      expect(hasPermission).toBe(true);

      // Step 2: Filter data to only show their children
      const filtered = AuthMiddleware.filterDataByPermissions(
        user,
        mockResources.students,
        ResourceType.STUDENT,
      );

      expect(filtered.length).toBeGreaterThan(0);
      expect(filtered.every((student) => student.parent_id === user.id)).toBe(
        true,
      );
    });

    test("Complete workflow: Driver updating attendance", () => {
      const user = mockUsers.driver;

      // Step 1: Check if driver has permission to manage attendance
      const hasPermission = RBACManager.hasPermission(
        user.role as UserRole,
        Permission.MANAGE_ATTENDANCE,
      );
      expect(hasPermission).toBe(true);

      // Step 2: Check if driver can perform the update action
      const canUpdate = RBACManager.canPerformAction(
        user.role as UserRole,
        ResourceType.ATTENDANCE,
        Action.UPDATE,
      );
      expect(canUpdate).toBe(true);

      // Step 3: Validate that driver can only update attendance for their assigned route
      const context = {
        userId: user.id,
        tenantId: user.tenant_id,
        assignedRoutes: ["route-1"],
      };

      const hasAccess = AuthMiddleware.checkResourceAccess(
        user,
        ResourceType.ATTENDANCE,
        Action.UPDATE,
        context,
      );
      expect(hasAccess.allowed).toBe(true);
    });

    test("Security violation: Driver attempting to access admin functions", () => {
      const user = mockUsers.driver;

      // Attempt to access system admin functions
      const hasSystemPermission = RBACManager.hasPermission(
        user.role as UserRole,
        Permission.SYSTEM_ADMIN,
      );
      expect(hasSystemPermission).toBe(false);

      // Attempt to manage all users
      const canManageUsers = RBACManager.canPerformAction(
        user.role as UserRole,
        ResourceType.USER,
        Action.MANAGE,
      );
      expect(canManageUsers).toBe(false);

      // This should trigger a security violation log
      const result = AuthMiddleware.checkResourceAccess(
        user,
        ResourceType.SYSTEM,
        Action.MANAGE,
      );
      expect(result.allowed).toBe(false);
      expect(result.error).toContain("insufficient permissions");
    });
  });

  describe("🔍 Security Audit Tests", () => {
    test("RBAC audit should identify security issues", () => {
      const auditReport = RBACSecurityAudit.generateAuditReport();

      expect(auditReport).toHaveProperty("timestamp");
      expect(auditReport).toHaveProperty("overallSecurityScore");
      expect(auditReport).toHaveProperty("roleAudits");
      expect(auditReport).toHaveProperty("systemRecommendations");
      expect(auditReport).toHaveProperty("complianceStatus");

      expect(auditReport.overallSecurityScore).toBeGreaterThan(0);
      expect(auditReport.overallSecurityScore).toBeLessThanOrEqual(100);
    });

    test("Database security validation should pass", () => {
      const validation = DatabaseSecurityAudit.validateSchemaForRBAC();

      expect(validation).toHaveProperty("isValid");
      expect(validation).toHaveProperty("recommendations");
      expect(Array.isArray(validation.recommendations)).toBe(true);
    });

    test("Permission matrix should be consistent", () => {
      // Test that all roles have at least basic permissions
      const roles = Object.values(UserRole);

      roles.forEach((role) => {
        const hasBasicPermission = RBACManager.hasPermission(
          role,
          Permission.VIEW_OWN_PROFILE,
        );
        expect(hasBasicPermission).toBe(true);
      });
    });
  });

  describe("⚡ Performance Tests", () => {
    test("Permission checks should be fast", () => {
      const startTime = performance.now();

      // Perform 1000 permission checks
      for (let i = 0; i < 1000; i++) {
        RBACManager.hasPermission(
          UserRole.SCHOOL_MANAGER,
          Permission.MANAGE_STUDENTS,
        );
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete 1000 checks in less than 100ms
      expect(duration).toBeLessThan(100);
    });

    test("Data filtering should handle large datasets efficiently", () => {
      // Create a large dataset
      const largeStudentDataset = Array.from({ length: 10000 }, (_, i) => ({
        id: `student-${i}`,
        tenant_id: i % 2 === 0 ? "school-1" : "school-2",
        parent_id: `parent-${Math.floor(i / 10)}`,
        name: `Student ${i}`,
      }));

      const startTime = performance.now();

      const filtered = AuthMiddleware.filterDataByPermissions(
        mockUsers.schoolManager,
        largeStudentDataset,
        ResourceType.STUDENT,
      );

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should filter 10k records in less than 50ms
      expect(duration).toBeLessThan(50);
      expect(filtered.length).toBe(5000); // Half should belong to school-1
    });
  });
});

// Helper functions for testing
function createMockContext(overrides: any = {}) {
  return {
    userId: "test-user",
    tenantId: "test-tenant",
    resourceTenantId: "test-tenant",
    ipAddress: "127.0.0.1",
    userAgent: "test-agent",
    ...overrides,
  };
}

function expectSecurityViolation(fn: () => any) {
  const consoleSpy = jest.spyOn(console, "log").mockImplementation();

  fn();

  expect(consoleSpy).toHaveBeenCalledWith(
    expect.stringContaining("Security Violation"),
  );

  consoleSpy.mockRestore();
}

// Test data generators
function generateMockUsers(count: number, role: UserRole, tenantId: string) {
  return Array.from({ length: count }, (_, i) => ({
    id: `${role}-${i}`,
    role,
    tenant_id: tenantId,
    email: `${role}${i}@test.com`,
    name: `${role} ${i}`,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }));
}

function generateMockResources(type: string, count: number, tenantId: string) {
  return Array.from({ length: count }, (_, i) => ({
    id: `${type}-${i}`,
    tenant_id: tenantId,
    name: `${type} ${i}`,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }));
}
