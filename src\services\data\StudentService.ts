/**
 * Student Service
 * Handles all student-related API operations
 * Phase 2: Application Structure Reorganization
 */

import { BaseService } from '../base/BaseService';
import {
  Student,
  APIResponse,
  PaginatedResponse,
  PaginationParams,
} from '../../api/types';

export interface StudentListParams extends PaginationParams {
  parent_id?: string;
  route_stop_id?: string;
  tenant_id?: string;
  grade?: string;
  class?: string;
  is_active?: boolean;
  search?: string;
  search_fields?: string[];
}

export interface CreateStudentRequest {
  name: string;
  student_id: string;
  grade: string;
  class: string;
  parent_id: string;
  route_stop_id?: string;
  tenant_id: string;
  profile: {
    date_of_birth: string;
    address: any;
    emergency_contacts: any[];
    medical_info?: any;
    photo_url?: string;
  };
}

export interface UpdateStudentRequest {
  name?: string;
  student_id?: string;
  grade?: string;
  class?: string;
  parent_id?: string;
  route_stop_id?: string;
  is_active?: boolean;
  profile?: Partial<CreateStudentRequest['profile']>;
}

/**
 * Student Service Class
 * Implements comprehensive student management operations
 */
export class StudentService extends BaseService {
  private readonly endpoint = '/students';

  /**
   * Get paginated list of students
   */
  async getStudents(params: StudentListParams = {}): Promise<APIResponse<PaginatedResponse<Student>>> {
    return this.getPaginated<Student>(this.endpoint, params);
  }

  /**
   * Get student by ID
   */
  async getStudentById(id: string): Promise<APIResponse<Student>> {
    return this.get<Student>(`${this.endpoint}/${id}`);
  }

  /**
   * Get students for current parent
   */
  async getMyChildren(): Promise<APIResponse<Student[]>> {
    const response = await this.get<PaginatedResponse<Student>>(`${this.endpoint}/my-children`);
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<Student[]>;
  }

  /**
   * Create new student
   */
  async createStudent(studentData: CreateStudentRequest): Promise<APIResponse<Student>> {
    const validation = await this.validateCreateStudentData(studentData);
    if (!validation.isValid) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid student data',
          details: validation.errors,
        },
      };
    }

    // استخدام Transaction Manager للعمليات المعقدة
    const { TransactionManager } = await import('../../lib/transaction-manager');
    return await TransactionManager.createStudentWithTransaction(studentData);
  }

  /**
   * Update student
   */
  async updateStudent(id: string, studentData: UpdateStudentRequest): Promise<APIResponse<Student>> {
    const validation = this.validateUpdateStudentData(studentData);
    if (!validation.isValid) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid update data',
          details: validation.errors,
        },
      };
    }

    return this.put<Student>(`${this.endpoint}/${id}`, studentData);
  }

  /**
   * Delete student
   */
  async deleteStudent(id: string): Promise<APIResponse<{ message: string }>> {
    return this.delete<{ message: string }>(`${this.endpoint}/${id}`);
  }

  /**
   * Assign student to route stop
   */
  async assignToRouteStop(studentId: string, routeStopId: string): Promise<APIResponse<Student>> {
    return this.patch<Student>(`${this.endpoint}/${studentId}/route-stop`, { route_stop_id: routeStopId });
  }

  /**
   * Get student statistics
   */
  async getStudentStats(id?: string, tenantId?: string): Promise<APIResponse<StudentStats>> {
    const params: any = {};
    if (tenantId) params.tenant_id = tenantId;
    
    const queryString = this.buildQueryString(params);
    const endpoint = id 
      ? `${this.endpoint}/${id}/stats${queryString}`
      : `${this.endpoint}/stats${queryString}`;
    
    return this.get<StudentStats>(endpoint);
  }

  /**
   * Get students by parent
   */
  async getStudentsByParent(parentId: string): Promise<APIResponse<Student[]>> {
    const response = await this.getStudents({ parent_id: parentId });
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<Student[]>;
  }

  /**
   * Get students by route stop
   */
  async getStudentsByRouteStop(routeStopId: string): Promise<APIResponse<Student[]>> {
    const response = await this.getStudents({ route_stop_id: routeStopId });
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<Student[]>;
  }

  /**
   * Get students by grade and class
   */
  async getStudentsByGradeAndClass(grade: string, className: string, tenantId?: string): Promise<APIResponse<Student[]>> {
    const params: StudentListParams = { grade, class: className };
    if (tenantId) params.tenant_id = tenantId;

    const response = await this.getStudents(params);
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<Student[]>;
  }

  /**
   * Search students
   */
  async searchStudents(
    query: string,
    fields: string[] = ['name', 'student_id'],
    tenantId?: string
  ): Promise<APIResponse<Student[]>> {
    const params: StudentListParams = {
      search: query,
      search_fields: fields,
    };
    if (tenantId) params.tenant_id = tenantId;

    const response = await this.getStudents(params);
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<Student[]>;
  }

  /**
   * Upload student photo
   */
  async uploadStudentPhoto(id: string, file: File): Promise<APIResponse<{ photo_url: string }>> {
    const formData = new FormData();
    formData.append('photo', file);

    return this.request<{ photo_url: string }>(`${this.endpoint}/${id}/photo`, {
      method: 'POST',
      body: formData,
      headers: {}, // Let browser set Content-Type for FormData
    });
  }

  /**
   * Bulk operations
   */
  async bulkCreateStudents(students: CreateStudentRequest[]): Promise<APIResponse<BulkOperationResult<Student>>> {
    return this.post<BulkOperationResult<Student>>(`${this.endpoint}/bulk`, { students });
  }

  async bulkUpdateStudents(
    updates: Array<{ id: string; data: UpdateStudentRequest }>
  ): Promise<APIResponse<BulkOperationResult<Student>>> {
    return this.put<BulkOperationResult<Student>>(`${this.endpoint}/bulk`, { updates });
  }

  async bulkAssignToRouteStop(
    studentIds: string[],
    routeStopId: string
  ): Promise<APIResponse<BulkOperationResult<Student>>> {
    return this.patch<BulkOperationResult<Student>>(`${this.endpoint}/bulk/assign-route-stop`, {
      student_ids: studentIds,
      route_stop_id: routeStopId,
    });
  }

  /**
   * Validation methods
   */
  private async validateCreateStudentData(data: CreateStudentRequest): Promise<ValidationResult> {
    // استخدام خدمة التحقق المحسنة
    const { EnhancedValidationService } = await import('../../lib/validation-enhanced');

    const errors: string[] = [];

    // التحقق الأساسي من البيانات
    const basicValidation = await EnhancedValidationService.validateStudentData({
      name: data.name,
      student_id: data.student_id,
      grade: data.grade,
      email: data.profile?.email,
      phone: data.profile?.phone,
      date_of_birth: data.profile?.date_of_birth,
      tenant_id: data.tenant_id
    });

    if (!basicValidation.isValid) {
      errors.push(...basicValidation.errors);
    }

    // التحقق من الحقول الإضافية
    if (!data.class || data.class.trim().length === 0) {
      errors.push('Class is required');
    }

    if (!data.parent_id || data.parent_id.trim().length === 0) {
      errors.push('Parent ID is required');
    }

    if (!data.profile || !data.profile.address) {
      errors.push('Address is required');
    }

    if (!data.profile || !data.profile.emergency_contacts || data.profile.emergency_contacts.length === 0) {
      errors.push('At least one emergency contact is required');
    }

    // التحقق من معلومات الاتصال الطارئ
    if (data.profile?.emergency_contacts) {
      for (let i = 0; i < data.profile.emergency_contacts.length; i++) {
        const contact = data.profile.emergency_contacts[i];

        if (!contact.name || contact.name.trim().length < 2) {
          errors.push(`Emergency contact ${i + 1}: Name is required`);
        }

        if (contact.phone) {
          const phoneCheck = EnhancedValidationService.validatePhone(contact.phone);
          if (!phoneCheck.isValid) {
            errors.push(`Emergency contact ${i + 1}: ${phoneCheck.error}`);
          }
        }

        if (contact.email) {
          const emailCheck = EnhancedValidationService.validateEmail(contact.email);
          if (!emailCheck.isValid) {
            errors.push(`Emergency contact ${i + 1}: ${emailCheck.error}`);
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings: basicValidation.warnings
    };
  }

  private validateUpdateStudentData(data: UpdateStudentRequest): ValidationResult {
    const errors: string[] = [];

    if (data.name !== undefined && (!data.name || data.name.trim().length < 2)) {
      errors.push('Student name must be at least 2 characters long');
    }

    if (data.student_id !== undefined && (!data.student_id || data.student_id.trim().length < 3)) {
      errors.push('Student ID must be at least 3 characters long');
    }

    if (data.grade !== undefined && (!data.grade || data.grade.trim().length === 0)) {
      errors.push('Grade is required');
    }

    if (data.class !== undefined && (!data.class || data.class.trim().length === 0)) {
      errors.push('Class is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// ============================================================================
// Supporting Types
// ============================================================================

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

interface StudentStats {
  total_students: number;
  active_students: number;
  by_grade: Record<string, number>;
  by_class: Record<string, number>;
  with_route_assignment: number;
  without_route_assignment: number;
  attendance_rate: number;
  last_updated: string;
}

interface BulkOperationResult<T> {
  successful: T[];
  failed: Array<{
    data: any;
    error: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

// ============================================================================
// Service Instance
// ============================================================================

export const studentService = new StudentService();
export { StudentService };
