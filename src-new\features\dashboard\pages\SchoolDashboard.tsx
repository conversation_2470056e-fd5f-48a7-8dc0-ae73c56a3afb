import React from 'react';
import { useTranslation } from 'react-i18next';
import { TrendingUp } from 'lucide-react';
import { Navbar } from '../../components/layout/Navbar';
import { Sidebar } from '../../components/layout/Sidebar';
import { SchoolStats } from '../../components/dashboard/SchoolStats';
import { AttendanceChart } from '../../components/dashboard/AttendanceChart';
import { LiveMap } from '../../components/map/LiveMap';
import { useAuth } from '../../contexts/AuthContext';

export const SchoolDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  
  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />
        
        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {t('dashboard.overview')}
                </h1>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {t('dashboard.welcome')}, {user?.name}
                </p>
              </div>
              
              <div className="mt-4 md:mt-0">
                <div className="inline-flex rounded-md shadow-sm">
                  <span className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md">
                    <TrendingUp size={16} className="mr-2 text-primary-500" />
                    {new Date().toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
            
            <SchoolStats />
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
              <div className="lg:col-span-2">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                      {t('tracking.liveTracking')}
                    </h2>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-accent-100 text-accent-800 dark:bg-accent-800 dark:text-accent-100">
                      Live
                    </span>
                  </div>
                  <div className="h-[400px]">
                    <LiveMap />
                  </div>
                </div>
              </div>
              
              <div>
                <AttendanceChart />
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};