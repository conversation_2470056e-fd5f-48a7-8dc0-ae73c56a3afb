#!/usr/bin/env tsx

/**
 * سكريبت لتطبيق SQL مباشرة
 * Script to apply SQL directly
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// تحميل متغيرات البيئة
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applySQLFixes() {
  console.log('🔧 تطبيق إصلاحات SQL مباشرة...');

  try {
    // 1. إزالة السياسات المتضاربة
    console.log('🗑️ إزالة السياسات المتضاربة...');
    
    const dropPoliciesSQL = `
      DROP POLICY IF EXISTS "users_select_policy" ON users;
      DROP POLICY IF EXISTS "users_insert_policy" ON users;
      DROP POLICY IF EXISTS "users_update_policy" ON users;
      DROP POLICY IF EXISTS "users_delete_policy" ON users;
      DROP POLICY IF EXISTS "Enable read access for all users" ON users;
      DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON users;
      DROP POLICY IF EXISTS "Enable update for users based on email" ON users;
      DROP POLICY IF EXISTS "Enable delete for users based on email" ON users;
      DROP POLICY IF EXISTS "Users can view their own profile" ON users;
      DROP POLICY IF EXISTS "Users can update their own profile" ON users;
      DROP POLICY IF EXISTS "Admin can view all users" ON users;
      DROP POLICY IF EXISTS "Admin can insert users" ON users;
      DROP POLICY IF EXISTS "Admin can update all users" ON users;
      DROP POLICY IF EXISTS "Admin can delete users" ON users;
      DROP POLICY IF EXISTS "School managers can view users in their tenant" ON users;
      DROP POLICY IF EXISTS "School managers can insert users in their tenant" ON users;
      DROP POLICY IF EXISTS "School managers can update users in their tenant" ON users;
    `;

    const { error: dropError } = await supabase.rpc('exec_sql', { sql: dropPoliciesSQL });
    if (dropError) {
      console.log('⚠️ تحذير في إزالة السياسات:', dropError.message);
    }

    // 2. تعطيل RLS مؤقتاً
    console.log('⏸️ تعطيل RLS مؤقتاً...');
    const { error: disableError } = await supabase.rpc('exec_sql', { 
      sql: 'ALTER TABLE users DISABLE ROW LEVEL SECURITY;' 
    });
    if (disableError) {
      console.log('⚠️ تحذير في تعطيل RLS:', disableError.message);
    }

    // 3. إنشاء الدوال المساعدة
    console.log('🔧 إنشاء الدوال المساعدة...');
    
    const createFunctionsSQL = `
      CREATE OR REPLACE FUNCTION auth.user_role()
      RETURNS text
      LANGUAGE sql
      SECURITY DEFINER
      STABLE
      AS $$
        SELECT COALESCE(
          (SELECT role FROM users WHERE id = auth.uid()),
          'anonymous'
        );
      $$;

      CREATE OR REPLACE FUNCTION auth.user_tenant_id()
      RETURNS uuid
      LANGUAGE sql
      SECURITY DEFINER
      STABLE
      AS $$
        SELECT tenant_id FROM users WHERE id = auth.uid();
      $$;
    `;

    const { error: functionsError } = await supabase.rpc('exec_sql', { sql: createFunctionsSQL });
    if (functionsError) {
      console.error('❌ خطأ في إنشاء الدوال:', functionsError);
      throw functionsError;
    }

    // 4. إنشاء السياسات الجديدة
    console.log('🛡️ إنشاء السياسات الجديدة...');
    
    const createPoliciesSQL = `
      CREATE POLICY "users_read_policy" ON users
        FOR SELECT
        USING (
          id = auth.uid()
          OR auth.user_role() = 'admin'
          OR (auth.user_role() = 'school_manager' AND tenant_id = auth.user_tenant_id())
          OR (auth.user_role() = 'supervisor' AND tenant_id = auth.user_tenant_id())
        );

      CREATE POLICY "users_insert_policy" ON users
        FOR INSERT
        WITH CHECK (
          auth.uid() IS NULL
          OR id = auth.uid()
          OR auth.user_role() = 'admin'
          OR (auth.user_role() = 'school_manager' AND tenant_id = auth.user_tenant_id())
        );

      CREATE POLICY "users_update_policy" ON users
        FOR UPDATE
        USING (
          id = auth.uid()
          OR auth.user_role() = 'admin'
          OR (auth.user_role() = 'school_manager' AND tenant_id = auth.user_tenant_id())
        );

      CREATE POLICY "users_delete_policy" ON users
        FOR DELETE
        USING (
          auth.user_role() = 'admin'
          OR (auth.user_role() = 'school_manager' AND tenant_id = auth.user_tenant_id() AND id != auth.uid())
        );
    `;

    const { error: policiesError } = await supabase.rpc('exec_sql', { sql: createPoliciesSQL });
    if (policiesError) {
      console.error('❌ خطأ في إنشاء السياسات:', policiesError);
      throw policiesError;
    }

    // 5. إعادة تفعيل RLS
    console.log('🔒 إعادة تفعيل RLS...');
    const { error: enableError } = await supabase.rpc('exec_sql', { 
      sql: 'ALTER TABLE users ENABLE ROW LEVEL SECURITY;' 
    });
    if (enableError) {
      console.error('❌ خطأ في تفعيل RLS:', enableError);
      throw enableError;
    }

    // 6. إنشاء دالة إنشاء المستخدم
    console.log('👤 إنشاء دالة إنشاء المستخدم...');
    
    const createUserFunctionSQL = `
      CREATE OR REPLACE FUNCTION public.create_user_profile(
        user_id uuid,
        user_email text,
        user_name text,
        user_role text DEFAULT 'student',
        user_tenant_id uuid DEFAULT NULL
      )
      RETURNS users
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      DECLARE
        new_user users;
      BEGIN
        SELECT * INTO new_user FROM users WHERE id = user_id;
        
        IF new_user.id IS NOT NULL THEN
          RETURN new_user;
        END IF;

        INSERT INTO users (
          id, email, name, role, tenant_id, is_active, created_at, updated_at
        ) VALUES (
          user_id, user_email, user_name, user_role, user_tenant_id, true, NOW(), NOW()
        ) RETURNING * INTO new_user;

        RETURN new_user;
      END;
      $$;
    `;

    const { error: userFunctionError } = await supabase.rpc('exec_sql', { sql: createUserFunctionSQL });
    if (userFunctionError) {
      console.error('❌ خطأ في إنشاء دالة المستخدم:', userFunctionError);
      throw userFunctionError;
    }

    console.log('✅ تم تطبيق جميع الإصلاحات بنجاح!');

  } catch (error) {
    console.error('🚨 خطأ في تطبيق الإصلاحات:', error);
    throw error;
  }
}

// تشغيل السكريبت
applySQLFixes()
  .then(() => {
    console.log('🎉 اكتمل تطبيق الإصلاحات بنجاح!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ فشل في تطبيق الإصلاحات:', error);
    process.exit(1);
  });
