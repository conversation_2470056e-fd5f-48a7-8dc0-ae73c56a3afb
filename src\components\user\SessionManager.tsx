/**
 * مكون إدارة الجلسات للمستخدم
 * User Session Manager Component
 * 
 * المرحلة 1: تأمين الدخول والسياسات الأمنية الأساسية
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  MapPin, 
  Clock, 
  Shield, 
  LogOut,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import { Button } from '../ui/Button';
import { SessionManagementService, UserSession } from '../../services/security/SessionManagement';
import { useAuth } from '../../contexts/AuthContext';

export const SessionManager: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [sessions, setSessions] = useState<UserSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [terminating, setTerminating] = useState<string | null>(null);

  const sessionService = SessionManagementService.getInstance();

  useEffect(() => {
    if (user) {
      loadSessions();
    }
  }, [user]);

  const loadSessions = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const userSessions = await sessionService.getUserActiveSessions(user.id);
      setSessions(userSessions);
    } catch (error) {
      console.error('Error loading sessions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTerminateSession = async (sessionToken: string) => {
    setTerminating(sessionToken);
    try {
      await sessionService.terminateSession(sessionToken, 'User terminated session');
      await loadSessions();
    } catch (error) {
      console.error('Error terminating session:', error);
    } finally {
      setTerminating(null);
    }
  };

  const handleTerminateAllOtherSessions = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // الحصول على التوكن الحالي (محاكاة)
      const currentToken = 'current-session-token'; // يجب الحصول عليه من السياق
      await sessionService.terminateAllUserSessions(user.id, currentToken);
      await loadSessions();
    } catch (error) {
      console.error('Error terminating all sessions:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile':
        return <Smartphone className="w-5 h-5" />;
      case 'tablet':
        return <Tablet className="w-5 h-5" />;
      default:
        return <Monitor className="w-5 h-5" />;
    }
  };

  const getDeviceTypeText = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile':
        return 'هاتف محمول';
      case 'tablet':
        return 'جهاز لوحي';
      default:
        return 'حاسوب مكتبي';
    }
  };

  const formatLastActivity = (lastActivity: string) => {
    const date = new Date(lastActivity);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffMinutes < 1) return 'الآن';
    if (diffMinutes < 60) return `منذ ${diffMinutes} دقيقة`;
    if (diffMinutes < 1440) return `منذ ${Math.floor(diffMinutes / 60)} ساعة`;
    return `منذ ${Math.floor(diffMinutes / 1440)} يوم`;
  };

  const isCurrentSession = (session: UserSession) => {
    // محاكاة فحص الجلسة الحالية
    // في التطبيق الحقيقي، قارن مع التوكن الحالي
    const now = new Date();
    const lastActivity = new Date(session.last_activity);
    return (now.getTime() - lastActivity.getTime()) < 60000; // آخر دقيقة
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-8 h-8 animate-spin text-primary-500" />
        <span className="mr-3 text-lg">جاري تحميل الجلسات...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* العنوان والأدوات */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 space-x-reverse">
          <Shield className="w-8 h-8 text-primary-500" />
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              إدارة الجلسات
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              عرض وإدارة جلسات تسجيل الدخول النشطة
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3 space-x-reverse">
          <Button
            onClick={loadSessions}
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            تحديث
          </Button>
          {sessions.length > 1 && (
            <Button
              onClick={handleTerminateAllOtherSessions}
              variant="outline"
              className="flex items-center gap-2 text-red-600 hover:text-red-700"
            >
              <LogOut className="w-4 h-4" />
              إنهاء الجلسات الأخرى
            </Button>
          )}
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                الجلسات النشطة
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {sessions.length}
              </p>
            </div>
            <Shield className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                أنواع الأجهزة
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {new Set(sessions.map(s => s.device_info?.device_type)).size}
              </p>
            </div>
            <Monitor className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                المواقع
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {new Set(sessions.map(s => s.location?.city || 'غير محدد')).size}
              </p>
            </div>
            <MapPin className="w-8 h-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* قائمة الجلسات */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            الجلسات النشطة
          </h3>
        </div>
        
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {sessions.length === 0 ? (
            <div className="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
              لا توجد جلسات نشطة
            </div>
          ) : (
            sessions.map((session) => (
              <div key={session.id} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 space-x-reverse">
                    {/* أيقونة الجهاز */}
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                        {getDeviceIcon(session.device_info?.device_type || 'desktop')}
                      </div>
                    </div>
                    
                    {/* معلومات الجلسة */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {session.device_info?.browser || 'متصفح غير محدد'} على {getDeviceTypeText(session.device_info?.device_type || 'desktop')}
                        </p>
                        {isCurrentSession(session) && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            الجلسة الحالية
                          </span>
                        )}
                      </div>
                      
                      <div className="mt-1 flex items-center space-x-4 space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                        <div className="flex items-center space-x-1 space-x-reverse">
                          <MapPin className="w-3 h-3" />
                          <span>
                            {session.location?.city || 'موقع غير محدد'}, {session.location?.country || ''}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-1 space-x-reverse">
                          <Clock className="w-3 h-3" />
                          <span>{formatLastActivity(session.last_activity)}</span>
                        </div>
                      </div>
                      
                      <div className="mt-1 text-xs text-gray-400 dark:text-gray-500">
                        IP: {session.ip_address} • 
                        نظام التشغيل: {session.device_info?.os || 'غير محدد'}
                      </div>
                    </div>
                  </div>
                  
                  {/* أزرار العمل */}
                  <div className="flex items-center space-x-2 space-x-reverse">
                    {!isCurrentSession(session) && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleTerminateSession(session.session_token)}
                        disabled={terminating === session.session_token}
                        className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
                      >
                        {terminating === session.session_token ? (
                          <RefreshCw className="w-4 h-4 animate-spin" />
                        ) : (
                          <>
                            <LogOut className="w-4 h-4 ml-1" />
                            إنهاء
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* تحذير أمني */}
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
        <div className="flex items-start space-x-3 space-x-reverse">
          <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              نصائح أمنية مهمة
            </h4>
            <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
              <ul className="list-disc list-inside space-y-1">
                <li>راجع الجلسات النشطة بانتظام للتأكد من عدم وجود نشاط مشبوه</li>
                <li>أنهِ أي جلسة لا تتعرف عليها فوراً</li>
                <li>استخدم شبكات آمنة وتجنب الشبكات العامة للأنشطة الحساسة</li>
                <li>سجل الخروج دائماً عند استخدام أجهزة مشتركة</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
