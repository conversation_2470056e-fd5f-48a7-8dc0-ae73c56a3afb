{"build_info": {"timestamp": "2025-06-02T10-20-21-641Z", "total_principles": 5, "total_guards": 5, "total_middleware": 4, "implementation_status": "completed"}, "principles": [{"name": "never_trust_always_verify", "description": "لا تثق أبداً، تحقق دائماً", "implementation": "continuous_authentication", "status": "implementing"}, {"name": "least_privilege_access", "description": "أقل صلاحيات ممكنة", "implementation": "dynamic_permissions", "status": "implementing"}, {"name": "assume_breach", "description": "افترض حدوث اختراق", "implementation": "zero_trust_network", "status": "implementing"}, {"name": "verify_explicitly", "description": "تحقق صراحة من كل شيء", "implementation": "multi_factor_verification", "status": "implementing"}, {"name": "secure_by_design", "description": "آمن بالتصميم", "implementation": "security_first_architecture", "status": "implementing"}], "guards": [{"name": "Authentication<PERSON><PERSON>", "description": "حارس المصادقة", "type": "route_guard", "priority": "critical"}, {"name": "AuthorizationGuard", "description": "حارس التخويل", "type": "route_guard", "priority": "critical"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "حارس المستأجر", "type": "data_guard", "priority": "high"}, {"name": "RateLimitGuard", "description": "حارس معدل الطلبات", "type": "network_guard", "priority": "medium"}, {"name": "SecurityHeadersGuard", "description": "حارس رؤوس الأمان", "type": "network_guard", "priority": "medium"}], "middleware": [{"name": "SecurityMiddleware", "description": "وسيط الأمان الرئيسي", "type": "global", "priority": "critical"}, {"name": "AuditMiddleware", "description": "وسيط التدقيق", "type": "logging", "priority": "high"}, {"name": "EncryptionMiddleware", "description": "وسيط التشفير", "type": "data", "priority": "high"}, {"name": "ValidationMiddleware", "description": "وسيط التحقق", "type": "input", "priority": "medium"}], "security_layers": ["continuous_verification", "security_monitoring", "threat_response", "audit_logging"]}