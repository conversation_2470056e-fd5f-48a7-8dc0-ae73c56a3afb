import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Calendar, ChevronDown } from "lucide-react";
import { Button } from "./Button";
import { cn } from "../../utils/cn";

interface DateRange {
  startDate: string;
  endDate: string;
}

interface DateRangeSelectorProps {
  value: DateRange;
  onChange: (range: DateRange) => void;
  className?: string;
  disabled?: boolean;
  label?: string;
  showPresets?: boolean;
  maxDate?: string;
  minDate?: string;
}

const DATE_PRESETS = [
  {
    label: "Today",
    getValue: () => {
      const today = new Date().toISOString().split("T")[0];
      return { startDate: today, endDate: today };
    },
  },
  {
    label: "Yesterday",
    getValue: () => {
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0];
      return { startDate: yesterday, endDate: yesterday };
    },
  },
  {
    label: "Last 7 days",
    getValue: () => {
      const endDate = new Date().toISOString().split("T")[0];
      const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0];
      return { startDate, endDate };
    },
  },
  {
    label: "Last 30 days",
    getValue: () => {
      const endDate = new Date().toISOString().split("T")[0];
      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0];
      return { startDate, endDate };
    },
  },
  {
    label: "This month",
    getValue: () => {
      const now = new Date();
      const startDate = new Date(now.getFullYear(), now.getMonth(), 1)
        .toISOString()
        .split("T")[0];
      const endDate = new Date().toISOString().split("T")[0];
      return { startDate, endDate };
    },
  },
  {
    label: "Last month",
    getValue: () => {
      const now = new Date();
      const startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        .toISOString()
        .split("T")[0];
      const endDate = new Date(now.getFullYear(), now.getMonth(), 0)
        .toISOString()
        .split("T")[0];
      return { startDate, endDate };
    },
  },
];

export const DateRangeSelector: React.FC<DateRangeSelectorProps> = ({
  value,
  onChange,
  className = "",
  disabled = false,
  label,
  showPresets = true,
  maxDate,
  minDate,
}) => {
  const { t } = useTranslation();
  const [showPresetDropdown, setShowPresetDropdown] = useState(false);

  const handlePresetSelect = (preset: (typeof DATE_PRESETS)[0]) => {
    const range = preset.getValue();
    onChange(range);
    setShowPresetDropdown(false);
  };

  const handleStartDateChange = (date: string) => {
    onChange({ ...value, startDate: date });
  };

  const handleEndDateChange = (date: string) => {
    onChange({ ...value, endDate: date });
  };

  const formatDateRange = () => {
    if (!value.startDate || !value.endDate) return "Select date range";

    const start = new Date(value.startDate).toLocaleDateString();
    const end = new Date(value.endDate).toLocaleDateString();

    if (value.startDate === value.endDate) {
      return start;
    }

    return `${start} - ${end}`;
  };

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </label>
      )}

      <div className="flex flex-col sm:flex-row gap-2">
        {/* Date Inputs */}
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <Calendar size={16} className="text-gray-500" />
            <span className="text-sm text-gray-600 dark:text-gray-400">
              From:
            </span>
          </div>
          <input
            type="date"
            value={value.startDate}
            onChange={(e) => handleStartDateChange(e.target.value)}
            disabled={disabled}
            min={minDate}
            max={maxDate}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600 dark:text-gray-400">To:</span>
          <input
            type="date"
            value={value.endDate}
            onChange={(e) => handleEndDateChange(e.target.value)}
            disabled={disabled}
            min={value.startDate || minDate}
            max={maxDate}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        {/* Preset Dropdown */}
        {showPresets && (
          <div className="relative">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPresetDropdown(!showPresetDropdown)}
              disabled={disabled}
              className="flex items-center gap-2 whitespace-nowrap"
            >
              Quick Select
              <ChevronDown size={14} />
            </Button>

            {showPresetDropdown && (
              <div className="absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50">
                <div className="py-1">
                  {DATE_PRESETS.map((preset) => (
                    <button
                      key={preset.label}
                      onClick={() => handlePresetSelect(preset)}
                      className="w-full px-3 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      {preset.label}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Selected Range Display */}
      <div className="text-xs text-gray-500 dark:text-gray-400">
        Selected: {formatDateRange()}
      </div>

      {/* Click outside to close dropdown */}
      {showPresetDropdown && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowPresetDropdown(false)}
        />
      )}
    </div>
  );
};

export default DateRangeSelector;
