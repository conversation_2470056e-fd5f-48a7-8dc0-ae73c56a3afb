/**
 * Components Index
 * Centralized export for all components
 * Phase 2: Application Structure Reorganization
 */

// ============================================================================
// Common UI Components
// ============================================================================

// UI Base Components
export { Button } from './common/ui/Button';
export { Card, CardContent, CardHeader, CardTitle } from './common/ui/Card';
export { Badge } from './common/ui/Badge';
export { Alert, AlertDescription } from './common/ui/Alert';

// Form Components
export { SearchInput, SearchWithSuggestions } from './common/forms/SearchInput';
export { FilterSelect, MultiSelect } from './common/forms/FilterSelect';

// Data Display Components
export { DataTable, PaginatedTable } from './common/data-display/DataTable';
export { Pagination, SimplePagination } from './common/data-display/Pagination';

// Layout Components
export { Navbar } from './layout/Navbar';
export { Sidebar } from './layout/Sidebar';

// ============================================================================
// Feature Components
// ============================================================================

// User Management
export { UserList } from './features/user-management/UserList';

// School Management
export { SchoolList } from './features/school-management/SchoolList';

// Bus Management
export { BusList } from './features/bus-management/BusList';

// ============================================================================
// Role-Based Components
// ============================================================================

// Admin Components
export { AdminDashboard } from './role-based/admin/AdminDashboard';

// Driver Components
export { DriverDashboard } from './role-based/driver/DriverDashboard';

// ============================================================================
// Authentication Components
// ============================================================================
export { CentralizedPermissionGuard } from './auth/CentralizedPermissionGuard';
export { ProtectedRoute } from './auth/ProtectedRoute';

// ============================================================================
// Component Types
// ============================================================================

export type { TableColumn } from './common/data-display/DataTable';
export type { SelectOption } from './common/forms/FilterSelect';
export type { SearchInputProps } from './common/forms/SearchInput';
export type { PaginationProps } from './common/data-display/Pagination';

// ============================================================================
// Component Factory
// ============================================================================

/**
 * Component Factory for dynamic component creation
 */
export class ComponentFactory {
  private static components: Map<string, React.ComponentType<any>> = new Map();

  /**
   * Register a component
   */
  static register<T>(name: string, component: React.ComponentType<T>): void {
    this.components.set(name, component);
  }

  /**
   * Get a registered component
   */
  static get<T>(name: string): React.ComponentType<T> | undefined {
    return this.components.get(name);
  }

  /**
   * Check if component is registered
   */
  static has(name: string): boolean {
    return this.components.has(name);
  }

  /**
   * Get all registered component names
   */
  static getRegisteredNames(): string[] {
    return Array.from(this.components.keys());
  }
}

// ============================================================================
// Component Utilities
// ============================================================================

/**
 * Component props validation utilities
 */
export const ComponentUtils = {
  /**
   * Validate required props
   */
  validateRequiredProps<T extends Record<string, any>>(
    props: T,
    requiredProps: (keyof T)[]
  ): { isValid: boolean; missingProps: string[] } {
    const missingProps = requiredProps.filter(prop => 
      props[prop] === undefined || props[prop] === null
    ).map(prop => String(prop));

    return {
      isValid: missingProps.length === 0,
      missingProps,
    };
  },

  /**
   * Merge class names safely
   */
  mergeClassNames(...classNames: (string | undefined | null)[]): string {
    return classNames.filter(Boolean).join(' ');
  },

  /**
   * Create component display name
   */
  createDisplayName(baseName: string, wrapperName?: string): string {
    return wrapperName ? `${wrapperName}(${baseName})` : baseName;
  },
};

// ============================================================================
// Component Hooks
// ============================================================================

/**
 * Hook for component registration
 */
export const useComponentRegistry = () => {
  return {
    register: ComponentFactory.register,
    get: ComponentFactory.get,
    has: ComponentFactory.has,
    getRegisteredNames: ComponentFactory.getRegisteredNames,
  };
};

// ============================================================================
// Component Themes
// ============================================================================

/**
 * Theme configuration for components
 */
export interface ComponentTheme {
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
}

export const defaultTheme: ComponentTheme = {
  colors: {
    primary: '#3b82f6',
    secondary: '#6b7280',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#06b6d4',
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
  },
  borderRadius: {
    sm: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
  },
};

// ============================================================================
// Component Performance
// ============================================================================

/**
 * Performance monitoring for components
 */
export const ComponentPerformance = {
  /**
   * Measure component render time
   */
  measureRenderTime<T extends React.ComponentType<any>>(
    Component: T,
    displayName?: string
  ): T {
    const WrappedComponent = React.forwardRef<any, React.ComponentProps<T>>((props, ref) => {
      const startTime = performance.now();
      
      React.useEffect(() => {
        const endTime = performance.now();
        const renderTime = endTime - startTime;
        
        if (renderTime > 16) { // More than one frame (60fps)
          console.warn(
            `Component ${displayName || Component.name} took ${renderTime.toFixed(2)}ms to render`
          );
        }
      });

      return React.createElement(Component, { ...props, ref });
    });

    WrappedComponent.displayName = ComponentUtils.createDisplayName(
      Component.displayName || Component.name,
      'PerformanceMonitor'
    );

    return WrappedComponent as T;
  },

  /**
   * Memoize component with custom comparison
   */
  memoize<T extends React.ComponentType<any>>(
    Component: T,
    areEqual?: (prevProps: React.ComponentProps<T>, nextProps: React.ComponentProps<T>) => boolean
  ): T {
    const MemoizedComponent = React.memo(Component, areEqual);
    
    MemoizedComponent.displayName = ComponentUtils.createDisplayName(
      Component.displayName || Component.name,
      'Memo'
    );

    return MemoizedComponent as T;
  },
};

// ============================================================================
// Component Testing Utilities
// ============================================================================

/**
 * Testing utilities for components
 */
export const ComponentTestUtils = {
  /**
   * Create mock props for component testing
   */
  createMockProps<T extends Record<string, any>>(
    defaultProps: Partial<T>,
    overrides?: Partial<T>
  ): T {
    return { ...defaultProps, ...overrides } as T;
  },

  /**
   * Create mock event handlers
   */
  createMockHandlers<T extends Record<string, (...args: any[]) => any>>(
    handlers: (keyof T)[]
  ): T {
    const mockHandlers = {} as T;
    
    handlers.forEach(handler => {
      mockHandlers[handler] = jest.fn() as any;
    });

    return mockHandlers;
  },
};

// ============================================================================
// Export All
// ============================================================================

export default {
  ComponentFactory,
  ComponentUtils,
  ComponentPerformance,
  ComponentTestUtils,
  defaultTheme,
};
