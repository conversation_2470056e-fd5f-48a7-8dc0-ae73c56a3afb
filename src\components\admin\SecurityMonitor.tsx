/**
 * مكون مراقبة الأمان للأدمن
 * Security Monitor Component for Admin
 * 
 * المرحلة 1: تأمين الدخول والسياسات الأمنية الأساسية
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Shield, 
  AlertTriangle, 
  Eye, 
  Clock, 
  Ban, 
  CheckCircle, 
  XCircle,
  RefreshCw,
  Download,
  Filter
} from 'lucide-react';
import { Button } from '../ui/Button';
import { BruteForceProtectionService } from '../../services/security/BruteForceProtection';
import { useAuth } from '../../contexts/AuthContext';

interface LoginAttempt {
  id: string;
  email: string;
  ip_address: string;
  user_agent: string;
  success: boolean;
  attempted_at: string;
}

interface SecurityStats {
  totalAttempts: number;
  failedAttempts: number;
  blockedEmails: number;
  blockedIPs: number;
  successRate: number;
}

export const SecurityMonitor: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [stats, setStats] = useState<SecurityStats | null>(null);
  const [attempts, setAttempts] = useState<LoginAttempt[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'failed' | 'success'>('all');
  const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');

  const bruteForceService = BruteForceProtectionService.getInstance();

  // التحقق من صلاحيات الأدمن
  if (!user || user.role !== 'admin') {
    return (
      <div className="text-center p-8">
        <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-600 mb-2">
          غير مصرح بالوصول
        </h2>
        <p className="text-gray-500">
          هذه الصفحة متاحة للمديرين فقط
        </p>
      </div>
    );
  }

  useEffect(() => {
    loadSecurityData();
  }, [filter, timeRange]);

  const loadSecurityData = async () => {
    setLoading(true);
    try {
      // تحميل الإحصائيات
      const statsData = await bruteForceService.getProtectionStats();
      setStats(statsData);

      // تحميل محاولات تسجيل الدخول (محاكاة - يجب تطبيقها في الخدمة)
      // const attemptsData = await loadLoginAttempts(filter, timeRange);
      // setAttempts(attemptsData);

    } catch (error) {
      console.error('Error loading security data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUnblockEmail = async (email: string) => {
    try {
      const success = await bruteForceService.unblockEmail(email);
      if (success) {
        await loadSecurityData();
        // إظهار رسالة نجاح
      }
    } catch (error) {
      console.error('Error unblocking email:', error);
    }
  };

  const handleUnblockIP = async (ipAddress: string) => {
    try {
      const success = await bruteForceService.unblockIP(ipAddress);
      if (success) {
        await loadSecurityData();
        // إظهار رسالة نجاح
      }
    } catch (error) {
      console.error('Error unblocking IP:', error);
    }
  };

  const exportSecurityReport = () => {
    // تصدير تقرير الأمان
    const reportData = {
      stats,
      attempts,
      generatedAt: new Date().toISOString(),
      timeRange,
      filter
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `security-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-8 h-8 animate-spin text-primary-500" />
        <span className="mr-3 text-lg">جاري تحميل بيانات الأمان...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* العنوان والأدوات */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 space-x-reverse">
          <Shield className="w-8 h-8 text-primary-500" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              مراقبة الأمان
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              مراقبة محاولات تسجيل الدخول والأنشطة المشبوهة
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3 space-x-reverse">
          <Button
            onClick={loadSecurityData}
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            تحديث
          </Button>
          <Button
            onClick={exportSecurityReport}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            تصدير التقرير
          </Button>
        </div>
      </div>

      {/* الإحصائيات */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  إجمالي المحاولات
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.totalAttempts}
                </p>
              </div>
              <Eye className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  المحاولات الفاشلة
                </p>
                <p className="text-2xl font-bold text-red-600">
                  {stats.failedAttempts}
                </p>
              </div>
              <XCircle className="w-8 h-8 text-red-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  حسابات محظورة
                </p>
                <p className="text-2xl font-bold text-orange-600">
                  {stats.blockedEmails}
                </p>
              </div>
              <Ban className="w-8 h-8 text-orange-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  عناوين IP محظورة
                </p>
                <p className="text-2xl font-bold text-orange-600">
                  {stats.blockedIPs}
                </p>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  معدل النجاح
                </p>
                <p className="text-2xl font-bold text-green-600">
                  {stats.successRate}%
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </div>
        </div>
      )}

      {/* فلاتر */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <div className="flex items-center space-x-4 space-x-reverse">
          <div className="flex items-center space-x-2 space-x-reverse">
            <Filter className="w-4 h-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              فلترة:
            </span>
          </div>
          
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
            className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white"
          >
            <option value="all">جميع المحاولات</option>
            <option value="failed">المحاولات الفاشلة</option>
            <option value="success">المحاولات الناجحة</option>
          </select>

          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white"
          >
            <option value="1h">آخر ساعة</option>
            <option value="24h">آخر 24 ساعة</option>
            <option value="7d">آخر 7 أيام</option>
            <option value="30d">آخر 30 يوم</option>
          </select>
        </div>
      </div>

      {/* جدول محاولات تسجيل الدخول */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            محاولات تسجيل الدخول الأخيرة
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  البريد الإلكتروني
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  عنوان IP
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  التوقيت
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {attempts.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    لا توجد محاولات تسجيل دخول في الفترة المحددة
                  </td>
                </tr>
              ) : (
                attempts.map((attempt) => (
                  <tr key={attempt.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {attempt.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {attempt.ip_address}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {attempt.success ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <CheckCircle className="w-3 h-3 ml-1" />
                          نجح
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          <XCircle className="w-3 h-3 ml-1" />
                          فشل
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 ml-1" />
                        {new Date(attempt.attempted_at).toLocaleString('ar-SA')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {!attempt.success && (
                        <div className="flex space-x-2 space-x-reverse">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleUnblockEmail(attempt.email)}
                          >
                            إلغاء حظر الحساب
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleUnblockIP(attempt.ip_address)}
                          >
                            إلغاء حظر IP
                          </Button>
                        </div>
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};
