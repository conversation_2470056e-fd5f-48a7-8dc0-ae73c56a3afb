/**
 * Cache Manager - مدير التخزين المؤقت
 * يحسن الأداء عبر تخزين البيانات المؤقت
 */

export interface CacheItem<T> {
  data: T;
  expiry: number;
  created: number;
  hits: number;
}

export interface CacheStats {
  totalItems: number;
  totalHits: number;
  totalMisses: number;
  hitRate: number;
  memoryUsage: number;
}

export class CacheManager {
  
  private static cache = new Map<string, CacheItem<any>>();
  private static defaultTTL = 5 * 60 * 1000; // 5 minutes
  private static maxItems = 1000; // حد أقصى للعناصر
  private static stats = {
    hits: 0,
    misses: 0
  };

  /**
   * الحصول على البيانات من Cache
   */
  static get<T>(key: string): T | null {
    const cached = this.cache.get(key);
    
    if (!cached) {
      this.stats.misses++;
      return null;
    }
    
    // التحقق من انتهاء الصلاحية
    if (Date.now() > cached.expiry) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }
    
    // تحديث إحصائيات الاستخدام
    cached.hits++;
    this.stats.hits++;
    
    return cached.data as T;
  }
  
  /**
   * حفظ البيانات في Cache
   */
  static set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {
    // التحقق من الحد الأقصى للعناصر
    if (this.cache.size >= this.maxItems) {
      this.evictLeastUsed();
    }

    this.cache.set(key, {
      data,
      expiry: Date.now() + ttl,
      created: Date.now(),
      hits: 0
    });
  }
  
  /**
   * حذف البيانات من Cache
   */
  static delete(key: string): boolean {
    return this.cache.delete(key);
  }
  
  /**
   * مسح Cache بالكامل
   */
  static clear(): void {
    this.cache.clear();
    this.stats.hits = 0;
    this.stats.misses = 0;
  }

  /**
   * إزالة العناصر الأقل استخداماً
   */
  private static evictLeastUsed(): void {
    let leastUsedKey: string | null = null;
    let leastHits = Infinity;
    let oldestTime = Infinity;

    for (const [key, item] of this.cache.entries()) {
      // إزالة العناصر المنتهية الصلاحية أولاً
      if (Date.now() > item.expiry) {
        this.cache.delete(key);
        continue;
      }

      // البحث عن الأقل استخداماً أو الأقدم
      if (item.hits < leastHits || (item.hits === leastHits && item.created < oldestTime)) {
        leastUsedKey = key;
        leastHits = item.hits;
        oldestTime = item.created;
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
    }
  }

  /**
   * تنظيف العناصر المنتهية الصلاحية
   */
  static cleanup(): number {
    let removedCount = 0;
    const now = Date.now();

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
        removedCount++;
      }
    }

    return removedCount;
  }

  /**
   * الحصول على البيانات مع Cache
   */
  static async getOrFetch<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttl?: number
  ): Promise<T> {
    
    // محاولة الحصول من Cache
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }
    
    try {
      // جلب البيانات
      const data = await fetchFunction();
      
      // حفظ في Cache
      this.set(key, data, ttl);
      
      return data;
    } catch (error) {
      console.error(`Failed to fetch data for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * الحصول على إحصائيات Cache
   */
  static getStats(): CacheStats {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0;
    
    // تقدير استخدام الذاكرة (تقريبي)
    const memoryUsage = this.cache.size * 1024; // تقدير 1KB لكل عنصر

    return {
      totalItems: this.cache.size,
      totalHits: this.stats.hits,
      totalMisses: this.stats.misses,
      hitRate: Math.round(hitRate * 100) / 100,
      memoryUsage
    };
  }

  /**
   * الحصول على جميع المفاتيح
   */
  static getKeys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * التحقق من وجود مفتاح
   */
  static has(key: string): boolean {
    const cached = this.cache.get(key);
    if (!cached) return false;
    
    // التحقق من انتهاء الصلاحية
    if (Date.now() > cached.expiry) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * تحديث TTL لمفتاح موجود
   */
  static updateTTL(key: string, newTTL: number): boolean {
    const cached = this.cache.get(key);
    if (!cached) return false;
    
    cached.expiry = Date.now() + newTTL;
    return true;
  }

  /**
   * الحصول على البيانات مع تحديث تلقائي
   */
  static async getWithRefresh<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttl: number = this.defaultTTL,
    refreshThreshold: number = 0.8 // تحديث عند 80% من انتهاء الصلاحية
  ): Promise<T> {
    
    const cached = this.cache.get(key);
    
    if (cached) {
      const timeLeft = cached.expiry - Date.now();
      const totalTTL = cached.expiry - cached.created;
      const timeUsed = totalTTL - timeLeft;
      
      // إذا تم استخدام أكثر من العتبة، قم بالتحديث في الخلفية
      if (timeUsed / totalTTL > refreshThreshold) {
        // تحديث في الخلفية دون انتظار
        fetchFunction().then(newData => {
          this.set(key, newData, ttl);
        }).catch(error => {
          console.error(`Background refresh failed for key ${key}:`, error);
        });
      }
      
      return cached.data;
    }
    
    // إذا لم توجد البيانات، جلبها
    return this.getOrFetch(key, fetchFunction, ttl);
  }

  /**
   * حفظ متعدد
   */
  static setMultiple<T>(items: Array<{ key: string; data: T; ttl?: number }>): void {
    for (const item of items) {
      this.set(item.key, item.data, item.ttl);
    }
  }

  /**
   * حذف متعدد
   */
  static deleteMultiple(keys: string[]): number {
    let deletedCount = 0;
    for (const key of keys) {
      if (this.delete(key)) {
        deletedCount++;
      }
    }
    return deletedCount;
  }

  /**
   * حذف بناءً على نمط
   */
  static deleteByPattern(pattern: RegExp): number {
    let deletedCount = 0;
    for (const key of this.cache.keys()) {
      if (pattern.test(key)) {
        this.cache.delete(key);
        deletedCount++;
      }
    }
    return deletedCount;
  }

  /**
   * تشغيل تنظيف دوري
   */
  static startPeriodicCleanup(intervalMs: number = 60000): NodeJS.Timeout {
    return setInterval(() => {
      const removed = this.cleanup();
      if (removed > 0) {
        console.log(`Cache cleanup: removed ${removed} expired items`);
      }
    }, intervalMs);
  }

  /**
   * إيقاف التنظيف الدوري
   */
  static stopPeriodicCleanup(intervalId: NodeJS.Timeout): void {
    clearInterval(intervalId);
  }

  /**
   * تصدير Cache للنسخ الاحتياطي
   */
  static export(): Array<{ key: string; item: CacheItem<any> }> {
    const exports: Array<{ key: string; item: CacheItem<any> }> = [];
    
    for (const [key, item] of this.cache.entries()) {
      // تصدير العناصر غير المنتهية الصلاحية فقط
      if (Date.now() <= item.expiry) {
        exports.push({ key, item });
      }
    }
    
    return exports;
  }

  /**
   * استيراد Cache من النسخة الاحتياطية
   */
  static import(data: Array<{ key: string; item: CacheItem<any> }>): number {
    let importedCount = 0;
    
    for (const { key, item } of data) {
      // استيراد العناصر غير المنتهية الصلاحية فقط
      if (Date.now() <= item.expiry) {
        this.cache.set(key, item);
        importedCount++;
      }
    }
    
    return importedCount;
  }

  /**
   * إعداد الحد الأقصى للعناصر
   */
  static setMaxItems(maxItems: number): void {
    this.maxItems = maxItems;
    
    // إذا كان العدد الحالي أكبر من الحد الجديد، قم بالتنظيف
    while (this.cache.size > this.maxItems) {
      this.evictLeastUsed();
    }
  }

  /**
   * إعداد TTL الافتراضي
   */
  static setDefaultTTL(ttl: number): void {
    this.defaultTTL = ttl;
  }
}

export default CacheManager;
