/**
 * خدمة الصلاحيات المركزية الجديدة
 * Centralized Permission Service - Phase 1 Implementation
 * 
 * هذه الخدمة هي المصدر الوحيد للحقيقة لجميع فحوصات الصلاحيات في النظام
 * This service is the single source of truth for all permission checks in the system
 */

import { supabase } from '../lib/supabase';
import { User, UserRole } from '../types';

// تعريف أنواع الموارد والعمليات
export enum ResourceType {
  USER = 'user',
  TENANT = 'tenant',
  BUS = 'bus',
  ROUTE = 'route',
  STUDENT = 'student',
  ATTENDANCE = 'attendance',
  NOTIFICATION = 'notification',
  MAINTENANCE = 'maintenance'
}

export enum Action {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete'
}

export enum PermissionScope {
  GLOBAL = 'global',
  TENANT = 'tenant',
  OWN = 'own',
  ASSIGNED = 'assigned',
  CHILDREN = 'children',
  ROUTE = 'route'
}

// واجهة نتيجة فحص الصلاحية
export interface PermissionCheckResult {
  allowed: boolean;
  reason?: string;
  scope?: PermissionScope;
  conditions?: Record<string, any>;
}

// واجهة سياق الصلاحية
export interface PermissionContext {
  resourceId?: string;
  resourceTenantId?: string;
  resourceOwnerId?: string;
  targetTenantId?: string;
  studentId?: string;
  additionalData?: Record<string, any>;
}

// واجهة مصفوفة الصلاحيات
export interface PermissionMatrix {
  id: string;
  role: UserRole;
  resource_type: ResourceType;
  action: Action;
  scope: PermissionScope;
  conditions: Record<string, any>;
  is_active: boolean;
}

/**
 * خدمة الصلاحيات المركزية
 * Centralized Permission Service
 */
export class CentralizedPermissionService {
  private static instance: CentralizedPermissionService;
  private permissionCache = new Map<string, PermissionCheckResult>();
  private cacheTimeout = 5 * 60 * 1000; // 5 دقائق

  private constructor() {}

  /**
   * الحصول على مثيل الخدمة (Singleton Pattern)
   */
  static getInstance(): CentralizedPermissionService {
    if (!CentralizedPermissionService.instance) {
      CentralizedPermissionService.instance = new CentralizedPermissionService();
    }
    return CentralizedPermissionService.instance;
  }

  /**
   * فحص الصلاحية الرئيسي - النقطة المركزية لجميع فحوصات الصلاحيات
   * Main permission check - central point for all permission validations
   */
  async checkPermission(
    user: User,
    resourceType: ResourceType,
    action: Action,
    context?: PermissionContext
  ): Promise<PermissionCheckResult> {
    try {
      // إنشاء مفتاح التخزين المؤقت
      const cacheKey = this.generateCacheKey(user.id, resourceType, action, context);
      
      // التحقق من التخزين المؤقت
      const cachedResult = this.permissionCache.get(cacheKey);
      if (cachedResult) {
        return cachedResult;
      }

      // استدعاء دالة قاعدة البيانات للفحص
      const { data, error } = await supabase.rpc('check_permission', {
        user_id: user.id,
        resource_type: resourceType,
        action: action,
        resource_tenant_id: context?.resourceTenantId || null,
        resource_owner_id: context?.resourceOwnerId || null,
        additional_context: context?.additionalData || {}
      });

      if (error) {
        console.error('Permission check error:', error);
        return {
          allowed: false,
          reason: `Database error: ${error.message}`
        };
      }

      const result: PermissionCheckResult = {
        allowed: data || false,
        reason: data ? 'Permission granted' : 'Permission denied'
      };

      // حفظ في التخزين المؤقت
      this.permissionCache.set(cacheKey, result);
      
      // إزالة من التخزين المؤقت بعد انتهاء المهلة
      setTimeout(() => {
        this.permissionCache.delete(cacheKey);
      }, this.cacheTimeout);

      return result;

    } catch (error) {
      console.error('Permission service error:', error);
      return {
        allowed: false,
        reason: `Service error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * فحص عدة صلاحيات في مرة واحدة
   * Check multiple permissions at once
   */
  async checkMultiplePermissions(
    user: User,
    permissions: Array<{
      resourceType: ResourceType;
      action: Action;
      context?: PermissionContext;
    }>
  ): Promise<PermissionCheckResult[]> {
    const results = await Promise.all(
      permissions.map(({ resourceType, action, context }) =>
        this.checkPermission(user, resourceType, action, context)
      )
    );

    return results;
  }

  /**
   * فحص ما إذا كان المستخدم يمكنه الوصول لمستأجر معين
   * Check if user can access a specific tenant
   */
  async canAccessTenant(user: User, tenantId: string): Promise<boolean> {
    const result = await this.checkPermission(
      user,
      ResourceType.TENANT,
      Action.READ,
      { resourceTenantId: tenantId }
    );

    return result.allowed;
  }

  /**
   * فحص ما إذا كان المستخدم يمكنه إدارة مستخدمين آخرين
   * Check if user can manage other users
   */
  async canManageUsers(user: User, targetTenantId?: string): Promise<boolean> {
    const result = await this.checkPermission(
      user,
      ResourceType.USER,
      Action.UPDATE,
      { targetTenantId }
    );

    return result.allowed;
  }

  /**
   * الحصول على مصفوفة الصلاحيات للدور المحدد
   * Get permission matrix for specific role
   */
  async getPermissionMatrix(role: UserRole): Promise<PermissionMatrix[]> {
    try {
      const { data, error } = await supabase
        .from('permission_matrix')
        .select('*')
        .eq('role', role)
        .eq('is_active', true);

      if (error) {
        console.error('Error fetching permission matrix:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Permission matrix service error:', error);
      return [];
    }
  }

  /**
   * إنشاء مفتاح التخزين المؤقت
   * Generate cache key
   */
  private generateCacheKey(
    userId: string,
    resourceType: ResourceType,
    action: Action,
    context?: PermissionContext
  ): string {
    const contextStr = context ? JSON.stringify(context) : '';
    return `${userId}:${resourceType}:${action}:${contextStr}`;
  }

  /**
   * مسح التخزين المؤقت
   * Clear permission cache
   */
  clearCache(): void {
    this.permissionCache.clear();
  }

  /**
   * مسح التخزين المؤقت لمستخدم معين
   * Clear cache for specific user
   */
  clearUserCache(userId: string): void {
    for (const [key] of this.permissionCache) {
      if (key.startsWith(`${userId}:`)) {
        this.permissionCache.delete(key);
      }
    }
  }

  /**
   * تسجيل حدث أمني
   * Log security event
   */
  async logSecurityEvent(
    eventType: string,
    severity: 'INFO' | 'WARNING' | 'ERROR',
    description: string,
    userId?: string,
    tenantId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      await supabase.rpc('log_security_event', {
        event_type: eventType,
        severity: severity,
        description: description,
        user_id: userId || null,
        tenant_id: tenantId || null,
        metadata: metadata || {}
      });
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  }

  /**
   * فحص ما إذا كان المستخدم أدمن
   * Check if user is system admin
   */
  async isSystemAdmin(user: User): Promise<boolean> {
    return user.role === UserRole.ADMIN;
  }

  /**
   * فحص ما إذا كان المستخدم مدير مدرسة
   * Check if user is school manager
   */
  async isSchoolManager(user: User): Promise<boolean> {
    return user.role === UserRole.SCHOOL_MANAGER;
  }

  /**
   * فحص ما إذا كان المستخدم يمكنه الوصول لطالب معين
   * Check if user can access specific student
   */
  async canAccessStudent(user: User, studentId: string): Promise<boolean> {
    const result = await this.checkPermission(
      user,
      ResourceType.STUDENT,
      Action.READ,
      { resourceId: studentId }
    );

    return result.allowed;
  }

  /**
   * فحص ما إذا كان المستخدم يمكنه الوصول لحافلة معينة
   * Check if user can access specific bus
   */
  async canAccessBus(user: User, busId: string): Promise<boolean> {
    const result = await this.checkPermission(
      user,
      ResourceType.BUS,
      Action.READ,
      { resourceId: busId }
    );

    return result.allowed;
  }

  /**
   * فحص ما إذا كان المستخدم يمكنه تسجيل الحضور
   * Check if user can record attendance
   */
  async canRecordAttendance(user: User, studentId: string): Promise<boolean> {
    const result = await this.checkPermission(
      user,
      ResourceType.ATTENDANCE,
      Action.CREATE,
      { studentId }
    );

    return result.allowed;
  }

  /**
   * الحصول على الصلاحيات المتاحة للمستخدم
   * Get available permissions for user
   */
  async getUserPermissions(user: User): Promise<PermissionMatrix[]> {
    return this.getPermissionMatrix(user.role);
  }

  /**
   * فحص صلاحيات متعددة وإرجاع ملخص
   * Check multiple permissions and return summary
   */
  async getPermissionSummary(
    user: User,
    resourceType: ResourceType
  ): Promise<{
    canCreate: boolean;
    canRead: boolean;
    canUpdate: boolean;
    canDelete: boolean;
  }> {
    const [createResult, readResult, updateResult, deleteResult] = await Promise.all([
      this.checkPermission(user, resourceType, Action.CREATE),
      this.checkPermission(user, resourceType, Action.READ),
      this.checkPermission(user, resourceType, Action.UPDATE),
      this.checkPermission(user, resourceType, Action.DELETE)
    ]);

    return {
      canCreate: createResult.allowed,
      canRead: readResult.allowed,
      canUpdate: updateResult.allowed,
      canDelete: deleteResult.allowed
    };
  }

  /**
   * التحقق من صحة السياق المطلوب
   * Validate required context
   */
  private validateContext(
    resourceType: ResourceType,
    action: Action,
    context?: PermissionContext
  ): boolean {
    // يمكن إضافة قواعد التحقق هنا حسب الحاجة
    return true;
  }

  /**
   * إنشاء تقرير أمني للمستخدم
   * Generate security report for user
   */
  async generateUserSecurityReport(user: User): Promise<{
    userId: string;
    role: UserRole;
    tenantId?: string;
    permissions: PermissionMatrix[];
    lastActivity?: Date;
    securityScore: number;
  }> {
    const permissions = await this.getUserPermissions(user);

    return {
      userId: user.id,
      role: user.role,
      tenantId: user.tenant_id,
      permissions,
      lastActivity: user.last_login ? new Date(user.last_login) : undefined,
      securityScore: this.calculateSecurityScore(user, permissions)
    };
  }

  /**
   * حساب نقاط الأمان للمستخدم
   * Calculate security score for user
   */
  private calculateSecurityScore(user: User, permissions: PermissionMatrix[]): number {
    let score = 100;

    // تقليل النقاط حسب عدد الصلاحيات
    score -= permissions.length * 2;

    // تقليل النقاط إذا كان الحساب غير نشط
    if (!user.is_active) {
      score -= 50;
    }

    // تقليل النقاط إذا لم يسجل دخول مؤخراً
    if (user.last_login) {
      const daysSinceLogin = (Date.now() - new Date(user.last_login).getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceLogin > 30) {
        score -= 20;
      }
    }

    return Math.max(0, Math.min(100, score));
  }
}
