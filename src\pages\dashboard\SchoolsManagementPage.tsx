import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Plus, Edit, Trash2, Search, Building, Users, Bus, Eye } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { useAuth } from '../../contexts/AuthContext';
import { useDatabase } from '../../contexts/DatabaseContext';
import { supabase } from '../../lib/supabase';
import { ResponsiveLayout } from '../../components/layout/ResponsiveLayout';

interface School {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  settings?: any;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  // Statistics
  users_count?: number;
  students_count?: number;
  buses_count?: number;
}

interface SchoolFormData {
  name: string;
  address: string;
  phone: string;
  email: string;
}

const SchoolsManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { tenants, refreshData } = useDatabase();
  
  const [schools, setSchools] = useState<School[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedSchool, setSelectedSchool] = useState<School | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Only admin can access this page
  if (user?.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Access Denied
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Only administrators can access school management.
          </p>
        </div>
      </div>
    );
  }

  // Load schools data
  const loadSchools = async () => {
    try {
      setLoading(true);
      console.log('🔄 SchoolsManagement: Loading schools data...');

      // Get schools with statistics
      const { data: schoolsData, error: schoolsError } = await supabase
        .from('tenants')
        .select('*')
        .order('created_at', { ascending: false });

      if (schoolsError) {
        console.error('❌ SchoolsManagement: Error loading schools:', schoolsError);
        return;
      }

      // Get statistics for each school
      const schoolsWithStats = await Promise.all(
        (schoolsData || []).map(async (school) => {
          try {
            // Get users count
            const { count: usersCount } = await supabase
              .from('users')
              .select('*', { count: 'exact', head: true })
              .eq('tenant_id', school.id);

            // Get students count
            const { count: studentsCount } = await supabase
              .from('students')
              .select('*', { count: 'exact', head: true })
              .eq('tenant_id', school.id);

            // Get buses count
            const { count: busesCount } = await supabase
              .from('buses')
              .select('*', { count: 'exact', head: true })
              .eq('tenant_id', school.id);

            return {
              ...school,
              users_count: usersCount || 0,
              students_count: studentsCount || 0,
              buses_count: busesCount || 0,
            };
          } catch (error) {
            console.error('❌ SchoolsManagement: Error loading stats for school:', school.id, error);
            return {
              ...school,
              users_count: 0,
              students_count: 0,
              buses_count: 0,
            };
          }
        })
      );

      setSchools(schoolsWithStats);
      console.log('✅ SchoolsManagement: Loaded', schoolsWithStats.length, 'schools');
    } catch (error) {
      console.error('❌ SchoolsManagement: Error loading schools:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSchools();
  }, []);

  // Handle form submission
  const handleSubmit = async (formData: SchoolFormData) => {
    try {
      setIsSubmitting(true);
      console.log('🔄 SchoolsManagement: Submitting school data...');

      if (selectedSchool) {
        // Update existing school
        const { data: result, error } = await supabase.rpc('update_school_tenant', {
          target_tenant_id: selectedSchool.id,
          school_name: formData.name,
          school_address: formData.address,
          school_phone: formData.phone,
          school_email: formData.email
        });

        if (error) {
          console.error('❌ SchoolsManagement: Update error:', error);
          throw error;
        }

        if (!result?.success) {
          console.error('❌ SchoolsManagement: Update failed:', result);
          throw new Error(result?.message || 'Failed to update school');
        }

        console.log('✅ SchoolsManagement: School updated successfully');
      } else {
        // Create new school
        const { data: result, error } = await supabase.rpc('create_school_tenant', {
          school_name: formData.name,
          school_address: formData.address,
          school_phone: formData.phone,
          school_email: formData.email
        });

        if (error) {
          console.error('❌ SchoolsManagement: Create error:', error);
          throw error;
        }

        if (!result?.success) {
          console.error('❌ SchoolsManagement: Create failed:', result);
          throw new Error(result?.message || 'Failed to create school');
        }

        console.log('✅ SchoolsManagement: School created successfully');
      }

      await loadSchools();
      await refreshData(); // Refresh global data
      setShowModal(false);
      setSelectedSchool(null);
    } catch (error) {
      console.error('❌ SchoolsManagement: Error submitting school:', error);
      alert(error instanceof Error ? error.message : 'Failed to save school');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete
  const handleDelete = async (school: School) => {
    if (!confirm(`Are you sure you want to delete "${school.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      console.log('🔄 SchoolsManagement: Deleting school...');
      
      // Soft delete by setting is_active to false
      const { error } = await supabase
        .from('tenants')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('id', school.id);

      if (error) {
        console.error('❌ SchoolsManagement: Delete error:', error);
        throw error;
      }

      console.log('✅ SchoolsManagement: School deleted successfully');
      await loadSchools();
      await refreshData();
    } catch (error) {
      console.error('❌ SchoolsManagement: Error deleting school:', error);
      alert(error instanceof Error ? error.message : 'Failed to delete school');
    }
  };

  // Filter schools
  const filteredSchools = schools.filter(school =>
    school.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (school.address && school.address.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <ResponsiveLayout>
      <div className="p-4 md:p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Schools Management
              </h1>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Manage schools and their configurations
              </p>
            </div>

            <div className="mt-4 md:mt-0 flex gap-2">
              <Button
                variant="outline"
                onClick={loadSchools}
                disabled={loading}
              >
                Refresh
              </Button>
              <Button
                className="flex items-center gap-2"
                onClick={() => {
                  setSelectedSchool(null);
                  setShowModal(true);
                }}
              >
                <Plus size={16} />
                Add School
              </Button>
            </div>
          </div>

          {/* Search */}
          <div className="mb-6">
            <div className="relative max-w-md">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={18} className="text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Search schools..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* Schools Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {loading ? (
              // Loading skeleton
              Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
                  <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
                  <div className="flex justify-between">
                    <div className="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    <div className="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  </div>
                </div>
              ))
            ) : filteredSchools.length > 0 ? (
              filteredSchools.map((school) => (
                <div key={school.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <div className="p-2 bg-primary-100 dark:bg-primary-800/20 rounded-lg">
                        <Building className="h-6 w-6 text-primary-600 dark:text-primary-400" />
                      </div>
                      <div className="ml-3">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                          {school.name}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {school.address || 'No address'}
                        </p>
                      </div>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      school.is_active
                        ? 'bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-400'
                        : 'bg-red-100 text-red-800 dark:bg-red-800/20 dark:text-red-400'
                    }`}>
                      {school.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>

                  {/* Statistics */}
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Users className="h-4 w-4 text-gray-400 mr-1" />
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {school.users_count || 0}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Users</p>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Users className="h-4 w-4 text-gray-400 mr-1" />
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {school.students_count || 0}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Students</p>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Bus className="h-4 w-4 text-gray-400 mr-1" />
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {school.buses_count || 0}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Buses</p>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex space-x-2">
                      <button
                        className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 p-1"
                        title="View Details"
                        onClick={() => {
                          setSelectedSchool(school);
                          setShowModal(true);
                        }}
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        className="text-warning-600 hover:text-warning-900 dark:text-warning-400 dark:hover:text-warning-300 p-1"
                        title="Edit"
                        onClick={() => {
                          setSelectedSchool(school);
                          setShowModal(true);
                        }}
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        className="text-error-600 hover:text-error-900 dark:text-error-400 dark:hover:text-error-300 p-1"
                        title="Delete"
                        onClick={() => handleDelete(school)}
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Created: {new Date(school.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No schools found
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  {searchQuery ? 'No schools match your search.' : 'Get started by creating your first school.'}
                </p>
                {!searchQuery && (
                  <Button
                    onClick={() => {
                      setSelectedSchool(null);
                      setShowModal(true);
                    }}
                  >
                    <Plus size={16} className="mr-2" />
                    Add School
                  </Button>
                )}
              </div>
            )}
          </div>

          {/* School Modal */}
          {showModal && (
            <SchoolModal
              school={selectedSchool}
              onSubmit={handleSubmit}
              onClose={() => {
                setShowModal(false);
                setSelectedSchool(null);
              }}
              isSubmitting={isSubmitting}
            />
          )}
        </div>
      </div>
    </ResponsiveLayout>
  );
};

// School Modal Component
interface SchoolModalProps {
  school: School | null;
  onSubmit: (data: SchoolFormData) => Promise<void>;
  onClose: () => void;
  isSubmitting: boolean;
}

const SchoolModal: React.FC<SchoolModalProps> = ({ school, onSubmit, onClose, isSubmitting }) => {
  const [formData, setFormData] = useState<SchoolFormData>({
    name: school?.name || '',
    address: school?.address || '',
    phone: school?.phone || '',
    email: school?.email || '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      alert('School name is required');
      return;
    }

    await onSubmit(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {school ? 'Edit School' : 'Add New School'}
          </h3>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                School Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Address
              </label>
              <input
                type="text"
                value={formData.address}
                onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Phone
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Email
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : school ? 'Update' : 'Create'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default SchoolsManagementPage;
