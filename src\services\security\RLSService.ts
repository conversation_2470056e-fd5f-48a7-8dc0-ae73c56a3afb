
import { supabase } from '../lib/supabase'
import { User, UserRole } from '../types'

export class RLSService {
  /**
   * الحصول على معرف المستأجر الحالي
   */
  static async getCurrentTenantId(): Promise<string | null> {
    const { data, error } = await supabase.rpc('get_current_tenant_id')
    
    if (error) {
      console.error('خطأ في الحصول على معرف المستأجر:', error)
      return null
    }
    
    return data
  }

  /**
   * الحصول على دور المستخدم الحالي
   */
  static async getCurrentUserRole(): Promise<UserRole | null> {
    const { data, error } = await supabase.rpc('get_current_user_role')
    
    if (error) {
      console.error('خطأ في الحصول على دور المستخدم:', error)
      return null
    }
    
    return data as UserRole
  }

  /**
   * التحقق من كون المستخدم مدير مستأجر
   */
  static async isTenantAdmin(): Promise<boolean> {
    const { data, error } = await supabase.rpc('is_tenant_admin')
    
    if (error) {
      console.error('خطأ في التحقق من دور المدير:', error)
      return false
    }
    
    return data || false
  }

  /**
   * التحقق من إمكانية الوصول لبيانات الطالب
   */
  static async canAccessStudent(studentId: string): Promise<boolean> {
    const { data, error } = await supabase.rpc('can_access_student', {
      student_id: studentId
    })
    
    if (error) {
      console.error('خطأ في التحقق من الوصول للطالب:', error)
      return false
    }
    
    return data || false
  }

  /**
   * التحقق من إمكانية الوصول لبيانات الحافلة
   */
  static async canAccessBus(busId: string): Promise<boolean> {
    const { data, error } = await supabase.rpc('can_access_bus', {
      bus_id: busId
    })
    
    if (error) {
      console.error('خطأ في التحقق من الوصول للحافلة:', error)
      return false
    }
    
    return data || false
  }

  /**
   * تسجيل محاولة الوصول
   */
  static async logAccess(
    tableName: string,
    operation: string,
    recordId: string
  ): Promise<void> {
    const { error } = await supabase.rpc('audit_log_access', {
      table_name: tableName,
      operation: operation,
      record_id: recordId
    })
    
    if (error) {
      console.error('خطأ في تسجيل محاولة الوصول:', error)
    }
  }
}
