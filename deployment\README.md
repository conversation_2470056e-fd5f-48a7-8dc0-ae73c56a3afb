# 🚀 دليل النشر السريع

## الطرق المتاحة للنشر

### 1. النشر عبر Supabase Dashboard (الأسهل)

1. اذهب إلى [app.supabase.com](https://app.supabase.com)
2. اختر مشروعك
3. اذ<PERSON><PERSON> إلى SQL Editor
4. انسخ محتوى `deployment/complete-migration.sql`
5. الصق المحتوى واضغط Run

### 2. النشر عبر JavaScript

```bash
# تأكد من إعداد متغيرات البيئة
export SUPABASE_URL="your_supabase_url"
export SUPABASE_SERVICE_ROLE_KEY="your_service_role_key"

# تشغيل النشر
node deployment/deploy.mjs
```

### 3. النشر عبر Supabase CLI

```bash
# تثبيت CLI
npm install -g supabase

# تسجيل الدخول
supabase login

# ربط المشروع
supabase link --project-ref your-project-ref

# تطبيق الهجرات
supabase db push
```

## ✅ التحقق من النشر

بعد النشر، تحقق من:

1. **الجداول**: يجب أن تجد 5 جدول جديد
2. **RLS**: يجب أن يكون مُفعل على جميع الجداول
3. **السياسات**: يجب أن تجد 32+ سياسة RLS
4. **الدوال**: يجب أن تجد 8+ دالة أمنية

## 🧪 اختبار سريع

```sql
-- فحص الجداول
SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';

-- فحص RLS
SELECT count(*) FROM pg_tables WHERE schemaname = 'public' AND rowsecurity = true;

-- فحص السياسات
SELECT count(*) FROM pg_policies WHERE schemaname = 'public';
```

## 📞 في حالة المشاكل

1. تأكد من صحة متغيرات البيئة
2. تأكد من صلاحيات Service Role Key
3. راجع سجلات Supabase للأخطاء
4. راجع `docs/migration-guide.md` للتفاصيل

---
**🎉 مبروك! نظام RLS جاهز للاستخدام!**
