/**
 * Environment Variables Type Definitions
 * Type definitions for Vite environment variables
 * Phase 3: UI/UX Enhancement - Environment
 */

/// <reference types="vite/client" />

interface ImportMetaEnv {
  // Supabase Configuration
  readonly VITE_SUPABASE_URL: string;
  readonly VITE_SUPABASE_ANON_KEY: string;
  
  // API Configuration
  readonly VITE_API_URL: string;
  
  // App Configuration
  readonly VITE_APP_NAME: string;
  readonly VITE_APP_VERSION: string;
  
  // Theme Configuration
  readonly VITE_DEFAULT_THEME: 'light' | 'dark';
  readonly VITE_ENABLE_THEME_CUSTOMIZATION: string;
  
  // Development Configuration
  readonly VITE_ENABLE_DEV_TOOLS: string;
  readonly VITE_LOG_LEVEL: 'debug' | 'info' | 'warn' | 'error';
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
