import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Clock,
  AlertTriangle,
  Bus,
  MapPin,
  Bell,
  Settings,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { supabase } from "../../lib/supabase";
import * as api from "../../lib/api";

interface DelayAlert {
  routeId: string;
  routeName: string;
  busId: string;
  busPlateNumber: string;
  stopId: string;
  stopName: string;
  scheduledTime: string;
  delayMinutes: number;
  tenantId: string;
}

export const DelayNotificationSystem: React.FC = () => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const [delays, setDelays] = useState<DelayAlert[]>([]);
  const [loading, setLoading] = useState(false);
  const [autoCheck, setAutoCheck] = useState(true);
  const [delayThreshold, setDelayThreshold] = useState(15); // minutes
  const [checkInterval, setCheckInterval] = useState(5); // minutes
  const [lastCheck, setLastCheck] = useState<Date | null>(null);

  const checkForDelays = async () => {
    if (!tenant?.id) return;

    try {
      setLoading(true);
      const detectedDelays = await api.detectBusDelays(tenant.id);
      setDelays(detectedDelays);

      // Send notifications for new delays
      if (detectedDelays.length > 0) {
        await api.sendDelayNotifications(detectedDelays);

        // Create system notification for administrators
        try {
          await supabase.from("notifications").insert({
            tenant_id: tenant.id,
            title: "Bus Delay Alert",
            message: `${detectedDelays.length} bus delay${detectedDelays.length > 1 ? "s" : ""} detected`,
            type: "system_alert",
            priority: "high",
            metadata: {
              type: "delay_summary",
              delayCount: detectedDelays.length,
              delays: detectedDelays.map((d) => ({
                busId: d.busId,
                routeName: d.routeName,
                delayMinutes: d.delayMinutes,
              })),
            },
          });
        } catch (notificationError) {
          console.warn(
            "Failed to create delay notification:",
            notificationError,
          );
        }
      }

      setLastCheck(new Date());
    } catch (error) {
      console.error("Error checking for delays:", error);
      // Enhanced error handling
      try {
        await supabase.from("notifications").insert({
          tenant_id: tenant.id,
          title: "Delay Detection Error",
          message:
            "Failed to check for bus delays. Please check system connectivity.",
          type: "system_error",
          priority: "medium",
          metadata: {
            type: "api_error",
            error: error instanceof Error ? error.message : "Unknown error",
            timestamp: new Date().toISOString(),
          },
        });
      } catch (notificationError) {
        console.warn("Failed to create error notification:", notificationError);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (autoCheck && tenant?.id) {
      checkForDelays();

      // Set up automatic checking based on configured interval
      const interval = setInterval(
        () => {
          checkForDelays().catch((error) => {
            console.error("Interval delay check failed:", error);
          });
        },
        checkInterval * 60 * 1000,
      );
      return () => clearInterval(interval);
    }
  }, [autoCheck, tenant?.id, checkInterval]);

  const formatDelayTime = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} minutes`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  const getSeverityColor = (minutes: number) => {
    if (minutes < 15) return "text-yellow-600 bg-yellow-100";
    if (minutes < 30) return "text-orange-600 bg-orange-100";
    return "text-red-600 bg-red-100";
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <Clock className="mr-2 h-5 w-5 text-primary-500" />
            Bus Delay Monitoring
          </h3>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  checked={autoCheck}
                  onChange={(e) => setAutoCheck(e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="text-gray-700 dark:text-gray-300">
                  Auto-check
                </span>
              </label>
              {lastCheck && (
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  Last check: {lastCheck.toLocaleTimeString()}
                </span>
              )}
            </div>
            <Button
              onClick={checkForDelays}
              disabled={loading}
              className="flex items-center gap-2"
              size="sm"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Bell size={16} />
              )}
              Check Now
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Configuration Panel */}
        <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center">
            <Settings className="mr-2 h-4 w-4" />
            Delay Detection Settings
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Delay Threshold (minutes)
              </label>
              <input
                type="number"
                value={delayThreshold}
                onChange={(e) =>
                  setDelayThreshold(parseInt(e.target.value) || 15)
                }
                min="5"
                max="60"
                className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Check Interval (minutes)
              </label>
              <select
                value={checkInterval}
                onChange={(e) => setCheckInterval(parseInt(e.target.value))}
                className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-600 dark:text-white"
              >
                <option value={1}>1 minute</option>
                <option value={2}>2 minutes</option>
                <option value={5}>5 minutes</option>
                <option value={10}>10 minutes</option>
                <option value={15}>15 minutes</option>
              </select>
            </div>
          </div>
        </div>

        {delays.length === 0 ? (
          <div className="text-center py-8">
            <Clock size={48} className="mx-auto mb-4 text-green-400" />
            <p className="text-gray-500 dark:text-gray-400">
              {loading
                ? "Checking for delays..."
                : "All buses are on schedule!"}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {delays.length} delay{delays.length !== 1 ? "s" : ""} detected
              </span>
            </div>

            <div className="space-y-3">
              {delays.map((delay, index) => (
                <div
                  key={`${delay.busId}-${delay.stopId}-${index}`}
                  className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Bus className="h-4 w-4 text-gray-500" />
                        <span className="font-medium text-gray-900 dark:text-white">
                          Bus {delay.busPlateNumber}
                        </span>
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          • {delay.routeName}
                        </span>
                      </div>

                      <div className="flex items-center gap-2 mb-2">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {delay.stopName}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          (Scheduled: {delay.scheduledTime})
                        </span>
                      </div>
                    </div>

                    <div
                      className={`px-3 py-1 rounded-full text-xs font-medium ${getSeverityColor(delay.delayMinutes)}`}
                    >
                      {formatDelayTime(delay.delayMinutes)} late
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DelayNotificationSystem;
