/**
 * Comprehensive Fixes Tests - اختبارات الإصلاحات الشاملة
 * يختبر جميع الإصلاحات المطبقة في النظام
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import EnhancedValidationService from '../lib/validation-enhanced';
import TransactionManager from '../lib/transaction-manager';
import EnhancedNotificationService from '../lib/notification-service-enhanced';
import CacheManager from '../lib/cache-manager';
import RBACEnhancedManager from '../lib/rbac-fixes';
import { 
  EnhancedStudentService, 
  EnhancedBusService, 
  EnhancedAttendanceService 
} from '../services/enhanced-services-integration';

describe('Enhanced Validation Service Tests', () => {
  
  test('should validate email correctly', () => {
    // اختبار بريد إلكتروني صحيح
    const validEmail = EnhancedValidationService.validateEmail('<EMAIL>');
    expect(validEmail.isValid).toBe(true);
    
    // اختبار بريد إلكتروني غير صحيح
    const invalidEmail = EnhancedValidationService.validateEmail('invalid-email');
    expect(invalidEmail.isValid).toBe(false);
    expect(invalidEmail.error).toContain('Invalid email format');
  });
  
  test('should validate Saudi phone numbers', () => {
    // اختبار رقم هاتف سعودي صحيح
    const validPhone = EnhancedValidationService.validatePhone('0501234567');
    expect(validPhone.isValid).toBe(true);
    
    // اختبار رقم هاتف مع رمز الدولة
    const validPhoneWithCode = EnhancedValidationService.validatePhone('+966501234567');
    expect(validPhoneWithCode.isValid).toBe(true);
    
    // اختبار رقم هاتف غير صحيح
    const invalidPhone = EnhancedValidationService.validatePhone('1234567890');
    expect(invalidPhone.isValid).toBe(false);
  });
  
  test('should validate dates correctly', () => {
    // اختبار تاريخ صحيح
    const validDate = EnhancedValidationService.validateDate('2000-01-01');
    expect(validDate.isValid).toBe(true);
    
    // اختبار تاريخ في المستقبل
    const futureDate = EnhancedValidationService.validateDate('2030-01-01');
    expect(futureDate.isValid).toBe(false);
    expect(futureDate.error).toContain('cannot be in the future');
  });
  
  test('should validate plate numbers', () => {
    // اختبار رقم لوحة صحيح
    const validPlate = EnhancedValidationService.validatePlateNumber('ABC-1234');
    expect(validPlate.isValid).toBe(true);
    
    // اختبار رقم لوحة غير صحيح
    const invalidPlate = EnhancedValidationService.validatePlateNumber('INVALID');
    expect(invalidPlate.isValid).toBe(false);
  });
  
  test('should validate grades', () => {
    // اختبار صفوف صحيحة
    expect(EnhancedValidationService.validateGrade('KG1').isValid).toBe(true);
    expect(EnhancedValidationService.validateGrade('5').isValid).toBe(true);
    expect(EnhancedValidationService.validateGrade('12').isValid).toBe(true);
    
    // اختبار صف غير صحيح
    const invalidGrade = EnhancedValidationService.validateGrade('13');
    expect(invalidGrade.isValid).toBe(false);
  });
});

describe('Cache Manager Tests', () => {
  
  beforeEach(() => {
    CacheManager.clear();
  });
  
  test('should store and retrieve data', () => {
    const testData = { name: 'Test Student', id: '123' };
    
    // حفظ البيانات
    CacheManager.set('test-key', testData);
    
    // استرجاع البيانات
    const retrieved = CacheManager.get('test-key');
    expect(retrieved).toEqual(testData);
  });
  
  test('should handle expiration', async () => {
    const testData = { name: 'Test Student' };
    
    // حفظ البيانات مع انتهاء صلاحية قصير
    CacheManager.set('test-key', testData, 100); // 100ms
    
    // التحقق من وجود البيانات
    expect(CacheManager.get('test-key')).toEqual(testData);
    
    // انتظار انتهاء الصلاحية
    await new Promise(resolve => setTimeout(resolve, 150));
    
    // التحقق من انتهاء الصلاحية
    expect(CacheManager.get('test-key')).toBeNull();
  });
  
  test('should provide correct statistics', () => {
    // إضافة بعض البيانات
    CacheManager.set('key1', 'data1');
    CacheManager.set('key2', 'data2');
    
    // استرجاع البيانات لزيادة hits
    CacheManager.get('key1');
    CacheManager.get('key1');
    CacheManager.get('nonexistent'); // miss
    
    const stats = CacheManager.getStats();
    expect(stats.totalItems).toBe(2);
    expect(stats.totalHits).toBe(2);
    expect(stats.totalMisses).toBe(1);
  });
  
  test('should handle getOrFetch correctly', async () => {
    let fetchCalled = false;
    const fetchFunction = async () => {
      fetchCalled = true;
      return { data: 'fetched' };
    };
    
    // أول استدعاء - يجب أن يستدعي fetch
    const result1 = await CacheManager.getOrFetch('test-key', fetchFunction);
    expect(fetchCalled).toBe(true);
    expect(result1).toEqual({ data: 'fetched' });
    
    // إعادة تعيين المتغير
    fetchCalled = false;
    
    // ثاني استدعاء - يجب أن يأتي من Cache
    const result2 = await CacheManager.getOrFetch('test-key', fetchFunction);
    expect(fetchCalled).toBe(false);
    expect(result2).toEqual({ data: 'fetched' });
  });
});

describe('Enhanced Notification Service Tests', () => {
  
  test('should validate notification data', () => {
    // بيانات صحيحة
    const validData = {
      title: 'Test Notification',
      message: 'This is a test message',
      type: 'announcements' as const,
      tenantId: 'test-tenant-id'
    };
    
    const validation = (EnhancedNotificationService as any).validateNotificationData(validData);
    expect(validation.isValid).toBe(true);
    
    // بيانات غير صحيحة - عنوان فارغ
    const invalidData = {
      title: '',
      message: 'This is a test message',
      type: 'announcements' as const,
      tenantId: 'test-tenant-id'
    };
    
    const invalidValidation = (EnhancedNotificationService as any).validateNotificationData(invalidData);
    expect(invalidValidation.isValid).toBe(false);
    expect(invalidValidation.errors).toContain('Notification title is required');
  });
  
  test('should handle priority validation', () => {
    const dataWithInvalidPriority = {
      title: 'Test',
      message: 'Test message',
      type: 'announcements' as const,
      tenantId: 'test-tenant-id',
      priority: 'invalid' as any
    };
    
    const validation = (EnhancedNotificationService as any).validateNotificationData(dataWithInvalidPriority);
    expect(validation.isValid).toBe(false);
    expect(validation.errors).toContain('Invalid priority level');
  });
});

describe('RBAC Enhanced Manager Tests', () => {
  
  test('should validate context correctly', () => {
    // سياق صحيح
    const validContext = {
      userId: '550e8400-e29b-41d4-a716-************',
      tenantId: '550e8400-e29b-41d4-a716-************'
    };
    
    const validation = RBACEnhancedManager.validateContext(validContext);
    expect(validation.isValid).toBe(true);
    
    // سياق غير صحيح - معرف مستخدم مفقود
    const invalidContext = {
      tenantId: '550e8400-e29b-41d4-a716-************'
    } as any;
    
    const invalidValidation = RBACEnhancedManager.validateContext(invalidContext);
    expect(invalidValidation.isValid).toBe(false);
    expect(invalidValidation.errors).toContain('User ID is required');
  });
  
  test('should validate UUID format', () => {
    const validUUID = '550e8400-e29b-41d4-a716-************';
    const invalidUUID = 'invalid-uuid';
    
    // اختبار UUID صحيح
    const validContext = {
      userId: validUUID,
      tenantId: validUUID
    };
    
    const validValidation = RBACEnhancedManager.validateContext(validContext);
    expect(validValidation.isValid).toBe(true);
    
    // اختبار UUID غير صحيح
    const invalidContext = {
      userId: invalidUUID,
      tenantId: validUUID
    };
    
    const invalidValidation = RBACEnhancedManager.validateContext(invalidContext);
    expect(invalidValidation.isValid).toBe(false);
    expect(invalidValidation.errors).toContain('Invalid user ID format');
  });
});

describe('Transaction Manager Tests', () => {
  
  test('should execute operations in sequence', async () => {
    const executionOrder: string[] = [];
    
    const operations = [
      {
        name: 'operation1',
        operation: async () => {
          executionOrder.push('op1');
          return 'result1';
        }
      },
      {
        name: 'operation2',
        operation: async () => {
          executionOrder.push('op2');
          return 'result2';
        }
      }
    ];
    
    const result = await TransactionManager.executeTransaction(operations);
    
    expect(result.success).toBe(true);
    expect(executionOrder).toEqual(['op1', 'op2']);
    expect(result.data).toEqual(['result1', 'result2']);
  });
  
  test('should handle operation failure and rollback', async () => {
    const rollbackCalled: string[] = [];
    
    const operations = [
      {
        name: 'operation1',
        operation: async () => 'result1',
        rollback: async () => {
          rollbackCalled.push('rollback1');
        }
      },
      {
        name: 'operation2',
        operation: async () => {
          throw new Error('Operation 2 failed');
        },
        rollback: async () => {
          rollbackCalled.push('rollback2');
        }
      }
    ];
    
    const result = await TransactionManager.executeTransaction(operations);
    
    expect(result.success).toBe(false);
    expect(result.error).toContain('Operation 2 failed');
    expect(rollbackCalled).toContain('rollback1');
    expect(result.completedOperations).toContain('operation1');
    expect(result.failedOperation).toBe('operation2');
  });
});

describe('Integration Tests', () => {
  
  test('should integrate validation with student service', async () => {
    const studentService = new EnhancedStudentService();
    
    // بيانات طالب غير صحيحة
    const invalidStudentData = {
      name: 'A', // اسم قصير جداً
      student_id: '12', // رقم قصير جداً
      grade: 'invalid-grade',
      class: '1A',
      parent_id: 'parent-id',
      tenant_id: 'tenant-id',
      profile: {
        date_of_birth: '2030-01-01', // تاريخ في المستقبل
        address: {},
        emergency_contacts: []
      }
    };
    
    // محاولة إنشاء الطالب
    const result = await studentService.createStudentEnhanced(invalidStudentData);
    
    expect(result.success).toBe(false);
    expect(result.error?.code).toBe('VALIDATION_FAILED');
    expect(result.error?.message).toContain('Student name must be at least 2 characters');
  });
});

describe('Performance Tests', () => {
  
  test('cache should improve performance', async () => {
    let fetchCount = 0;
    const slowFetchFunction = async () => {
      fetchCount++;
      await new Promise(resolve => setTimeout(resolve, 100)); // محاكاة عملية بطيئة
      return { data: 'slow-data', fetchCount };
    };
    
    // قياس الوقت بدون cache
    const start1 = Date.now();
    const result1 = await slowFetchFunction();
    const time1 = Date.now() - start1;
    
    // قياس الوقت مع cache
    const start2 = Date.now();
    const result2 = await CacheManager.getOrFetch('slow-key', slowFetchFunction);
    const time2 = Date.now() - start2;
    
    const start3 = Date.now();
    const result3 = await CacheManager.getOrFetch('slow-key', slowFetchFunction);
    const time3 = Date.now() - start3;
    
    expect(fetchCount).toBe(2); // مرة واحدة مباشرة، مرة واحدة مع cache
    expect(time3).toBeLessThan(time2); // الاستدعاء الثاني أسرع
    expect(result2).toEqual(result3); // نفس النتيجة
  });
});

// تنظيف بعد الاختبارات
afterEach(() => {
  CacheManager.clear();
});
