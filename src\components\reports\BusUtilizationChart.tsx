import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
} from "recharts";
import {
  Calendar,
  Filter,
  TrendingUp,
  Bar<PERSON>hart3,
  PieC<PERSON> as PieChartIcon,
  Activity,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { getBusUtilization } from "../../lib/api";

interface ChartData {
  date: string;
  utilization: number;
  trips: number;
  distance: number;
  students: number;
  busName: string;
}

interface BusUtilizationChartProps {
  className?: string;
}

type ChartType = "line" | "area" | "bar" | "pie";
type TimeGrouping = "day" | "week" | "month";

export const BusUtilizationChart: React.FC<BusUtilizationChartProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const { buses } = useDatabase();
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [loading, setLoading] = useState(false);
  const [chartType, setChartType] = useState<ChartType>("line");
  const [timeGrouping, setTimeGrouping] = useState<TimeGrouping>("day");
  const [filters, setFilters] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
    busId: "",
    metric: "utilization" as "utilization" | "trips" | "distance" | "students",
  });

  useEffect(() => {
    loadChartData();
  }, [filters, timeGrouping, tenant?.id]);

  const loadChartData = async () => {
    if (!tenant?.id) return;

    setLoading(true);
    try {
      // Generate sample data for demonstration
      const data = generateSampleData();
      setChartData(data);
    } catch (error) {
      console.error("Error loading chart data:", error);
    } finally {
      setLoading(false);
    }
  };

  const generateSampleData = (): ChartData[] => {
    const data: ChartData[] = [];
    const startDate = new Date(filters.startDate);
    const endDate = new Date(filters.endDate);
    const relevantBuses = filters.busId
      ? buses.filter((b) => b.id === filters.busId)
      : buses.slice(0, 3);

    let currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      // Skip weekends for school bus data
      if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) {
        relevantBuses.forEach((bus) => {
          const baseUtilization = 70 + Math.random() * 25; // 70-95%
          const dayVariation =
            Math.sin(((currentDate.getDay() - 1) * Math.PI) / 4) * 10; // Weekly pattern
          const utilization = Math.max(
            50,
            Math.min(100, baseUtilization + dayVariation),
          );

          data.push({
            date: currentDate.toISOString().split("T")[0],
            utilization: Math.round(utilization * 100) / 100,
            trips: Math.floor(Math.random() * 6) + 4, // 4-10 trips
            distance: Math.floor(Math.random() * 100) + 80, // 80-180 km
            students: Math.floor((bus.capacity * utilization) / 100),
            busName: bus.plate_number,
          });
        });
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return groupDataByTime(data);
  };

  const groupDataByTime = (data: ChartData[]): ChartData[] => {
    if (timeGrouping === "day") return data;

    const grouped = data.reduce((acc, item) => {
      let key: string;
      const date = new Date(item.date);

      if (timeGrouping === "week") {
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        key = `${weekStart.toISOString().split("T")[0]}-${item.busName}`;
      } else {
        // month
        key = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${item.busName}`;
      }

      if (!acc[key]) {
        acc[key] = {
          date:
            timeGrouping === "week"
              ? `Week of ${new Date(date.getTime() - date.getDay() * 24 * 60 * 60 * 1000).toLocaleDateString()}`
              : `${date.toLocaleDateString("en-US", { month: "long", year: "numeric" })}`,
          utilization: 0,
          trips: 0,
          distance: 0,
          students: 0,
          busName: item.busName,
          count: 0,
        };
      }

      acc[key].utilization += item.utilization;
      acc[key].trips += item.trips;
      acc[key].distance += item.distance;
      acc[key].students += item.students;
      acc[key].count++;

      return acc;
    }, {} as any);

    return Object.values(grouped).map((item: any) => ({
      ...item,
      utilization: Math.round((item.utilization / item.count) * 100) / 100,
      trips: Math.round(item.trips / item.count),
      distance: Math.round(item.distance / item.count),
      students: Math.round(item.students / item.count),
    }));
  };

  const getChartColors = () => {
    const colors = [
      "#3B82F6",
      "#10B981",
      "#F59E0B",
      "#EF4444",
      "#8B5CF6",
      "#06B6D4",
    ];
    return colors;
  };

  const renderLineChart = () => {
    const colors = getChartColors();
    const busGroups = chartData.reduce(
      (acc, item) => {
        if (!acc[item.busName]) acc[item.busName] = [];
        acc[item.busName].push(item);
        return acc;
      },
      {} as Record<string, ChartData[]>,
    );

    const combinedData = chartData.reduce((acc, item) => {
      const existing = acc.find((d) => d.date === item.date);
      if (existing) {
        existing[item.busName] = item[filters.metric];
      } else {
        acc.push({
          date: item.date,
          [item.busName]: item[filters.metric],
        });
      }
      return acc;
    }, [] as any[]);

    return (
      <ResponsiveContainer width="100%" height={400}>
        <LineChart data={combinedData}>
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            dataKey="date"
            tick={{ fontSize: 12 }}
            angle={-45}
            textAnchor="end"
            height={80}
          />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip
            contentStyle={{
              backgroundColor: "rgba(255, 255, 255, 0.95)",
              border: "1px solid #e5e7eb",
              borderRadius: "8px",
            }}
          />
          <Legend />
          {Object.keys(busGroups).map((busName, index) => (
            <Line
              key={busName}
              type="monotone"
              dataKey={busName}
              stroke={colors[index % colors.length]}
              strokeWidth={2}
              dot={{ r: 4 }}
              activeDot={{ r: 6 }}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    );
  };

  const renderAreaChart = () => {
    const colors = getChartColors();
    const busGroups = chartData.reduce(
      (acc, item) => {
        if (!acc[item.busName]) acc[item.busName] = [];
        acc[item.busName].push(item);
        return acc;
      },
      {} as Record<string, ChartData[]>,
    );

    const combinedData = chartData.reduce((acc, item) => {
      const existing = acc.find((d) => d.date === item.date);
      if (existing) {
        existing[item.busName] = item[filters.metric];
      } else {
        acc.push({
          date: item.date,
          [item.busName]: item[filters.metric],
        });
      }
      return acc;
    }, [] as any[]);

    return (
      <ResponsiveContainer width="100%" height={400}>
        <AreaChart data={combinedData}>
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            dataKey="date"
            tick={{ fontSize: 12 }}
            angle={-45}
            textAnchor="end"
            height={80}
          />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip
            contentStyle={{
              backgroundColor: "rgba(255, 255, 255, 0.95)",
              border: "1px solid #e5e7eb",
              borderRadius: "8px",
            }}
          />
          <Legend />
          {Object.keys(busGroups).map((busName, index) => (
            <Area
              key={busName}
              type="monotone"
              dataKey={busName}
              stackId="1"
              stroke={colors[index % colors.length]}
              fill={colors[index % colors.length]}
              fillOpacity={0.6}
            />
          ))}
        </AreaChart>
      </ResponsiveContainer>
    );
  };

  const renderBarChart = () => {
    const colors = getChartColors();
    const busGroups = chartData.reduce(
      (acc, item) => {
        if (!acc[item.busName]) acc[item.busName] = [];
        acc[item.busName].push(item);
        return acc;
      },
      {} as Record<string, ChartData[]>,
    );

    const combinedData = chartData.reduce((acc, item) => {
      const existing = acc.find((d) => d.date === item.date);
      if (existing) {
        existing[item.busName] = item[filters.metric];
      } else {
        acc.push({
          date: item.date,
          [item.busName]: item[filters.metric],
        });
      }
      return acc;
    }, [] as any[]);

    return (
      <ResponsiveContainer width="100%" height={400}>
        <BarChart data={combinedData}>
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            dataKey="date"
            tick={{ fontSize: 12 }}
            angle={-45}
            textAnchor="end"
            height={80}
          />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip
            contentStyle={{
              backgroundColor: "rgba(255, 255, 255, 0.95)",
              border: "1px solid #e5e7eb",
              borderRadius: "8px",
            }}
          />
          <Legend />
          {Object.keys(busGroups).map((busName, index) => (
            <Bar
              key={busName}
              dataKey={busName}
              fill={colors[index % colors.length]}
            />
          ))}
        </BarChart>
      </ResponsiveContainer>
    );
  };

  const renderPieChart = () => {
    const colors = getChartColors();
    const busAverages = chartData.reduce(
      (acc, item) => {
        if (!acc[item.busName]) {
          acc[item.busName] = { total: 0, count: 0 };
        }
        acc[item.busName].total += item[filters.metric];
        acc[item.busName].count += 1;
        return acc;
      },
      {} as Record<string, { total: number; count: number }>,
    );

    const pieData = Object.entries(busAverages).map(([busName, data]) => ({
      name: busName,
      value: Math.round((data.total / data.count) * 100) / 100,
    }));

    return (
      <ResponsiveContainer width="100%" height={400}>
        <PieChart>
          <Pie
            data={pieData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, value }) =>
              `${name}: ${value}${filters.metric === "utilization" ? "%" : ""}`
            }
            outerRadius={120}
            fill="#8884d8"
            dataKey="value"
          >
            {pieData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={colors[index % colors.length]}
              />
            ))}
          </Pie>
          <Tooltip />
        </PieChart>
      </ResponsiveContainer>
    );
  };

  const renderChart = () => {
    switch (chartType) {
      case "line":
        return renderLineChart();
      case "area":
        return renderAreaChart();
      case "bar":
        return renderBarChart();
      case "pie":
        return renderPieChart();
      default:
        return renderLineChart();
    }
  };

  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}
    >
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Activity className="mr-2 h-5 w-5 text-primary-500" />
              Bus Utilization Analytics
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Interactive charts showing bus utilization trends and patterns
              over time.
            </p>
          </div>

          <div className="flex items-center gap-2">
            {/* Chart Type Selector */}
            <div className="flex border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
              {[
                { type: "line" as ChartType, icon: TrendingUp, label: "Line" },
                { type: "area" as ChartType, icon: Activity, label: "Area" },
                { type: "bar" as ChartType, icon: BarChart3, label: "Bar" },
                { type: "pie" as ChartType, icon: PieChartIcon, label: "Pie" },
              ].map(({ type, icon: Icon, label }) => (
                <button
                  key={type}
                  onClick={() => setChartType(type)}
                  className={`px-2 md:px-3 py-1.5 md:py-2 text-xs md:text-sm font-medium transition-colors flex-1 ${
                    chartType === type
                      ? "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300"
                      : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                  }`}
                  title={label}
                >
                  <Icon size={14} className="md:w-4 md:h-4" />
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Filters */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3 md:gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Start Date
            </label>
            <input
              type="date"
              value={filters.startDate}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, startDate: e.target.value }))
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              End Date
            </label>
            <input
              type="date"
              value={filters.endDate}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, endDate: e.target.value }))
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Bus
            </label>
            <select
              value={filters.busId}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, busId: e.target.value }))
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
            >
              <option value="">All Buses</option>
              {buses.map((bus) => (
                <option key={bus.id} value={bus.id}>
                  {bus.plate_number}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Metric
            </label>
            <select
              value={filters.metric}
              onChange={(e) =>
                setFilters((prev) => ({
                  ...prev,
                  metric: e.target.value as any,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
            >
              <option value="utilization">Utilization %</option>
              <option value="trips">Number of Trips</option>
              <option value="distance">Distance (km)</option>
              <option value="students">Students Count</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Group By
            </label>
            <select
              value={timeGrouping}
              onChange={(e) => setTimeGrouping(e.target.value as TimeGrouping)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
            >
              <option value="day">Daily</option>
              <option value="week">Weekly</option>
              <option value="month">Monthly</option>
            </select>
          </div>
        </div>

        {/* Chart */}
        {loading ? (
          <div className="flex items-center justify-center h-64 md:h-96">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
        ) : chartData.length > 0 ? (
          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-2 md:p-4">
            <div className="w-full overflow-x-auto">
              <div className="min-w-[300px] h-64 md:h-96">{renderChart()}</div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 md:py-12">
            <Activity
              size={32}
              className="mx-auto mb-4 text-gray-300 md:w-12 md:h-12"
            />
            <p className="text-gray-500 dark:text-gray-400 text-sm md:text-base">
              No data available for the selected criteria.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default BusUtilizationChart;
