import React from "react";
import { Button } from "../ui/Button";
import { ExternalLink, MapPin, CheckCircle, AlertCircle } from "lucide-react";
import type { Tables } from "../../lib/api";

interface NotificationActionsProps {
  notification: Tables<"notifications">;
  onActionComplete?: () => void;
}

export const NotificationActions: React.FC<NotificationActionsProps> = ({
  notification,
  onActionComplete,
}) => {
  const metadata = notification.metadata as any;
  if (!metadata) return null;

  const handleAction = (action: string) => {
    // Handle different action types
    switch (action) {
      case "view_location":
        if (metadata.busId) {
          window.open(`/tracking?bus=${metadata.busId}`, "_blank");
        }
        break;
      case "view_attendance":
        if (metadata.studentId) {
          window.open(`/attendance?student=${metadata.studentId}`, "_blank");
        }
        break;
      case "view_maintenance":
        if (metadata.busId) {
          window.open(`/buses/${metadata.busId}?tab=maintenance`, "_blank");
        }
        break;
      case "schedule":
        if (metadata.busId) {
          window.open(
            `/buses/${metadata.busId}?tab=maintenance&schedule=true`,
            "_blank",
          );
        }
        break;
      case "read_more":
        if (metadata.url) {
          window.open(metadata.url, "_blank");
        }
        break;
      default:
        console.log(`Action ${action} not implemented`);
    }

    // Notify parent component that action was taken
    onActionComplete?.();
  };

  // Return different action buttons based on notification type
  switch (metadata.type) {
    case "geofence":
      return (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            className="text-xs h-7"
            onClick={() => handleAction("view_location")}
          >
            <MapPin size={14} className="mr-1" />
            View Location
          </Button>
        </div>
      );
    case "attendance":
      return (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            className="text-xs h-7"
            onClick={() => handleAction("view_attendance")}
          >
            <CheckCircle size={14} className="mr-1" />
            View Details
          </Button>
        </div>
      );
    case "maintenance":
      return (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            className="text-xs h-7"
            onClick={() => handleAction("view_maintenance")}
          >
            <AlertCircle size={14} className="mr-1" />
            View Details
          </Button>
          <Button
            size="sm"
            variant="outline"
            className="text-xs h-7"
            onClick={() => handleAction("schedule")}
          >
            Schedule
          </Button>
        </div>
      );
    case "announcement":
      return (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            className="text-xs h-7"
            onClick={() => handleAction("read_more")}
          >
            <ExternalLink size={14} className="mr-1" />
            Read More
          </Button>
        </div>
      );
    default:
      if (metadata.url) {
        return (
          <div className="flex space-x-2">
            <Button
              size="sm"
              variant="outline"
              className="text-xs h-7"
              onClick={() => window.open(metadata.url, "_blank")}
            >
              <ExternalLink size={14} className="mr-1" />
              View Details
            </Button>
          </div>
        );
      }
      return null;
  }
};

export default NotificationActions;
