import React from 'react';
import { cn } from '../../utils/cn';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}

export const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  icon, 
  trend, 
  className 
}) => {
  return (
    <div className={cn('bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-5 flex flex-col transition-all hover:shadow-md', className)}>
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">{title}</h3>
          <p className="text-2xl font-bold mt-1 text-gray-900 dark:text-white">{value}</p>
        </div>
        <div className="p-2 bg-primary-50 dark:bg-gray-700 rounded-lg text-primary-500 dark:text-primary-400">
          {icon}
        </div>
      </div>
      
      {trend && (
        <div className="mt-3 flex items-center">
          <span
            className={`inline-flex items-center text-xs font-medium ${
              trend.isPositive 
                ? 'text-accent-600 dark:text-accent-400' 
                : 'text-error-600 dark:text-error-400'
            }`}
          >
            <svg
              className={`mr-1 h-3 w-3 ${trend.isPositive ? 'rotate-0' : 'rotate-180'}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 15l7-7 7 7"
              />
            </svg>
            {trend.value}%
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">vs last month</span>
        </div>
      )}
    </div>
  );
};