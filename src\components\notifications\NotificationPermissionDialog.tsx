import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Bell,
  BellOff,
  Smartphone,
  Shield,
  Zap,
  X,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { Button } from "../ui/Button";
import { pushNotificationService } from "../../lib/pushNotifications";
import { cn } from "../../utils/cn";

interface NotificationPermissionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onPermissionGranted?: () => void;
  onPermissionDenied?: () => void;
  className?: string;
}

type PermissionStep = "intro" | "requesting" | "granted" | "denied" | "error";

export const NotificationPermissionDialog: React.FC<
  NotificationPermissionDialogProps
> = ({
  isOpen,
  onClose,
  onPermissionGranted,
  onPermissionDenied,
  className = "",
}) => {
  const { t } = useTranslation();
  const [step, setStep] = useState<PermissionStep>("intro");
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      // Check current permission status
      if ("Notification" in window) {
        const permission = Notification.permission;
        if (permission === "granted") {
          setStep("granted");
        } else if (permission === "denied") {
          setStep("denied");
        } else {
          setStep("intro");
        }
      } else {
        setStep("error");
        setError("Notifications are not supported in this browser");
      }
    }
  }, [isOpen]);

  const handleRequestPermission = async () => {
    setIsLoading(true);
    setStep("requesting");
    setError(null);

    try {
      const permission = await pushNotificationService.requestPermission();

      if (permission === "granted") {
        setStep("granted");
        onPermissionGranted?.();
      } else {
        setStep("denied");
        onPermissionDenied?.();
      }
    } catch (err: any) {
      console.error("Error requesting notification permission:", err);
      setError(err.message || "Failed to request notification permission");
      setStep("error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setStep("intro");
    setError(null);
    onClose();
  };

  const benefits = [
    {
      icon: Zap,
      title: "Instant Updates",
      description: "Get notified immediately when your bus arrives or departs",
    },
    {
      icon: Shield,
      title: "Safety Alerts",
      description: "Receive important safety and emergency notifications",
    },
    {
      icon: Bell,
      title: "Attendance Reminders",
      description: "Never miss attendance updates for your children",
    },
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div
        className={cn(
          "bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md",
          className,
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary-100 dark:bg-primary-900/20 rounded-lg">
              <Smartphone
                size={20}
                className="text-primary-600 dark:text-primary-400"
              />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Enable Notifications
            </h2>
          </div>
          <Button variant="ghost" size="sm" onClick={handleClose}>
            <X size={20} />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === "intro" && (
            <div className="space-y-6">
              <div className="text-center">
                <div className="mx-auto w-16 h-16 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center mb-4">
                  <Bell
                    size={32}
                    className="text-primary-600 dark:text-primary-400"
                  />
                </div>
                <p className="text-gray-600 dark:text-gray-400">
                  Stay informed with real-time notifications about your school
                  bus service
                </p>
              </div>

              <div className="space-y-4">
                {benefits.map((benefit, index) => {
                  const Icon = benefit.icon;
                  return (
                    <div key={index} className="flex items-start space-x-3">
                      <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg flex-shrink-0">
                        <Icon
                          size={16}
                          className="text-gray-600 dark:text-gray-400"
                        />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {benefit.title}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {benefit.description}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div className="flex items-start space-x-2">
                  <Shield
                    size={16}
                    className="text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0"
                  />
                  <div>
                    <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                      Privacy Protected
                    </p>
                    <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                      We only send notifications relevant to your school bus
                      service. You can disable them anytime.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {step === "requesting" && (
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto" />
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Requesting Permission
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Please allow notifications when prompted by your browser
                </p>
              </div>
            </div>
          )}

          {step === "granted" && (
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                <CheckCircle
                  size={32}
                  className="text-green-600 dark:text-green-400"
                />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Notifications Enabled!
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  You'll now receive important updates about your school bus
                  service
                </p>
              </div>
            </div>
          )}

          {step === "denied" && (
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                <BellOff size={32} className="text-red-600 dark:text-red-400" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Notifications Blocked
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  To enable notifications, please:
                </p>
                <div className="text-left space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <p>1. Click the lock icon in your browser's address bar</p>
                  <p>2. Change notifications from "Block" to "Allow"</p>
                  <p>3. Refresh the page</p>
                </div>
              </div>
            </div>
          )}

          {step === "error" && (
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                <AlertCircle
                  size={32}
                  className="text-red-600 dark:text-red-400"
                />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Error
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {error ||
                    "An error occurred while requesting notification permission"}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
          {step === "intro" && (
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={handleClose}
                className="flex-1"
              >
                Maybe Later
              </Button>
              <Button
                onClick={handleRequestPermission}
                disabled={isLoading}
                className="flex-1"
                leftIcon={<Bell size={16} />}
              >
                Enable Notifications
              </Button>
            </div>
          )}

          {(step === "granted" || step === "denied" || step === "error") && (
            <Button onClick={handleClose} className="w-full">
              {step === "granted" ? "Continue" : "Close"}
            </Button>
          )}

          {step === "requesting" && (
            <Button variant="outline" onClick={handleClose} className="w-full">
              Cancel
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationPermissionDialog;
