#!/usr/bin/env tsx

/**
 * سكريبت لتطبيق إصلاحات سياسات المستخدمين
 * Script to apply user policies fixes
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';
import dotenv from 'dotenv';

// تحميل متغيرات البيئة
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: VITE_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applyUserFixes() {
  console.log('🔧 بدء تطبيق إصلاحات سياسات المستخدمين...');
  console.log('🔧 Starting user policies fixes...');

  try {
    // قراءة ملف التهجير
    const migrationPath = join(process.cwd(), 'supabase', 'migrations', '20250602000001_fix_user_creation_policies.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');

    console.log('📄 تطبيق ملف التهجير...');
    console.log('📄 Applying migration file...');

    // تطبيق ملف التهجير
    const { error: migrationError } = await supabase.rpc('exec', {
      sql: migrationSQL
    });

    if (migrationError) {
      console.error('❌ خطأ في تطبيق ملف التهجير:', migrationError);
      throw migrationError;
    }

    console.log('✅ تم تطبيق ملف التهجير بنجاح');

    // فحص سلامة النظام
    console.log('🔍 فحص سلامة النظام...');
    const { data: healthCheck, error: healthError } = await supabase
      .rpc('check_user_system_health');

    if (healthError) {
      console.error('❌ خطأ في فحص سلامة النظام:', healthError);
    } else {
      console.log('✅ نتائج فحص سلامة النظام:');
      console.log(JSON.stringify(healthCheck, null, 2));
    }

    // اختبار إنشاء مستخدم تجريبي
    console.log('🧪 اختبار إنشاء مستخدم تجريبي...');
    
    const testUserId = 'test-user-' + Date.now();
    const { data: testUser, error: testError } = await supabase
      .rpc('create_user_profile', {
        user_id: testUserId,
        user_email: '<EMAIL>',
        user_name: 'Test User',
        user_role: 'student',
        user_tenant_id: null
      });

    if (testError) {
      console.error('❌ خطأ في اختبار إنشاء المستخدم:', testError);
    } else {
      console.log('✅ تم إنشاء المستخدم التجريبي بنجاح');
      
      // حذف المستخدم التجريبي
      const { error: deleteError } = await supabase
        .from('users')
        .delete()
        .eq('id', testUserId);

      if (deleteError) {
        console.warn('⚠️ تحذير: لم يتم حذف المستخدم التجريبي:', deleteError);
      } else {
        console.log('✅ تم حذف المستخدم التجريبي');
      }
    }

    // فحص السياسات النشطة
    console.log('📋 فحص السياسات النشطة...');
    const { data: policies, error: policiesError } = await supabase
      .from('pg_policies')
      .select('policyname, tablename, cmd, qual')
      .eq('tablename', 'users')
      .eq('schemaname', 'public');

    if (policiesError) {
      console.error('❌ خطأ في فحص السياسات:', policiesError);
    } else {
      console.log(`✅ تم العثور على ${policies?.length || 0} سياسة نشطة لجدول المستخدمين`);
      policies?.forEach(policy => {
        console.log(`  - ${policy.policyname} (${policy.cmd})`);
      });
    }

    console.log('\n🎉 تم تطبيق جميع الإصلاحات بنجاح!');
    console.log('🎉 All fixes applied successfully!');

  } catch (error) {
    console.error('🚨 خطأ في تطبيق الإصلاحات:', error);
    process.exit(1);
  }
}

// تشغيل السكريبت
applyUserFixes()
  .then(() => {
    console.log('✅ اكتمل السكريبت بنجاح');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ فشل السكريبت:', error);
    process.exit(1);
  });

export { applyUserFixes };
