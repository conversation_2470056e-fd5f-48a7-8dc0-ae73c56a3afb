<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/bus-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#3b82f6" />
    <meta name="description" content="Smart School Bus Management System" />
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap"
      rel="stylesheet"
      crossorigin="anonymous"
    />
    <title>SchoolBus - Smart Bus Management System</title>
    <style>
      /* Critical CSS for loading state */
      #root {
        min-height: 100vh;
        background-color: #f9fafb;
      }
      .loading-fallback {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        font-family: "Cairo", sans-serif;
      }
      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading-fallback">
        <div class="spinner"></div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
    <script
      src="https://api.tempo.new/proxy-asset?url=https://storage.googleapis.com/tempo-public-assets/error-handling.js"
      defer
    ></script>
  </body>
</html>
