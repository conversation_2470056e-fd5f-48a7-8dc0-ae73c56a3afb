/**
 * Admin Theme Management Component
 * Comprehensive theme management for all schools
 * Phase 3: UI/UX Enhancement - Admin Controls
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { useThemePermissions } from '../../../hooks/useThemePermissions';
import { schoolService } from '../../../services/data/SchoolService';
import { tenantThemeManager, TenantThemeConfig } from '../../../design-system/themes/tenant/TenantTheme';

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '../../common/ui/Card';
import { Button } from '../../ui/Button';
import { Badge } from '../../common/ui/Badge';
import { Alert, AlertDescription } from '../../common/ui/Alert';
import { DataTable } from '../../common/data-display/DataTable';
import { SearchInput } from '../../common/forms/SearchInput';

// Icons
import {
  Settings,
  Eye,
  Edit,
  Trash2,
  Plus,
  Download,
  Upload,
  CheckCircle,
  XCircle,
  Clock,
  Palette
} from 'lucide-react';

interface SchoolThemeData {
  id: string;
  schoolName: string;
  schoolCode: string;
  hasCustomTheme: boolean;
  themeStatus: 'active' | 'pending' | 'disabled';
  lastModified: string;
  modifiedBy: string;
  previewUrl?: string;
}

interface ThemeManagementState {
  schools: SchoolThemeData[];
  loading: boolean;
  error: string | null;
  selectedSchool: SchoolThemeData | null;
  showThemeEditor: boolean;
  searchQuery: string;
  statusFilter: string;
}

/**
 * Admin Theme Management Component
 */
export const AdminThemeManagement: React.FC = () => {
  const { user } = useAuth();
  const permissions = useThemePermissions();

  // Check admin permissions
  const canManageAllThemes = permissions.canManageAllThemes;
  const canCreateThemes = permissions.canManageAllThemes;
  const canDeleteThemes = permissions.canDeleteThemes;
  const canApproveChanges = permissions.canApproveThemes;

  const [state, setState] = useState<ThemeManagementState>({
    schools: [],
    loading: false,
    error: null,
    selectedSchool: null,
    showThemeEditor: false,
    searchQuery: '',
    statusFilter: 'all',
  });

  /**
   * Load schools with theme data
   */
  const loadSchoolsThemeData = useCallback(async () => {
    if (!canManageAllThemes) {
      setState(prev => ({
        ...prev,
        error: 'ليس لديك صلاحية لإدارة ثيمات المدارس',
      }));
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Get all schools
      const schoolsResponse = await schoolService.getSchools({ limit: 100 });

      if (schoolsResponse.success && schoolsResponse.data) {
        const schoolsWithThemes = await Promise.all(
          schoolsResponse.data.data.map(async (school) => {
            // Check if school has custom theme
            const themeConfig = tenantThemeManager.getTenantTheme(school.id);

            return {
              id: school.id,
              schoolName: school.name,
              schoolCode: school.code,
              hasCustomTheme: !!themeConfig,
              themeStatus: themeConfig ? 'active' : 'disabled',
              lastModified: themeConfig?.tenant.branding.name ? new Date().toISOString() : '',
              modifiedBy: school.manager?.name || 'غير محدد',
              previewUrl: themeConfig ? `/preview/theme/${school.id}` : undefined,
            } as SchoolThemeData;
          })
        );

        setState(prev => ({
          ...prev,
          schools: schoolsWithThemes,
          loading: false,
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'فشل في تحميل بيانات المدارس',
        loading: false,
      }));
    }
  }, [canManageAllThemes]);

  useEffect(() => {
    loadSchoolsThemeData();
  }, [loadSchoolsThemeData]);

  /**
   * Handle theme actions
   */
  const handleCreateTheme = useCallback((schoolId: string) => {
    if (!canCreateThemes) return;

    setState(prev => ({
      ...prev,
      selectedSchool: prev.schools.find(s => s.id === schoolId) || null,
      showThemeEditor: true,
    }));
  }, [canCreateThemes]);

  const handleEditTheme = useCallback((schoolId: string) => {
    setState(prev => ({
      ...prev,
      selectedSchool: prev.schools.find(s => s.id === schoolId) || null,
      showThemeEditor: true,
    }));
  }, []);

  const handleDeleteTheme = useCallback(async (schoolId: string) => {
    if (!canDeleteThemes) return;

    if (confirm('هل أنت متأكد من حذف ثيم هذه المدرسة؟')) {
      try {
        // Remove theme from manager
        tenantThemeManager.clearThemes();

        // Update state
        setState(prev => ({
          ...prev,
          schools: prev.schools.map(school =>
            school.id === schoolId
              ? { ...school, hasCustomTheme: false, themeStatus: 'disabled' }
              : school
          ),
        }));
      } catch (error) {
        console.error('فشل في حذف الثيم:', error);
      }
    }
  }, [canDeleteThemes]);

  const handlePreviewTheme = useCallback((schoolId: string) => {
    const school = state.schools.find(s => s.id === schoolId);
    if (school?.previewUrl) {
      window.open(school.previewUrl, '_blank');
    }
  }, [state.schools]);

  /**
   * Filter schools based on search and status
   */
  const filteredSchools = state.schools.filter(school => {
    const matchesSearch = school.schoolName.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
                         school.schoolCode.toLowerCase().includes(state.searchQuery.toLowerCase());

    const matchesStatus = state.statusFilter === 'all' || school.themeStatus === state.statusFilter;

    return matchesSearch && matchesStatus;
  });

  /**
   * Table columns
   */
  const columns = [
    {
      key: 'school',
      title: 'المدرسة',
      render: (school: SchoolThemeData) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <Palette className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <div className="font-medium">{school.schoolName}</div>
            <div className="text-sm text-gray-500">كود: {school.schoolCode}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'theme_status',
      title: 'حالة الثيم',
      render: (school: SchoolThemeData) => (
        <div className="flex items-center space-x-2">
          <Badge
            variant={
              school.themeStatus === 'active' ? 'default' :
              school.themeStatus === 'pending' ? 'warning' : 'destructive'
            }
          >
            {school.themeStatus === 'active' ? 'نشط' :
             school.themeStatus === 'pending' ? 'في الانتظار' : 'معطل'}
          </Badge>
          {school.hasCustomTheme && (
            <CheckCircle className="w-4 h-4 text-green-500" />
          )}
        </div>
      ),
    },
    {
      key: 'last_modified',
      title: 'آخر تعديل',
      render: (school: SchoolThemeData) => (
        <div>
          {school.lastModified ? (
            <>
              <div className="text-sm">
                {new Date(school.lastModified).toLocaleDateString('ar-SA')}
              </div>
              <div className="text-xs text-gray-500">
                بواسطة: {school.modifiedBy}
              </div>
            </>
          ) : (
            <span className="text-gray-400">لم يتم التعديل</span>
          )}
        </div>
      ),
    },
    {
      key: 'actions',
      title: 'الإجراءات',
      render: (school: SchoolThemeData) => (
        <div className="flex items-center space-x-2">
          {school.hasCustomTheme ? (
            <>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handlePreviewTheme(school.id)}
                title="معاينة الثيم"
              >
                <Eye className="w-4 h-4" />
              </Button>

              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleEditTheme(school.id)}
                title="تعديل الثيم"
              >
                <Edit className="w-4 h-4" />
              </Button>

              {canDeleteThemes && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleDeleteTheme(school.id)}
                  title="حذف الثيم"
                >
                  <Trash2 className="w-4 h-4 text-red-500" />
                </Button>
              )}
            </>
          ) : (
            canCreateThemes && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleCreateTheme(school.id)}
              >
                <Plus className="w-4 h-4 mr-1" />
                إنشاء ثيم
              </Button>
            )
          )}
        </div>
      ),
    },
  ];

  // Status filter options
  const statusOptions = [
    { value: 'all', label: 'جميع الحالات' },
    { value: 'active', label: 'نشط' },
    { value: 'pending', label: 'في الانتظار' },
    { value: 'disabled', label: 'معطل' },
  ];

  if (!canManageAllThemes) {
    return (
      <Alert variant="destructive">
        <XCircle className="h-4 w-4" />
        <AlertDescription>
          ليس لديك صلاحية لإدارة ثيمات المدارس.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">إدارة ثيمات المدارس</h2>
          <p className="text-gray-600">إدارة وتخصيص ثيمات جميع المدارس في النظام</p>
        </div>

        <div className="flex items-center space-x-2">
          {canCreateThemes && (
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              إنشاء ثيم جديد
            </Button>
          )}
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المدارس</p>
                <p className="text-2xl font-bold">{state.schools.length}</p>
              </div>
              <Settings className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">ثيمات نشطة</p>
                <p className="text-2xl font-bold text-green-600">
                  {state.schools.filter(s => s.themeStatus === 'active').length}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">في الانتظار</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {state.schools.filter(s => s.themeStatus === 'pending').length}
                </p>
              </div>
              <Clock className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">بدون ثيم</p>
                <p className="text-2xl font-bold text-gray-600">
                  {state.schools.filter(s => !s.hasCustomTheme).length}
                </p>
              </div>
              <XCircle className="w-8 h-8 text-gray-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <SearchInput
              placeholder="البحث في المدارس..."
              value={state.searchQuery}
              onChange={(value) => setState(prev => ({ ...prev, searchQuery: value }))}
              className="flex-1"
            />

            <select
              value={state.statusFilter}
              onChange={(e) => setState(prev => ({ ...prev, statusFilter: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-md"
            >
              {statusOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Schools Table */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة المدارس</CardTitle>
        </CardHeader>
        <CardContent>
          {state.error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{state.error}</AlertDescription>
            </Alert>
          )}

          <DataTable
            data={filteredSchools}
            columns={columns}
            loading={state.loading}
            emptyMessage="لا توجد مدارس"
          />
        </CardContent>
      </Card>

      {/* Theme Editor Modal */}
      {state.showThemeEditor && state.selectedSchool && (
        <ThemeEditorModal
          schoolId={state.selectedSchool.id}
          schoolName={state.selectedSchool.schoolName}
          onClose={() => setState(prev => ({ ...prev, showThemeEditor: false, selectedSchool: null }))}
          onSave={() => {
            loadSchoolsThemeData();
            setState(prev => ({ ...prev, showThemeEditor: false, selectedSchool: null }));
          }}
        />
      )}
    </div>
  );
};

/**
 * Theme Editor Modal Component
 */
interface ThemeEditorModalProps {
  schoolId: string;
  schoolName: string;
  onClose: () => void;
  onSave: () => void;
}

const ThemeEditorModal: React.FC<ThemeEditorModalProps> = ({
  schoolId,
  schoolName,
  onClose,
  onSave,
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">
            تخصيص ثيم {schoolName}
          </h3>
          <Button variant="ghost" onClick={onClose}>
            ✕
          </Button>
        </div>

        {/* Theme Customizer Component */}
        <div className="space-y-4">
          <p className="text-gray-600">
            يمكنك تخصيص ثيم المدرسة من هنا...
          </p>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose}>
              إلغاء
            </Button>
            <Button onClick={onSave}>
              حفظ التغييرات
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
