import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Star, Send, X } from "lucide-react";
import { Button } from "../ui/Button";
import { createEvaluation } from "../../lib/api";
import { useAuth } from "../../contexts/AuthContext";

interface EvaluationFormProps {
  targetType: "driver" | "service" | "route";
  targetId: string;
  targetName: string;
  onClose: () => void;
  onSuccess: () => void;
}

export const EvaluationForm: React.FC<EvaluationFormProps> = ({
  targetType,
  targetId,
  targetName,
  onClose,
  onSuccess,
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [comment, setComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || rating === 0) return;

    setIsSubmitting(true);
    try {
      await createEvaluation(
        user.tenant_id!,
        user.id,
        targetType,
        targetId,
        rating,
        comment || undefined,
      );
      onSuccess();
      onClose();
    } catch (error) {
      console.error("Error submitting evaluation:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getEvaluationTitle = () => {
    switch (targetType) {
      case "driver":
        return t("evaluation.evaluateDriver");
      case "service":
        return t("evaluation.evaluateService");
      case "route":
        return t("evaluation.evaluateRoute");
      default:
        return t("evaluation.evaluate");
    }
  };

  const getEvaluationCriteria = () => {
    switch (targetType) {
      case "driver":
        return [
          t("evaluation.criteria.punctuality"),
          t("evaluation.criteria.safety"),
          t("evaluation.criteria.professionalism"),
          t("evaluation.criteria.communication"),
        ];
      case "service":
        return [
          t("evaluation.criteria.comfort"),
          t("evaluation.criteria.punctuality"),
          t("evaluation.criteria.cleanliness"),
          t("evaluation.criteria.overall"),
        ];
      case "route":
        return [
          t("evaluation.criteria.efficiency"),
          t("evaluation.criteria.safety"),
          t("evaluation.criteria.timing"),
          t("evaluation.criteria.stops"),
        ];
      default:
        return [];
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {getEvaluationTitle()}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="mb-6">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              {t("evaluation.evaluating")}:{" "}
              <span className="font-medium">{targetName}</span>
            </p>
          </div>

          {/* Rating Stars */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              {t("evaluation.rating")} *
            </label>
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => setRating(star)}
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  className="p-1 transition-colors"
                >
                  <Star
                    size={32}
                    className={`${
                      star <= (hoveredRating || rating)
                        ? "text-yellow-400 fill-current"
                        : "text-gray-300 dark:text-gray-600"
                    } transition-colors`}
                  />
                </button>
              ))}
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {rating > 0 && (
                <span>
                  {rating === 1 && t("evaluation.ratings.poor")}
                  {rating === 2 && t("evaluation.ratings.fair")}
                  {rating === 3 && t("evaluation.ratings.good")}
                  {rating === 4 && t("evaluation.ratings.veryGood")}
                  {rating === 5 && t("evaluation.ratings.excellent")}
                </span>
              )}
            </p>
          </div>

          {/* Evaluation Criteria */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t("evaluation.criteria.title")}
            </label>
            <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
              {getEvaluationCriteria().map((criterion, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-1 h-1 bg-gray-400 rounded-full mr-2"></div>
                  {criterion}
                </div>
              ))}
            </div>
          </div>

          {/* Comment */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t("evaluation.comment")} ({t("common.optional")})
            </label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              placeholder={t("evaluation.commentPlaceholder")}
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              {t("common.cancel")}
            </Button>
            <Button
              type="submit"
              disabled={rating === 0 || isSubmitting}
              leftIcon={<Send size={16} />}
            >
              {isSubmitting ? t("common.submitting") : t("evaluation.submit")}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EvaluationForm;
