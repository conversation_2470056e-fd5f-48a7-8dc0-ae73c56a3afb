import React, { useRef, useEffect, useState, useCallback } from 'react';
import Map, { <PERSON><PERSON>, <PERSON>up, Source, Layer, NavigationControl, GeolocateControl } from 'react-map-gl';
import { MapPin, Navigation, Zap, AlertTriangle, Users, Clock } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import 'mapbox-gl/dist/mapbox-gl.css';

// Types
interface BusLocation {
  id: string;
  plateNumber: string;
  latitude: number;
  longitude: number;
  speed: number;
  heading: number;
  status: 'moving' | 'stopped' | 'maintenance' | 'emergency';
  driver: string;
  route: string;
  studentsCount: number;
  lastUpdate: string;
  nextStop?: string;
  eta?: string;
}

interface RouteData {
  id: string;
  name: string;
  coordinates: [number, number][];
  stops: Array<{
    id: string;
    name: string;
    latitude: number;
    longitude: number;
    order: number;
    studentsCount: number;
  }>;
  color: string;
}

interface InteractiveMapProps {
  buses?: BusLocation[];
  routes?: RouteData[];
  selectedBus?: string | null;
  onBusSelect?: (busId: string | null) => void;
  showRoutes?: boolean;
  showStops?: boolean;
  showTrails?: boolean;
  autoRefresh?: boolean;
  className?: string;
}

const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN;

// Default center (Riyadh, Saudi Arabia)
const DEFAULT_CENTER: [number, number] = [46.6753, 24.7136];
const DEFAULT_ZOOM = 11;

export const InteractiveMap: React.FC<InteractiveMapProps> = ({
  buses = [],
  routes = [],
  selectedBus,
  onBusSelect,
  showRoutes = true,
  showStops = true,
  showTrails = false,
  autoRefresh = false,
  className = '',
}) => {
  const { t } = useTranslation();
  const mapRef = useRef<any>(null);
  
  const [viewState, setViewState] = useState({
    longitude: DEFAULT_CENTER[0],
    latitude: DEFAULT_CENTER[1],
    zoom: DEFAULT_ZOOM,
  });
  
  const [selectedBusData, setSelectedBusData] = useState<BusLocation | null>(null);
  const [mapStyle, setMapStyle] = useState('mapbox://styles/mapbox/streets-v12');

  // Auto refresh functionality
  useEffect(() => {
    if (!autoRefresh) return;
    
    const interval = setInterval(() => {
      // Trigger data refresh (would be connected to real API)
      console.log('Auto refreshing map data...');
    }, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(interval);
  }, [autoRefresh]);

  // Handle bus selection
  const handleBusClick = useCallback((bus: BusLocation) => {
    setSelectedBusData(bus);
    onBusSelect?.(bus.id);
    
    // Center map on selected bus
    setViewState(prev => ({
      ...prev,
      longitude: bus.longitude,
      latitude: bus.latitude,
      zoom: Math.max(prev.zoom, 14),
    }));
  }, [onBusSelect]);

  // Get bus icon based on status
  const getBusIcon = (status: BusLocation['status']) => {
    switch (status) {
      case 'moving':
        return <Navigation className="w-4 h-4 text-green-600" />;
      case 'stopped':
        return <MapPin className="w-4 h-4 text-yellow-600" />;
      case 'maintenance':
        return <Zap className="w-4 h-4 text-orange-600" />;
      case 'emergency':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <MapPin className="w-4 h-4 text-gray-600" />;
    }
  };

  // Get bus color based on status
  const getBusColor = (status: BusLocation['status']) => {
    switch (status) {
      case 'moving':
        return '#16a34a'; // green-600
      case 'stopped':
        return '#ca8a04'; // yellow-600
      case 'maintenance':
        return '#ea580c'; // orange-600
      case 'emergency':
        return '#dc2626'; // red-600
      default:
        return '#6b7280'; // gray-500
    }
  };

  // Create route line data for Mapbox
  const createRouteLineData = (route: RouteData) => ({
    type: 'Feature' as const,
    properties: {
      id: route.id,
      name: route.name,
      color: route.color,
    },
    geometry: {
      type: 'LineString' as const,
      coordinates: route.coordinates,
    },
  });

  // Route layer style
  const routeLayerStyle = {
    id: 'route-line',
    type: 'line' as const,
    paint: {
      'line-color': ['get', 'color'],
      'line-width': 4,
      'line-opacity': 0.8,
    },
  };

  if (!MAPBOX_TOKEN) {
    return (
      <div className={`flex items-center justify-center h-96 bg-gray-100 rounded-lg ${className}`}>
        <div className="text-center">
          <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t('tracking.mapIntegrationNote')}
          </h3>
          <p className="text-gray-500">
            Mapbox access token is required for map functionality
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative h-96 rounded-lg overflow-hidden ${className}`}>
      <div style={{ width: '100%', height: '100%' }}>
        <Map
          ref={mapRef}
          {...viewState}
          onMove={evt => setViewState(evt.viewState)}
          mapboxAccessToken={MAPBOX_TOKEN}
          style={{ width: '100%', height: '100%' }}
          mapStyle={mapStyle}
          attributionControl={false}
        >
        {/* Navigation Controls */}
        <NavigationControl position="top-right" />
        <GeolocateControl position="top-right" />

        {/* Route Lines */}
        {showRoutes && routes.map(route => (
          <Source
            key={`route-${route.id}`}
            id={`route-${route.id}`}
            type="geojson"
            data={createRouteLineData(route)}
          >
            <Layer {...routeLayerStyle} id={`route-line-${route.id}`} />
          </Source>
        ))}

        {/* Bus Stops */}
        {showStops && routes.map(route =>
          route.stops.map(stop => (
            <Marker
              key={`stop-${stop.id}`}
              longitude={stop.longitude}
              latitude={stop.latitude}
            >
              <div className="relative">
                <div className="w-3 h-3 bg-blue-500 rounded-full border-2 border-white shadow-lg" />
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 hover:opacity-100 transition-opacity">
                  {stop.name} ({stop.studentsCount} {t('students.students')})
                </div>
              </div>
            </Marker>
          ))
        )}

        {/* Bus Markers */}
        {buses.map(bus => (
          <Marker
            key={bus.id}
            longitude={bus.longitude}
            latitude={bus.latitude}
            onClick={() => handleBusClick(bus)}
          >
            <div 
              className={`relative cursor-pointer transform transition-transform hover:scale-110 ${
                selectedBus === bus.id ? 'scale-125' : ''
              }`}
            >
              <div 
                className="w-8 h-8 rounded-full border-3 border-white shadow-lg flex items-center justify-center"
                style={{ backgroundColor: getBusColor(bus.status) }}
              >
                {getBusIcon(bus.status)}
              </div>
              {selectedBus === bus.id && (
                <div className="absolute -top-2 -right-2 w-4 h-4 bg-blue-500 rounded-full animate-pulse" />
              )}
            </div>
          </Marker>
        ))}

        {/* Bus Info Popup */}
        {selectedBusData && (
          <Popup
            longitude={selectedBusData.longitude}
            latitude={selectedBusData.latitude}
            anchor="bottom"
            onClose={() => {
              setSelectedBusData(null);
              onBusSelect?.(null);
            }}
            closeButton={true}
            closeOnClick={false}
            className="bus-popup"
          >
            <div className="p-4 min-w-64">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold text-gray-900">
                  {selectedBusData.plateNumber}
                </h3>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  selectedBusData.status === 'moving' ? 'bg-green-100 text-green-800' :
                  selectedBusData.status === 'stopped' ? 'bg-yellow-100 text-yellow-800' :
                  selectedBusData.status === 'maintenance' ? 'bg-orange-100 text-orange-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {t(`tracking.${selectedBusData.status}`)}
                </span>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <Users className="w-4 h-4 text-gray-400 mr-2" />
                  <span>{selectedBusData.driver}</span>
                </div>
                
                <div className="flex items-center">
                  <Navigation className="w-4 h-4 text-gray-400 mr-2" />
                  <span>{selectedBusData.route}</span>
                </div>
                
                <div className="flex items-center">
                  <Users className="w-4 h-4 text-gray-400 mr-2" />
                  <span>{selectedBusData.studentsCount} {t('students.students')}</span>
                </div>
                
                {selectedBusData.speed > 0 && (
                  <div className="flex items-center">
                    <Zap className="w-4 h-4 text-gray-400 mr-2" />
                    <span>{selectedBusData.speed} km/h</span>
                  </div>
                )}
                
                {selectedBusData.nextStop && (
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 text-gray-400 mr-2" />
                    <span>{t('tracking.nextStop')}: {selectedBusData.nextStop}</span>
                  </div>
                )}
                
                {selectedBusData.eta && (
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 text-gray-400 mr-2" />
                    <span>{t('tracking.eta')}: {selectedBusData.eta}</span>
                  </div>
                )}
                
                <div className="text-xs text-gray-500 mt-3">
                  {t('tracking.lastUpdated')}: {selectedBusData.lastUpdate}
                </div>
              </div>
            </div>
          </Popup>
        )}
      </Map>
      </div>

      {/* Map Style Selector */}
      <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-2">
        <select
          value={mapStyle}
          onChange={(e) => setMapStyle(e.target.value)}
          className="text-sm border-none focus:ring-0"
        >
          <option value="mapbox://styles/mapbox/streets-v12">Streets</option>
          <option value="mapbox://styles/mapbox/satellite-v9">Satellite</option>
          <option value="mapbox://styles/mapbox/light-v11">Light</option>
          <option value="mapbox://styles/mapbox/dark-v11">Dark</option>
        </select>
      </div>

      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3">
        <h4 className="text-sm font-medium text-gray-900 mb-2">
          {t('tracking.busStatus')}
        </h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-600 rounded-full mr-2" />
            <span>{t('tracking.moving')}</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-yellow-600 rounded-full mr-2" />
            <span>{t('tracking.stopped')}</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-orange-600 rounded-full mr-2" />
            <span>{t('tracking.maintenance')}</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-600 rounded-full mr-2" />
            <span>{t('tracking.emergency')}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InteractiveMap;
