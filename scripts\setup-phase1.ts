/**
 * Setup Script for Phase 1: Centralized Permission System
 * Initializes and validates the centralized permission service
 */

import { PermissionService } from "../src/lib/permissionService";
import { SecurityAuditService } from "../src/lib/securityAuditService";
import { CentralizedAuthMiddleware } from "../src/middleware/centralizedAuthMiddleware";
import { ResourceType, Action, DataScope } from "../src/lib/rbac";
import { UserRole } from "../src/types";

// Test user data
const testUsers = {
  admin: {
    id: "admin-test",
    email: "<EMAIL>",
    role: UserRole.ADMIN,
    tenant_id: "test-tenant-1",
    is_active: true,
    name: "Test Admin",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  schoolManager: {
    id: "manager-test",
    email: "<EMAIL>",
    role: UserRole.SCHOOL_MANAGER,
    tenant_id: "test-tenant-1",
    is_active: true,
    name: "Test School Manager",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  driver: {
    id: "driver-test",
    email: "<EMAIL>",
    role: UserRole.DRIVER,
    tenant_id: "test-tenant-1",
    is_active: true,
    name: "Test Driver",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
};

/**
 * Initialize and test the centralized permission system
 */
async function setupPhase1(): Promise<void> {
  console.log("🚀 Setting up Phase 1: Centralized Permission System");
  console.log("=" .repeat(60));

  try {
    // 1. Initialize services
    console.log("\n1. Initializing services...");
    const permissionService = PermissionService.getInstance();
    const securityAuditService = SecurityAuditService.getInstance();
    const authMiddleware = CentralizedAuthMiddleware.getInstance();

    console.log("✅ Permission Service initialized");
    console.log("✅ Security Audit Service initialized");
    console.log("✅ Auth Middleware initialized");

    // 2. Test basic permission checks
    console.log("\n2. Testing basic permission checks...");
    
    // Admin should have full access
    const adminResult = permissionService.checkPermission(
      testUsers.admin,
      ResourceType.USER,
      Action.DELETE
    );
    console.log(`✅ Admin DELETE USER: ${adminResult.allowed ? "ALLOWED" : "DENIED"}`);

    // School Manager should have limited access
    const managerResult = permissionService.checkPermission(
      testUsers.schoolManager,
      ResourceType.USER,
      Action.DELETE
    );
    console.log(`✅ Manager DELETE USER: ${managerResult.allowed ? "ALLOWED" : "DENIED"}`);

    // Driver should have restricted access
    const driverResult = permissionService.checkPermission(
      testUsers.driver,
      ResourceType.BUS,
      Action.UPDATE
    );
    console.log(`✅ Driver UPDATE BUS: ${driverResult.allowed ? "ALLOWED" : "DENIED"}`);

    // 3. Test tenant isolation
    console.log("\n3. Testing tenant isolation...");
    
    const crossTenantResult = permissionService.checkPermission(
      testUsers.schoolManager,
      ResourceType.STUDENT,
      Action.READ,
      {
        userId: testUsers.schoolManager.id,
        tenantId: testUsers.schoolManager.tenant_id,
        resourceTenantId: "different-tenant",
      }
    );
    console.log(`✅ Cross-tenant access: ${crossTenantResult.allowed ? "ALLOWED (❌ SECURITY ISSUE)" : "DENIED (✅ SECURE)"}`);

    // 4. Test rate limiting
    console.log("\n4. Testing rate limiting...");
    
    let rateLimitHit = false;
    for (let i = 0; i < 105; i++) {
      const result = permissionService.checkPermission(
        testUsers.driver,
        ResourceType.BUS,
        Action.READ
      );
      if (!result.allowed && result.reason === "Rate limit exceeded") {
        rateLimitHit = true;
        break;
      }
    }
    console.log(`✅ Rate limiting: ${rateLimitHit ? "WORKING" : "NOT TRIGGERED"}`);

    // 5. Test role hierarchy
    console.log("\n5. Testing role hierarchy...");
    
    const adminCanManageManager = permissionService.canManageRole(UserRole.ADMIN, UserRole.SCHOOL_MANAGER);
    const managerCanManageAdmin = permissionService.canManageRole(UserRole.SCHOOL_MANAGER, UserRole.ADMIN);
    const driverCanManageParent = permissionService.canManageRole(UserRole.DRIVER, UserRole.PARENT);

    console.log(`✅ Admin can manage Manager: ${adminCanManageManager ? "YES" : "NO"}`);
    console.log(`✅ Manager can manage Admin: ${managerCanManageAdmin ? "YES (❌ SECURITY ISSUE)" : "NO (✅ SECURE)"}`);
    console.log(`✅ Driver can manage Parent: ${driverCanManageParent ? "YES (❌ SECURITY ISSUE)" : "NO (✅ SECURE)"}`);

    // 6. Test data scopes
    console.log("\n6. Testing data scopes...");
    
    const adminScopes = permissionService.getUserDataScopes(UserRole.ADMIN);
    const driverScopes = permissionService.getUserDataScopes(UserRole.DRIVER);
    const parentScopes = permissionService.getUserDataScopes(UserRole.PARENT);

    console.log(`✅ Admin scopes: ${adminScopes.join(", ")}`);
    console.log(`✅ Driver scopes: ${driverScopes.join(", ")}`);
    console.log(`✅ Parent scopes: ${parentScopes.join(", ")}`);

    // 7. Test data filtering
    console.log("\n7. Testing data filtering...");
    
    const mockData = [
      { id: "1", tenant_id: "test-tenant-1", name: "Item 1" },
      { id: "2", tenant_id: "test-tenant-2", name: "Item 2" },
      { id: "3", tenant_id: "test-tenant-1", name: "Item 3" },
    ];

    const adminFiltered = permissionService.filterDataByPermissions(
      testUsers.admin,
      mockData,
      ResourceType.STUDENT
    );
    const managerFiltered = permissionService.filterDataByPermissions(
      testUsers.schoolManager,
      mockData,
      ResourceType.STUDENT
    );

    console.log(`✅ Admin sees ${adminFiltered.length}/3 items (should be 3)`);
    console.log(`✅ Manager sees ${managerFiltered.length}/3 items (should be 2)`);

    // 8. Test security events
    console.log("\n8. Testing security events...");
    
    const securityEvents = permissionService.getSecurityEvents(10);
    console.log(`✅ Security events logged: ${securityEvents.length}`);

    // 9. Test middleware
    console.log("\n9. Testing middleware...");
    
    const middlewareResult = await authMiddleware.validateRequest({
      user: testUsers.driver,
      resource: ResourceType.USER,
      action: Action.DELETE,
      requestId: "test-request-1",
    });

    console.log(`✅ Middleware validation: ${middlewareResult.allowed ? "ALLOWED (❌ SHOULD BE DENIED)" : "DENIED (✅ CORRECT)"}`);

    // 10. Test security metrics
    console.log("\n10. Testing security metrics...");
    
    const metrics = securityAuditService.getSecurityMetrics();
    console.log(`✅ Total events: ${metrics.totalEvents}`);
    console.log(`✅ Permission denials: ${metrics.permissionDenials}`);
    console.log(`✅ Tenant violations: ${metrics.tenantViolations}`);
    console.log(`✅ Average risk score: ${metrics.averageRiskScore}`);

    // 11. Performance test
    console.log("\n11. Running performance test...");
    
    const startTime = performance.now();
    for (let i = 0; i < 1000; i++) {
      permissionService.checkPermission(
        testUsers.driver,
        ResourceType.BUS,
        Action.READ
      );
    }
    const endTime = performance.now();
    const avgTime = (endTime - startTime) / 1000;
    
    console.log(`✅ 1000 permission checks in ${avgTime.toFixed(2)}ms (avg: ${(avgTime / 1000).toFixed(3)}ms per check)`);

    // 12. Generate summary report
    console.log("\n" + "=".repeat(60));
    console.log("📊 PHASE 1 SETUP SUMMARY");
    console.log("=".repeat(60));
    
    const summary = {
      servicesInitialized: 3,
      permissionChecksWorking: true,
      tenantIsolationSecure: !crossTenantResult.allowed,
      rateLimitingActive: rateLimitHit,
      roleHierarchySecure: !managerCanManageAdmin && !driverCanManageParent,
      dataFilteringWorking: adminFiltered.length === 3 && managerFiltered.length === 2,
      securityEventsLogged: securityEvents.length > 0,
      middlewareWorking: !middlewareResult.allowed,
      performanceAcceptable: avgTime < 100, // Less than 100ms for 1000 checks
    };

    const allTestsPassed = Object.values(summary).every(value => 
      typeof value === 'boolean' ? value : true
    );

    console.log(`\n🔧 Services Initialized: ${summary.servicesInitialized}/3`);
    console.log(`🔐 Permission Checks: ${summary.permissionChecksWorking ? "✅ WORKING" : "❌ FAILED"}`);
    console.log(`🏢 Tenant Isolation: ${summary.tenantIsolationSecure ? "✅ SECURE" : "❌ VULNERABLE"}`);
    console.log(`⏱️  Rate Limiting: ${summary.rateLimitingActive ? "✅ ACTIVE" : "❌ INACTIVE"}`);
    console.log(`👥 Role Hierarchy: ${summary.roleHierarchySecure ? "✅ SECURE" : "❌ VULNERABLE"}`);
    console.log(`🔍 Data Filtering: ${summary.dataFilteringWorking ? "✅ WORKING" : "❌ FAILED"}`);
    console.log(`📝 Security Events: ${summary.securityEventsLogged ? "✅ LOGGED" : "❌ NOT LOGGED"}`);
    console.log(`🛡️  Middleware: ${summary.middlewareWorking ? "✅ WORKING" : "❌ FAILED"}`);
    console.log(`⚡ Performance: ${summary.performanceAcceptable ? "✅ ACCEPTABLE" : "❌ SLOW"}`);

    console.log(`\n🎯 Overall Status: ${allTestsPassed ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED"}`);
    
    if (allTestsPassed) {
      console.log("\n🎉 Phase 1 setup completed successfully!");
      console.log("🚀 The centralized permission system is ready for production use.");
      console.log("\n📋 Next Steps:");
      console.log("   1. Run the test suite: npm test permissionService.test.ts");
      console.log("   2. Integrate with existing components");
      console.log("   3. Monitor security dashboard");
      console.log("   4. Proceed to Phase 2: Database Security Enhancement");
    } else {
      console.log("\n⚠️  Some tests failed. Please review the implementation.");
    }

  } catch (error) {
    console.error("\n❌ Setup failed:", error);
    process.exit(1);
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  setupPhase1().catch(console.error);
}

export { setupPhase1 };
