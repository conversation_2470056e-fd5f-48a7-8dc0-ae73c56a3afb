import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Calendar,
  Clock,
  DollarSign,
  FileText,
  Wrench,
  Filter,
  Search,
  CheckCircle,
  AlertTriangle,
  Download,
  Eye,
} from "lucide-react";
import { Button } from "../ui/Button";
import { supabase } from "../../lib/supabase";
import { useAuth } from "../../contexts/AuthContext";
import type { Tables } from "../../lib/api";

interface BusMaintenanceHistoryProps {
  busId?: string;
  busPlateNumber?: string;
  className?: string;
}

interface MaintenanceRecord extends Tables<"bus_maintenance"> {
  bus?: Tables<"buses">;
}

interface MaintenanceStats {
  totalCost: number;
  totalRecords: number;
  avgCostPerRecord: number;
  upcomingMaintenance: number;
  overdueCount: number;
}

export const BusMaintenanceHistory: React.FC<BusMaintenanceHistoryProps> = ({
  busId,
  busPlateNumber,
  className = "",
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [records, setRecords] = useState<MaintenanceRecord[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<MaintenanceRecord[]>(
    [],
  );
  const [loading, setLoading] = useState(true);
  const [filterType, setFilterType] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [dateRange, setDateRange] = useState({
    start: "",
    end: "",
  });
  const [stats, setStats] = useState<MaintenanceStats | null>(null);
  const [selectedRecord, setSelectedRecord] =
    useState<MaintenanceRecord | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  useEffect(() => {
    fetchMaintenanceRecords();
  }, [busId, user?.tenant_id]);

  useEffect(() => {
    applyFilters();
  }, [records, filterType, filterStatus, searchQuery, dateRange]);

  const fetchMaintenanceRecords = async () => {
    try {
      setLoading(true);
      let query = supabase
        .from("bus_maintenance")
        .select(
          `
          *,
          bus:buses(id, plate_number)
        `,
        )
        .order("maintenance_date", { ascending: false });

      if (busId) {
        query = query.eq("bus_id", busId);
      } else if (user?.tenant_id) {
        query = query.eq("tenant_id", user.tenant_id);
      } else {
        setRecords([]);
        return;
      }

      const { data, error } = await query;

      if (error) {
        console.error("Error fetching maintenance records:", error);
        setRecords([]);
        return;
      }

      const maintenanceRecords = data || [];
      setRecords(maintenanceRecords);
      calculateStats(maintenanceRecords);
    } catch (error) {
      console.error("Error fetching maintenance records:", error);
      setRecords([]);
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (records: MaintenanceRecord[]) => {
    const totalCost = records.reduce(
      (sum, record) => sum + (record.cost || 0),
      0,
    );
    const totalRecords = records.length;
    const avgCostPerRecord = totalRecords > 0 ? totalCost / totalRecords : 0;

    const now = new Date();
    const upcomingMaintenance = records.filter(
      (record) =>
        record.status === "scheduled" &&
        new Date(record.maintenance_date) >= now,
    ).length;

    const overdueCount = records.filter(
      (record) =>
        record.status === "scheduled" &&
        new Date(record.maintenance_date) < now,
    ).length;

    setStats({
      totalCost,
      totalRecords,
      avgCostPerRecord,
      upcomingMaintenance,
      overdueCount,
    });
  };

  const applyFilters = () => {
    let filtered = [...records];

    // Filter by type
    if (filterType !== "all") {
      filtered = filtered.filter((record) => record.type === filterType);
    }

    // Filter by status
    if (filterStatus !== "all") {
      if (filterStatus === "overdue") {
        filtered = filtered.filter(
          (record) =>
            record.status === "scheduled" &&
            new Date(record.maintenance_date) < new Date(),
        );
      } else {
        filtered = filtered.filter((record) => record.status === filterStatus);
      }
    }

    // Filter by search query
    if (searchQuery) {
      const searchLower = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (record) =>
          record.description.toLowerCase().includes(searchLower) ||
          record.bus?.plate_number?.toLowerCase().includes(searchLower) ||
          record.notes?.toLowerCase().includes(searchLower),
      );
    }

    // Filter by date range
    if (dateRange.start) {
      filtered = filtered.filter(
        (record) =>
          new Date(record.maintenance_date) >= new Date(dateRange.start),
      );
    }
    if (dateRange.end) {
      filtered = filtered.filter(
        (record) =>
          new Date(record.maintenance_date) <= new Date(dateRange.end),
      );
    }

    setFilteredRecords(filtered);
  };

  const getStatusColor = (record: MaintenanceRecord) => {
    if (record.status === "completed") {
      return "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20";
    }
    if (record.status === "in_progress") {
      return "text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20";
    }
    if (
      record.status === "scheduled" &&
      new Date(record.maintenance_date) < new Date()
    ) {
      return "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20";
    }
    return "text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20";
  };

  const getStatusIcon = (record: MaintenanceRecord) => {
    if (record.status === "completed") {
      return <CheckCircle size={16} />;
    }
    if (record.status === "in_progress") {
      return <Clock size={16} />;
    }
    if (
      record.status === "scheduled" &&
      new Date(record.maintenance_date) < new Date()
    ) {
      return <AlertTriangle size={16} />;
    }
    return <Calendar size={16} />;
  };

  const getStatusText = (record: MaintenanceRecord) => {
    if (
      record.status === "scheduled" &&
      new Date(record.maintenance_date) < new Date()
    ) {
      return "Overdue";
    }
    return (
      record.status.charAt(0).toUpperCase() +
      record.status.slice(1).replace("_", " ")
    );
  };

  const exportToCSV = () => {
    if (filteredRecords.length === 0) return;

    const headers = [
      "Date",
      "Bus",
      "Type",
      "Description",
      "Status",
      "Cost",
      "Notes",
    ];

    const csvContent = [
      headers.join(","),
      ...filteredRecords.map((record) =>
        [
          new Date(record.maintenance_date).toLocaleDateString(),
          record.bus?.plate_number || "Unknown",
          record.type,
          `"${record.description}"`,
          getStatusText(record),
          record.cost?.toFixed(2) || "0.00",
          `"${record.notes || ""}"`,
        ].join(","),
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `maintenance-history-${busPlateNumber || "all-buses"}-${new Date().toISOString().split("T")[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {busId
              ? `Maintenance History - ${busPlateNumber}`
              : "Bus Maintenance History"}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Complete timeline of maintenance activities and costs
          </p>
        </div>
        <Button
          onClick={exportToCSV}
          disabled={filteredRecords.length === 0}
          variant="outline"
          leftIcon={<Download size={16} />}
        >
          Export CSV
        </Button>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <DollarSign className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Total Cost
                </p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  ${stats.totalCost.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <FileText className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Total Records
                </p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {stats.totalRecords}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
                <Calendar className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Upcoming
                </p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {stats.upcomingMaintenance}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
                <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Overdue
                </p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {stats.overdueCount}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={16} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search maintenance records..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>

          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          >
            <option value="all">All Types</option>
            <option value="routine">Routine</option>
            <option value="repair">Repair</option>
            <option value="inspection">Inspection</option>
            <option value="emergency">Emergency</option>
          </select>

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          >
            <option value="all">All Status</option>
            <option value="scheduled">Scheduled</option>
            <option value="in_progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="overdue">Overdue</option>
          </select>

          <input
            type="date"
            value={dateRange.start}
            onChange={(e) =>
              setDateRange({ ...dateRange, start: e.target.value })
            }
            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            placeholder="Start Date"
          />

          <input
            type="date"
            value={dateRange.end}
            onChange={(e) =>
              setDateRange({ ...dateRange, end: e.target.value })
            }
            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            placeholder="End Date"
          />
        </div>
      </div>

      {/* Timeline View */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Maintenance Timeline ({filteredRecords.length} records)
          </h3>
        </div>

        {filteredRecords.length === 0 ? (
          <div className="p-8 text-center text-gray-500 dark:text-gray-400">
            <Wrench size={48} className="mx-auto mb-4 opacity-50" />
            <p>No maintenance records found matching your criteria.</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700 max-h-96 overflow-y-auto">
            {filteredRecords.map((record, index) => (
              <div
                key={record.id}
                className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="relative">
                      <div className="p-2 bg-gray-100 dark:bg-gray-600 rounded-lg">
                        <Wrench size={16} />
                      </div>
                      {index < filteredRecords.length - 1 && (
                        <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-0.5 h-8 bg-gray-300 dark:bg-gray-600"></div>
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {record.type.charAt(0).toUpperCase() +
                            record.type.slice(1)}{" "}
                          Maintenance
                        </h4>
                        <span
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record)}`}
                        >
                          {getStatusIcon(record)}
                          <span className="ml-1">{getStatusText(record)}</span>
                        </span>
                      </div>
                      <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        {record.description}
                      </p>
                      {!busId && record.bus && (
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                          Bus: {record.bus.plate_number}
                        </p>
                      )}
                      {record.notes && (
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                          Notes: {record.notes}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                      <Calendar size={14} />
                      <span>
                        {new Date(record.maintenance_date).toLocaleDateString()}
                      </span>
                    </div>
                    {record.cost && (
                      <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mt-1">
                        <DollarSign size={14} />
                        <span>${record.cost.toFixed(2)}</span>
                      </div>
                    )}
                    <Button
                      onClick={() => {
                        setSelectedRecord(record);
                        setShowDetailModal(true);
                      }}
                      variant="outline"
                      size="sm"
                      className="mt-2"
                    >
                      <Eye size={14} className="mr-1" />
                      Details
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Detail Modal */}
      {showDetailModal && selectedRecord && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Maintenance Record Details
                </h3>
                <Button
                  onClick={() => setShowDetailModal(false)}
                  variant="outline"
                  size="sm"
                >
                  Close
                </Button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Type
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {selectedRecord.type.charAt(0).toUpperCase() +
                        selectedRecord.type.slice(1)}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Status
                    </label>
                    <span
                      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedRecord)}`}
                    >
                      {getStatusIcon(selectedRecord)}
                      <span className="ml-1">
                        {getStatusText(selectedRecord)}
                      </span>
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Description
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {selectedRecord.description}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Maintenance Date
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {new Date(
                        selectedRecord.maintenance_date,
                      ).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Cost
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      ${selectedRecord.cost?.toFixed(2) || "0.00"}
                    </p>
                  </div>
                </div>

                {selectedRecord.next_maintenance_date && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Next Maintenance Date
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {new Date(
                        selectedRecord.next_maintenance_date,
                      ).toLocaleDateString()}
                    </p>
                  </div>
                )}

                {selectedRecord.notes && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Notes
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {selectedRecord.notes}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BusMaintenanceHistory;
