/**
 * Color Design Tokens
 * Centralized color system for consistent theming
 * Phase 3: UI/UX Enhancement
 */

export interface ColorToken {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
  950: string;
}

export interface ColorPalette {
  primary: ColorToken;
  secondary: ColorToken;
  success: ColorToken;
  warning: ColorToken;
  error: ColorToken;
  info: ColorToken;
  neutral: ColorToken;
}

export interface SemanticColors {
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
    inverse: string;
    overlay: string;
  };
  surface: {
    primary: string;
    secondary: string;
    tertiary: string;
    inverse: string;
    elevated: string;
  };
  border: {
    primary: string;
    secondary: string;
    tertiary: string;
    inverse: string;
    focus: string;
    error: string;
    success: string;
    warning: string;
  };
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
    inverse: string;
    disabled: string;
    link: string;
    error: string;
    success: string;
    warning: string;
  };
  interactive: {
    primary: string;
    primaryHover: string;
    primaryActive: string;
    primaryDisabled: string;
    secondary: string;
    secondaryHover: string;
    secondaryActive: string;
    secondaryDisabled: string;
  };
}

/**
 * Base Color Palette
 */
export const baseColors: ColorPalette = {
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
    950: '#172554',
  },
  secondary: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
    950: '#020617',
  },
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    950: '#052e16',
  },
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03',
  },
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
    950: '#450a0a',
  },
  info: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
    950: '#082f49',
  },
  neutral: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
    950: '#0a0a0a',
  },
};

/**
 * Light Theme Semantic Colors
 */
export const lightSemanticColors: SemanticColors = {
  background: {
    primary: baseColors.neutral[50],
    secondary: baseColors.neutral[100],
    tertiary: baseColors.neutral[200],
    inverse: baseColors.neutral[900],
    overlay: 'rgba(0, 0, 0, 0.5)',
  },
  surface: {
    primary: '#ffffff',
    secondary: baseColors.neutral[50],
    tertiary: baseColors.neutral[100],
    inverse: baseColors.neutral[900],
    elevated: '#ffffff',
  },
  border: {
    primary: baseColors.neutral[200],
    secondary: baseColors.neutral[300],
    tertiary: baseColors.neutral[400],
    inverse: baseColors.neutral[700],
    focus: baseColors.primary[500],
    error: baseColors.error[500],
    success: baseColors.success[500],
    warning: baseColors.warning[500],
  },
  text: {
    primary: baseColors.neutral[900],
    secondary: baseColors.neutral[700],
    tertiary: baseColors.neutral[500],
    inverse: baseColors.neutral[50],
    disabled: baseColors.neutral[400],
    link: baseColors.primary[600],
    error: baseColors.error[600],
    success: baseColors.success[600],
    warning: baseColors.warning[600],
  },
  interactive: {
    primary: baseColors.primary[600],
    primaryHover: baseColors.primary[700],
    primaryActive: baseColors.primary[800],
    primaryDisabled: baseColors.neutral[300],
    secondary: baseColors.neutral[200],
    secondaryHover: baseColors.neutral[300],
    secondaryActive: baseColors.neutral[400],
    secondaryDisabled: baseColors.neutral[100],
  },
};

/**
 * Dark Theme Semantic Colors
 */
export const darkSemanticColors: SemanticColors = {
  background: {
    primary: baseColors.neutral[950],
    secondary: baseColors.neutral[900],
    tertiary: baseColors.neutral[800],
    inverse: baseColors.neutral[50],
    overlay: 'rgba(0, 0, 0, 0.7)',
  },
  surface: {
    primary: baseColors.neutral[900],
    secondary: baseColors.neutral[800],
    tertiary: baseColors.neutral[700],
    inverse: baseColors.neutral[50],
    elevated: baseColors.neutral[800],
  },
  border: {
    primary: baseColors.neutral[700],
    secondary: baseColors.neutral[600],
    tertiary: baseColors.neutral[500],
    inverse: baseColors.neutral[300],
    focus: baseColors.primary[400],
    error: baseColors.error[400],
    success: baseColors.success[400],
    warning: baseColors.warning[400],
  },
  text: {
    primary: baseColors.neutral[50],
    secondary: baseColors.neutral[300],
    tertiary: baseColors.neutral[400],
    inverse: baseColors.neutral[900],
    disabled: baseColors.neutral[600],
    link: baseColors.primary[400],
    error: baseColors.error[400],
    success: baseColors.success[400],
    warning: baseColors.warning[400],
  },
  interactive: {
    primary: baseColors.primary[500],
    primaryHover: baseColors.primary[400],
    primaryActive: baseColors.primary[300],
    primaryDisabled: baseColors.neutral[700],
    secondary: baseColors.neutral[700],
    secondaryHover: baseColors.neutral[600],
    secondaryActive: baseColors.neutral[500],
    secondaryDisabled: baseColors.neutral[800],
  },
};

/**
 * Color utilities
 */
export const colorUtils = {
  /**
   * Convert hex to RGB
   */
  hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  },

  /**
   * Convert RGB to hex
   */
  rgbToHex(r: number, g: number, b: number): string {
    return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
  },

  /**
   * Add alpha to color
   */
  addAlpha(color: string, alpha: number): string {
    const rgb = this.hexToRgb(color);
    if (!rgb) return color;
    return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${alpha})`;
  },

  /**
   * Lighten color
   */
  lighten(color: string, amount: number): string {
    const rgb = this.hexToRgb(color);
    if (!rgb) return color;
    
    const r = Math.min(255, Math.floor(rgb.r + (255 - rgb.r) * amount));
    const g = Math.min(255, Math.floor(rgb.g + (255 - rgb.g) * amount));
    const b = Math.min(255, Math.floor(rgb.b + (255 - rgb.b) * amount));
    
    return this.rgbToHex(r, g, b);
  },

  /**
   * Darken color
   */
  darken(color: string, amount: number): string {
    const rgb = this.hexToRgb(color);
    if (!rgb) return color;
    
    const r = Math.max(0, Math.floor(rgb.r * (1 - amount)));
    const g = Math.max(0, Math.floor(rgb.g * (1 - amount)));
    const b = Math.max(0, Math.floor(rgb.b * (1 - amount)));
    
    return this.rgbToHex(r, g, b);
  },

  /**
   * Get contrast ratio between two colors
   */
  getContrastRatio(color1: string, color2: string): number {
    const getLuminance = (color: string): number => {
      const rgb = this.hexToRgb(color);
      if (!rgb) return 0;
      
      const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      });
      
      return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    };

    const lum1 = getLuminance(color1);
    const lum2 = getLuminance(color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    
    return (brightest + 0.05) / (darkest + 0.05);
  },

  /**
   * Check if color meets WCAG contrast requirements
   */
  meetsContrastRequirement(
    foreground: string,
    background: string,
    level: 'AA' | 'AAA' = 'AA'
  ): boolean {
    const ratio = this.getContrastRatio(foreground, background);
    return level === 'AA' ? ratio >= 4.5 : ratio >= 7;
  },
};
