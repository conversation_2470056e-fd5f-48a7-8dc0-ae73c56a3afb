/**
 * Apply Migration Script - سكريبت تطبيق الهجرة
 * يطبق ملف الهجرة الشامل على قاعدة البيانات
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';

// إعداد Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigration() {
  console.log('🚀 بدء تطبيق الهجرة الشاملة...');
  
  try {
    // قراءة ملف الهجرة
    const migrationPath = join(process.cwd(), 'supabase', 'migrations', '20250129000000_comprehensive_fixes.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');
    
    console.log('📄 تم قراءة ملف الهجرة بنجاح');
    
    // تقسيم الـ SQL إلى statements منفصلة
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📊 عدد العمليات المطلوب تنفيذها: ${statements.length}`);
    
    let successCount = 0;
    let errorCount = 0;
    const errors: string[] = [];
    
    // تنفيذ كل statement على حدة
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';';
      
      try {
        console.log(`⏳ تنفيذ العملية ${i + 1}/${statements.length}...`);
        
        // تنفيذ الـ statement
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          // محاولة تنفيذ مباشر إذا فشل RPC
          const { error: directError } = await supabase
            .from('_temp_migration')
            .select('*')
            .limit(1);
            
          if (directError && directError.message.includes('does not exist')) {
            // إنشاء جدول مؤقت لتنفيذ SQL
            await createTempExecutionFunction(statement);
          }
        }
        
        successCount++;
        console.log(`✅ تم تنفيذ العملية ${i + 1} بنجاح`);
        
      } catch (error) {
        errorCount++;
        const errorMsg = `❌ فشل في العملية ${i + 1}: ${error}`;
        console.error(errorMsg);
        errors.push(errorMsg);
        
        // لا نتوقف عند الأخطاء، نحاول باقي العمليات
        continue;
      }
    }
    
    // تقرير النتائج
    console.log('\n📋 تقرير تطبيق الهجرة:');
    console.log(`✅ العمليات الناجحة: ${successCount}`);
    console.log(`❌ العمليات الفاشلة: ${errorCount}`);
    
    if (errors.length > 0) {
      console.log('\n🚨 الأخطاء التي حدثت:');
      errors.forEach(error => console.log(error));
    }
    
    // التحقق من نجاح الهجرة
    await verifyMigration();
    
    console.log('\n🎉 تم الانتهاء من تطبيق الهجرة!');
    
  } catch (error) {
    console.error('💥 خطأ في تطبيق الهجرة:', error);
    process.exit(1);
  }
}

/**
 * إنشاء دالة مؤقتة لتنفيذ SQL
 */
async function createTempExecutionFunction(sql: string) {
  try {
    // محاولة تنفيذ SQL مباشرة عبر HTTP API
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey
      },
      body: JSON.stringify({ sql })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
  } catch (error) {
    console.warn('⚠️ تعذر تنفيذ SQL مباشرة:', error);
  }
}

/**
 * التحقق من نجاح الهجرة
 */
async function verifyMigration() {
  console.log('\n🔍 التحقق من نجاح الهجرة...');
  
  const checks = [
    {
      name: 'جدول maintenance_history',
      query: "SELECT table_name FROM information_schema.tables WHERE table_name = 'maintenance_history'"
    },
    {
      name: 'جدول notification_analytics',
      query: "SELECT table_name FROM information_schema.tables WHERE table_name = 'notification_analytics'"
    },
    {
      name: 'دالة check_unique_student_id',
      query: "SELECT routine_name FROM information_schema.routines WHERE routine_name = 'check_unique_student_id'"
    },
    {
      name: 'فهرس idx_students_tenant_grade',
      query: "SELECT indexname FROM pg_indexes WHERE indexname = 'idx_students_tenant_grade'"
    }
  ];
  
  for (const check of checks) {
    try {
      const { data, error } = await supabase.rpc('exec_sql', { sql: check.query });
      
      if (error) {
        console.log(`⚠️ تعذر التحقق من ${check.name}: ${error.message}`);
      } else if (data && data.length > 0) {
        console.log(`✅ ${check.name}: موجود`);
      } else {
        console.log(`❌ ${check.name}: غير موجود`);
      }
      
    } catch (error) {
      console.log(`⚠️ خطأ في التحقق من ${check.name}: ${error}`);
    }
  }
}

/**
 * تطبيق الهجرة بطريقة مبسطة
 */
async function applyMigrationSimple() {
  console.log('🔄 تطبيق الهجرة بالطريقة المبسطة...');
  
  try {
    // قراءة ملف الهجرة
    const migrationPath = join(process.cwd(), 'supabase', 'migrations', '20250129000000_comprehensive_fixes.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');
    
    // تطبيق الهجرة كاملة
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });
    
    if (error) {
      console.error('❌ فشل في تطبيق الهجرة:', error);
      
      // محاولة تطبيق أجزاء مهمة فقط
      await applyEssentialParts();
    } else {
      console.log('✅ تم تطبيق الهجرة بنجاح!');
    }
    
  } catch (error) {
    console.error('💥 خطأ في تطبيق الهجرة المبسطة:', error);
    await applyEssentialParts();
  }
}

/**
 * تطبيق الأجزاء الأساسية فقط
 */
async function applyEssentialParts() {
  console.log('🔧 تطبيق الأجزاء الأساسية...');
  
  const essentialQueries = [
    // إضافة الأعمدة المفقودة
    "ALTER TABLE buses ADD COLUMN IF NOT EXISTS notes TEXT;",
    
    // إنشاء الجداول المهمة
    `CREATE TABLE IF NOT EXISTS maintenance_history (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      bus_id UUID REFERENCES buses(id) ON DELETE CASCADE,
      maintenance_date DATE NOT NULL,
      description TEXT,
      cost DECIMAL(10,2),
      created_at TIMESTAMP DEFAULT NOW(),
      tenant_id UUID REFERENCES tenants(id) NOT NULL
    );`,
    
    // إضافة فهارس أساسية
    "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_students_tenant_grade ON students(tenant_id, grade) WHERE is_active = true;",
    
    // إضافة قيود أساسية
    "ALTER TABLE students ADD CONSTRAINT IF NOT EXISTS unique_student_id_per_tenant UNIQUE (student_id, tenant_id);"
  ];
  
  for (const query of essentialQueries) {
    try {
      await supabase.rpc('exec_sql', { sql: query });
      console.log('✅ تم تطبيق جزء أساسي');
    } catch (error) {
      console.log('⚠️ تعذر تطبيق جزء:', error);
    }
  }
}

// تشغيل السكريبت
if (require.main === module) {
  applyMigration().catch(console.error);
}

export { applyMigration, applyMigrationSimple };
