import React from 'react';
import { useTranslation } from 'react-i18next';

export const AttendanceChart: React.FC = () => {
  const { t } = useTranslation();
  
  // Example attendance data
  const attendance = {
    present: 95,
    absent: 5,
  };
  
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
        {t('dashboard.todayAttendance')}
      </h2>
      
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500 dark:text-gray-400">{t('students.present')}</span>
          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-900 dark:text-white">{attendance.present}</span>
            <span className="text-xs text-accent-600 dark:text-accent-400 ml-2">({attendance.present}%)</span>
          </div>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div 
            className="bg-accent-500 h-2 rounded-full" 
            style={{ width: `${attendance.present}%` }}
          />
        </div>
        
        <div className="flex items-center justify-between mt-2">
          <span className="text-sm text-gray-500 dark:text-gray-400">{t('students.absent')}</span>
          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-900 dark:text-white">{attendance.absent}</span>
            <span className="text-xs text-error-600 dark:text-error-400 ml-2">({attendance.absent}%)</span>
          </div>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div 
            className="bg-error-500 h-2 rounded-full" 
            style={{ width: `${attendance.absent}%` }}
          />
        </div>
      </div>
    </div>
  );
};