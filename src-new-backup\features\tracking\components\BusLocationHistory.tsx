import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  MapPin,
  Calendar,
  Clock,
  Download,
  Filter,
  RefreshCw,
  FileText,
  Map,
} from "lucide-react";
import { Button } from "../ui/Button";
import { supabase } from "../../lib/supabase";
import * as api from "../../lib/api";
import type { Tables } from "../../lib/api";

interface BusLocationHistoryProps {
  busId: string;
  className?: string;
}

interface LocationHistoryRecord extends Tables<"bus_location_history"> {
  bus?: {
    id: string;
    plate_number: string;
  };
}

export const BusLocationHistory: React.FC<BusLocationHistoryProps> = ({
  busId,
  className = "",
}) => {
  const { t } = useTranslation();
  const [history, setHistory] = useState<LocationHistoryRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [startDate, setStartDate] = useState(
    new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0],
  );
  const [endDate, setEndDate] = useState(
    new Date().toISOString().split("T")[0],
  );

  const fetchHistory = async () => {
    try {
      setLoading(true);
      const data = await api.getBusLocationHistory(busId, startDate, endDate);
      setHistory(data || []);
    } catch (error) {
      console.error("Error fetching bus location history:", error);
    } finally {
      setLoading(false);
    }
  };

  const refreshHistory = async () => {
    await fetchHistory();
  };

  useEffect(() => {
    fetchHistory();
  }, [busId, startDate, endDate]);

  const exportHistory = (format: "csv" | "json" | "kml" = "csv") => {
    if (history.length === 0) return;

    const processedData = history.map((record) => {
      const date = new Date(record.timestamp);
      // Parse location from PostGIS format
      const locationMatch = record.location
        ?.toString()
        .match(/POINT\(([^)]+)\)/);
      const [lng, lat] = locationMatch
        ? locationMatch[1].split(" ").map(Number)
        : [0, 0];

      return {
        date: date.toLocaleDateString(),
        time: date.toLocaleTimeString(),
        timestamp: record.timestamp,
        latitude: lat,
        longitude: lng,
        busPlateNumber: record.bus?.plate_number || "Unknown",
        busId: record.bus_id,
        metadata: record.metadata,
      };
    });

    let content: string;
    let mimeType: string;
    let fileExtension: string;

    switch (format) {
      case "json":
        content = JSON.stringify(
          {
            exportDate: new Date().toISOString(),
            busId,
            dateRange: { startDate, endDate },
            totalRecords: processedData.length,
            locationHistory: processedData,
          },
          null,
          2,
        );
        mimeType = "application/json";
        fileExtension = "json";
        break;

      case "kml":
        const kmlPlacemarks = processedData
          .map(
            (record, index) => `
    <Placemark>
      <name>Location ${index + 1}</name>
      <description>
        <![CDATA[
          <b>Bus:</b> ${record.busPlateNumber}<br/>
          <b>Date:</b> ${record.date}<br/>
          <b>Time:</b> ${record.time}<br/>
          <b>Coordinates:</b> ${record.latitude}, ${record.longitude}
        ]]>
      </description>
      <TimeStamp>
        <when>${record.timestamp}</when>
      </TimeStamp>
      <Point>
        <coordinates>${record.longitude},${record.latitude},0</coordinates>
      </Point>
    </Placemark>`,
          )
          .join("");

        content = `<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <name>Bus Location History - ${processedData[0]?.busPlateNumber || busId}</name>
    <description>Location history from ${startDate} to ${endDate}</description>
    <Style id="busLocationStyle">
      <IconStyle>
        <Icon>
          <href>http://maps.google.com/mapfiles/kml/shapes/bus.png</href>
        </Icon>
      </IconStyle>
    </Style>${kmlPlacemarks}
  </Document>
</kml>`;
        mimeType = "application/vnd.google-earth.kml+xml";
        fileExtension = "kml";
        break;

      default: // csv
        content = [
          ["Date", "Time", "Latitude", "Longitude", "Bus Plate Number"],
          ...processedData.map((record) => [
            record.date,
            record.time,
            record.latitude.toString(),
            record.longitude.toString(),
            record.busPlateNumber,
          ]),
        ]
          .map((row) => row.join(","))
          .join("\n");
        mimeType = "text/csv";
        fileExtension = "csv";
    }

    const blob = new Blob([content], { type: mimeType });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `bus-${busId}-location-history-${startDate}-to-${endDate}.${fileExtension}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}
    >
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <MapPin className="mr-2 h-5 w-5 text-primary-500" />
            Bus Location History
          </h3>
          <div className="flex items-center gap-2">
            <Button
              onClick={refreshHistory}
              disabled={loading}
              className="flex items-center gap-2"
              variant="outline"
              size="sm"
            >
              <RefreshCw size={16} className={loading ? "animate-spin" : ""} />
              Refresh
            </Button>
            <div className="flex items-center gap-2">
              <Button
                onClick={() => exportHistory("csv")}
                disabled={history.length === 0}
                className="flex items-center gap-2"
                variant="outline"
                size="sm"
              >
                <FileText size={14} />
                CSV
              </Button>
              <Button
                onClick={() => exportHistory("json")}
                disabled={history.length === 0}
                className="flex items-center gap-2"
                variant="outline"
                size="sm"
              >
                <Download size={14} />
                JSON
              </Button>
              <Button
                onClick={() => exportHistory("kml")}
                disabled={history.length === 0}
                className="flex items-center gap-2"
                variant="outline"
                size="sm"
              >
                <Map size={14} />
                KML
              </Button>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Calendar size={16} className="text-gray-500" />
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              From:
            </label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              To:
            </label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white"
            />
          </div>
          <Button
            onClick={fetchHistory}
            className="flex items-center gap-2"
            size="sm"
          >
            <Filter size={16} />
            Filter
          </Button>
        </div>
      </div>

      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
        ) : history.length === 0 ? (
          <div className="text-center py-8">
            <MapPin size={48} className="mx-auto mb-4 text-gray-300" />
            <p className="text-gray-500 dark:text-gray-400">
              No location history found for the selected date range.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Showing {history.length} location records
            </div>
            <div className="max-h-96 overflow-y-auto">
              <div className="space-y-2">
                {history.map((record, index) => {
                  const date = new Date(record.timestamp);
                  // Parse location from PostGIS format
                  const locationMatch = record.location
                    ?.toString()
                    .match(/POINT\(([^)]+)\)/);
                  const [lng, lat] = locationMatch
                    ? locationMatch[1].split(" ").map(Number)
                    : [0, 0];

                  return (
                    <div
                      key={record.id}
                      className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {date.toLocaleDateString()} at{" "}
                            {date.toLocaleTimeString()}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Lat: {lat.toFixed(6)}, Lng: {lng.toFixed(6)}
                          </div>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        #{index + 1}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BusLocationHistory;
