# School Bus Management API Documentation

## Overview

This document provides comprehensive documentation for the School Bus Management System API. The API follows RESTful principles and implements role-based access control (RBAC) for security.

## Base URL

```
Development: http://localhost:3000/api
Production: https://api.schoolbus.com
```

## Authentication

All API endpoints (except authentication endpoints) require a valid JWT token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

### Token Refresh

Tokens expire after 24 hours. Use the refresh token to obtain a new access token:

```http
POST /auth/refresh
Content-Type: application/json

{
  "refreshToken": "your_refresh_token"
}
```

## Error Handling

The API uses standard HTTP status codes and returns errors in the following format:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      "field": "Additional error details"
    }
  }
}
```

### Common Error Codes

| Status Code | Description |
|-------------|-------------|
| 400 | Bad Request - Invalid input data |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 422 | Unprocessable Entity - Validation errors |
| 500 | Internal Server Error - Server error |

## Rate Limiting

API requests are rate limited to prevent abuse:

- **Authenticated users**: 1000 requests per hour
- **Unauthenticated users**: 100 requests per hour

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## Pagination

List endpoints support pagination using query parameters:

```
GET /users?page=1&limit=20&sort=name&order=asc
```

Response format:

```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Filtering and Sorting

Most list endpoints support filtering and sorting:

### Filtering
```
GET /users?role=driver&is_active=true&tenant_id=school_1
```

### Sorting
```
GET /users?sort=created_at&order=desc
```

### Search
```
GET /users?search=john&search_fields=name,email
```

## Role-Based Access Control

The API implements comprehensive RBAC with the following roles:

### Role Hierarchy

```
Admin
├── School Manager
│   ├── Supervisor
│   │   ├── Driver
│   │   └── Parent
│   └── Student
```

### Permission Matrix

| Resource | Admin | School Manager | Supervisor | Driver | Parent | Student |
|----------|-------|----------------|------------|--------|--------|---------|
| Users | CRUD | CRU* | R* | R* | R* | R* |
| Schools | CRUD | RU* | R* | R* | R* | R* |
| Buses | CRUD | CRU* | R* | RU** | R* | R* |
| Routes | CRUD | CRU* | R* | R** | R* | R* |
| Students | CRUD | CRU* | RU* | R* | R*** | R* |
| Attendance | CRUD | CRU* | CRU* | CRU** | R*** | R* |

**Legend:**
- C: Create, R: Read, U: Update, D: Delete
- \*: Tenant-scoped access only
- \*\*: Assigned resources only
- \*\*\*: Own children/data only

## Data Scopes

Different roles have different data access scopes:

- **Global**: Access to all data across all tenants (Admin only)
- **Tenant**: Access to data within their school/organization
- **Assigned**: Access to specifically assigned resources
- **Personal**: Access to their own data only
- **Children**: Access to their children's data (Parents only)

## Webhook Events

The system supports webhooks for real-time notifications:

### Supported Events

- `user.created`
- `user.updated`
- `user.deleted`
- `bus.location_updated`
- `attendance.recorded`
- `route.started`
- `route.completed`
- `emergency.triggered`

### Webhook Payload

```json
{
  "event": "bus.location_updated",
  "timestamp": "2023-12-01T10:30:00Z",
  "data": {
    "bus_id": "bus_123",
    "location": {
      "latitude": 40.7128,
      "longitude": -74.0060
    },
    "speed": 45,
    "heading": 180
  },
  "tenant_id": "school_1"
}
```

## API Versioning

The API uses URL versioning:

```
/api/v1/users
/api/v2/users
```

Current version: **v1**

## SDK and Libraries

Official SDKs are available for:

- **JavaScript/TypeScript**: `@schoolbus/api-client`
- **Python**: `schoolbus-api-python`
- **PHP**: `schoolbus/api-php`

### JavaScript Example

```javascript
import { SchoolBusAPI } from '@schoolbus/api-client';

const api = new SchoolBusAPI({
  baseURL: 'https://api.schoolbus.com',
  token: 'your_jwt_token'
});

// Get users
const users = await api.users.list({
  page: 1,
  limit: 10,
  role: 'driver'
});

// Create user
const newUser = await api.users.create({
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'driver'
});
```

## Testing

### Postman Collection

Download our Postman collection for easy API testing:
[Download Collection](./postman/schoolbus-api.postman_collection.json)

### Test Environment

Use our test environment for development:

```
Base URL: https://api-test.schoolbus.com
Test Token: test_jwt_token_here
```

## Support

For API support and questions:

- **Documentation**: [https://docs.schoolbus.com](https://docs.schoolbus.com)
- **Support Email**: <EMAIL>
- **GitHub Issues**: [https://github.com/schoolbus/api/issues](https://github.com/schoolbus/api/issues)

## Changelog

### v1.2.0 (2023-12-01)
- Added webhook support
- Improved error handling
- Enhanced rate limiting

### v1.1.0 (2023-11-01)
- Added real-time bus tracking
- Improved permission system
- Added bulk operations

### v1.0.0 (2023-10-01)
- Initial API release
- Basic CRUD operations
- Role-based access control
