import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Filter } from "lucide-react";
import { useNotifications } from "../../contexts/NotificationsContext";
import { NotificationActions } from "./NotificationActions";
import { NotificationFilter } from "./NotificationFilter";
import { Button } from "../ui/Button";
import type { Tables } from "../../lib/api";

interface NotificationsListProps {
  onClose?: () => void;
  className?: string;
  showFilters?: boolean;
}

export const NotificationsList: React.FC<NotificationsListProps> = ({
  onClose,
  className = "",
  showFilters = true,
}) => {
  const { t } = useTranslation();
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    loading,
    getNotificationsByCategory,
    getNotificationCategories,
  } = useNotifications();
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedPriority, setSelectedPriority] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [filtersVisible, setFiltersVisible] = useState(false);

  const getNotificationIcon = (notification: Tables<"notifications">) => {
    const metadata = notification.metadata as any;
    switch (metadata?.type) {
      case "geofence":
        return "🚌";
      case "attendance":
        return "👥";
      case "maintenance":
        return "🔧";
      case "announcements":
        return "📢";
      default:
        return "ℹ️";
    }
  };

  const getNotificationColor = (notification: Tables<"notifications">) => {
    const metadata = notification.metadata as any;
    switch (metadata?.type) {
      case "geofence":
        return "text-blue-600 dark:text-blue-400";
      case "attendance":
        return "text-green-600 dark:text-green-400";
      case "maintenance":
        return "text-orange-600 dark:text-orange-400";
      case "announcements":
        return "text-purple-600 dark:text-purple-400";
      default:
        return "text-gray-600 dark:text-gray-400";
    }
  };

  const handleNotificationClick = (notification: Tables<"notifications">) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }
  };

  const filteredNotifications = React.useMemo(() => {
    return notifications.filter((notification) => {
      // Status filter
      if (selectedStatus === "unread" && notification.read) return false;
      if (selectedStatus === "read" && !notification.read) return false;

      // Category filter
      if (selectedCategory !== "all") {
        const metadata = notification.metadata as any;
        if (metadata?.type !== selectedCategory) return false;
      }

      // Priority filter
      if (selectedPriority !== "all") {
        const metadata = notification.metadata as any;
        if (metadata?.priority !== selectedPriority) return false;
      }

      return true;
    });
  }, [notifications, selectedStatus, selectedCategory, selectedPriority]);

  const clearAllFilters = () => {
    setSelectedCategory("all");
    setSelectedPriority("all");
    setSelectedStatus("all");
  };

  if (loading) {
    return (
      <div className="p-4 text-center">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500 mx-auto" />
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
          {t("common.loading")}
        </p>
      </div>
    );
  }

  if (notifications.length === 0) {
    return (
      <div className="p-8 text-center text-gray-500 dark:text-gray-400">
        <Bell className="mx-auto h-12 w-12 mb-3 opacity-50" />
        <p className="text-sm">{t("notifications.empty")}</p>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Header with Mark All as Read and Filters */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between p-3">
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => markAllAsRead()}
              className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
            >
              <CheckCheck size={16} className="mr-2" />
              {t("notifications.markAllAsRead")}
            </Button>
          )}
          {showFilters && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setFiltersVisible(!filtersVisible)}
              leftIcon={<Filter size={14} />}
              className="text-xs"
            >
              Filters
            </Button>
          )}
        </div>

        {/* Advanced Filters */}
        {showFilters && filtersVisible && (
          <div className="px-3 pb-3 border-t border-gray-200 dark:border-gray-700">
            <NotificationFilter
              selectedCategory={selectedCategory}
              selectedPriority={selectedPriority}
              selectedStatus={selectedStatus}
              onCategoryChange={setSelectedCategory}
              onPriorityChange={setSelectedPriority}
              onStatusChange={setSelectedStatus}
              onClearFilters={clearAllFilters}
              availableCategories={getNotificationCategories()}
              className="mt-3"
            />
          </div>
        )}
      </div>

      {/* Notifications */}
      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {filteredNotifications.slice(0, 10).map((notification) => (
          <div
            key={notification.id}
            className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer ${
              notification.read ? "opacity-75" : ""
            }`}
            onClick={() => handleNotificationClick(notification)}
          >
            <div className="space-y-3">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  <div className="text-lg flex-shrink-0">
                    {getNotificationIcon(notification)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {notification.title}
                      </p>
                      {!notification.read && (
                        <div className="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0 animate-pulse" />
                      )}
                    </div>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                      {notification.message}
                    </p>
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                      {notification.created_at &&
                        new Date(notification.created_at).toLocaleString()}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-1 ml-2">
                  {!notification.read && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        markAsRead(notification.id);
                      }}
                      className="p-1 h-auto"
                      title={t("notifications.markAsRead")}
                    >
                      <Check size={14} />
                    </Button>
                  )}
                </div>
              </div>

              {/* Notification Actions */}
              <NotificationActions
                notification={notification}
                onActionComplete={() => {
                  markAsRead(notification.id);
                  onClose?.();
                }}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      {filteredNotifications.length > 10 && (
        <div className="p-3 border-t border-gray-200 dark:border-gray-700 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Showing 10 of {filteredNotifications.length} notifications
          </p>
        </div>
      )}
    </div>
  );
};

export default NotificationsList;
