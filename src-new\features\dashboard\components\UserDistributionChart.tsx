/**
 * مكون الرسوم البيانية لتوزيع المستخدمين
 * User Distribution Charts Component
 */

import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDatabase } from "../../contexts/DatabaseContext";
import { useAuth } from "../../contexts/AuthContext";
import { usePermissions } from "../../hooks/usePermissions";
import {
  PieChart,
  BarChart3,
  Users,
  TrendingUp,
  Calendar,
  Target,
  Award,
  Activity,
} from "lucide-react";
import { UserRole } from "../../types";

interface UserDistributionData {
  byRole: Array<{
    role: string;
    count: number;
    percentage: number;
    color: string;
    arabicName: string;
  }>;
  byStatus: {
    active: number;
    inactive: number;
    total: number;
  };
  growth: {
    thisMonth: number;
    lastMonth: number;
    percentage: number;
  };
  engagement: {
    daily: number;
    weekly: number;
    monthly: number;
  };
}

export const UserDistributionChart: React.FC = () => {
  const { t } = useTranslation();
  const { users } = useDatabase();
  const { user } = useAuth();
  const { isAdmin, isSchoolManager, hasPermission } = usePermissions();

  // حساب بيانات التوزيع
  const distributionData: UserDistributionData = useMemo(() => {
    // تصفية المستخدمين حسب الصلاحيات
    let filteredUsers = users;
    if (!isAdmin && user?.tenant_id) {
      filteredUsers = users.filter(u => u.tenant_id === user.tenant_id);
    }

    // حساب التوزيع حسب الأدوار
    const roleCounts: Record<string, number> = {};
    filteredUsers.forEach(user => {
      roleCounts[user.role] = (roleCounts[user.role] || 0) + 1;
    });

    const roleColors: Record<string, string> = {
      [UserRole.ADMIN]: "#ef4444", // أحمر
      [UserRole.SCHOOL_MANAGER]: "#3b82f6", // أزرق
      [UserRole.SUPERVISOR]: "#10b981", // أخضر
      [UserRole.DRIVER]: "#f59e0b", // أصفر
      [UserRole.PARENT]: "#8b5cf6", // بنفسجي
      [UserRole.STUDENT]: "#ec4899", // وردي
    };

    const roleNames: Record<string, string> = {
      [UserRole.ADMIN]: "المديرون",
      [UserRole.SCHOOL_MANAGER]: "مديرو المدارس",
      [UserRole.SUPERVISOR]: "المشرفون",
      [UserRole.DRIVER]: "السائقون",
      [UserRole.PARENT]: "أولياء الأمور",
      [UserRole.STUDENT]: "الطلاب",
    };

    const total = filteredUsers.length;
    const byRole = Object.entries(roleCounts).map(([role, count]) => ({
      role,
      count,
      percentage: total > 0 ? (count / total) * 100 : 0,
      color: roleColors[role] || "#6b7280",
      arabicName: roleNames[role] || role,
    })).sort((a, b) => b.count - a.count);

    // حساب الحالة
    const activeUsers = filteredUsers.filter(u => u.is_active).length;
    const byStatus = {
      active: activeUsers,
      inactive: total - activeUsers,
      total,
    };

    // محاكاة بيانات النمو (يمكن حسابها من البيانات التاريخية)
    const growth = {
      thisMonth: total,
      lastMonth: Math.floor(total * 0.92), // افتراض نمو 8%
      percentage: 8.3,
    };

    // محاكاة بيانات المشاركة
    const engagement = {
      daily: Math.floor(activeUsers * 0.65),
      weekly: Math.floor(activeUsers * 0.85),
      monthly: activeUsers,
    };

    return {
      byRole,
      byStatus,
      growth,
      engagement,
    };
  }, [users, user, isAdmin]);

  // فقط الأدمن ومديرو المدارس يمكنهم رؤية هذه الإحصائيات
  if (!isAdmin && !isSchoolManager) {
    return null;
  }

  return (
    <div className="space-y-8">
      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Users className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                إجمالي المستخدمين
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {distributionData.byStatus.total}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Activity className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                المستخدمون النشطون
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {distributionData.byStatus.active}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                النمو الشهري
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                +{distributionData.growth.percentage}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calendar className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                نشاط يومي
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {distributionData.engagement.daily}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* توزيع المستخدمين حسب الأدوار */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* الرسم البياني الدائري (محاكاة) */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
            <PieChart className="h-5 w-5 mr-2 text-blue-600" />
            توزيع المستخدمين حسب الأدوار
          </h3>
          
          {/* محاكاة الرسم البياني الدائري */}
          <div className="space-y-4">
            {distributionData.byRole.map((item, index) => (
              <div key={item.role} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div 
                    className="w-4 h-4 rounded-full mr-3"
                    style={{ backgroundColor: item.color }}
                  ></div>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {item.arabicName}
                  </span>
                </div>
                <div className="text-left">
                  <span className="text-sm font-bold text-gray-900 dark:text-white">
                    {item.count}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                    ({item.percentage.toFixed(1)}%)
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* شريط التقدم لكل دور */}
          <div className="mt-6 space-y-3">
            {distributionData.byRole.slice(0, 3).map((item) => (
              <div key={`progress-${item.role}`}>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-700 dark:text-gray-300">{item.arabicName}</span>
                  <span className="text-gray-500 dark:text-gray-400">{item.percentage.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="h-2 rounded-full transition-all duration-300"
                    style={{ 
                      width: `${item.percentage}%`,
                      backgroundColor: item.color 
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* إحصائيات تفصيلية */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2 text-green-600" />
            إحصائيات تفصيلية
          </h3>

          <div className="space-y-6">
            {/* حالة المستخدمين */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                حالة المستخدمين
              </h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {distributionData.byStatus.active}
                  </p>
                  <p className="text-sm text-green-700 dark:text-green-300">نشط</p>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <p className="text-2xl font-bold text-gray-600 dark:text-gray-400">
                    {distributionData.byStatus.inactive}
                  </p>
                  <p className="text-sm text-gray-700 dark:text-gray-300">غير نشط</p>
                </div>
              </div>
            </div>

            {/* مستويات المشاركة */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                مستويات المشاركة
              </h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">نشاط يومي</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {distributionData.engagement.daily} مستخدم
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">نشاط أسبوعي</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {distributionData.engagement.weekly} مستخدم
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">نشاط شهري</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {distributionData.engagement.monthly} مستخدم
                  </span>
                </div>
              </div>
            </div>

            {/* معدل النمو */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                معدل النمو
              </h4>
              <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div>
                  <p className="text-sm text-blue-700 dark:text-blue-300">هذا الشهر</p>
                  <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
                    {distributionData.growth.thisMonth}
                  </p>
                </div>
                <div className="text-center">
                  <TrendingUp className="h-6 w-6 text-green-600 mx-auto mb-1" />
                  <p className="text-sm font-medium text-green-600">
                    +{distributionData.growth.percentage}%
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500 dark:text-gray-400">الشهر الماضي</p>
                  <p className="text-lg font-medium text-gray-600 dark:text-gray-400">
                    {distributionData.growth.lastMonth}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDistributionChart;
