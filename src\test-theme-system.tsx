/**
 * Test Theme System
 * Simple test to verify theme system is working
 * Phase 3: UI/UX Enhancement - Testing
 */

import React from 'react';
import { useAuth } from './contexts/AuthContext';
import { useThemePermissions } from './hooks/useThemePermissions';
import { UserRole } from './types';

export const TestThemeSystem: React.FC = () => {
  const { user } = useAuth();
  const permissions = useThemePermissions();

  return (
    <div className="p-6 bg-white rounded-lg shadow-md max-w-md mx-auto mt-8">
      <h2 className="text-xl font-bold mb-4">🎨 اختبار نظام الثيمات</h2>
      
      <div className="space-y-3">
        <div>
          <strong>المستخدم:</strong> {user?.name || 'غير مسجل'}
        </div>
        
        <div>
          <strong>الدور:</strong> {
            user?.role === UserRole.ADMIN ? 'مدير النظام' :
            user?.role === UserRole.SCHOOL_MANAGER ? 'مدير المدرسة' :
            user?.role === UserRole.TEACHER ? 'معلم' :
            user?.role === UserRole.DRIVER ? 'سائق' :
            user?.role === UserRole.PARENT ? 'ولي أمر' :
            'غير محدد'
          }
        </div>
        
        <div className="border-t pt-3">
          <strong>الصلاحيات:</strong>
          <ul className="mt-2 space-y-1 text-sm">
            <li className={permissions.canManageAllThemes ? 'text-green-600' : 'text-red-600'}>
              {permissions.canManageAllThemes ? '✅' : '❌'} إدارة جميع الثيمات
            </li>
            <li className={permissions.canManageOwnTheme ? 'text-green-600' : 'text-red-600'}>
              {permissions.canManageOwnTheme ? '✅' : '❌'} إدارة ثيم المدرسة
            </li>
            <li className={permissions.canCustomizeColors ? 'text-green-600' : 'text-red-600'}>
              {permissions.canCustomizeColors ? '✅' : '❌'} تخصيص الألوان
            </li>
            <li className={permissions.canUploadLogo ? 'text-green-600' : 'text-red-600'}>
              {permissions.canUploadLogo ? '✅' : '❌'} رفع الشعار
            </li>
            <li className={permissions.canChangeFonts ? 'text-green-600' : 'text-red-600'}>
              {permissions.canChangeFonts ? '✅' : '❌'} تغيير الخطوط
            </li>
          </ul>
        </div>
        
        <div className="border-t pt-3">
          <strong>الروابط المتاحة:</strong>
          <div className="mt-2 space-y-1">
            {permissions.canManageAllThemes && (
              <a 
                href="/admin/themes" 
                className="block text-blue-600 hover:text-blue-800 text-sm"
              >
                🔗 إدارة الثيمات (أدمن)
              </a>
            )}
            {permissions.canManageOwnTheme && (
              <a 
                href="/school/theme" 
                className="block text-blue-600 hover:text-blue-800 text-sm"
              >
                🔗 ثيم المدرسة
              </a>
            )}
            {!permissions.canManageAllThemes && !permissions.canManageOwnTheme && (
              <p className="text-gray-500 text-sm">لا توجد روابط متاحة</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
