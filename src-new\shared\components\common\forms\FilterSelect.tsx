/**
 * Filter Select Component
 * Reusable select component for filtering data
 * Phase 2: Application Structure Reorganization
 */

import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Check } from 'lucide-react';
import { cn } from '../../../utils/cn';

export interface SelectOption {
  value: any;
  label: string;
  disabled?: boolean;
  icon?: React.ReactNode;
}

export interface FilterSelectProps {
  options: SelectOption[];
  value: any;
  onChange: (value: any) => void;
  placeholder?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  clearable?: boolean;
  searchable?: boolean;
  multiple?: boolean;
  maxHeight?: string;
}

/**
 * Filter Select Component
 * Implements Interface Segregation Principle - focused interface for filtering
 */
export const FilterSelect: React.FC<FilterSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = 'Select option...',
  className,
  size = 'md',
  disabled = false,
  clearable = false,
  searchable = false,
  multiple = false,
  maxHeight = '200px',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const selectRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  /**
   * Filter options based on search term
   */
  const filteredOptions = searchable
    ? options.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  /**
   * Get selected option(s) for display
   */
  const getSelectedDisplay = () => {
    if (multiple) {
      const selectedOptions = options.filter(option => 
        Array.isArray(value) && value.includes(option.value)
      );
      
      if (selectedOptions.length === 0) return placeholder;
      if (selectedOptions.length === 1) return selectedOptions[0].label;
      return `${selectedOptions.length} selected`;
    } else {
      const selectedOption = options.find(option => option.value === value);
      return selectedOption ? selectedOption.label : placeholder;
    }
  };

  /**
   * Handle option selection
   */
  const handleOptionSelect = (optionValue: any) => {
    if (multiple) {
      const currentValues = Array.isArray(value) ? value : [];
      const newValues = currentValues.includes(optionValue)
        ? currentValues.filter(v => v !== optionValue)
        : [...currentValues, optionValue];
      onChange(newValues);
    } else {
      onChange(optionValue);
      setIsOpen(false);
      setSearchTerm('');
    }
  };

  /**
   * Handle clear selection
   */
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange(multiple ? [] : '');
  };

  /**
   * Check if option is selected
   */
  const isOptionSelected = (optionValue: any) => {
    if (multiple) {
      return Array.isArray(value) && value.includes(optionValue);
    }
    return value === optionValue;
  };

  /**
   * Size classes
   */
  const sizeClasses = {
    sm: 'h-8 text-sm px-3',
    md: 'h-10 text-sm px-4',
    lg: 'h-12 text-base px-4',
  };

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  return (
    <div ref={selectRef} className={cn('relative', className)}>
      {/* Select Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={cn(
          'w-full flex items-center justify-between',
          'border border-gray-300 rounded-md shadow-sm',
          'bg-white text-left',
          'focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
          'disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed',
          'transition-colors duration-200',
          sizeClasses[size],
          isOpen && 'ring-2 ring-blue-500 border-blue-500'
        )}
      >
        <span className={cn(
          'truncate',
          (!value || (Array.isArray(value) && value.length === 0)) && 'text-gray-400'
        )}>
          {getSelectedDisplay()}
        </span>
        
        <div className="flex items-center space-x-1">
          {clearable && value && (Array.isArray(value) ? value.length > 0 : value !== '') && (
            <button
              type="button"
              onClick={handleClear}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              ×
            </button>
          )}
          <ChevronDown
            className={cn(
              'text-gray-400 transition-transform duration-200',
              iconSizeClasses[size],
              isOpen && 'transform rotate-180'
            )}
          />
        </div>
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
          {/* Search Input */}
          {searchable && (
            <div className="p-2 border-b border-gray-200">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search options..."
                className="w-full px-3 py-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                autoFocus
              />
            </div>
          )}

          {/* Options List */}
          <div
            className="py-1 overflow-auto"
            style={{ maxHeight }}
          >
            {filteredOptions.length === 0 ? (
              <div className="px-4 py-2 text-sm text-gray-500">
                No options found
              </div>
            ) : (
              filteredOptions.map((option, index) => (
                <button
                  key={`${option.value}-${index}`}
                  type="button"
                  onClick={() => !option.disabled && handleOptionSelect(option.value)}
                  disabled={option.disabled}
                  className={cn(
                    'w-full flex items-center justify-between px-4 py-2 text-left text-sm',
                    'hover:bg-gray-100 focus:bg-gray-100 focus:outline-none',
                    'disabled:text-gray-400 disabled:cursor-not-allowed',
                    isOptionSelected(option.value) && 'bg-blue-50 text-blue-700'
                  )}
                >
                  <div className="flex items-center space-x-2">
                    {option.icon && (
                      <span className="flex-shrink-0">
                        {option.icon}
                      </span>
                    )}
                    <span className="truncate">{option.label}</span>
                  </div>
                  
                  {isOptionSelected(option.value) && (
                    <Check className="w-4 h-4 text-blue-600" />
                  )}
                </button>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Multi-Select Component
 * Specialized version for multiple selections
 */
export interface MultiSelectProps extends Omit<FilterSelectProps, 'multiple' | 'value' | 'onChange'> {
  value: any[];
  onChange: (values: any[]) => void;
  selectAllOption?: boolean;
}

export const MultiSelect: React.FC<MultiSelectProps> = ({
  value = [],
  onChange,
  selectAllOption = false,
  options,
  ...props
}) => {
  /**
   * Handle select all
   */
  const handleSelectAll = () => {
    if (value.length === options.length) {
      onChange([]);
    } else {
      onChange(options.map(option => option.value));
    }
  };

  const allSelected = value.length === options.length;
  const someSelected = value.length > 0 && value.length < options.length;

  const enhancedOptions = selectAllOption
    ? [
        {
          value: '__select_all__',
          label: 'Select All',
          icon: (
            <div className={cn(
              'w-4 h-4 border border-gray-300 rounded flex items-center justify-center',
              allSelected && 'bg-blue-600 border-blue-600',
              someSelected && 'bg-blue-600 border-blue-600'
            )}>
              {allSelected && <Check className="w-3 h-3 text-white" />}
              {someSelected && <div className="w-2 h-2 bg-white rounded-sm" />}
            </div>
          ),
        },
        ...options,
      ]
    : options;

  const handleChange = (selectedValue: any) => {
    if (selectedValue === '__select_all__') {
      handleSelectAll();
    } else {
      const newValues = value.includes(selectedValue)
        ? value.filter(v => v !== selectedValue)
        : [...value, selectedValue];
      onChange(newValues);
    }
  };

  return (
    <FilterSelect
      {...props}
      options={enhancedOptions}
      value={value}
      onChange={handleChange}
      multiple={true}
    />
  );
};
