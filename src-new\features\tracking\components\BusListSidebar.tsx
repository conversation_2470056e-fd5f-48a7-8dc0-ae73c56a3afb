/**
 * Bus List Sidebar Component
 * مكون قائمة الحافلات الجانبية
 */

import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Search,
  Navigation,
  Users,
  Clock,
  AlertTriangle,
  MapPin,
  Route as RouteIcon,
  ChevronLeft,
  ChevronRight,
  Filter,
  SortAsc,
  SortDesc,
  Activity,
  Pause,
  Wrench,
  Zap,
} from 'lucide-react';
import { BusTrackingData } from './TrackingDashboard';

interface BusListSidebarProps {
  buses: BusTrackingData[];
  selectedBusId: string | null;
  onBusSelect: (busId: string) => void;
  collapsed: boolean;
  onToggleCollapse: () => void;
}

type SortOption = 'plateNumber' | 'status' | 'speed' | 'students' | 'lastUpdate';
type SortDirection = 'asc' | 'desc';

export const BusListSidebar: React.FC<BusListSidebarProps> = ({
  buses,
  selectedBusId,
  onBusSelect,
  collapsed,
  onToggleCollapse,
}) => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('plateNumber');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  // Filter and sort buses
  const filteredAndSortedBuses = useMemo(() => {
    let filtered = buses;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = buses.filter(bus =>
        bus.plateNumber.toLowerCase().includes(query) ||
        bus.driverName.toLowerCase().includes(query) ||
        bus.route.name.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'plateNumber':
          aValue = a.plateNumber;
          bValue = b.plateNumber;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'speed':
          aValue = a.currentLocation?.speed || 0;
          bValue = b.currentLocation?.speed || 0;
          break;
        case 'students':
          aValue = a.studentsCount;
          bValue = b.studentsCount;
          break;
        case 'lastUpdate':
          aValue = new Date(a.lastUpdate).getTime();
          bValue = new Date(b.lastUpdate).getTime();
          break;
        default:
          return 0;
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [buses, searchQuery, sortBy, sortDirection]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Navigation className="w-4 h-4 text-green-600" />;
      case 'stopped':
        return <Pause className="w-4 h-4 text-gray-600" />;
      case 'maintenance':
        return <Wrench className="w-4 h-4 text-yellow-600" />;
      case 'emergency':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <MapPin className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'stopped':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'emergency':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatLastUpdate = (timestamp: string) => {
    const diff = Date.now() - new Date(timestamp).getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    return `${Math.floor(hours / 24)}d ago`;
  };

  const handleSort = (option: SortOption) => {
    if (sortBy === option) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(option);
      setSortDirection('asc');
    }
  };

  if (collapsed) {
    return (
      <div className="w-16 bg-white shadow-lg border-r border-gray-200 flex flex-col items-center py-4">
        <button
          onClick={onToggleCollapse}
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <ChevronRight className="w-5 h-5 text-gray-600" />
        </button>
        
        <div className="mt-4 space-y-2">
          {buses.slice(0, 5).map((bus) => (
            <button
              key={bus.id}
              onClick={() => onBusSelect(bus.id)}
              className={`
                w-10 h-10 rounded-lg flex items-center justify-center transition-all
                ${selectedBusId === bus.id 
                  ? 'bg-blue-100 border-2 border-blue-500' 
                  : 'bg-gray-100 hover:bg-gray-200 border border-gray-300'
                }
              `}
            >
              {getStatusIcon(bus.status)}
            </button>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 bg-white shadow-lg border-r border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            {t('tracking.busList')}
          </h3>
          <button
            onClick={onToggleCollapse}
            className="p-1 rounded hover:bg-gray-100 transition-colors"
          >
            <ChevronLeft className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder={t('tracking.searchBuses')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Sort Options */}
        <div className="flex items-center space-x-2 mt-3">
          <span className="text-xs text-gray-500">{t('tracking.sortBy')}:</span>
          <div className="flex space-x-1">
            {[
              { key: 'plateNumber', label: t('buses.plateNumber') },
              { key: 'status', label: t('tracking.status.title') },
              { key: 'speed', label: t('tracking.speed') },
            ].map((option) => (
              <button
                key={option.key}
                onClick={() => handleSort(option.key as SortOption)}
                className={`
                  px-2 py-1 text-xs rounded transition-colors flex items-center space-x-1
                  ${sortBy === option.key 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }
                `}
              >
                <span>{option.label}</span>
                {sortBy === option.key && (
                  sortDirection === 'asc' ? 
                    <SortAsc className="w-3 h-3" /> : 
                    <SortDesc className="w-3 h-3" />
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Bus List */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-2 space-y-2">
          {filteredAndSortedBuses.map((bus) => (
            <div
              key={bus.id}
              onClick={() => onBusSelect(bus.id)}
              className={`
                p-4 rounded-xl border cursor-pointer transition-all duration-200 hover:shadow-md
                ${selectedBusId === bus.id
                  ? 'border-blue-500 bg-blue-50 shadow-md'
                  : 'border-gray-200 bg-white hover:border-gray-300'
                }
              `}
            >
              {/* Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(bus.status)}
                  <span className="font-semibold text-gray-900">
                    {bus.plateNumber}
                  </span>
                </div>
                <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(bus.status)}`}>
                  {t(`tracking.status.${bus.status}`)}
                </span>
              </div>

              {/* Driver */}
              <div className="text-sm text-gray-600 mb-2">
                <span className="font-medium">{bus.driverName}</span>
              </div>

              {/* Route */}
              <div className="flex items-center space-x-2 mb-3">
                <RouteIcon className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-600 truncate">
                  {bus.route.name}
                </span>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-3 mb-3">
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">
                    {bus.studentsCount}/{bus.capacity}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Activity className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">
                    {bus.currentLocation?.speed || 0} km/h
                  </span>
                </div>
              </div>

              {/* Next Stop */}
              {bus.nextStop && (
                <div className="flex items-center space-x-2 mb-2">
                  <MapPin className="w-4 h-4 text-blue-500" />
                  <span className="text-sm text-blue-600">
                    {bus.nextStop.name} • {bus.nextStop.eta}
                  </span>
                </div>
              )}

              {/* Last Update */}
              <div className="flex items-center justify-between text-xs text-gray-500">
                <div className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>{formatLastUpdate(bus.lastUpdate)}</span>
                </div>
                {bus.alerts.length > 0 && (
                  <div className="flex items-center space-x-1 text-red-500">
                    <AlertTriangle className="w-3 h-3" />
                    <span>{bus.alerts.length}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {filteredAndSortedBuses.length === 0 && (
          <div className="p-8 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <p className="text-gray-500 mb-2">{t('tracking.noBusesFound')}</p>
            <p className="text-sm text-gray-400">{t('tracking.tryDifferentSearch')}</p>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="text-xs text-gray-500 text-center">
          {filteredAndSortedBuses.length} of {buses.length} buses
        </div>
      </div>
    </div>
  );
};

export default BusListSidebar;
