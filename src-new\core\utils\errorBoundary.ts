// Enhanced error handling for runtime issues
export class ErrorHandler {
  private static errorQueue: Array<{
    error: Error;
    context: string;
    timestamp: number;
  }> = [];
  private static maxErrors = 50;
  private static reportingEnabled = true;

  static handleError(error: Error, context: string = "Unknown"): void {
    console.error(`[${context}] Error:`, error);

    // Add to error queue
    this.errorQueue.push({
      error,
      context,
      timestamp: Date.now(),
    });

    // Keep only recent errors
    if (this.errorQueue.length > this.maxErrors) {
      this.errorQueue.shift();
    }

    // Report critical errors
    if (this.isCriticalError(error)) {
      this.reportCriticalError(error, context);
    }
  }

  static handleAPIError(error: any, endpoint: string): void {
    let errorMessage = "Unknown API error";
    let errorCode = "UNKNOWN";

    if (error?.message) {
      errorMessage = error.message;
    }

    if (error?.code) {
      errorCode = error.code;
    }

    // Handle specific Supabase errors
    if (error?.details) {
      errorMessage = error.details;
    }

    // Handle network errors
    if (error?.name === "NetworkError" || error?.code === "NETWORK_ERROR") {
      errorMessage =
        "Network connection error. Please check your internet connection.";
      errorCode = "NETWORK_ERROR";
    }

    // Handle authentication errors
    if (error?.status === 401 || error?.code === "UNAUTHORIZED") {
      errorMessage = "Authentication required. Please log in again.";
      errorCode = "AUTH_ERROR";
    }

    // Handle rate limiting
    if (error?.status === 429) {
      errorMessage = "Too many requests. Please wait a moment and try again.";
      errorCode = "RATE_LIMIT";
    }

    this.handleError(
      new Error(`${errorCode}: ${errorMessage}`),
      `API:${endpoint}`,
    );
  }

  static handleCSSError(error: Error): void {
    console.warn("CSS loading error:", error);

    // Try to reload CSS if it failed to load
    const links = document.querySelectorAll('link[rel="stylesheet"]');
    links.forEach((link) => {
      const href = (link as HTMLLinkElement).href;
      if (href.includes("index.css") || href.includes("main.css")) {
        // Reload the CSS file
        const newLink = document.createElement("link");
        newLink.rel = "stylesheet";
        newLink.href = href + "?v=" + Date.now();
        document.head.appendChild(newLink);

        // Remove old link after new one loads
        newLink.onload = () => {
          link.remove();
        };
      }
    });
  }

  static handleHMRError(error: Error): void {
    console.warn("HMR error:", error);

    // In development, try to reconnect HMR
    if (import.meta.env.DEV) {
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  }

  private static isCriticalError(error: Error): boolean {
    const criticalPatterns = [
      "ChunkLoadError",
      "Loading chunk",
      "Failed to fetch",
      "NetworkError",
      "CORS",
      "Unexpected token",
    ];

    return criticalPatterns.some(
      (pattern) =>
        error.message.includes(pattern) || error.name.includes(pattern),
    );
  }

  private static reportCriticalError(error: Error, context: string): void {
    if (!this.reportingEnabled) return;

    // In a real app, send to error reporting service
    console.error("🚨 Critical Error Reported:", {
      error: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    });
  }

  static getErrorStats(): {
    totalErrors: number;
    recentErrors: number;
    criticalErrors: number;
  } {
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;

    const recentErrors = this.errorQueue.filter(
      (e) => e.timestamp > oneHourAgo,
    ).length;
    const criticalErrors = this.errorQueue.filter((e) =>
      this.isCriticalError(e.error),
    ).length;

    return {
      totalErrors: this.errorQueue.length,
      recentErrors,
      criticalErrors,
    };
  }

  static clearErrors(): void {
    this.errorQueue = [];
  }
}

// Global error handlers
window.addEventListener("error", (event) => {
  ErrorHandler.handleError(event.error || new Error(event.message), "Global");
});

window.addEventListener("unhandledrejection", (event) => {
  ErrorHandler.handleError(
    event.reason instanceof Error
      ? event.reason
      : new Error(String(event.reason)),
    "Promise",
  );
});

// CSS error detection
window.addEventListener(
  "error",
  (event) => {
    if (event.target && (event.target as any).tagName === "LINK") {
      ErrorHandler.handleCSSError(new Error("CSS file failed to load"));
    }
  },
  true,
);
