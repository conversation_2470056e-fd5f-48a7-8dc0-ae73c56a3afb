/**
 * Bus List Component
 * Displays paginated list of buses with management actions
 * Phase 2: Application Structure Reorganization
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Bus, BusStatus } from '../../../api/types';
import { busService, BusListParams } from '../../../services/data/BusService';
import { usePermissionService } from '../../../hooks/usePermissionService';
import { ResourceType, Action } from '../../../lib/rbac';

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '../../common/ui/Card';
import { Button } from '../../common/ui/Button';
import { Badge } from '../../common/ui/Badge';
import { Alert, AlertDescription } from '../../common/ui/Alert';
import { DataTable } from '../../common/data-display/DataTable';
import { SearchInput } from '../../common/forms/SearchInput';
import { FilterSelect } from '../../common/forms/FilterSelect';
import { Pagination } from '../../common/data-display/Pagination';

// Icons
import { Plus, Edit, Trash2, Eye, Bus as BusIcon, MapPin, User, Settings } from 'lucide-react';

interface BusListProps {
  tenantId?: string;
  onBusSelect?: (bus: Bus) => void;
  onBusCreate?: () => void;
  onBusEdit?: (bus: Bus) => void;
  onBusDelete?: (bus: Bus) => void;
  onBusTrack?: (bus: Bus) => void;
  className?: string;
}

interface BusListState {
  buses: Bus[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filters: {
    search: string;
    status: BusStatus | '';
    isActive: boolean | '';
    driverId: string;
  };
}

/**
 * Bus List Component
 * Implements comprehensive bus management with real-time status
 */
export const BusList: React.FC<BusListProps> = ({
  tenantId,
  onBusSelect,
  onBusCreate,
  onBusEdit,
  onBusDelete,
  onBusTrack,
  className,
}) => {
  // Permissions
  const { canRead, canCreate, canUpdate, canDelete } = usePermissionService();

  // State
  const [state, setState] = useState<BusListState>({
    buses: [],
    loading: false,
    error: null,
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
    },
    filters: {
      search: '',
      status: '',
      isActive: '',
      driverId: '',
    },
  });

  // Check permissions
  const canViewBuses = canRead(ResourceType.BUS);
  const canCreateBuses = canCreate(ResourceType.BUS);
  const canUpdateBuses = canUpdate(ResourceType.BUS);
  const canDeleteBuses = canDelete(ResourceType.BUS);

  /**
   * Load buses with current filters and pagination
   */
  const loadBuses = useCallback(async () => {
    if (!canViewBuses) {
      setState(prev => ({
        ...prev,
        error: 'You do not have permission to view buses',
      }));
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const params: BusListParams = {
        page: state.pagination.page,
        limit: state.pagination.limit,
        ...(state.filters.search && { search: state.filters.search }),
        ...(state.filters.status && { status: state.filters.status }),
        ...(state.filters.isActive !== '' && { is_active: state.filters.isActive }),
        ...(state.filters.driverId && { driver_id: state.filters.driverId }),
        ...(tenantId && { tenant_id: tenantId }),
      };

      const response = await busService.getBuses(params);

      if (response.success && response.data) {
        setState(prev => ({
          ...prev,
          buses: response.data!.data,
          pagination: {
            ...prev.pagination,
            total: response.data!.pagination.total,
            totalPages: response.data!.pagination.totalPages,
          },
          loading: false,
        }));
      } else {
        setState(prev => ({
          ...prev,
          error: response.error?.message || 'Failed to load buses',
          loading: false,
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'An unexpected error occurred',
        loading: false,
      }));
    }
  }, [state.pagination.page, state.pagination.limit, state.filters, tenantId, canViewBuses]);

  // Load buses on mount and when dependencies change
  useEffect(() => {
    loadBuses();
  }, [loadBuses]);

  /**
   * Handle search input change
   */
  const handleSearchChange = useCallback((search: string) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, search },
      pagination: { ...prev.pagination, page: 1 },
    }));
  }, []);

  /**
   * Handle status filter change
   */
  const handleStatusFilterChange = useCallback((status: BusStatus | '') => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, status },
      pagination: { ...prev.pagination, page: 1 },
    }));
  }, []);

  /**
   * Handle active status filter change
   */
  const handleActiveFilterChange = useCallback((isActive: boolean | '') => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, isActive },
      pagination: { ...prev.pagination, page: 1 },
    }));
  }, []);

  /**
   * Handle page change
   */
  const handlePageChange = useCallback((page: number) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page },
    }));
  }, []);

  /**
   * Handle page size change
   */
  const handlePageSizeChange = useCallback((limit: number) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, limit, page: 1 },
    }));
  }, []);

  /**
   * Get status badge variant
   */
  const getStatusBadgeVariant = (status: BusStatus) => {
    switch (status) {
      case BusStatus.IN_ROUTE:
        return 'default';
      case BusStatus.IDLE:
        return 'secondary';
      case BusStatus.MAINTENANCE:
        return 'destructive';
      case BusStatus.OUT_OF_SERVICE:
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  /**
   * Table columns configuration
   */
  const columns = useMemo(() => [
    {
      key: 'bus_info',
      title: 'Bus Information',
      render: (bus: Bus) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <BusIcon className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <div className="font-medium">{bus.plate_number}</div>
            <div className="text-sm text-gray-500">{bus.model} ({bus.year})</div>
          </div>
        </div>
      ),
    },
    {
      key: 'capacity',
      title: 'Capacity',
      render: (bus: Bus) => (
        <div className="text-center">
          <div className="font-medium">{bus.capacity}</div>
          <div className="text-xs text-gray-500">seats</div>
        </div>
      ),
    },
    {
      key: 'driver',
      title: 'Driver',
      render: (bus: Bus) => (
        <div className="flex items-center space-x-2">
          <User className="w-4 h-4 text-gray-400" />
          <span className="text-sm">
            {bus.driver?.name || 'Not assigned'}
          </span>
        </div>
      ),
    },
    {
      key: 'route',
      title: 'Route',
      render: (bus: Bus) => (
        <div className="flex items-center space-x-2">
          <MapPin className="w-4 h-4 text-gray-400" />
          <span className="text-sm">
            {bus.route?.name || 'Not assigned'}
          </span>
        </div>
      ),
    },
    {
      key: 'status',
      title: 'Status',
      render: (bus: Bus) => (
        <div className="space-y-1">
          <Badge variant={getStatusBadgeVariant(bus.status)}>
            {bus.status.replace('_', ' ').toUpperCase()}
          </Badge>
          <div>
            <Badge variant={bus.is_active ? 'default' : 'destructive'} size="sm">
              {bus.is_active ? 'Active' : 'Inactive'}
            </Badge>
          </div>
        </div>
      ),
    },
    {
      key: 'location',
      title: 'Last Location',
      render: (bus: Bus) => (
        <div className="text-sm">
          {bus.location ? (
            <div>
              <div>Lat: {bus.location.latitude.toFixed(4)}</div>
              <div>Lng: {bus.location.longitude.toFixed(4)}</div>
              <div className="text-xs text-gray-500">
                {bus.last_updated ? new Date(bus.last_updated).toLocaleTimeString() : 'Unknown'}
              </div>
            </div>
          ) : (
            <span className="text-gray-500">No location data</span>
          )}
        </div>
      ),
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (bus: Bus) => (
        <div className="flex items-center space-x-2">
          {onBusSelect && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onBusSelect(bus)}
              title="View Details"
            >
              <Eye className="w-4 h-4" />
            </Button>
          )}
          
          {onBusTrack && bus.location && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onBusTrack(bus)}
              title="Track Bus"
            >
              <MapPin className="w-4 h-4 text-green-500" />
            </Button>
          )}
          
          {canUpdateBuses && onBusEdit && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onBusEdit(bus)}
              title="Edit Bus"
            >
              <Edit className="w-4 h-4" />
            </Button>
          )}
          
          {canDeleteBuses && onBusDelete && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onBusDelete(bus)}
              title="Delete Bus"
            >
              <Trash2 className="w-4 h-4 text-red-500" />
            </Button>
          )}
        </div>
      ),
    },
  ], [canUpdateBuses, canDeleteBuses, onBusSelect, onBusEdit, onBusDelete, onBusTrack]);

  // Filter options
  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: BusStatus.IDLE, label: 'Idle' },
    { value: BusStatus.IN_ROUTE, label: 'In Route' },
    { value: BusStatus.MAINTENANCE, label: 'Maintenance' },
    { value: BusStatus.OUT_OF_SERVICE, label: 'Out of Service' },
  ];

  const activeOptions = [
    { value: '', label: 'All' },
    { value: true, label: 'Active' },
    { value: false, label: 'Inactive' },
  ];

  if (!canViewBuses) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          You do not have permission to view buses.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <BusIcon className="w-5 h-5" />
            <span>Buses</span>
          </CardTitle>
          {canCreateBuses && onBusCreate && (
            <Button onClick={onBusCreate}>
              <Plus className="w-4 h-4 mr-2" />
              Add Bus
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Filters */}
        <div className="flex items-center space-x-4 mb-6">
          <SearchInput
            placeholder="Search buses..."
            value={state.filters.search}
            onChange={handleSearchChange}
            className="flex-1"
          />
          
          <FilterSelect
            options={statusOptions}
            value={state.filters.status}
            onChange={handleStatusFilterChange}
            placeholder="Filter by status"
          />
          
          <FilterSelect
            options={activeOptions}
            value={state.filters.isActive}
            onChange={handleActiveFilterChange}
            placeholder="Filter by active"
          />
        </div>

        {/* Error Display */}
        {state.error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{state.error}</AlertDescription>
          </Alert>
        )}

        {/* Data Table */}
        <DataTable
          data={state.buses}
          columns={columns}
          loading={state.loading}
          emptyMessage="No buses found"
        />

        {/* Pagination */}
        <div className="mt-6">
          <Pagination
            currentPage={state.pagination.page}
            totalPages={state.pagination.totalPages}
            pageSize={state.pagination.limit}
            totalItems={state.pagination.total}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        </div>
      </CardContent>
    </Card>
  );
};
