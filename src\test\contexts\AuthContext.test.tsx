import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { AuthProvider, useAuth } from '@contexts/AuthContext'
import { createMockUser, createMockTenant, mockSupabase } from '../utils/test-utils'

// Mock Supabase
vi.mock('@lib/supabase', () => ({
  supabase: mockSupabase
}))

describe('AuthContext', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('AuthProvider', () => {
    it('should provide initial auth state', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider
      })

      expect(result.current.user).toBeNull()
      expect(result.current.tenant).toBeNull()
      expect(result.current.isLoading).toBe(true)
      expect(result.current.isAuthenticated).toBe(false)
    })

    it('should handle successful login', async () => {
      const mockUser = createMockUser()
      const mockTenant = createMockTenant()

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockTenant,
          error: null
        })
      })

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider
      })

      await act(async () => {
        await result.current.login('<EMAIL>', 'password')
      })

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password'
      })
    })

    it('should handle login error', async () => {
      const mockError = { message: 'Invalid credentials' }

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null },
        error: mockError
      })

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider
      })

      await act(async () => {
        try {
          await result.current.login('<EMAIL>', 'wrongpassword')
        } catch (error) {
          expect(error).toEqual(mockError)
        }
      })
    })

    it('should handle logout', async () => {
      mockSupabase.auth.signOut.mockResolvedValue({
        error: null
      })

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider
      })

      await act(async () => {
        await result.current.logout()
      })

      expect(mockSupabase.auth.signOut).toHaveBeenCalled()
      expect(result.current.user).toBeNull()
      expect(result.current.tenant).toBeNull()
    })

    it('should check user permissions', () => {
      const mockUser = createMockUser({ role: 'admin' })

      const { result } = renderHook(() => useAuth(), {
        wrapper: ({ children }) => (
          <AuthProvider>
            {children}
          </AuthProvider>
        )
      })

      // محاكاة تسجيل دخول المستخدم
      act(() => {
        // هنا يمكن محاكاة تحديث حالة المستخدم
      })

      // اختبار الصلاحيات
      expect(result.current.hasPermission).toBeDefined()
      expect(typeof result.current.hasPermission).toBe('function')
    })

    it('should handle auth state changes', () => {
      const mockCallback = vi.fn()

      mockSupabase.auth.onAuthStateChange.mockImplementation((callback) => {
        mockCallback.mockImplementation(callback)
        return { data: { subscription: { unsubscribe: vi.fn() } } }
      })

      renderHook(() => useAuth(), {
        wrapper: AuthProvider
      })

      expect(mockSupabase.auth.onAuthStateChange).toHaveBeenCalled()
    })
  })

  describe('useAuth hook', () => {
    it('should throw error when used outside AuthProvider', () => {
      // تعطيل console.error مؤقتاً لهذا الاختبار
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      expect(() => {
        renderHook(() => useAuth())
      }).toThrow('useAuth must be used within an AuthProvider')

      consoleSpy.mockRestore()
    })

    it('should return auth context value', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider
      })

      expect(result.current).toHaveProperty('user')
      expect(result.current).toHaveProperty('tenant')
      expect(result.current).toHaveProperty('isLoading')
      expect(result.current).toHaveProperty('isAuthenticated')
      expect(result.current).toHaveProperty('login')
      expect(result.current).toHaveProperty('logout')
      expect(result.current).toHaveProperty('hasPermission')
    })
  })

  describe('Permission system', () => {
    it('should correctly check admin permissions', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider
      })

      // محاكاة مستخدم admin
      act(() => {
        // تحديث حالة المستخدم
      })

      // اختبار صلاحيات الإدارة
      expect(result.current.hasPermission).toBeDefined()
    })

    it('should correctly check school manager permissions', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider
      })

      // محاكاة مستخدم school_manager
      act(() => {
        // تحديث حالة المستخدم
      })

      // اختبار صلاحيات مدير المدرسة
      expect(result.current.hasPermission).toBeDefined()
    })

    it('should correctly check driver permissions', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider
      })

      // محاكاة مستخدم driver
      act(() => {
        // تحديث حالة المستخدم
      })

      // اختبار صلاحيات السائق
      expect(result.current.hasPermission).toBeDefined()
    })
  })

  describe('Tenant management', () => {
    it('should load tenant data on user login', async () => {
      const mockUser = createMockUser()
      const mockTenant = createMockTenant()

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockTenant,
          error: null
        })
      })

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider
      })

      // محاكاة تحميل بيانات المستأجر
      await act(async () => {
        // تحديث حالة المستأجر
      })

      expect(mockSupabase.from).toHaveBeenCalledWith('tenants')
    })

    it('should handle tenant loading error', async () => {
      const mockError = { message: 'Tenant not found' }

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: null,
          error: mockError
        })
      })

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider
      })

      // محاكاة خطأ في تحميل المستأجر
      await act(async () => {
        // محاولة تحميل المستأجر
      })

      // التحقق من التعامل مع الخطأ
      expect(result.current.tenant).toBeNull()
    })
  })

  describe('Loading states', () => {
    it('should show loading state during authentication', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider
      })

      expect(result.current.isLoading).toBe(true)
    })

    it('should clear loading state after authentication', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      })

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider
      })

      // انتظار انتهاء التحميل
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 100))
      })

      expect(result.current.isLoading).toBe(false)
    })
  })
})
