/**
 * مكون إحصائيات مبسط للاختبار - Simple Stats Component
 * يعرض الإحصائيات الأساسية بشكل مبسط
 */

import React from "react";
import { useDatabase } from "../../contexts/DatabaseContext";
import { useAuth } from "../../contexts/AuthContext";
import { usePermissions } from "../../hooks/usePermissions";
import { EnhancedStatCard as StatCard } from "./EnhancedStatCard";
import { AttendanceStats } from "./AttendanceStats";
import {
  Users,
  Bus,
  Route,
  GraduationCap,
  School,
  Activity,
  AlertTriangle,
  UserCheck,
  UserX,
  Calendar,
} from "lucide-react";

export const SimpleStats: React.FC = () => {
  const { users, buses, routes, students, tenants, loading, error } = useDatabase();
  const { user } = useAuth();
  const { isAdmin, isSchoolManager } = usePermissions();

  console.log("SimpleStats - Raw Data:", {
    users: users.length,
    buses: buses.length,
    students: students.length,
    routes: routes.length,
    tenants: tenants.length,
    loading,
    error: error?.message,
    userRole: user?.role,
    tenantId: user?.tenant_id,
    isAdmin,
    isSchoolManager,
  });

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
        <p className="mt-4 text-gray-600 dark:text-gray-400">جاري تحميل الإحصائيات...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2" />
          <p className="text-red-800 dark:text-red-200">
            خطأ في تحميل الإحصائيات: {error.message}
          </p>
        </div>
      </div>
    );
  }

  // تصفية البيانات حسب الدور
  let filteredUsers = users;
  let filteredBuses = buses;
  let filteredRoutes = routes;
  let filteredStudents = students;
  let filteredTenants = tenants;

  if (!isAdmin && user?.tenant_id) {
    filteredUsers = users.filter(u => u.tenant_id === user.tenant_id);
    filteredBuses = buses.filter(b => b.tenant_id === user.tenant_id);
    filteredRoutes = routes.filter(r => r.tenant_id === user.tenant_id);
    filteredStudents = students.filter(s => s.tenant_id === user.tenant_id);
    filteredTenants = tenants.filter(t => t.id === user.tenant_id);
  }

  // حساب الإحصائيات
  const activeUsers = filteredUsers.filter(u => u.is_active).length;
  const activeBuses = filteredBuses.filter(b => b.is_active).length;
  const activeRoutes = filteredRoutes.filter(r => r.is_active).length;
  const activeStudents = filteredStudents.filter(s => s.is_active).length;
  const activeTenants = filteredTenants.filter(t => t.is_active).length;

  return (
    <div className="space-y-6">
      {/* معلومات تشخيصية */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">معلومات تشخيصية</h3>
        <div className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
          <p><strong>البيانات الخام:</strong> مستخدمون: {users.length} | حافلات: {buses.length} | طلاب: {students.length} | مسارات: {routes.length} | مدارس: {tenants.length}</p>
          <p><strong>البيانات المفلترة:</strong> مستخدمون: {filteredUsers.length} | حافلات: {filteredBuses.length} | طلاب: {filteredStudents.length} | مسارات: {filteredRoutes.length}</p>
          <p><strong>المستخدم:</strong> الدور: {user?.role} | المستأجر: {user?.tenant_id}</p>
          <p><strong>الصلاحيات:</strong> Admin: {isAdmin.toString()} | SchoolManager: {isSchoolManager.toString()}</p>
        </div>
      </div>

      {/* الإحصائيات الأساسية */}
      <div>
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
          الإحصائيات الأساسية
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* إحصائيات المدارس (للأدمن فقط) */}
          {isAdmin && (
            <StatCard
              title="المدارس المسجلة"
              value={filteredTenants.length}
              icon={<School className="h-6 w-6" />}
              trend={{
                value: 12.5,
                label: "نمو شهري",
                isPositive: true,
              }}
              color="indigo"
              subtitle={`${activeTenants} نشطة`}
            />
          )}

          {/* إجمالي المستخدمين */}
          <StatCard
            title={isAdmin ? "إجمالي المستخدمين" : "المستخدمون"}
            value={filteredUsers.length}
            icon={<Users className="h-6 w-6" />}
            trend={{
              value: 8.3,
              label: "نمو شهري",
              isPositive: true,
            }}
            color="blue"
            subtitle={`${activeUsers} نشط`}
          />

          {/* إحصائيات الحافلات */}
          <StatCard
            title={isAdmin ? "إجمالي الحافلات" : "الحافلات"}
            value={filteredBuses.length}
            icon={<Bus className="h-6 w-6" />}
            trend={{
              value: filteredBuses.length > 0 ? (activeBuses / filteredBuses.length) * 100 : 0,
              label: "معدل النشاط",
              isPositive: true,
            }}
            color="green"
            subtitle={`${activeBuses} نشطة`}
          />

          {/* إحصائيات الطلاب */}
          <StatCard
            title={isAdmin ? "إجمالي الطلاب" : "الطلاب"}
            value={filteredStudents.length}
            icon={<GraduationCap className="h-6 w-6" />}
            trend={{
              value: 15.2,
              label: "نمو شهري",
              isPositive: true,
            }}
            color="orange"
            subtitle={`${activeStudents} نشط`}
          />

          {/* إحصائيات المسارات */}
          <StatCard
            title={isAdmin ? "إجمالي المسارات" : "المسارات"}
            value={filteredRoutes.length}
            icon={<Route className="h-6 w-6" />}
            trend={{
              value: filteredRoutes.length > 0 ? (activeRoutes / filteredRoutes.length) * 100 : 0,
              label: "معدل النشاط",
              isPositive: true,
            }}
            color="purple"
            subtitle={`${activeRoutes} نشطة`}
          />
        </div>
      </div>

      {/* إحصائيات إضافية للحافلات */}
      {(isAdmin || isSchoolManager) && filteredBuses.length > 0 && (
        <div>
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
            تفاصيل الحافلات
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <StatCard
              title="في الخدمة"
              value={activeBuses}
              icon={<Activity className="h-6 w-6" />}
              color="green"
              subtitle="حافلات تعمل حالياً"
            />
            <StatCard
              title="في الصيانة"
              value={filteredBuses.filter(b => b.status === 'maintenance').length}
              icon={<AlertTriangle className="h-6 w-6" />}
              color="yellow"
              subtitle="صيانة دورية"
            />
            <StatCard
              title="خارج الخدمة"
              value={filteredBuses.length - activeBuses}
              icon={<AlertTriangle className="h-6 w-6" />}
              color="red"
              subtitle="حافلات متوقفة"
            />
          </div>
        </div>
      )}

      {/* إحصائيات الحضور والغياب */}
      <AttendanceStats />

      {/* رسالة في حالة عدم وجود بيانات */}
      {filteredUsers.length === 0 && filteredBuses.length === 0 && filteredStudents.length === 0 && (
        <div className="text-center py-12">
          <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            لا توجد بيانات متاحة
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            لم يتم العثور على أي بيانات لعرضها. تأكد من وجود البيانات في النظام.
          </p>
        </div>
      )}
    </div>
  );
};

export default SimpleStats;
