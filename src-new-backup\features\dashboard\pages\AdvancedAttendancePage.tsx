/**
 * Advanced Attendance Management Page
 * Phase 4: Core System Functionality
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Activity,
  Users,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Play,
  Square,
  BarChart3,
  Download,
  Filter,
  Search,
  Camera,
  MapPin,
} from 'lucide-react';
import { ResponsiveLayout } from '../../components/layout/ResponsiveLayout';
import { Button } from '../../components/ui/Button';
import { useAuth } from '../../contexts/AuthContext';
import { AdvancedAttendanceService } from '../../services/AdvancedAttendanceService';

interface AttendanceStats {
  totalStudents: number;
  presentToday: number;
  absentToday: number;
  lateToday: number;
  attendanceRate: number;
  activeSessions: number;
}

interface AttendanceSession {
  id: string;
  busPlateNumber: string;
  routeName: string;
  driverName: string;
  sessionType: 'pickup' | 'dropoff';
  startedAt: string;
  totalStudents: number;
  presentCount: number;
  absentCount: number;
  status: 'active' | 'completed';
}

export const AdvancedAttendancePage: React.FC = () => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<AttendanceStats>({
    totalStudents: 0,
    presentToday: 0,
    absentToday: 0,
    lateToday: 0,
    attendanceRate: 0,
    activeSessions: 0,
  });
  const [activeSessions, setActiveSessions] = useState<AttendanceSession[]>([]);
  const [activeTab, setActiveTab] = useState<'overview' | 'sessions' | 'reports' | 'analytics'>('overview');

  const attendanceService = AdvancedAttendanceService.getInstance();

  useEffect(() => {
    loadAttendanceData();
  }, [tenant?.id]);

  const loadAttendanceData = async () => {
    try {
      setIsLoading(true);
      console.log('🔄 AdvancedAttendancePage: Loading attendance data for user:', user?.role, 'tenant:', tenant?.id);

      // For admin users, load global attendance data
      if (user?.role === 'admin') {
        console.log('🔧 AdvancedAttendancePage: Loading admin attendance data');

        // Use enhanced mock data for admin
        const adminStats: AttendanceStats = {
          totalStudents: 450, // Global across all schools
          presentToday: 425,
          absentToday: 18,
          lateToday: 7,
          attendanceRate: 94.4,
          activeSessions: 8,
        };

        const adminSessions: AttendanceSession[] = [
          {
            id: '1',
            busPlateNumber: 'ABC-123',
            routeName: 'المسار الشمالي - مدرسة الأمل',
            driverName: 'أحمد محمد',
            sessionType: 'pickup',
            startedAt: new Date().toISOString(),
            totalStudents: 25,
            presentCount: 20,
            absentCount: 5,
            status: 'active',
          },
          {
            id: '2',
            busPlateNumber: 'XYZ-456',
            routeName: 'المسار الجنوبي - مدرسة النور',
            driverName: 'محمد علي',
            sessionType: 'pickup',
            startedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
            totalStudents: 30,
            presentCount: 28,
            absentCount: 2,
            status: 'active',
          },
          {
            id: '3',
            busPlateNumber: 'DEF-789',
            routeName: 'المسار الشرقي - مدرسة المستقبل',
            driverName: 'سالم أحمد',
            sessionType: 'dropoff',
            startedAt: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
            totalStudents: 22,
            presentCount: 22,
            absentCount: 0,
            status: 'completed',
          },
        ];

        setStats(adminStats);
        setActiveSessions(adminSessions);
        console.log('✅ AdvancedAttendancePage: Admin data loaded successfully');
        return;
      }

      // For tenant users, load tenant-specific data
      if (!tenant?.id) {
        console.log('⚠️ AdvancedAttendancePage: No tenant ID available');
        setStats({
          totalStudents: 0,
          presentToday: 0,
          absentToday: 0,
          lateToday: 0,
          attendanceRate: 0,
          activeSessions: 0,
        });
        setActiveSessions([]);
        return;
      }

      console.log('🔄 AdvancedAttendancePage: Loading tenant attendance data for:', tenant.id);

      // Mock data for tenant users
      const mockStats: AttendanceStats = {
        totalStudents: 150,
        presentToday: 142,
        absentToday: 6,
        lateToday: 2,
        attendanceRate: 94.7,
        activeSessions: 3,
      };

      const mockSessions: AttendanceSession[] = [
        {
          id: '1',
          busPlateNumber: 'ABC-123',
          routeName: 'المسار الشمالي',
          driverName: 'أحمد محمد',
          sessionType: 'pickup',
          startedAt: new Date().toISOString(),
          totalStudents: 25,
          presentCount: 20,
          absentCount: 5,
          status: 'active',
        },
        {
          id: '2',
          busPlateNumber: 'XYZ-456',
          routeName: 'المسار الجنوبي',
          driverName: 'محمد علي',
          sessionType: 'pickup',
          startedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          totalStudents: 30,
          presentCount: 28,
          absentCount: 2,
          status: 'active',
        },
      ];

      setStats(mockStats);
      setActiveSessions(mockSessions);
      console.log('✅ AdvancedAttendancePage: Tenant data loaded successfully');
    } catch (error) {
      console.error('❌ AdvancedAttendancePage: Error loading attendance data:', error);
      // Set fallback data
      setStats({
        totalStudents: 0,
        presentToday: 0,
        absentToday: 0,
        lateToday: 0,
        attendanceRate: 0,
        activeSessions: 0,
      });
      setActiveSessions([]);
    } finally {
      setIsLoading(false);
      console.log('✅ AdvancedAttendancePage: Loading completed');
    }
  };

  const startAttendanceSession = async (busId: string, routeId: string, sessionType: 'pickup' | 'dropoff') => {
    try {
      if (!user?.id || !tenant?.id) return;

      const sessionId = await attendanceService.startAttendanceSession(
        busId,
        routeId,
        user.id,
        sessionType,
        tenant.id
      );

      console.log('Attendance session started:', sessionId);
      await loadAttendanceData();
    } catch (error) {
      console.error('Error starting attendance session:', error);
    }
  };

  const endAttendanceSession = async (sessionId: string) => {
    try {
      await attendanceService.endAttendanceSession(sessionId);
      await loadAttendanceData();
    } catch (error) {
      console.error('Error ending attendance session:', error);
    }
  };

  const getSessionStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-50 border-green-200';
      case 'completed': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getSessionTypeIcon = (type: string) => {
    return type === 'pickup' ? 
      <Play className="w-4 h-4 text-green-500" /> : 
      <Square className="w-4 h-4 text-blue-500" />;
  };

  if (isLoading) {
    return (
      <ResponsiveLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </ResponsiveLayout>
    );
  }

  return (
    <ResponsiveLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('attendance.advancedAttendance')}
            </h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {t('attendance.advancedDescription')}
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex space-x-3">
            <Button variant="outline" leftIcon={<Download />}>
              {t('attendance.exportReport')}
            </Button>
            <Button leftIcon={<Play />}>
              {t('attendance.startSession')}
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('attendance.totalStudents')}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {stats.totalStudents}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('attendance.presentToday')}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {stats.presentToday}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                <XCircle className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('attendance.absentToday')}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {stats.absentToday}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                <Clock className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('attendance.lateToday')}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {stats.lateToday}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <BarChart3 className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('attendance.attendanceRate')}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {stats.attendanceRate}%
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'overview', label: t('attendance.overview'), icon: Activity },
              { key: 'sessions', label: t('attendance.activeSessions'), icon: Play },
              { key: 'reports', label: t('attendance.reports'), icon: BarChart3 },
              { key: 'analytics', label: t('attendance.analytics'), icon: Download },
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setActiveTab(key as any)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === key
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === 'sessions' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {t('attendance.activeSessions')} ({activeSessions.length})
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {t('attendance.activeSessionsDescription')}
                </p>
              </div>
              <div className="p-6">
                {activeSessions.length === 0 ? (
                  <div className="text-center py-8">
                    <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400">
                      {t('attendance.noActiveSessions')}
                    </p>
                    <Button className="mt-4" leftIcon={<Play />}>
                      {t('attendance.startFirstSession')}
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {activeSessions.map((session) => (
                      <div
                        key={session.id}
                        className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            {getSessionTypeIcon(session.sessionType)}
                            <div className="ml-3">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                                {session.busPlateNumber} - {session.routeName}
                              </h4>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                {t('attendance.driver')}: {session.driverName}
                              </p>
                              <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                                <Clock className="w-3 h-3 mr-1" />
                                <span>
                                  {t('attendance.started')}: {new Date(session.startedAt).toLocaleTimeString()}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center space-x-4 mb-2">
                              <div className="text-center">
                                <p className="text-lg font-semibold text-green-600">
                                  {session.presentCount}
                                </p>
                                <p className="text-xs text-gray-500">{t('attendance.present')}</p>
                              </div>
                              <div className="text-center">
                                <p className="text-lg font-semibold text-red-600">
                                  {session.absentCount}
                                </p>
                                <p className="text-xs text-gray-500">{t('attendance.absent')}</p>
                              </div>
                              <div className="text-center">
                                <p className="text-lg font-semibold text-gray-600">
                                  {session.totalStudents}
                                </p>
                                <p className="text-xs text-gray-500">{t('attendance.total')}</p>
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline" leftIcon={<Camera />}>
                                {t('attendance.takePhoto')}
                              </Button>
                              <Button size="sm" variant="outline" leftIcon={<MapPin />}>
                                {t('attendance.location')}
                              </Button>
                              <Button 
                                size="sm" 
                                variant="outline"
                                onClick={() => endAttendanceSession(session.id)}
                              >
                                {t('attendance.endSession')}
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Today's Summary */}
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  {t('attendance.todaySummary')}
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {t('attendance.morningPickup')}
                    </span>
                    <span className="text-sm font-medium text-green-600">
                      95% {t('attendance.completed')}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {t('attendance.afternoonDropoff')}
                    </span>
                    <span className="text-sm font-medium text-yellow-600">
                      {t('attendance.inProgress')}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {t('attendance.averageTime')}
                    </span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      12 {t('attendance.minutes')}
                    </span>
                  </div>
                </div>
              </div>

              {/* Recent Alerts */}
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  {t('attendance.recentAlerts')}
                </h3>
                <div className="space-y-3">
                  {[
                    { type: 'absent', student: 'أحمد محمد', time: '07:30' },
                    { type: 'late', student: 'فاطمة علي', time: '07:45' },
                    { type: 'absent', student: 'محمد حسن', time: '08:00' },
                  ].map((alert, index) => (
                    <div key={index} className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      {alert.type === 'absent' ? (
                        <XCircle className="w-4 h-4 text-red-500 mr-3" />
                      ) : (
                        <Clock className="w-4 h-4 text-yellow-500 mr-3" />
                      )}
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {alert.student}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {alert.type === 'absent' ? t('attendance.absent') : t('attendance.late')} - {alert.time}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </ResponsiveLayout>
  );
};

export default AdvancedAttendancePage;
