/**
 * Permission Service Tests
 * Comprehensive tests for the centralized permission service
 * Phase 1: RBAC Security Enhancement
 */

import { PermissionService, SecurityEventType } from "../lib/permissionService";
import { ResourceType, Action, DataScope } from "../lib/rbac";
import { UserRole } from "../types";

// Mock user data for testing
const mockUsers = {
  admin: {
    id: "admin-1",
    email: "<EMAIL>",
    role: UserRole.ADMIN,
    tenant_id: "tenant-1",
    is_active: true,
    name: "Admin User",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  schoolManager: {
    id: "manager-1",
    email: "<EMAIL>",
    role: UserRole.SCHOOL_MANAGER,
    tenant_id: "tenant-1",
    is_active: true,
    name: "School Manager",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  driver: {
    id: "driver-1",
    email: "<EMAIL>",
    role: UserRole.DRIVER,
    tenant_id: "tenant-1",
    is_active: true,
    name: "Driver User",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  parent: {
    id: "parent-1",
    email: "<EMAIL>",
    role: UserRole.PARENT,
    tenant_id: "tenant-1",
    is_active: true,
    name: "Parent User",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  inactiveUser: {
    id: "inactive-1",
    email: "<EMAIL>",
    role: UserRole.DRIVER,
    tenant_id: "tenant-1",
    is_active: false,
    name: "Inactive User",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
};

describe("PermissionService", () => {
  let permissionService: PermissionService;

  beforeEach(() => {
    permissionService = PermissionService.getInstance();
    permissionService.clearRateLimitCache();
  });

  describe("Basic Permission Checks", () => {
    test("Admin should have access to all resources and actions", () => {
      const result = permissionService.checkPermission(
        mockUsers.admin,
        ResourceType.USER,
        Action.DELETE
      );

      expect(result.allowed).toBe(true);
      expect(result.securityLevel).toBe("low");
      expect(result.riskScore).toBe(10);
    });

    test("School Manager should have limited access", () => {
      const result = permissionService.checkPermission(
        mockUsers.schoolManager,
        ResourceType.USER,
        Action.DELETE
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain("Insufficient permissions");
    });

    test("Driver should only access assigned resources", () => {
      const result = permissionService.checkPermission(
        mockUsers.driver,
        ResourceType.BUS,
        Action.UPDATE
      );

      expect(result.allowed).toBe(true);
    });

    test("Parent should have read-only access to most resources", () => {
      const result = permissionService.checkPermission(
        mockUsers.parent,
        ResourceType.STUDENT,
        Action.CREATE
      );

      expect(result.allowed).toBe(false);
    });
  });

  describe("User Validation", () => {
    test("Should reject inactive users", () => {
      const result = permissionService.checkPermission(
        mockUsers.inactiveUser,
        ResourceType.BUS,
        Action.READ
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe("User account is deactivated");
      expect(result.securityLevel).toBe("high");
    });

    test("Should reject null/undefined users", () => {
      const result = permissionService.checkPermission(
        null as any,
        ResourceType.BUS,
        Action.READ
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe("User not authenticated");
    });
  });

  describe("Tenant Isolation", () => {
    test("Should allow same-tenant access", () => {
      const result = permissionService.checkPermission(
        mockUsers.schoolManager,
        ResourceType.STUDENT,
        Action.READ,
        {
          userId: mockUsers.schoolManager.id,
          tenantId: mockUsers.schoolManager.tenant_id,
          resourceTenantId: "tenant-1",
        }
      );

      expect(result.allowed).toBe(true);
    });

    test("Should block cross-tenant access for non-admin users", () => {
      const result = permissionService.checkPermission(
        mockUsers.schoolManager,
        ResourceType.STUDENT,
        Action.READ,
        {
          userId: mockUsers.schoolManager.id,
          tenantId: mockUsers.schoolManager.tenant_id,
          resourceTenantId: "tenant-2", // Different tenant
        }
      );

      expect(result.allowed).toBe(false);
      expect(result.securityLevel).toBe("critical");
      expect(result.reason).toContain("different tenant");
    });

    test("Should allow admin cross-tenant access", () => {
      const result = permissionService.checkPermission(
        mockUsers.admin,
        ResourceType.STUDENT,
        Action.READ,
        {
          userId: mockUsers.admin.id,
          tenantId: mockUsers.admin.tenant_id,
          resourceTenantId: "tenant-2", // Different tenant
        }
      );

      expect(result.allowed).toBe(true);
    });
  });

  describe("Rate Limiting", () => {
    test("Should allow requests within rate limit", () => {
      for (let i = 0; i < 50; i++) {
        const result = permissionService.checkPermission(
          mockUsers.driver,
          ResourceType.BUS,
          Action.READ
        );
        expect(result.allowed).toBe(true);
      }
    });

    test("Should block requests exceeding rate limit", () => {
      // Make 101 requests (exceeds limit of 100)
      for (let i = 0; i < 101; i++) {
        permissionService.checkPermission(
          mockUsers.driver,
          ResourceType.BUS,
          Action.READ
        );
      }

      const result = permissionService.checkPermission(
        mockUsers.driver,
        ResourceType.BUS,
        Action.READ
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe("Rate limit exceeded");
      expect(result.securityLevel).toBe("medium");
    });
  });

  describe("Role Hierarchy", () => {
    test("Admin should be able to manage all roles", () => {
      expect(permissionService.canManageRole(UserRole.ADMIN, UserRole.SCHOOL_MANAGER)).toBe(true);
      expect(permissionService.canManageRole(UserRole.ADMIN, UserRole.DRIVER)).toBe(true);
      expect(permissionService.canManageRole(UserRole.ADMIN, UserRole.PARENT)).toBe(true);
    });

    test("School Manager should manage limited roles", () => {
      expect(permissionService.canManageRole(UserRole.SCHOOL_MANAGER, UserRole.ADMIN)).toBe(false);
      expect(permissionService.canManageRole(UserRole.SCHOOL_MANAGER, UserRole.DRIVER)).toBe(true);
      expect(permissionService.canManageRole(UserRole.SCHOOL_MANAGER, UserRole.PARENT)).toBe(true);
    });

    test("Driver should not manage any roles", () => {
      expect(permissionService.canManageRole(UserRole.DRIVER, UserRole.PARENT)).toBe(false);
      expect(permissionService.canManageRole(UserRole.DRIVER, UserRole.STUDENT)).toBe(false);
    });
  });

  describe("Data Scope", () => {
    test("Should return correct data scopes for each role", () => {
      expect(permissionService.getUserDataScopes(UserRole.ADMIN)).toContain(DataScope.GLOBAL);
      expect(permissionService.getUserDataScopes(UserRole.SCHOOL_MANAGER)).toContain(DataScope.TENANT);
      expect(permissionService.getUserDataScopes(UserRole.DRIVER)).toContain(DataScope.ASSIGNED);
      expect(permissionService.getUserDataScopes(UserRole.PARENT)).toContain(DataScope.CHILDREN);
      expect(permissionService.getUserDataScopes(UserRole.STUDENT)).toContain(DataScope.PERSONAL);
    });
  });

  describe("Data Filtering", () => {
    const mockData = [
      { id: "1", tenant_id: "tenant-1", name: "Item 1" },
      { id: "2", tenant_id: "tenant-2", name: "Item 2" },
      { id: "3", tenant_id: "tenant-1", name: "Item 3" },
    ];

    test("Admin should see all data", () => {
      const filtered = permissionService.filterDataByPermissions(
        mockUsers.admin,
        mockData,
        ResourceType.STUDENT
      );

      expect(filtered).toHaveLength(3);
    });

    test("School Manager should see only tenant data", () => {
      const filtered = permissionService.filterDataByPermissions(
        mockUsers.schoolManager,
        mockData,
        ResourceType.STUDENT
      );

      expect(filtered).toHaveLength(2);
      expect(filtered.every(item => item.tenant_id === "tenant-1")).toBe(true);
    });
  });

  describe("Security Events", () => {
    test("Should log security events", () => {
      permissionService.checkPermission(
        mockUsers.driver,
        ResourceType.USER,
        Action.DELETE // This should be denied
      );

      const events = permissionService.getSecurityEvents(10);
      expect(events.length).toBeGreaterThan(0);
      
      const denialEvent = events.find(e => e.type === SecurityEventType.PERMISSION_DENIED);
      expect(denialEvent).toBeDefined();
    });

    test("Should track tenant isolation violations", () => {
      permissionService.checkPermission(
        mockUsers.schoolManager,
        ResourceType.STUDENT,
        Action.READ,
        {
          userId: mockUsers.schoolManager.id,
          tenantId: mockUsers.schoolManager.tenant_id,
          resourceTenantId: "tenant-2",
        }
      );

      const events = permissionService.getSecurityEvents(10);
      const violationEvent = events.find(e => e.type === SecurityEventType.TENANT_ISOLATION_VIOLATION);
      expect(violationEvent).toBeDefined();
    });
  });

  describe("Permission Matrix", () => {
    test("Should return correct allowed actions for each role", () => {
      const adminActions = permissionService.getAllowedActions(UserRole.ADMIN, ResourceType.USER);
      expect(adminActions).toContain(Action.CREATE);
      expect(adminActions).toContain(Action.DELETE);

      const driverActions = permissionService.getAllowedActions(UserRole.DRIVER, ResourceType.USER);
      expect(driverActions).toContain(Action.READ);
      expect(driverActions).not.toContain(Action.DELETE);
    });
  });

  describe("Error Handling", () => {
    test("Should handle invalid user roles gracefully", () => {
      const invalidUser = {
        ...mockUsers.driver,
        role: "invalid_role" as any,
      };

      const result = permissionService.checkPermission(
        invalidUser,
        ResourceType.BUS,
        Action.READ
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe("Invalid user role");
    });

    test("Should handle missing user properties", () => {
      const incompleteUser = {
        id: "test",
        email: "<EMAIL>",
        // Missing role and other properties
      } as any;

      const result = permissionService.checkPermission(
        incompleteUser,
        ResourceType.BUS,
        Action.READ
      );

      expect(result.allowed).toBe(false);
    });
  });
});
