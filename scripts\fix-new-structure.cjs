/**
 * إصلاح البنية الجديدة الشامل
 * Comprehensive New Structure Fix
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 بدء إصلاح البنية الجديدة الشامل...\n');

class StructureFixer {
  constructor() {
    this.issues = [];
    this.fixes = [];
    this.stats = {
      filesAnalyzed: 0,
      issuesFound: 0,
      fixesApplied: 0,
      errors: 0
    };
  }

  /**
   * تحليل وتشخيص المشاكل
   */
  async analyzeIssues() {
    console.log('🔍 تحليل وتشخيص المشاكل...');

    // فحص الملفات المفقودة
    await this.checkMissingFiles();
    
    // فحص مسارات الاستيراد
    await this.checkImportPaths();
    
    // فحص التبعيات
    await this.checkDependencies();
    
    // إنشاء تقرير التشخيص
    await this.generateDiagnosticReport();
  }

  /**
   * فحص الملفات المفقودة
   */
  async checkMissingFiles() {
    console.log('📁 فحص الملفات المفقودة...');

    const criticalFiles = [
      'src-new/core/contexts/CustomThemeContext.tsx',
      'src-new/core/contexts/CustomThemeProvider.tsx',
      'src-new/assets/locales/index.ts',
      'src-new/core/constants/app.ts',
      'src-new/shared/services/lib/supabase.ts',
      'src-new/shared/components/ui/Button.tsx',
      'src-new/shared/layouts/Navbar.tsx',
      'src-new/shared/layouts/ResponsiveLayout.tsx'
    ];

    for (const file of criticalFiles) {
      if (!fs.existsSync(file)) {
        this.issues.push({
          type: 'missing_file',
          file: file,
          severity: 'high',
          description: `ملف مهم مفقود: ${file}`
        });
      }
    }
  }

  /**
   * فحص مسارات الاستيراد
   */
  async checkImportPaths() {
    console.log('🔗 فحص مسارات الاستيراد...');

    const filesToCheck = [
      'src-new/main.tsx',
      'src-new/App.tsx'
    ];

    for (const file of filesToCheck) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        const imports = this.extractImports(content);
        
        for (const importPath of imports) {
          if (!this.validateImportPath(importPath, file)) {
            this.issues.push({
              type: 'invalid_import',
              file: file,
              import: importPath,
              severity: 'high',
              description: `مسار استيراد غير صحيح: ${importPath} في ${file}`
            });
          }
        }
      }
    }
  }

  /**
   * استخراج مسارات الاستيراد من الملف
   */
  extractImports(content) {
    const importRegex = /import.*from\s+['"`]([^'"`]+)['"`]/g;
    const imports = [];
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      imports.push(match[1]);
    }
    
    return imports;
  }

  /**
   * التحقق من صحة مسار الاستيراد
   */
  validateImportPath(importPath, fromFile) {
    // تجاهل المسارات الخارجية
    if (!importPath.startsWith('.')) {
      return true;
    }

    const fromDir = path.dirname(fromFile);
    const resolvedPath = path.resolve(fromDir, importPath);
    
    // فحص الملف مع امتدادات مختلفة
    const extensions = ['', '.ts', '.tsx', '.js', '.jsx'];
    
    for (const ext of extensions) {
      if (fs.existsSync(resolvedPath + ext)) {
        return true;
      }
    }
    
    // فحص إذا كان مجلد مع index
    const indexExtensions = ['/index.ts', '/index.tsx', '/index.js', '/index.jsx'];
    for (const indexExt of indexExtensions) {
      if (fs.existsSync(resolvedPath + indexExt)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * فحص التبعيات
   */
  async checkDependencies() {
    console.log('📦 فحص التبعيات...');

    // فحص الملفات المطلوبة في core
    const coreFiles = [
      'src-new/core/contexts/AuthContext.tsx',
      'src-new/core/contexts/ThemeContext.tsx',
      'src-new/core/contexts/DatabaseContext.tsx',
      'src-new/core/contexts/NotificationsContext.tsx'
    ];

    for (const file of coreFiles) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        
        // فحص استيراد supabase
        if (content.includes('../lib/supabase')) {
          this.issues.push({
            type: 'wrong_import_path',
            file: file,
            wrongPath: '../lib/supabase',
            correctPath: '../shared/services/lib/supabase',
            severity: 'high',
            description: `مسار استيراد خاطئ في ${file}`
          });
        }
      }
    }
  }

  /**
   * إنشاء تقرير التشخيص
   */
  async generateDiagnosticReport() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportDir = path.join(process.cwd(), 'reports', 'structure-fix');
    
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const report = {
      diagnostic_info: {
        timestamp: timestamp,
        total_issues: this.issues.length,
        high_severity: this.issues.filter(i => i.severity === 'high').length,
        medium_severity: this.issues.filter(i => i.severity === 'medium').length,
        low_severity: this.issues.filter(i => i.severity === 'low').length
      },
      issues_by_type: {
        missing_files: this.issues.filter(i => i.type === 'missing_file').length,
        invalid_imports: this.issues.filter(i => i.type === 'invalid_import').length,
        wrong_import_paths: this.issues.filter(i => i.type === 'wrong_import_path').length
      },
      detailed_issues: this.issues,
      recommended_fixes: [
        'إنشاء الملفات المفقودة',
        'تصحيح مسارات الاستيراد',
        'إنشاء ملفات index.ts مناسبة',
        'تحديث التبعيات'
      ]
    };

    const reportPath = path.join(reportDir, `diagnostic-report-${timestamp}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log(`📊 تم إنشاء تقرير التشخيص: ${reportPath}`);
    console.log(`🔍 تم العثور على ${this.issues.length} مشكلة`);
    
    return report;
  }

  /**
   * إصلاح الملفات المفقودة
   */
  async fixMissingFiles() {
    console.log('🔨 إصلاح الملفات المفقودة...');

    // إنشاء CustomThemeContext
    await this.createCustomThemeContext();
    
    // إنشاء ملف locales index
    await this.createLocalesIndex();
    
    // إنشاء ملف app.ts
    await this.createAppConfig();
    
    // إنشاء مكونات UI مفقودة
    await this.createMissingUIComponents();
  }

  /**
   * إنشاء CustomThemeContext
   */
  async createCustomThemeContext() {
    const contextPath = 'src-new/core/contexts/CustomThemeContext.tsx';
    
    if (!fs.existsSync(contextPath)) {
      const content = `import React, { createContext, useContext, useState, useEffect } from 'react';

interface CustomThemeContextType {
  theme: string;
  setTheme: (theme: string) => void;
  isDark: boolean;
}

const CustomThemeContext = createContext<CustomThemeContextType | undefined>(undefined);

export const CustomThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [theme, setTheme] = useState('light');
  const isDark = theme === 'dark';

  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
  }, []);

  useEffect(() => {
    localStorage.setItem('theme', theme);
    document.documentElement.classList.toggle('dark', isDark);
  }, [theme, isDark]);

  return (
    <CustomThemeContext.Provider value={{ theme, setTheme, isDark }}>
      {children}
    </CustomThemeContext.Provider>
  );
};

export const useCustomTheme = () => {
  const context = useContext(CustomThemeContext);
  if (!context) {
    throw new Error('useCustomTheme must be used within CustomThemeProvider');
  }
  return context;
};
`;

      fs.writeFileSync(contextPath, content);
      this.fixes.push(`تم إنشاء: ${contextPath}`);
    }
  }

  /**
   * إنشاء ملف locales index
   */
  async createLocalesIndex() {
    const localesPath = 'src-new/assets/locales/index.ts';
    
    if (!fs.existsSync(localesPath)) {
      // إنشاء المجلد إذا لم يكن موجود
      const localesDir = path.dirname(localesPath);
      if (!fs.existsSync(localesDir)) {
        fs.mkdirSync(localesDir, { recursive: true });
      }

      const content = `// Internationalization setup
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

const resources = {
  en: {
    translation: {
      welcome: 'Welcome',
      login: 'Login',
      logout: 'Logout'
    }
  },
  ar: {
    translation: {
      welcome: 'مرحباً',
      login: 'تسجيل الدخول',
      logout: 'تسجيل الخروج'
    }
  }
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'ar',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false
    }
  });

export default i18n;
`;

      fs.writeFileSync(localesPath, content);
      this.fixes.push(`تم إنشاء: ${localesPath}`);
    }
  }

  /**
   * إنشاء ملف app.ts
   */
  async createAppConfig() {
    const appPath = 'src-new/core/constants/app.ts';
    
    if (!fs.existsSync(appPath)) {
      const content = `// Application configuration
export const APP_CONFIG = {
  name: 'School Bus Management SaaS',
  version: '1.0.0',
  description: 'نظام إدارة النقل المدرسي',
  author: 'School Bus Team'
};

export const initializeApp = async () => {
  console.log('🚀 تهيئة التطبيق...');
  
  // تهيئة الخدمات الأساسية
  try {
    // يمكن إضافة تهيئة إضافية هنا
    console.log('✅ تم تهيئة التطبيق بنجاح');
  } catch (error) {
    console.error('❌ خطأ في تهيئة التطبيق:', error);
    throw error;
  }
};
`;

      fs.writeFileSync(appPath, content);
      this.fixes.push(`تم إنشاء: ${appPath}`);
    }
  }

  /**
   * إنشاء مكونات UI مفقودة
   */
  async createMissingUIComponents() {
    // إنشاء Button component
    const buttonPath = 'src-new/shared/components/ui/Button.tsx';
    
    if (!fs.existsSync(buttonPath)) {
      // إنشاء المجلد إذا لم يكن موجود
      const buttonDir = path.dirname(buttonPath);
      if (!fs.existsSync(buttonDir)) {
        fs.mkdirSync(buttonDir, { recursive: true });
      }

      const buttonContent = `import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  children,
  className = '',
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
  };
  
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  const classes = \`\${baseClasses} \${variantClasses[variant]} \${sizeClasses[size]} \${className}\`;

  return (
    <button className={classes} {...props}>
      {children}
    </button>
  );
};
`;

      fs.writeFileSync(buttonPath, buttonContent);
      this.fixes.push(`تم إنشاء: ${buttonPath}`);
    }

    // إنشاء Navbar component
    const navbarPath = 'src-new/shared/layouts/Navbar.tsx';
    
    if (!fs.existsSync(navbarPath)) {
      const navbarDir = path.dirname(navbarPath);
      if (!fs.existsSync(navbarDir)) {
        fs.mkdirSync(navbarDir, { recursive: true });
      }

      const navbarContent = `import React from 'react';

interface NavbarProps {
  title?: string;
  children?: React.ReactNode;
}

export const Navbar: React.FC<NavbarProps> = ({ 
  title = 'School Bus Management',
  children 
}) => {
  return (
    <nav className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <h1 className="text-xl font-semibold text-gray-900">
              {title}
            </h1>
          </div>
          <div className="flex items-center space-x-4">
            {children}
          </div>
        </div>
      </div>
    </nav>
  );
};
`;

      fs.writeFileSync(navbarPath, navbarContent);
      this.fixes.push(`تم إنشاء: ${navbarPath}`);
    }

    // إنشاء ResponsiveLayout component
    const layoutPath = 'src-new/shared/layouts/ResponsiveLayout.tsx';
    
    if (!fs.existsSync(layoutPath)) {
      const layoutContent = `import React from 'react';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  title?: string;
  sidebar?: React.ReactNode;
}

export const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  title,
  sidebar
}) => {
  return (
    <div className="min-h-screen bg-gray-50">
      {title && (
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
          </div>
        </header>
      )}
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row gap-6">
          {sidebar && (
            <aside className="lg:w-64 flex-shrink-0">
              <div className="bg-white rounded-lg shadow p-6">
                {sidebar}
              </div>
            </aside>
          )}
          
          <main className="flex-1">
            <div className="bg-white rounded-lg shadow p-6">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};
`;

      fs.writeFileSync(layoutPath, layoutContent);
      this.fixes.push(`تم إنشاء: ${layoutPath}`);
    }
  }

  /**
   * تصحيح مسارات الاستيراد
   */
  async fixImportPaths() {
    console.log('🔗 تصحيح مسارات الاستيراد...');

    // إصلاح main.tsx
    await this.fixMainTsx();
    
    // إصلاح App.tsx
    await this.fixAppTsx();
    
    // إصلاح ملفات contexts
    await this.fixContextFiles();
  }

  /**
   * إصلاح main.tsx
   */
  async fixMainTsx() {
    const mainPath = 'src-new/main.tsx';
    
    if (fs.existsSync(mainPath)) {
      let content = fs.readFileSync(mainPath, 'utf8');
      
      // تصحيح مسارات الاستيراد
      content = content.replace(
        'import { CustomThemeProvider } from "./core/contexts/CustomThemeProvider";',
        'import { CustomThemeProvider } from "./core/contexts/CustomThemeContext";'
      );
      
      fs.writeFileSync(mainPath, content);
      this.fixes.push(`تم إصلاح: ${mainPath}`);
    }
  }

  /**
   * إصلاح App.tsx
   */
  async fixAppTsx() {
    const appPath = 'src-new/App.tsx';
    
    if (fs.existsSync(appPath)) {
      let content = fs.readFileSync(appPath, 'utf8');
      
      // إصلاح مسارات الاستيراد الأساسية
      const fixes = [
        {
          from: 'import { TestPage } from "./features/testing/TestPage";',
          to: 'import { SecurityTestPage as TestPage } from "./features/testing/SecurityTestPage";'
        },
        {
          from: 'import { ENHANCED_ROUTE_PERMISSIONS } from "./shared/services/lib/rbacCentralizedConfigEnhanced";',
          to: '// import { ENHANCED_ROUTE_PERMISSIONS } from "./shared/services/lib/rbacCentralizedConfig";'
        },
        {
          from: 'import { useThemePermissions } from "./core/hooks/useThemePermissions";',
          to: '// import { useThemePermissions } from "./core/hooks/usePermissions";'
        }
      ];

      for (const fix of fixes) {
        content = content.replace(fix.from, fix.to);
      }
      
      fs.writeFileSync(appPath, content);
      this.fixes.push(`تم إصلاح: ${appPath}`);
    }
  }

  /**
   * إصلاح ملفات contexts
   */
  async fixContextFiles() {
    const contextFiles = [
      'src-new/core/contexts/AuthContext.tsx',
      'src-new/core/contexts/DatabaseContext.tsx',
      'src-new/core/contexts/NotificationsContext.tsx'
    ];

    for (const file of contextFiles) {
      if (fs.existsSync(file)) {
        let content = fs.readFileSync(file, 'utf8');
        
        // تصحيح مسار supabase
        content = content.replace(
          /import.*from ['"`]\.\.\/lib\/supabase['"`]/g,
          'import { supabase } from "../../shared/services/lib/supabase"'
        );
        
        fs.writeFileSync(file, content);
        this.fixes.push(`تم إصلاح: ${file}`);
      }
    }
  }

  /**
   * تنفيذ الإصلاح الشامل
   */
  async executeComprehensiveFix() {
    console.log('🚀 تنفيذ الإصلاح الشامل...\n');

    try {
      // تحليل المشاكل
      const report = await this.analyzeIssues();
      
      // إصلاح الملفات المفقودة
      await this.fixMissingFiles();
      
      // تصحيح مسارات الاستيراد
      await this.fixImportPaths();
      
      // إنشاء تقرير الإصلاح
      await this.generateFixReport();
      
      console.log('\n✅ تم إكمال الإصلاح الشامل بنجاح!');
      this.showFixSummary();
      
    } catch (error) {
      console.error('❌ خطأ في الإصلاح:', error);
      this.stats.errors++;
    }
  }

  /**
   * إنشاء تقرير الإصلاح
   */
  async generateFixReport() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportDir = path.join(process.cwd(), 'reports', 'structure-fix');
    
    const fixReport = {
      fix_info: {
        timestamp: timestamp,
        total_fixes: this.fixes.length,
        issues_resolved: this.issues.length,
        success_rate: this.stats.errors === 0 ? 100 : ((this.fixes.length / (this.fixes.length + this.stats.errors)) * 100).toFixed(1)
      },
      applied_fixes: this.fixes,
      remaining_issues: this.issues.filter(issue => !this.fixes.some(fix => fix.includes(issue.file))),
      next_steps: [
        'اختبار البنية الجديدة',
        'التبديل للبنية الجديدة',
        'إجراء اختبارات شاملة'
      ]
    };

    const reportPath = path.join(reportDir, `fix-report-${timestamp}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(fixReport, null, 2));

    console.log(`📊 تم إنشاء تقرير الإصلاح: ${reportPath}`);
  }

  /**
   * عرض ملخص الإصلاح
   */
  showFixSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 ملخص الإصلاح الشامل');
    console.log('='.repeat(60));
    console.log(`🔍 المشاكل المكتشفة: ${this.issues.length}`);
    console.log(`🔨 الإصلاحات المطبقة: ${this.fixes.length}`);
    console.log(`❌ الأخطاء: ${this.stats.errors}`);
    console.log(`📈 معدل النجاح: ${this.stats.errors === 0 ? 100 : ((this.fixes.length / (this.fixes.length + this.stats.errors)) * 100).toFixed(1)}%`);
    
    if (this.fixes.length > 0) {
      console.log('\n✅ الإصلاحات المطبقة:');
      this.fixes.forEach(fix => console.log(`  • ${fix}`));
    }
    
    console.log('\n🎯 الخطوات التالية:');
    console.log('1. اختبار البنية الجديدة: npm run switch:new && npm run dev');
    console.log('2. إصلاح أي مشاكل متبقية');
    console.log('3. التبديل النهائي للبنية الجديدة');
    console.log('='.repeat(60));
  }
}

/**
 * الدالة الرئيسية
 */
async function main() {
  try {
    const fixer = new StructureFixer();
    await fixer.executeComprehensiveFix();
  } catch (error) {
    console.error('💥 خطأ في نظام الإصلاح:', error);
    process.exit(1);
  }
}

// تشغيل النظام
main();
