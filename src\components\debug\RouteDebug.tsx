/**
 * Route Debug Component
 * Debug component to test theme routes
 * Phase 3: UI/UX Enhancement - Debug
 */

import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole } from '../../types';
import { useLocation, Link } from 'react-router-dom';

export const RouteDebug: React.FC = () => {
  const { user } = useAuth();
  const location = useLocation();

  const testRoutes = [
    {
      path: '/admin/themes',
      label: 'إدارة الثيمات (أدمن)',
      requiredRole: UserRole.ADMIN,
      userCanAccess: user?.role === UserRole.ADMIN,
    },
    {
      path: '/school/theme',
      label: 'ثيم المدرسة (مدير)',
      requiredRole: UserRole.SCHOOL_MANAGER,
      userCanAccess: user?.role === UserRole.SCHOOL_MANAGER,
    },
    {
      path: '/dashboard',
      label: 'لوحة التحكم',
      requiredRole: null,
      userCanAccess: true,
    },
  ];

  return (
    <div className="fixed bottom-4 left-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-sm z-50">
      <h3 className="font-bold text-sm mb-2">🛣️ Route Debug</h3>
      
      <div className="space-y-2 text-xs">
        <div>
          <strong>Current Path:</strong> {location.pathname}
        </div>
        
        <div>
          <strong>User Role:</strong> {user?.role || 'Not logged in'}
        </div>
        
        <div className="border-t pt-2">
          <strong>Test Routes:</strong>
          <div className="space-y-1 mt-1">
            {testRoutes.map((route) => (
              <div key={route.path} className="flex items-center justify-between">
                <Link
                  to={route.path}
                  className={`text-xs underline ${
                    route.userCanAccess ? 'text-blue-600' : 'text-gray-400'
                  }`}
                  onClick={(e) => {
                    if (!route.userCanAccess) {
                      e.preventDefault();
                      alert(`Access denied! Required role: ${route.requiredRole}`);
                    }
                  }}
                >
                  {route.label}
                </Link>
                <span className={route.userCanAccess ? 'text-green-600' : 'text-red-600'}>
                  {route.userCanAccess ? '✅' : '❌'}
                </span>
              </div>
            ))}
          </div>
        </div>
        
        <div className="border-t pt-2">
          <strong>Expected Behavior:</strong>
          <ul className="text-xs mt-1 space-y-1">
            <li>✅ Admin → /admin/themes should work</li>
            <li>✅ School Manager → /school/theme should work</li>
            <li>❌ Wrong role → redirect to /dashboard</li>
            <li>❌ No auth → redirect to /login</li>
          </ul>
        </div>
        
        <div className="border-t pt-2">
          <strong>Debug Info:</strong>
          <div className="text-xs">
            <div>UserRole.ADMIN: "{UserRole.ADMIN}"</div>
            <div>UserRole.SCHOOL_MANAGER: "{UserRole.SCHOOL_MANAGER}"</div>
            <div>Current user.role: "{user?.role}"</div>
          </div>
        </div>
      </div>
    </div>
  );
};
