import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Calendar,
  Clock,
  DollarSign,
  FileText,
  Plus,
  AlertTriangle,
  CheckCircle,
  Wrench,
  Filter,
  Search,
} from "lucide-react";
import { Button } from "../ui/Button";
import { MaintenanceModal } from "./MaintenanceModal";
import { supabase } from "../../lib/supabase";
import { useAuth } from "../../contexts/AuthContext";
import type { Tables } from "../../lib/api";

interface BusMaintenanceScheduleProps {
  busId?: string;
  busPlateNumber?: string;
  showAddButton?: boolean;
  className?: string;
}

interface MaintenanceRecord extends Tables<"bus_maintenance"> {
  bus?: Tables<"buses">;
}

export const BusMaintenanceSchedule: React.FC<BusMaintenanceScheduleProps> = ({
  busId,
  busPlateNumber,
  showAddButton = true,
  className = "",
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [records, setRecords] = useState<MaintenanceRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterType, setFilterType] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    fetchMaintenanceRecords();
  }, [busId, user?.tenant_id]);

  const fetchMaintenanceRecords = async () => {
    try {
      setLoading(true);
      let query = supabase
        .from("bus_maintenance")
        .select(
          `
          *,
          bus:buses(id, plate_number)
        `,
        )
        .order("scheduled_date", { ascending: false });

      if (busId) {
        query = query.eq("bus_id", busId);
      } else if (user?.tenant_id) {
        query = query.eq("tenant_id", user.tenant_id);
      } else {
        // If no tenant_id, don't fetch any records
        setRecords([]);
        return;
      }

      const { data, error } = await query;

      if (error) {
        console.error("Error fetching maintenance records:", error);
        setRecords([]);
        return;
      }
      setRecords(data || []);
    } catch (error) {
      console.error("Error fetching maintenance records:", error);
      setRecords([]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20";
      case "in_progress":
        return "text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20";
      case "overdue":
        return "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20";
      default:
        return "text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle size={16} />;
      case "in_progress":
        return <Clock size={16} />;
      case "overdue":
        return <AlertTriangle size={16} />;
      default:
        return <Calendar size={16} />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "routine":
        return <Wrench size={16} />;
      case "repair":
        return <AlertTriangle size={16} />;
      case "inspection":
        return <CheckCircle size={16} />;
      default:
        return <Wrench size={16} />;
    }
  };

  const isOverdue = (scheduledDate: string, status: string) => {
    if (status === "completed") return false;
    return new Date(scheduledDate) < new Date();
  };

  const filteredRecords = records
    .filter((record) => {
      if (filterStatus !== "all" && record.status !== filterStatus)
        return false;
      if (filterType !== "all" && record.type !== filterType) return false;
      if (searchQuery) {
        const searchLower = searchQuery.toLowerCase();
        return (
          record.description.toLowerCase().includes(searchLower) ||
          record.bus?.plate_number?.toLowerCase().includes(searchLower) ||
          record.notes?.toLowerCase().includes(searchLower)
        );
      }
      return true;
    })
    .map((record) => ({
      ...record,
      status: isOverdue(record.scheduled_date, record.status)
        ? "overdue"
        : record.status,
    }));

  const upcomingRecords = filteredRecords.filter(
    (r) => r.status === "scheduled" && new Date(r.scheduled_date) >= new Date(),
  );
  const overdueRecords = filteredRecords.filter((r) => r.status === "overdue");
  const completedRecords = filteredRecords.filter(
    (r) => r.status === "completed",
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {busId
              ? `${t("buses.maintenanceRecords")} - ${busPlateNumber}`
              : t("buses.maintenanceRecords")}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t("buses.maintenanceScheduleDescription")}
          </p>
        </div>
        {showAddButton && busId && (
          <Button
            onClick={() => setIsModalOpen(true)}
            leftIcon={<Plus size={16} />}
          >
            {t("buses.scheduleMaintenance")}
          </Button>
        )}
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
              <Calendar className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("buses.upcomingMaintenance")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {upcomingRecords.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
              <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("buses.overdueMaintenance")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {overdueRecords.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("buses.completedMaintenance")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {completedRecords.length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={16} className="text-gray-400" />
              </div>
              <input
                type="text"
                placeholder={t("common.search")}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Filter size={16} className="text-gray-400" />
              </div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="block pl-10 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="all">{t("common.all")} Status</option>
                <option value="scheduled">{t("buses.scheduled")}</option>
                <option value="in_progress">{t("buses.inProgress")}</option>
                <option value="completed">{t("buses.completed")}</option>
                <option value="overdue">{t("buses.overdue")}</option>
              </select>
            </div>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="block px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="all">{t("common.all")} Types</option>
              <option value="routine">{t("buses.routineMaintenance")}</option>
              <option value="repair">{t("buses.repair")}</option>
              <option value="inspection">{t("buses.inspection")}</option>
            </select>
          </div>
        </div>
      </div>

      {/* Maintenance Records */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t("buses.maintenanceRecords")} ({filteredRecords.length})
          </h3>
        </div>

        {filteredRecords.length === 0 ? (
          <div className="p-8 text-center text-gray-500 dark:text-gray-400">
            <Wrench size={48} className="mx-auto mb-4 opacity-50" />
            <p>{t("buses.noMaintenanceRecords")}</p>
            {showAddButton && busId && (
              <p className="text-sm mt-2">
                {t("buses.scheduleFirstMaintenance")}
              </p>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredRecords.map((record) => (
              <div key={record.id} className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
                      {getTypeIcon(record.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {t(`buses.${record.type}`)}
                        </h4>
                        <span
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                            record.status,
                          )}`}
                        >
                          {getStatusIcon(record.status)}
                          <span className="ml-1">
                            {t(`buses.${record.status}`)}
                          </span>
                        </span>
                      </div>
                      <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        {record.description}
                      </p>
                      {!busId && record.bus && (
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                          {t("buses.bus")}: {record.bus.plate_number}
                        </p>
                      )}
                      {record.notes && (
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                          {record.notes}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                      <Calendar size={14} />
                      <span>
                        {new Date(record.scheduled_date).toLocaleDateString()}
                      </span>
                    </div>
                    {record.cost && (
                      <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mt-1">
                        <DollarSign size={14} />
                        <span>${record.cost.toFixed(2)}</span>
                      </div>
                    )}
                    {record.completed_date && (
                      <div className="flex items-center space-x-2 text-xs text-gray-400 dark:text-gray-500 mt-1">
                        <CheckCircle size={12} />
                        <span>
                          {new Date(record.completed_date).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Maintenance Modal */}
      {isModalOpen && busId && (
        <MaintenanceModal
          isOpen={isModalOpen}
          onClose={(refreshData) => {
            setIsModalOpen(false);
            if (refreshData) {
              fetchMaintenanceRecords();
            }
          }}
          busId={busId}
          busPlateNumber={busPlateNumber || ""}
        />
      )}
    </div>
  );
};

export default BusMaintenanceSchedule;
