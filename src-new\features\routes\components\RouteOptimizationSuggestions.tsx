import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Route,
  TrendingUp,
  Clock,
  Fuel,
  MapPin,
  Eye,
  ThumbsUp,
  ThumbsDown,
  CheckCircle,
  XCircle,
  AlertCircle,
  Zap,
  BarChart3,
  Navigation,
  RefreshCw,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";

interface RouteMetrics {
  total_distance: number;
  estimated_time: number;
  fuel_consumption: number;
  student_count: number;
  delay_frequency: number;
}

interface RouteImprovements {
  distance_saved: number;
  time_saved: number;
  fuel_saved: number;
  efficiency_gain: number;
}

interface SuggestedChanges {
  route_adjustments?: string;
  stop_reordering?: number[];
  timing_changes?: string;
  capacity_optimization?: string;
}

interface RouteOptimization {
  id: string;
  route_id: string;
  tenant_id: string;
  optimization_type:
    | "distance"
    | "time"
    | "fuel"
    | "capacity"
    | "delay_reduction";
  current_metrics: RouteMetrics;
  suggested_metrics: RouteMetrics;
  improvements: RouteImprovements;
  suggested_changes: SuggestedChanges;
  confidence_score: number;
  status: "pending" | "approved" | "rejected" | "implemented";
  created_at: string;
  updated_at: string;
}

interface RouteOptimizationSuggestionsProps {
  selectedRoute?: string;
  onOptimizationApplied?: (routeId: string) => void;
}

export const RouteOptimizationSuggestions: React.FC<
  RouteOptimizationSuggestionsProps
> = ({ selectedRoute, onOptimizationApplied }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { routes } = useDatabase();
  const [optimizations, setOptimizations] = useState<RouteOptimization[]>([]);
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [selectedOptimization, setSelectedOptimization] =
    useState<RouteOptimization | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  const currentRoute = routes.find((r) => r.id === selectedRoute);

  // Fetch existing optimizations
  const fetchOptimizations = async () => {
    if (!selectedRoute || !user?.tenant_id) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("route_optimizations")
        .select("*")
        .eq("route_id", selectedRoute)
        .eq("tenant_id", user.tenant_id)
        .order("created_at", { ascending: false });

      if (error) throw error;
      setOptimizations(data || []);
    } catch (error) {
      console.error("Error fetching optimizations:", error);
    } finally {
      setLoading(false);
    }
  };

  // Generate new optimization suggestions
  const generateOptimizations = async () => {
    if (!selectedRoute || !user?.tenant_id || !currentRoute) return;

    setGenerating(true);
    try {
      // Simulate AI-powered route optimization analysis
      const mockOptimizations: Omit<
        RouteOptimization,
        "id" | "created_at" | "updated_at"
      >[] = [
        {
          route_id: selectedRoute,
          tenant_id: user.tenant_id,
          optimization_type: "distance",
          current_metrics: {
            total_distance: 45.2,
            estimated_time: 85,
            fuel_consumption: 12.5,
            student_count: currentRoute.stops?.length || 0,
            delay_frequency: 15.3,
          },
          suggested_metrics: {
            total_distance: 38.7,
            estimated_time: 72,
            fuel_consumption: 10.8,
            student_count: currentRoute.stops?.length || 0,
            delay_frequency: 12.1,
          },
          improvements: {
            distance_saved: 6.5,
            time_saved: 13,
            fuel_saved: 1.7,
            efficiency_gain: 14.4,
          },
          suggested_changes: {
            route_adjustments:
              "Reorder stops to minimize backtracking. Combine nearby pickup points to reduce total distance by 14.4%.",
            stop_reordering: [1, 3, 2, 5, 4, 7, 6, 8],
            timing_changes:
              "Adjust departure time by 10 minutes earlier to avoid peak traffic.",
          },
          confidence_score: 87,
          status: "pending",
        },
        {
          route_id: selectedRoute,
          tenant_id: user.tenant_id,
          optimization_type: "time",
          current_metrics: {
            total_distance: 45.2,
            estimated_time: 85,
            fuel_consumption: 12.5,
            student_count: currentRoute.stops?.length || 0,
            delay_frequency: 15.3,
          },
          suggested_metrics: {
            total_distance: 46.1,
            estimated_time: 68,
            fuel_consumption: 12.8,
            student_count: currentRoute.stops?.length || 0,
            delay_frequency: 8.7,
          },
          improvements: {
            distance_saved: -0.9,
            time_saved: 17,
            fuel_saved: -0.3,
            efficiency_gain: 20.0,
          },
          suggested_changes: {
            route_adjustments:
              "Use main roads during peak hours to reduce delays, even if distance increases slightly.",
            timing_changes:
              "Start route 15 minutes earlier and use highway segments to avoid school zone traffic.",
          },
          confidence_score: 92,
          status: "pending",
        },
        {
          route_id: selectedRoute,
          tenant_id: user.tenant_id,
          optimization_type: "delay_reduction",
          current_metrics: {
            total_distance: 45.2,
            estimated_time: 85,
            fuel_consumption: 12.5,
            student_count: currentRoute.stops?.length || 0,
            delay_frequency: 15.3,
          },
          suggested_metrics: {
            total_distance: 44.8,
            estimated_time: 79,
            fuel_consumption: 12.2,
            student_count: currentRoute.stops?.length || 0,
            delay_frequency: 6.2,
          },
          improvements: {
            distance_saved: 0.4,
            time_saved: 6,
            fuel_saved: 0.3,
            efficiency_gain: 59.5,
          },
          suggested_changes: {
            route_adjustments:
              "Add buffer time at high-delay stops and optimize pickup sequence based on historical delay patterns.",
            timing_changes:
              "Implement dynamic scheduling with 5-minute buffers at problematic intersections.",
          },
          confidence_score: 78,
          status: "pending",
        },
      ];

      // Insert optimizations into database
      for (const optimization of mockOptimizations) {
        const { error } = await supabase.from("route_optimizations").insert({
          ...optimization,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

        if (error) {
          console.error("Error inserting optimization:", error);
        }
      }

      // Refresh the list
      await fetchOptimizations();
    } catch (error) {
      console.error("Error generating optimizations:", error);
    } finally {
      setGenerating(false);
    }
  };

  // Update optimization status
  const updateOptimizationStatus = async (
    optimizationId: string,
    status: string,
  ) => {
    try {
      const { error } = await supabase
        .from("route_optimizations")
        .update({
          status,
          updated_at: new Date().toISOString(),
        })
        .eq("id", optimizationId);

      if (error) throw error;

      // Update local state
      setOptimizations((prev) =>
        prev.map((opt) =>
          opt.id === optimizationId ? { ...opt, status: status as any } : opt,
        ),
      );

      // Notify parent component if optimization was approved
      if (status === "approved" && onOptimizationApplied && selectedRoute) {
        onOptimizationApplied(selectedRoute);
      }
    } catch (error) {
      console.error("Error updating optimization status:", error);
    }
  };

  // Helper functions for UI
  const getOptimizationIcon = (type: string) => {
    switch (type) {
      case "distance":
        return <Navigation size={20} />;
      case "time":
        return <Clock size={20} />;
      case "fuel":
        return <Fuel size={20} />;
      case "capacity":
        return <BarChart3 size={20} />;
      case "delay_reduction":
        return <Zap size={20} />;
      default:
        return <TrendingUp size={20} />;
    }
  };

  const getOptimizationColor = (type: string) => {
    switch (type) {
      case "distance":
        return "bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400";
      case "time":
        return "bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400";
      case "fuel":
        return "bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "capacity":
        return "bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400";
      case "delay_reduction":
        return "bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400";
      default:
        return "bg-gray-100 text-gray-600 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircle size={16} className="text-green-500" />;
      case "rejected":
        return <XCircle size={16} className="text-red-500" />;
      case "implemented":
        return <CheckCircle size={16} className="text-blue-500" />;
      default:
        return <AlertCircle size={16} className="text-yellow-500" />;
    }
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 80) return "text-green-600 dark:text-green-400";
    if (score >= 60) return "text-yellow-600 dark:text-yellow-400";
    return "text-red-600 dark:text-red-400";
  };

  useEffect(() => {
    if (selectedRoute) {
      fetchOptimizations();
    }
  }, [selectedRoute]);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Route Optimization Suggestions
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              AI-powered suggestions to improve route efficiency
            </p>
          </div>

          {selectedRoute && (
            <div className="flex space-x-3">
              <Button
                onClick={fetchOptimizations}
                variant="outline"
                size="sm"
                leftIcon={<RefreshCw size={16} />}
                disabled={loading}
              >
                Refresh
              </Button>
              <Button
                onClick={generateOptimizations}
                size="sm"
                leftIcon={<TrendingUp size={16} />}
                disabled={generating || loading}
                className="bg-primary-600 hover:bg-primary-700 text-white"
              >
                {generating ? "Analyzing..." : "Generate Suggestions"}
              </Button>
            </div>
          )}
        </div>

        {!selectedRoute ? (
          <div className="text-center py-12">
            <Route size={48} className="mx-auto mb-4 text-gray-300" />
            <p className="text-gray-500 dark:text-gray-400 mb-2">
              Select a route to view optimization suggestions
            </p>
            <p className="text-sm text-gray-400 dark:text-gray-500">
              Choose a route from the list to analyze and get AI-powered
              improvement recommendations.
            </p>
          </div>
        ) : (
          <>
            {/* Current Route Info */}
            {currentRoute && (
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-6">
                <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-2">
                  Current Route: {currentRoute.name}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="text-blue-700 dark:text-blue-200 font-medium">
                      Total Stops
                    </p>
                    <p className="text-blue-900 dark:text-blue-100">
                      {currentRoute.stops?.length || 0}
                    </p>
                  </div>
                  <div>
                    <p className="text-blue-700 dark:text-blue-200 font-medium">
                      Estimated Distance
                    </p>
                    <p className="text-blue-900 dark:text-blue-100">~45 km</p>
                  </div>
                  <div>
                    <p className="text-blue-700 dark:text-blue-200 font-medium">
                      Estimated Time
                    </p>
                    <p className="text-blue-900 dark:text-blue-100">~85 min</p>
                  </div>
                  <div>
                    <p className="text-blue-700 dark:text-blue-200 font-medium">
                      Status
                    </p>
                    <p className="text-blue-900 dark:text-blue-100">
                      {currentRoute.is_active ? "Active" : "Inactive"}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Optimization Cards */}
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
              </div>
            ) : optimizations.length === 0 ? (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-8 text-center">
                <Route size={48} className="mx-auto mb-4 text-gray-300" />
                <p className="text-gray-500 dark:text-gray-400 mb-2">
                  No optimization suggestions found.
                </p>
                <p className="text-sm text-gray-400 dark:text-gray-500">
                  Click "Generate Suggestions" to analyze this route for
                  potential improvements.
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {optimizations.map((optimization) => (
                  <div
                    key={optimization.id}
                    className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700"
                  >
                    <div className="p-6">
                      {/* Header */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div
                            className={`p-2 rounded-lg ${getOptimizationColor(optimization.optimization_type)}`}
                          >
                            {getOptimizationIcon(
                              optimization.optimization_type,
                            )}
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white capitalize">
                              {optimization.optimization_type.replace("_", " ")}{" "}
                              Optimization
                            </h3>
                            <div className="flex items-center space-x-2">
                              {getStatusIcon(optimization.status)}
                              <span className="text-sm text-gray-500 dark:text-gray-400 capitalize">
                                {optimization.status}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div
                            className={`text-sm font-medium ${getConfidenceColor(optimization.confidence_score)}`}
                          >
                            {optimization.confidence_score}% Confidence
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {new Date(
                              optimization.created_at,
                            ).toLocaleDateString()}
                          </div>
                        </div>
                      </div>

                      {/* Metrics Comparison */}
                      <div className="space-y-3 mb-4">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            Distance
                          </span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-500">
                              {optimization.current_metrics.total_distance.toFixed(
                                1,
                              )}{" "}
                              km
                            </span>
                            <span className="text-sm text-gray-400">→</span>
                            <span className="text-sm font-medium text-gray-900 dark:text-white">
                              {optimization.suggested_metrics.total_distance.toFixed(
                                1,
                              )}{" "}
                              km
                            </span>
                            {optimization.improvements.distance_saved !== 0 && (
                              <span
                                className={`text-xs px-2 py-1 rounded-full ${
                                  optimization.improvements.distance_saved > 0
                                    ? "text-green-700 bg-green-100"
                                    : "text-red-700 bg-red-100"
                                }`}
                              >
                                {optimization.improvements.distance_saved > 0
                                  ? "-"
                                  : "+"}
                                {Math.abs(
                                  optimization.improvements.distance_saved,
                                ).toFixed(1)}{" "}
                                km
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            Time
                          </span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-500">
                              {optimization.current_metrics.estimated_time} min
                            </span>
                            <span className="text-sm text-gray-400">→</span>
                            <span className="text-sm font-medium text-gray-900 dark:text-white">
                              {optimization.suggested_metrics.estimated_time}{" "}
                              min
                            </span>
                            {optimization.improvements.time_saved !== 0 && (
                              <span
                                className={`text-xs px-2 py-1 rounded-full ${
                                  optimization.improvements.time_saved > 0
                                    ? "text-green-700 bg-green-100"
                                    : "text-red-700 bg-red-100"
                                }`}
                              >
                                {optimization.improvements.time_saved > 0
                                  ? "-"
                                  : "+"}
                                {Math.abs(optimization.improvements.time_saved)}{" "}
                                min
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            Fuel
                          </span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-500">
                              {optimization.current_metrics.fuel_consumption.toFixed(
                                1,
                              )}{" "}
                              L
                            </span>
                            <span className="text-sm text-gray-400">→</span>
                            <span className="text-sm font-medium text-gray-900 dark:text-white">
                              {optimization.suggested_metrics.fuel_consumption.toFixed(
                                1,
                              )}{" "}
                              L
                            </span>
                            {optimization.improvements.fuel_saved !== 0 && (
                              <span
                                className={`text-xs px-2 py-1 rounded-full ${
                                  optimization.improvements.fuel_saved > 0
                                    ? "text-green-700 bg-green-100"
                                    : "text-red-700 bg-red-100"
                                }`}
                              >
                                {optimization.improvements.fuel_saved > 0
                                  ? "-"
                                  : "+"}
                                {Math.abs(
                                  optimization.improvements.fuel_saved,
                                ).toFixed(1)}{" "}
                                L
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Efficiency Gain */}
                      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            Overall Efficiency Gain
                          </span>
                          <span
                            className={`text-lg font-bold ${
                              optimization.improvements.efficiency_gain > 0
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {optimization.improvements.efficiency_gain > 0
                              ? "+"
                              : ""}
                            {optimization.improvements.efficiency_gain.toFixed(
                              1,
                            )}
                            %
                          </span>
                        </div>
                      </div>

                      {/* Suggested Changes */}
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Suggested Changes
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {optimization.suggested_changes.route_adjustments ||
                            "No specific adjustments suggested."}
                        </p>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-600">
                        <Button
                          onClick={() => {
                            setSelectedOptimization(optimization);
                            setShowDetailModal(true);
                          }}
                          variant="outline"
                          size="sm"
                          leftIcon={<Eye size={14} />}
                        >
                          View Details
                        </Button>

                        {optimization.status === "pending" && (
                          <div className="flex space-x-2">
                            <Button
                              onClick={() =>
                                updateOptimizationStatus(
                                  optimization.id,
                                  "rejected",
                                )
                              }
                              variant="outline"
                              size="sm"
                              leftIcon={<ThumbsDown size={14} />}
                              className="text-red-600 border-red-300 hover:bg-red-50"
                            >
                              Reject
                            </Button>
                            <Button
                              onClick={() =>
                                updateOptimizationStatus(
                                  optimization.id,
                                  "approved",
                                )
                              }
                              size="sm"
                              leftIcon={<ThumbsUp size={14} />}
                              className="bg-green-600 hover:bg-green-700 text-white"
                            >
                              Approve
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}

        {/* Detail Modal */}
        {showDetailModal && selectedOptimization && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {selectedOptimization.optimization_type
                      .charAt(0)
                      .toUpperCase() +
                      selectedOptimization.optimization_type
                        .slice(1)
                        .replace("_", " ")}{" "}
                    Optimization Details
                  </h3>
                  <Button
                    onClick={() => setShowDetailModal(false)}
                    variant="outline"
                    size="sm"
                  >
                    Close
                  </Button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Current Metrics */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                      Current Route Metrics
                    </h4>
                    <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Total Distance:
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {selectedOptimization.current_metrics.total_distance.toFixed(
                            1,
                          )}{" "}
                          km
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Estimated Time:
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {selectedOptimization.current_metrics.estimated_time}{" "}
                          minutes
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Fuel Consumption:
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {selectedOptimization.current_metrics.fuel_consumption.toFixed(
                            1,
                          )}{" "}
                          L
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Student Count:
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {selectedOptimization.current_metrics.student_count}{" "}
                          students
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Delay Frequency:
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {selectedOptimization.current_metrics.delay_frequency}
                          % of trips
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Suggested Metrics */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                      Optimized Route Metrics
                    </h4>
                    <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Total Distance:
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {selectedOptimization.suggested_metrics.total_distance.toFixed(
                            1,
                          )}{" "}
                          km
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Estimated Time:
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {
                            selectedOptimization.suggested_metrics
                              .estimated_time
                          }{" "}
                          minutes
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Fuel Consumption:
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {selectedOptimization.suggested_metrics.fuel_consumption.toFixed(
                            1,
                          )}{" "}
                          L
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Student Count:
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {selectedOptimization.suggested_metrics.student_count}{" "}
                          students
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Delay Frequency:
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {
                            selectedOptimization.suggested_metrics
                              .delay_frequency
                          }
                          % of trips
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Improvements Summary */}
                <div className="mt-6">
                  <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Expected Improvements
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div
                        className={`text-2xl font-bold ${
                          selectedOptimization.improvements.distance_saved > 0
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {selectedOptimization.improvements.distance_saved > 0
                          ? "-"
                          : "+"}
                        {Math.abs(
                          selectedOptimization.improvements.distance_saved,
                        ).toFixed(1)}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        km saved
                      </div>
                    </div>
                    <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div
                        className={`text-2xl font-bold ${
                          selectedOptimization.improvements.time_saved > 0
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {selectedOptimization.improvements.time_saved > 0
                          ? "-"
                          : "+"}
                        {Math.abs(selectedOptimization.improvements.time_saved)}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        min saved
                      </div>
                    </div>
                    <div className="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                      <div
                        className={`text-2xl font-bold ${
                          selectedOptimization.improvements.fuel_saved > 0
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {selectedOptimization.improvements.fuel_saved > 0
                          ? "-"
                          : "+"}
                        {Math.abs(
                          selectedOptimization.improvements.fuel_saved,
                        ).toFixed(1)}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        L saved
                      </div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <div
                        className={`text-2xl font-bold ${
                          selectedOptimization.improvements.efficiency_gain > 0
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {selectedOptimization.improvements.efficiency_gain > 0
                          ? "+"
                          : ""}
                        {selectedOptimization.improvements.efficiency_gain.toFixed(
                          1,
                        )}
                        %
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        efficiency
                      </div>
                    </div>
                  </div>
                </div>

                {/* Detailed Changes */}
                <div className="mt-6">
                  <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Detailed Changes
                  </h4>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      {selectedOptimization.suggested_changes
                        .route_adjustments || "No detailed changes available."}
                    </p>

                    {selectedOptimization.suggested_changes.stop_reordering && (
                      <div className="mt-4">
                        <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Suggested Stop Order:
                        </h5>
                        <div className="flex flex-wrap gap-2">
                          {selectedOptimization.suggested_changes.stop_reordering.map(
                            (stopIndex, index) => (
                              <span
                                key={index}
                                className="px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded text-xs"
                              >
                                Stop {stopIndex}
                              </span>
                            ),
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                {selectedOptimization.status === "pending" && (
                  <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
                    <Button
                      onClick={() => {
                        updateOptimizationStatus(
                          selectedOptimization.id,
                          "rejected",
                        );
                        setShowDetailModal(false);
                      }}
                      variant="outline"
                      leftIcon={<ThumbsDown size={16} />}
                      className="text-red-600 border-red-300 hover:bg-red-50"
                    >
                      Reject Optimization
                    </Button>
                    <Button
                      onClick={() => {
                        updateOptimizationStatus(
                          selectedOptimization.id,
                          "approved",
                        );
                        setShowDetailModal(false);
                      }}
                      leftIcon={<ThumbsUp size={16} />}
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      Approve & Implement
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RouteOptimizationSuggestions;
