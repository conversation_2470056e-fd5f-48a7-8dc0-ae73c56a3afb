import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  X,
  Calendar,
  DollarSign,
  FileText,
  Wrench,
  User,
  Clock,
} from "lucide-react";
import { Button } from "../ui/Button";
import { createMaintenanceRecord, getSpareParts } from "../../lib/api";
import { useAuth } from "../../contexts/AuthContext";

interface MaintenanceModalProps {
  isOpen: boolean;
  onClose: (refreshData?: boolean) => void;
  busId: string;
  busPlateNumber: string;
}

interface SparePart {
  id: string;
  name: string;
  part_number: string | null;
  quantity: number;
  cost: number | null;
}

export const MaintenanceModal: React.FC<MaintenanceModalProps> = ({
  isOpen,
  onClose,
  busId,
  busPlateNumber,
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [spareParts, setSpareParts] = useState<SparePart[]>([]);
  const [selectedParts, setSelectedParts] = useState<
    { id: string; quantity: number; name: string }[]
  >([]);
  const [formData, setFormData] = useState({
    type: "routine" as "routine" | "repair" | "inspection",
    description: "",
    scheduledDate: "",
    cost: "",
    notes: "",
    technicianName: "",
    nextMaintenanceDate: "",
    isRecurring: false,
    recurringInterval: "30", // days
  });
  const [error, setError] = useState("");

  useEffect(() => {
    if (isOpen && user?.tenant_id) {
      loadSpareParts();
    }
  }, [isOpen, user?.tenant_id]);

  const loadSpareParts = async () => {
    try {
      if (user?.tenant_id) {
        const parts = await getSpareParts(user.tenant_id);
        setSpareParts(parts || []);
      }
    } catch (err) {
      console.error("Error loading spare parts:", err);
    }
  };

  const handlePartSelection = (
    partId: string,
    isSelected: boolean,
    partName: string,
  ) => {
    if (isSelected) {
      setSelectedParts([
        ...selectedParts,
        { id: partId, quantity: 1, name: partName },
      ]);
    } else {
      setSelectedParts(selectedParts.filter((part) => part.id !== partId));
    }
  };

  const updatePartQuantity = (partId: string, quantity: number) => {
    setSelectedParts(
      selectedParts.map((part) =>
        part.id === partId ? { ...part, quantity } : part,
      ),
    );
  };

  const calculateTotalCost = () => {
    let baseCost = formData.cost ? parseFloat(formData.cost) : 0;

    // Add cost of selected parts
    const partsCost = selectedParts.reduce((total, selectedPart) => {
      const part = spareParts.find((p) => p.id === selectedPart.id);
      return total + (part?.cost || 0) * selectedPart.quantity;
    }, 0);

    return baseCost + partsCost;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.description || !formData.scheduledDate) {
      setError(t("common.requiredFieldsMissing"));
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      // Calculate next maintenance date if recurring
      let nextMaintenanceDate = formData.nextMaintenanceDate;
      if (formData.isRecurring && !nextMaintenanceDate) {
        const scheduledDate = new Date(formData.scheduledDate);
        scheduledDate.setDate(
          scheduledDate.getDate() + parseInt(formData.recurringInterval),
        );
        nextMaintenanceDate = scheduledDate.toISOString().split("T")[0];
      }

      await createMaintenanceRecord(
        busId,
        formData.type,
        formData.description,
        formData.scheduledDate,
        user?.tenant_id || "", // tenant_id
        calculateTotalCost(),
        formData.notes || undefined,
        formData.technicianName || undefined,
        nextMaintenanceDate || undefined,
        selectedParts.map((part) => ({
          partId: part.id,
          quantityUsed: part.quantity,
        })),
      );

      onClose(true);

      // Reset form
      setFormData({
        type: "routine",
        description: "",
        scheduledDate: "",
        cost: "",
        notes: "",
        technicianName: "",
        nextMaintenanceDate: "",
        isRecurring: false,
        recurringInterval: "30",
      });
      setSelectedParts([]);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : t("buses.maintenanceScheduleError"),
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 overflow-y-auto">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-2xl my-8">
        <div className="flex justify-between items-center border-b border-gray-200 dark:border-gray-700 p-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t("buses.scheduleMaintenance")}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onClose()}
            aria-label="Close"
          >
            <X size={20} />
          </Button>
        </div>

        <form
          onSubmit={handleSubmit}
          className="p-4 max-h-[80vh] overflow-y-auto"
        >
          {error && (
            <div className="bg-error-50 dark:bg-error-900/20 border border-error-200 dark:border-error-800 rounded-md p-3 mb-4">
              <p className="text-sm text-error-600 dark:text-error-400">
                {error}
              </p>
            </div>
          )}

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t("buses.busPlateNumber")}
              </label>
              <p className="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 p-2 rounded">
                {busPlateNumber}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="type"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {t("buses.maintenanceType")}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Wrench size={16} className="text-gray-400" />
                  </div>
                  <select
                    id="type"
                    value={formData.type}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        type: e.target.value as any,
                      }))
                    }
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    required
                  >
                    <option value="routine">
                      {t("buses.routineMaintenance")}
                    </option>
                    <option value="repair">{t("buses.repair")}</option>
                    <option value="inspection">{t("buses.inspection")}</option>
                  </select>
                </div>
              </div>

              <div>
                <label
                  htmlFor="technicianName"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {t("buses.technicianName")}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User size={16} className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    id="technicianName"
                    value={formData.technicianName}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        technicianName: e.target.value,
                      }))
                    }
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    placeholder={t("buses.technicianNamePlaceholder")}
                  />
                </div>
              </div>
            </div>

            <div>
              <label
                htmlFor="description"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                {t("common.description")}
              </label>
              <div className="relative">
                <div className="absolute top-3 left-3 pointer-events-none">
                  <FileText size={16} className="text-gray-400" />
                </div>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  rows={3}
                  placeholder={t("buses.maintenanceDescription")}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="scheduledDate"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {t("buses.scheduledDate")}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Calendar size={16} className="text-gray-400" />
                  </div>
                  <input
                    type="date"
                    id="scheduledDate"
                    value={formData.scheduledDate}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        scheduledDate: e.target.value,
                      }))
                    }
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    min={new Date().toISOString().split("T")[0]}
                    required
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="cost"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {t("buses.laborCost")} ({t("common.optional")})
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <DollarSign size={16} className="text-gray-400" />
                  </div>
                  <input
                    type="number"
                    id="cost"
                    value={formData.cost}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, cost: e.target.value }))
                    }
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isRecurring"
                checked={formData.isRecurring}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    isRecurring: e.target.checked,
                  }))
                }
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label
                htmlFor="isRecurring"
                className="text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                {t("buses.recurringMaintenance")}
              </label>
            </div>

            {formData.isRecurring && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="recurringInterval"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    {t("buses.recurringInterval")} ({t("buses.days")})
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Clock size={16} className="text-gray-400" />
                    </div>
                    <input
                      type="number"
                      id="recurringInterval"
                      value={formData.recurringInterval}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          recurringInterval: e.target.value,
                        }))
                      }
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      min="1"
                      required={formData.isRecurring}
                    />
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="nextMaintenanceDate"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    {t("buses.nextMaintenanceDate")} ({t("common.optional")})
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Calendar size={16} className="text-gray-400" />
                    </div>
                    <input
                      type="date"
                      id="nextMaintenanceDate"
                      value={formData.nextMaintenanceDate}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          nextMaintenanceDate: e.target.value,
                        }))
                      }
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      min={formData.scheduledDate}
                    />
                  </div>
                </div>
              </div>
            )}

            <div>
              <label
                htmlFor="notes"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                {t("common.notes")} ({t("common.optional")})
              </label>
              <textarea
                id="notes"
                value={formData.notes}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, notes: e.target.value }))
                }
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                rows={2}
                placeholder={t("buses.additionalNotes")}
              />
            </div>

            {/* Spare Parts Section */}
            <div>
              <h3 className="text-md font-medium text-gray-900 dark:text-white mb-2 flex items-center">
                <Wrench size={16} className="mr-2" />
                {t("buses.spareParts")}
              </h3>

              {spareParts.length > 0 ? (
                <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          {t("common.select")}
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          {t("buses.partName")}
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          {t("buses.partNumber")}
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          {t("buses.quantity")}
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          {t("buses.cost")}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                      {spareParts.map((part) => {
                        const isSelected = selectedParts.some(
                          (p) => p.id === part.id,
                        );
                        const selectedPart = selectedParts.find(
                          (p) => p.id === part.id,
                        );

                        return (
                          <tr key={part.id}>
                            <td className="px-4 py-2">
                              <input
                                type="checkbox"
                                checked={isSelected}
                                onChange={(e) =>
                                  handlePartSelection(
                                    part.id,
                                    e.target.checked,
                                    part.name,
                                  )
                                }
                                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                              />
                            </td>
                            <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">
                              {part.name}
                            </td>
                            <td className="px-4 py-2 text-sm text-gray-500 dark:text-gray-300">
                              {part.part_number || "-"}
                            </td>
                            <td className="px-4 py-2">
                              {isSelected ? (
                                <input
                                  type="number"
                                  min="1"
                                  max={part.quantity}
                                  value={selectedPart?.quantity || 1}
                                  onChange={(e) =>
                                    updatePartQuantity(
                                      part.id,
                                      parseInt(e.target.value),
                                    )
                                  }
                                  className="w-16 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                                />
                              ) : (
                                <span className="text-sm text-gray-500 dark:text-gray-300">
                                  {part.quantity}
                                </span>
                              )}
                            </td>
                            <td className="px-4 py-2 text-sm text-gray-500 dark:text-gray-300">
                              {part.cost ? `${part.cost.toFixed(2)}` : "-"}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t("buses.noSpareParts")}
                </p>
              )}

              {selectedParts.length > 0 && (
                <div className="mt-2 text-sm text-gray-700 dark:text-gray-300">
                  <p className="font-medium">{t("buses.selectedParts")}:</p>
                  <ul className="list-disc pl-5 mt-1">
                    {selectedParts.map((part) => (
                      <li key={part.id}>
                        {part.name} × {part.quantity}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Total Cost Display */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("buses.totalCost")}:
                </span>
                <span className="text-lg font-semibold text-gray-900 dark:text-white">
                  ${calculateTotalCost().toFixed(2)}
                </span>
              </div>
            </div>
          </div>

          <div className="mt-6 flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => onClose()}
              disabled={isLoading}
            >
              {t("common.cancel")}
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin mr-2 h-4 w-4 border-b-2 border-white rounded-full"></div>
                  {t("common.scheduling")}
                </div>
              ) : (
                t("buses.scheduleMaintenance")
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
