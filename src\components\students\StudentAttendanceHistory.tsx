import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Calendar,
  Clock,
  MapPin,
  User,
  TrendingUp,
  Filter,
  Download,
} from "lucide-react";
import { supabase } from "../../lib/supabase";
import { Button } from "../ui/Button";
import type { Tables } from "../../lib/api";

interface StudentAttendanceHistoryProps {
  studentId: string;
  studentName?: string;
}

interface AttendanceRecord extends Tables<"attendance"> {
  bus?: Tables<"buses">;
}

interface AttendanceStats {
  totalDays: number;
  presentDays: number;
  attendanceRate: number;
  pickupCount: number;
  dropoffCount: number;
}

export const StudentAttendanceHistory: React.FC<
  StudentAttendanceHistoryProps
> = ({ studentId, studentName = "Student" }) => {
  const { t } = useTranslation();
  const [records, setRecords] = useState<AttendanceRecord[]>([]);
  const [stats, setStats] = useState<AttendanceStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0], // 30 days ago
    end: new Date().toISOString().split("T")[0], // today
  });
  const [filterType, setFilterType] = useState<"all" | "pickup" | "dropoff">(
    "all",
  );

  useEffect(() => {
    fetchAttendanceHistory();
  }, [studentId, dateRange]);

  const fetchAttendanceHistory = async () => {
    try {
      setLoading(true);

      const startDate = new Date(dateRange.start);
      startDate.setHours(0, 0, 0, 0);

      const endDate = new Date(dateRange.end);
      endDate.setHours(23, 59, 59, 999);

      const { data, error } = await supabase
        .from("attendance")
        .select(
          `
          *,
          bus:buses(id, plate_number)
        `,
        )
        .eq("student_id", studentId)
        .gte("recorded_at", startDate.toISOString())
        .lte("recorded_at", endDate.toISOString())
        .order("recorded_at", { ascending: false });

      if (error) throw error;

      setRecords(data || []);
      calculateStats(data || []);
    } catch (error) {
      console.error("Error fetching attendance history:", error);
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (attendanceRecords: AttendanceRecord[]) => {
    const pickupCount = attendanceRecords.filter(
      (r) => r.type === "pickup",
    ).length;
    const dropoffCount = attendanceRecords.filter(
      (r) => r.type === "dropoff",
    ).length;

    // Calculate unique days with attendance
    const uniqueDays = new Set(
      attendanceRecords.map((r) => new Date(r.recorded_at).toDateString()),
    ).size;

    // Calculate total school days in the date range
    const start = new Date(dateRange.start);
    const end = new Date(dateRange.end);
    const totalDays =
      Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    // Filter out weekends (assuming school days are Mon-Fri)
    let schoolDays = 0;
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dayOfWeek = d.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        // Not Sunday or Saturday
        schoolDays++;
      }
    }

    const attendanceRate = schoolDays > 0 ? (uniqueDays / schoolDays) * 100 : 0;

    setStats({
      totalDays: schoolDays,
      presentDays: uniqueDays,
      attendanceRate,
      pickupCount,
      dropoffCount,
    });
  };

  const filteredRecords = records.filter((record) => {
    if (filterType === "all") return true;
    return record.type === filterType;
  });

  const exportToCSV = () => {
    const headers = [
      "Date",
      "Student Name",
      "Grade",
      "Pickup Time",
      "Dropoff Time",
      "Status",
    ];
    const csvContent = [
      headers.join(","),
      ...filteredRecords.map((record) =>
        [
          new Date(record.recorded_at).toLocaleDateString(),
          record.student_name || "N/A",
          record.grade || "N/A",
          record.pickup_time || "N/A",
          record.dropoff_time || "N/A",
          record.status || "N/A",
        ].join(","),
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${studentName}_attendance_${dateRange.start}_to_${dateRange.end}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t("students.attendanceHistory")} - {studentName}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t("students.attendanceHistoryDescription")}
          </p>
        </div>
        <Button
          onClick={exportToCSV}
          variant="outline"
          size="sm"
          leftIcon={<Download size={16} />}
          disabled={filteredRecords.length === 0}
        >
          {t("common.export")}
        </Button>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("common.startDate")}
            </label>
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) =>
                setDateRange((prev) => ({ ...prev, start: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("common.endDate")}
            </label>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) =>
                setDateRange((prev) => ({ ...prev, end: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("common.type")}
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Filter size={16} className="text-gray-400" />
              </div>
              <select
                value={filterType}
                onChange={(e) =>
                  setFilterType(e.target.value as "all" | "pickup" | "dropoff")
                }
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="all">{t("common.all")}</option>
                <option value="pickup">{t("students.pickup")}</option>
                <option value="dropoff">{t("students.dropoff")}</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-primary-100 dark:bg-primary-800/20 rounded-lg">
                <Calendar className="h-6 w-6 text-primary-600 dark:text-primary-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("students.schoolDays")}
                </p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {stats.totalDays}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-accent-100 dark:bg-accent-800/20 rounded-lg">
                <User className="h-6 w-6 text-accent-600 dark:text-accent-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("students.presentDays")}
                </p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {stats.presentDays}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-secondary-100 dark:bg-secondary-800/20 rounded-lg">
                <TrendingUp className="h-6 w-6 text-secondary-600 dark:text-secondary-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("students.attendanceRate")}
                </p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {stats.attendanceRate.toFixed(1)}%
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-warning-100 dark:bg-warning-800/20 rounded-lg">
                <Clock className="h-6 w-6 text-warning-600 dark:text-warning-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("students.totalTrips")}
                </p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {stats.pickupCount + stats.dropoffCount}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Attendance Records */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t("students.attendanceRecords")} ({filteredRecords.length})
          </h3>
        </div>

        <div className="divide-y divide-gray-200 dark:divide-gray-700 max-h-96 overflow-y-auto">
          {filteredRecords.length > 0 ? (
            filteredRecords.map((record) => (
              <div key={record.id} className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div
                      className={`p-2 rounded-full ${
                        record.type === "pickup"
                          ? "bg-accent-100 text-accent-700 dark:bg-accent-900/20 dark:text-accent-400"
                          : "bg-secondary-100 text-secondary-700 dark:bg-secondary-900/20 dark:text-secondary-400"
                      }`}
                    >
                      <User size={16} />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {record.type === "pickup"
                          ? t("students.pickup")
                          : t("students.dropoff")}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {record.bus?.plate_number || "Unknown Bus"}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                      <Calendar size={14} />
                      <span>
                        {new Date(record.recorded_at).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mt-1">
                      <Clock size={14} />
                      <span>
                        {new Date(record.recorded_at).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="mt-2 flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                  <MapPin size={12} />
                  <span>
                    {(record.location as any)?.coordinates
                      ? `${(record.location as any).coordinates[1].toFixed(6)}, ${(record.location as any).coordinates[0].toFixed(6)}`
                      : "Location not available"}
                  </span>
                </div>
              </div>
            ))
          ) : (
            <div className="p-8 text-center text-gray-500 dark:text-gray-400">
              <User size={48} className="mx-auto mb-4 opacity-50" />
              <p>{t("students.noAttendanceRecords")}</p>
              <p className="text-sm mt-2">
                {t("students.adjustDateRangeOrFilters")}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
