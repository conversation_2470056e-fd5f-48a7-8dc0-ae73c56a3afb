/**
 * Security Compliance Report Component
 * Generates comprehensive security and compliance reports for the RBAC system
 */

import React, { useState, useEffect } from "react";
import {
  Shield,
  FileText,
  Download,
  Calendar,
  Users,
  Lock,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Eye,
  Clock,
} from "lucide-react";
import { useAuth } from "../../contexts/AuthContext";
import { usePermissions } from "../../hooks/usePermissions";
import { Permission } from "../../lib/rbac";
import { Button } from "../ui/Button";
import { PermissionGuard } from "../auth/PermissionGuard";

interface ComplianceMetrics {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  lockedUsers: number;
  adminUsers: number;
  schoolManagerUsers: number;
  supervisorUsers: number;
  driverUsers: number;
  parentUsers: number;
  studentUsers: number;
  totalPermissions: number;
  securityEvents: number;
  permissionViolations: number;
  auditLogEntries: number;
  complianceScore: number;
  lastAuditDate: string;
}

interface SecurityEvent {
  id: string;
  type: string;
  severity: "low" | "medium" | "high" | "critical";
  description: string;
  timestamp: string;
  userId?: string;
  resolved: boolean;
}

interface PermissionViolation {
  id: string;
  userId: string;
  attemptedAction: string;
  resourceType: string;
  timestamp: string;
  severity: "low" | "medium" | "high" | "critical";
}

export const SecurityComplianceReport: React.FC = () => {
  const { user } = useAuth();
  const { hasPermission } = usePermissions();

  const [metrics, setMetrics] = useState<ComplianceMetrics>({
    totalUsers: 0,
    activeUsers: 0,
    inactiveUsers: 0,
    lockedUsers: 0,
    adminUsers: 0,
    schoolManagerUsers: 0,
    supervisorUsers: 0,
    driverUsers: 0,
    parentUsers: 0,
    studentUsers: 0,
    totalPermissions: 85,
    securityEvents: 0,
    permissionViolations: 0,
    auditLogEntries: 0,
    complianceScore: 78,
    lastAuditDate: new Date().toISOString(),
  });

  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [violations, setViolations] = useState<PermissionViolation[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [reportPeriod, setReportPeriod] = useState("7d");

  useEffect(() => {
    loadComplianceData();
  }, [reportPeriod]);

  const loadComplianceData = async () => {
    try {
      // Simulate loading compliance data
      // In a real implementation, this would fetch from the security audit APIs

      const mockMetrics: ComplianceMetrics = {
        totalUsers: 1247,
        activeUsers: 1198,
        inactiveUsers: 49,
        lockedUsers: 3,
        adminUsers: 5,
        schoolManagerUsers: 45,
        supervisorUsers: 120,
        driverUsers: 340,
        parentUsers: 650,
        studentUsers: 87,
        totalPermissions: 85,
        securityEvents: 23,
        permissionViolations: 12,
        auditLogEntries: 15420,
        complianceScore: 78,
        lastAuditDate: new Date().toISOString(),
      };

      const mockEvents: SecurityEvent[] = [
        {
          id: "1",
          type: "permission_violation",
          severity: "high",
          description: "User attempted to access admin-only resource",
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          userId: "user-123",
          resolved: false,
        },
        {
          id: "2",
          type: "failed_login",
          severity: "medium",
          description: "Multiple failed login attempts detected",
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          userId: "user-456",
          resolved: true,
        },
        {
          id: "3",
          type: "suspicious_activity",
          severity: "critical",
          description: "Unusual access pattern detected",
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          userId: "user-789",
          resolved: false,
        },
      ];

      const mockViolations: PermissionViolation[] = [
        {
          id: "1",
          userId: "user-123",
          attemptedAction: "delete_user",
          resourceType: "user",
          timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          severity: "high",
        },
        {
          id: "2",
          userId: "user-456",
          attemptedAction: "view_all_schools",
          resourceType: "school",
          timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
          severity: "medium",
        },
      ];

      setMetrics(mockMetrics);
      setSecurityEvents(mockEvents);
      setViolations(mockViolations);
    } catch (error) {
      console.error("Error loading compliance data:", error);
    }
  };

  const generateReport = async () => {
    setIsGenerating(true);

    try {
      // Simulate report generation
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // In a real implementation, this would generate and download a PDF report
      const reportData = {
        generatedAt: new Date().toISOString(),
        period: reportPeriod,
        metrics,
        securityEvents,
        violations,
        complianceStatus: getComplianceStatus(metrics.complianceScore),
      };

      // Create and download JSON report (in real app, would be PDF)
      const blob = new Blob([JSON.stringify(reportData, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `security-compliance-report-${new Date().toISOString().split("T")[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error generating report:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  const getComplianceStatus = (score: number) => {
    if (score >= 90) return { status: "Excellent", color: "text-green-600" };
    if (score >= 80) return { status: "Good", color: "text-blue-600" };
    if (score >= 70) return { status: "Fair", color: "text-yellow-600" };
    return { status: "Poor", color: "text-red-600" };
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical":
        return "text-red-600 bg-red-50";
      case "high":
        return "text-orange-600 bg-orange-50";
      case "medium":
        return "text-yellow-600 bg-yellow-50";
      case "low":
        return "text-blue-600 bg-blue-50";
      default:
        return "text-gray-600 bg-gray-50";
    }
  };

  const complianceStatus = getComplianceStatus(metrics.complianceScore);

  return (
    <PermissionGuard permission={Permission.SYSTEM_ADMIN}>
      <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Security Compliance Report
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Comprehensive security and compliance analysis for the RBAC
                system
              </p>
            </div>
            <div className="flex items-center gap-4">
              <select
                value={reportPeriod}
                onChange={(e) => setReportPeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="1d">Last 24 Hours</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 90 Days</option>
              </select>
              <Button
                onClick={generateReport}
                disabled={isGenerating}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                {isGenerating ? "Generating..." : "Generate Report"}
              </Button>
            </div>
          </div>
        </div>

        {/* Compliance Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  Compliance Score
                </p>
                <p className={`text-2xl font-bold ${complianceStatus.color}`}>
                  {metrics.complianceScore}%
                </p>
                <p className="text-xs text-gray-500">
                  {complianceStatus.status}
                </p>
              </div>
              <Shield className="h-8 w-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-600 dark:text-green-400">
                  Active Users
                </p>
                <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                  {metrics.activeUsers}
                </p>
                <p className="text-xs text-gray-500">
                  of {metrics.totalUsers} total
                </p>
              </div>
              <Users className="h-8 w-8 text-green-500" />
            </div>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-yellow-600 dark:text-yellow-400">
                  Security Events
                </p>
                <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                  {metrics.securityEvents}
                </p>
                <p className="text-xs text-gray-500">
                  {violations.length} violations
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-500" />
            </div>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-600 dark:text-purple-400">
                  Audit Entries
                </p>
                <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                  {metrics.auditLogEntries.toLocaleString()}
                </p>
                <p className="text-xs text-gray-500">Last 30 days</p>
              </div>
              <FileText className="h-8 w-8 text-purple-500" />
            </div>
          </div>
        </div>

        {/* User Distribution */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            User Role Distribution
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
            {[
              {
                role: "Admin",
                count: metrics.adminUsers,
                color: "bg-red-100 text-red-800",
              },
              {
                role: "School Manager",
                count: metrics.schoolManagerUsers,
                color: "bg-blue-100 text-blue-800",
              },
              {
                role: "Supervisor",
                count: metrics.supervisorUsers,
                color: "bg-green-100 text-green-800",
              },
              {
                role: "Driver",
                count: metrics.driverUsers,
                color: "bg-yellow-100 text-yellow-800",
              },
              {
                role: "Parent",
                count: metrics.parentUsers,
                color: "bg-purple-100 text-purple-800",
              },
              {
                role: "Student",
                count: metrics.studentUsers,
                color: "bg-gray-100 text-gray-800",
              },
            ].map((item) => (
              <div key={item.role} className="text-center">
                <div
                  className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${item.color}`}
                >
                  {item.count}
                </div>
                <p className="text-xs text-gray-500 mt-1">{item.role}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Security Events */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Recent Security Events
          </h3>
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {securityEvents.map((event) => (
                <div key={event.id} className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(event.severity)}`}
                        >
                          {event.severity.toUpperCase()}
                        </span>
                        <span className="text-sm text-gray-500">
                          {event.type}
                        </span>
                      </div>
                      <p className="text-sm text-gray-900 dark:text-white">
                        {event.description}
                      </p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {new Date(event.timestamp).toLocaleString()}
                        </span>
                        {event.userId && (
                          <span className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            {event.userId}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {event.resolved ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <AlertTriangle className="h-5 w-5 text-amber-500" />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Permission Violations */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Recent Permission Violations
          </h3>
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {violations.map((violation) => (
                <div key={violation.id} className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(violation.severity)}`}
                        >
                          {violation.severity.toUpperCase()}
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {violation.attemptedAction}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        User attempted to perform action on{" "}
                        {violation.resourceType}
                      </p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {new Date(violation.timestamp).toLocaleString()}
                        </span>
                        <span className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          {violation.userId}
                        </span>
                      </div>
                    </div>
                    <Lock className="h-5 w-5 text-red-500" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </PermissionGuard>
  );
};

export default SecurityComplianceReport;
