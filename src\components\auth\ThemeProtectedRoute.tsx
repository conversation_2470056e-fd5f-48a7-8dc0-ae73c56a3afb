/**
 * Theme Protected Route Component
 * Special protection for theme management routes
 * Phase 3: UI/UX Enhancement - Route Protection
 */

import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole } from '../../types';

interface ThemeProtectedRouteProps {
  children: React.ReactNode;
  requiredRole: 'admin' | 'school_manager';
  fallbackRoute?: string;
}

/**
 * Theme Protected Route Component
 */
export const ThemeProtectedRoute: React.FC<ThemeProtectedRouteProps> = ({
  children,
  requiredRole,
  fallbackRoute = '/dashboard',
}) => {
  const { user, isLoading } = useAuth();

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Check role-specific permissions
  const hasPermission = 
    (requiredRole === 'admin' && user.role === UserRole.ADMIN) ||
    (requiredRole === 'school_manager' && user.role === UserRole.SCHOOL_MANAGER);

  if (!hasPermission) {
    console.log(`Access denied to theme route. Required: ${requiredRole}, User role: ${user.role}`);
    return <Navigate to={fallbackRoute} replace />;
  }

  // User has permission, render the protected content
  return <>{children}</>;
};
