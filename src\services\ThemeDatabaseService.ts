/**
 * Theme Database Service
 * Handles permanent storage and retrieval of themes from Supabase
 * Phase 3: UI/UX Enhancement - Theme Persistence
 */

import { supabase } from '../lib/supabase';
import { ThemeConfig } from './ThemeService';

export interface DatabaseTheme {
  id: string;
  tenant_id: string | null;
  name: string;
  description?: string;
  theme_type: 'school' | 'admin' | 'global';
  colors: ThemeConfig['colors'];
  typography: ThemeConfig['typography'];
  layout: ThemeConfig['layout'];
  branding: ThemeConfig['branding'];
  is_active: boolean;
  is_default: boolean;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export class ThemeDatabaseService {
  /**
   * Save theme to database
   */
  static async saveTheme(
    theme: ThemeConfig,
    name: string,
    tenantId: string | null,
    userId: string,
    options: {
      description?: string;
      themeType?: 'school' | 'admin' | 'global';
      setAsActive?: boolean;
    } = {}
  ): Promise<DatabaseTheme> {
    const {
      description = '',
      themeType = 'school',
      setAsActive = true
    } = options;

    try {
      console.log('🎨 Starting theme save process:', {
        name,
        tenantId,
        userId,
        themeType,
        setAsActive
      });

      // If setting as active, deactivate other themes for this tenant
      if (setAsActive && tenantId) {
        console.log('Deactivating existing themes for tenant:', tenantId);
        await this.deactivateThemes(tenantId);
      }

      const themeData = {
        tenant_id: tenantId,
        name,
        description,
        theme_type: themeType,
        colors: theme.colors,
        typography: theme.typography,
        layout: theme.layout,
        branding: theme.branding,
        is_active: setAsActive,
        is_default: false,
        created_by: userId,
      };

      console.log('Attempting to save theme with data:', themeData);

      const { data, error } = await supabase
        .from('themes')
        .insert(themeData)
        .select()
        .single();

      if (error) {
        console.error('Error saving theme:', error);
        console.error('Error details:', {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });
        throw new Error(`Failed to save theme: ${error.message}`);
      }

      console.log('✅ Theme saved successfully:', data);
      return data;
    } catch (error) {
      console.error('Error in saveTheme:', error);
      throw error;
    }
  }

  /**
   * Save or update theme (checks if active theme exists and updates it, otherwise creates new)
   */
  static async saveOrUpdateTheme(
    theme: ThemeConfig,
    name: string,
    tenantId: string | null,
    userId: string,
    options: {
      description?: string;
      themeType?: 'school' | 'admin' | 'global';
      setAsActive?: boolean;
    } = {}
  ): Promise<DatabaseTheme> {
    const {
      description = '',
      themeType = 'school',
      setAsActive = true
    } = options;

    try {
      console.log('🎨 Starting saveOrUpdateTheme process:', {
        name,
        tenantId,
        userId,
        themeType,
        setAsActive
      });

      if (!tenantId) {
        throw new Error('Tenant ID is required for school themes');
      }

      // Check if there's an active theme for this tenant
      const existingActiveTheme = await this.getActiveTheme(tenantId);

      if (existingActiveTheme) {
        console.log('📝 Found existing active theme, updating it:', existingActiveTheme.id);

        // Update the existing theme
        const updatedTheme = await this.updateTheme(existingActiveTheme.id, theme, {
          name,
          description,
          setAsActive: true
        });

        console.log('✅ Theme updated successfully');
        return updatedTheme;
      } else {
        console.log('🆕 No active theme found, creating new one');

        // Create new theme
        const newTheme = await this.saveTheme(theme, name, tenantId, userId, {
          description,
          themeType,
          setAsActive
        });

        console.log('✅ New theme created successfully');
        return newTheme;
      }
    } catch (error) {
      console.error('Error in saveOrUpdateTheme:', error);
      throw error;
    }
  }

  /**
   * Update existing theme
   */
  static async updateTheme(
    themeId: string,
    theme: ThemeConfig,
    options: {
      name?: string;
      description?: string;
      setAsActive?: boolean;
    } = {}
  ): Promise<DatabaseTheme> {
    try {
      const updateData: any = {
        colors: theme.colors,
        typography: theme.typography,
        layout: theme.layout,
        branding: theme.branding,
        updated_at: new Date().toISOString(),
      };

      if (options.name) updateData.name = options.name;
      if (options.description !== undefined) updateData.description = options.description;
      if (options.setAsActive !== undefined) updateData.is_active = options.setAsActive;

      // If setting as active, deactivate other themes for this tenant
      if (options.setAsActive) {
        const { data: currentTheme } = await supabase
          .from('themes')
          .select('tenant_id')
          .eq('id', themeId)
          .single();

        if (currentTheme?.tenant_id) {
          await this.deactivateThemes(currentTheme.tenant_id, themeId);
        }
      }

      const { data, error } = await supabase
        .from('themes')
        .update(updateData)
        .eq('id', themeId)
        .select()
        .single();

      if (error) {
        console.error('Error updating theme:', error);
        throw new Error(`Failed to update theme: ${error.message}`);
      }

      console.log('✅ Theme updated successfully:', data);
      return data;
    } catch (error) {
      console.error('Error in updateTheme:', error);
      throw error;
    }
  }

  /**
   * Get active theme for tenant
   */
  static async getActiveTheme(tenantId: string): Promise<DatabaseTheme | null> {
    try {
      const { data, error } = await supabase
        .from('themes')
        .select('*')
        .eq('tenant_id', tenantId)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error getting active theme:', error);
        throw new Error(`Failed to get active theme: ${error.message}`);
      }

      return data || null;
    } catch (error) {
      console.error('Error in getActiveTheme:', error);
      return null;
    }
  }

  /**
   * Get all themes for tenant
   */
  static async getThemes(tenantId: string): Promise<DatabaseTheme[]> {
    try {
      const { data, error } = await supabase
        .from('themes')
        .select('*')
        .eq('tenant_id', tenantId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error getting themes:', error);
        throw new Error(`Failed to get themes: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('Error in getThemes:', error);
      return [];
    }
  }

  /**
   * Delete theme
   */
  static async deleteTheme(themeId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('themes')
        .delete()
        .eq('id', themeId);

      if (error) {
        console.error('Error deleting theme:', error);
        throw new Error(`Failed to delete theme: ${error.message}`);
      }

      console.log('✅ Theme deleted successfully');
    } catch (error) {
      console.error('Error in deleteTheme:', error);
      throw error;
    }
  }

  /**
   * Set theme as active
   */
  static async setActiveTheme(themeId: string, tenantId: string): Promise<void> {
    try {
      // Deactivate all themes for this tenant
      await this.deactivateThemes(tenantId);

      // Activate the selected theme
      const { error } = await supabase
        .from('themes')
        .update({ is_active: true, updated_at: new Date().toISOString() })
        .eq('id', themeId);

      if (error) {
        console.error('Error setting active theme:', error);
        throw new Error(`Failed to set active theme: ${error.message}`);
      }

      console.log('✅ Theme set as active successfully');
    } catch (error) {
      console.error('Error in setActiveTheme:', error);
      throw error;
    }
  }

  /**
   * Deactivate all themes for tenant (except specified theme)
   */
  private static async deactivateThemes(tenantId: string, exceptThemeId?: string): Promise<void> {
    try {
      let query = supabase
        .from('themes')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('tenant_id', tenantId);

      if (exceptThemeId) {
        query = query.neq('id', exceptThemeId);
      }

      const { error } = await query;

      if (error) {
        console.error('Error deactivating themes:', error);
        throw new Error(`Failed to deactivate themes: ${error.message}`);
      }
    } catch (error) {
      console.error('Error in deactivateThemes:', error);
      throw error;
    }
  }

  /**
   * Convert DatabaseTheme to ThemeConfig
   */
  static toThemeConfig(dbTheme: DatabaseTheme): ThemeConfig {
    return {
      colors: dbTheme.colors,
      typography: dbTheme.typography,
      layout: dbTheme.layout,
      branding: dbTheme.branding,
    };
  }

  /**
   * Create default theme for new tenant
   */
  static async createDefaultTheme(tenantId: string, tenantName: string, userId: string): Promise<DatabaseTheme> {
    const defaultTheme: ThemeConfig = {
      colors: {
        primary: '#3b82f6',
        secondary: '#64748b',
        accent: '#10b981',
        background: '#ffffff',
        surface: '#f8fafc',
        text: '#1f2937',
        textSecondary: '#6b7280',
        sidebarBackground: '#1f2937',
        sidebarText: '#f9fafb',
        sidebarActiveBackground: '#3b82f6',
        sidebarActiveText: '#ffffff',
        sidebarHoverBackground: '#374151',
      },
      typography: {
        fontFamily: 'Cairo, sans-serif',
        fontSize: '16px',
        fontWeight: '400',
      },
      layout: {
        borderRadius: '8px',
        spacing: '16px',
        shadows: true,
      },
      branding: {
        schoolName: tenantName,
        tagline: 'التعليم هو المستقبل',
        logo: '',
      },
    };

    return await this.saveTheme(
      defaultTheme,
      'الثيم الافتراضي',
      tenantId,
      userId,
      {
        description: 'الثيم الافتراضي للمدرسة',
        themeType: 'school',
        setAsActive: true,
      }
    );
  }
}
