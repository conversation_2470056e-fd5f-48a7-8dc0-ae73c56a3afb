/**
 * سكريبت تطبيق الهجرات عبر JavaScript
 * JavaScript Migration Deployment Script
 */

import { createClient } from '@supabase/supabase-js'

// إعداد Supabase
const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('متغيرات البيئة مطلوبة: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

/**
 * تطبيق الهجرات
 */
async function deployMigrations() {
  console.log('🚀 بدء تطبيق الهجرات...')
  
  try {
    // قراءة السكريبت الموحد
    const fs = await import('fs')
    const path = await import('path')
    
    const sqlPath = path.join(process.cwd(), 'deployment', 'complete-migration.sql')
    const sqlContent = fs.readFileSync(sqlPath, 'utf8')
    
    // تقسيم السكريبت إلى أجزاء
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    console.log(`📊 سيتم تطبيق ${statements.length} استعلام`)
    
    // تطبيق كل استعلام
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      
      if (statement.includes('SELECT') && statement.includes('status')) {
        // استعلامات التحقق
        const { data, error } = await supabase.rpc('exec_sql', {
          sql: statement + ';'
        })
        
        if (error) {
          console.warn(`⚠️ تحذير في التحقق: ${error.message}`)
        } else {
          console.log(`✅ التحقق: ${JSON.stringify(data)}`)
        }
      } else {
        // استعلامات التطبيق
        const { error } = await supabase.rpc('exec_sql', {
          sql: statement + ';'
        })
        
        if (error) {
          console.error(`❌ خطأ في الاستعلام ${i + 1}: ${error.message}`)
          throw error
        } else {
          console.log(`✅ تم تطبيق الاستعلام ${i + 1}`)
        }
      }
    }
    
    console.log('🎉 تم تطبيق جميع الهجرات بنجاح!')
    
    // اختبار النظام
    await testSystem()
    
  } catch (error) {
    console.error('💥 خطأ في تطبيق الهجرات:', error)
    process.exit(1)
  }
}

/**
 * اختبار النظام
 */
async function testSystem() {
  console.log('🧪 اختبار النظام...')
  
  try {
    // اختبار الجداول
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
    
    if (tablesError) {
      console.warn('⚠️ لا يمكن اختبار الجداول:', tablesError.message)
    } else {
      console.log(`✅ تم إنشاء ${tables?.length || 0} جدول`)
    }
    
    // اختبار المستأجرين
    const { data: tenants, error: tenantsError } = await supabase
      .from('tenants')
      .select('*')
      .limit(1)
    
    if (tenantsError) {
      console.warn('⚠️ لا يمكن اختبار المستأجرين:', tenantsError.message)
    } else {
      console.log(`✅ تم العثور على ${tenants?.length || 0} مستأجر اختبار`)
    }
    
    // اختبار الدوال
    const { data: functions, error: functionsError } = await supabase
      .rpc('get_current_tenant_id')
    
    if (functionsError) {
      console.warn('⚠️ الدوال الأمنية تحتاج مستخدم مسجل:', functionsError.message)
    } else {
      console.log('✅ الدوال الأمنية تعمل')
    }
    
    console.log('🎯 اختبار النظام مكتمل')
    
  } catch (error) {
    console.warn('⚠️ بعض الاختبارات فشلت:', error.message)
  }
}

/**
 * تشغيل النشر
 */
if (import.meta.url === `file://${process.argv[1]}`) {
  deployMigrations()
}

export { deployMigrations, testSystem }
