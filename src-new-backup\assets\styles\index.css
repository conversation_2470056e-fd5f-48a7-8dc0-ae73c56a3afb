@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* تحسين تحميل CSS وإصلاح المشاكل */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* متغيرات الألوان المحدثة */
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }

  /* دعم RTL محسن */
  [dir="rtl"] * {
    letter-spacing: 0;
  }

  [dir="rtl"] .ltr-only {
    display: none !important;
  }

  [dir="ltr"] .rtl-only {
    display: none !important;
  }

  /* Define custom utility classes */
  .border-border {
    border-color: hsl(var(--border));
  }

  .bg-background {
    background-color: hsl(var(--background));
  }

  .text-foreground {
    color: hsl(var(--foreground));
  }

  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
  }

  /* تحسين الأداء */
  img,
  video {
    max-width: 100%;
    height: auto;
  }

  /* إصلاح مشاكل التحميل */
  .loading-skeleton {
    @apply animate-pulse bg-muted;
  }

  /* تحسين الوصولية */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
}

/* Map styles */
.mapboxgl-ctrl-logo {
  display: none !important;
}

.bus-marker {
  width: 30px;
  height: 30px;
  background-color: theme("colors.primary.500");
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 20px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Add smooth transitions for dark mode */
html {
  transition: background-color 0.3s ease;
}

html.dark {
  color-scheme: dark;
}

/* RTL support */
html[dir="rtl"] .ltr-only {
  display: none;
}

html:not([dir="rtl"]) .rtl-only {
  display: none;
}
