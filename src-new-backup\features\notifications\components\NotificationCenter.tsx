import React, { useState, useRef, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Trash2, Filter } from "lucide-react";
import { useNotifications } from "../../contexts/NotificationsContext";
import { NotificationActions } from "./NotificationActions";
import { NotificationFilter } from "./NotificationFilter";
import { Button } from "../ui/Button";
import type { Tables } from "../../lib/api";

interface NotificationCenterProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({
  isOpen,
  onClose,
  className = "",
}) => {
  const { t } = useTranslation();
  const {
    notifications,
    markAsRead,
    markAllAsRead,
    loading,
    getNotificationsByCategory,
    getNotificationCategories,
  } = useNotifications();
  const [filter, setFilter] = useState<"all" | "unread">("all");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedPriority, setSelectedPriority] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [showFilters, setShowFilters] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  const filteredNotifications = React.useMemo(() => {
    return notifications.filter((notification) => {
      // Status filter
      if (selectedStatus === "unread" && notification.read) return false;
      if (selectedStatus === "read" && !notification.read) return false;

      // Category filter
      if (selectedCategory !== "all") {
        const metadata = notification.metadata as any;
        if (metadata?.type !== selectedCategory) return false;
      }

      // Priority filter
      if (selectedPriority !== "all") {
        const metadata = notification.metadata as any;
        if (metadata?.priority !== selectedPriority) return false;
      }

      // Legacy filter for backward compatibility
      if (filter === "unread") {
        return !notification.read;
      }

      return true;
    });
  }, [
    notifications,
    selectedStatus,
    selectedCategory,
    selectedPriority,
    filter,
  ]);

  const clearAllFilters = () => {
    setSelectedCategory("all");
    setSelectedPriority("all");
    setSelectedStatus("all");
    setFilter("all");
  };

  const unreadCount = notifications.filter((n) => !n.read).length;

  const getNotificationIcon = (notification: Tables<"notifications">) => {
    const metadata = notification.metadata as any;
    if (metadata?.type === "geofence") {
      return "🚌";
    }
    if (metadata?.type === "attendance") {
      return "👥";
    }
    if (metadata?.type === "maintenance") {
      return "🔧";
    }
    return "ℹ️";
  };

  const getNotificationColor = (notification: Tables<"notifications">) => {
    const metadata = notification.metadata as any;
    if (metadata?.type === "geofence") {
      return "text-blue-600 dark:text-blue-400";
    }
    if (metadata?.type === "attendance") {
      return "text-green-600 dark:text-green-400";
    }
    if (metadata?.type === "maintenance") {
      return "text-orange-600 dark:text-orange-400";
    }
    return "text-gray-600 dark:text-gray-400";
  };

  if (!isOpen) return null;

  return (
    <div
      ref={dropdownRef}
      className={`absolute right-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50 ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <Bell size={20} className="text-gray-600 dark:text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t("notifications.new")}
          </h3>
          {unreadCount > 0 && (
            <span className="bg-primary-500 text-white text-xs px-2 py-1 rounded-full">
              {unreadCount}
            </span>
          )}
        </div>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X size={16} />
        </Button>
      </div>

      {/* Filter Tabs and Controls */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between px-4 py-2">
          <div className="flex space-x-4">
            <button
              onClick={() => setFilter("all")}
              className={`px-3 py-1 text-sm font-medium transition-colors ${
                filter === "all"
                  ? "text-primary-600 dark:text-primary-400"
                  : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              }`}
            >
              {t("common.all")} ({notifications.length})
            </button>
            <button
              onClick={() => setFilter("unread")}
              className={`px-3 py-1 text-sm font-medium transition-colors ${
                filter === "unread"
                  ? "text-primary-600 dark:text-primary-400"
                  : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              }`}
            >
              {t("notifications.new")} ({unreadCount})
            </button>
          </div>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setShowFilters(!showFilters)}
            leftIcon={<Filter size={14} />}
            className="text-xs"
          >
            Filters
          </Button>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="px-4 pb-4 border-t border-gray-200 dark:border-gray-700">
            <NotificationFilter
              selectedCategory={selectedCategory}
              selectedPriority={selectedPriority}
              selectedStatus={selectedStatus}
              onCategoryChange={setSelectedCategory}
              onPriorityChange={setSelectedPriority}
              onStatusChange={setSelectedStatus}
              onClearFilters={clearAllFilters}
              availableCategories={getNotificationCategories()}
              className="mt-4"
            />
          </div>
        )}
      </div>

      {/* Actions */}
      {unreadCount > 0 && (
        <div className="p-3 border-b border-gray-200 dark:border-gray-700">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => markAllAsRead()}
            className="w-full justify-start text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
          >
            <CheckCheck size={16} className="mr-2" />
            {t("notifications.markAllAsRead")}
          </Button>
        </div>
      )}

      {/* Notifications List */}
      <div className="max-h-96 overflow-y-auto">
        {loading ? (
          <div className="p-4 text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500 mx-auto" />
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              {t("common.loading")}
            </p>
          </div>
        ) : filteredNotifications.length === 0 ? (
          <div className="p-8 text-center text-gray-500 dark:text-gray-400">
            <Bell className="mx-auto h-12 w-12 mb-3 opacity-50" />
            <p className="text-sm">
              {filter === "unread"
                ? t("notifications.noUnreadNotifications")
                : t("notifications.empty")}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                  notification.read ? "opacity-75" : ""
                }`}
              >
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="text-lg">
                        {getNotificationIcon(notification)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {notification.title}
                          </p>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0 animate-pulse" />
                          )}
                        </div>
                        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                          {notification.message}
                        </p>
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                          {notification.created_at &&
                            new Date(notification.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1 ml-2">
                      {!notification.read && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => markAsRead(notification.id)}
                          className="p-1 h-auto"
                          title={t("notifications.markAsRead")}
                        >
                          <Check size={14} />
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Notification Actions */}
                  <NotificationActions
                    notification={notification}
                    onActionComplete={() => {
                      // Play notification sound
                      const audio = new Audio("/sounds/notification.mp3");
                      audio.play().catch(() => {});

                      // Refresh notifications or handle action completion
                      markAsRead(notification.id);
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {filteredNotifications.length > 0 && (
        <div className="p-3 border-t border-gray-200 dark:border-gray-700 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {filteredNotifications.length} {t("notifications.notifications")}
          </p>
        </div>
      )}
    </div>
  );
};

export default NotificationCenter;
