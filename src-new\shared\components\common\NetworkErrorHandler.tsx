/**
 * مكون معالجة أخطاء الشبكة - Network Error Handler
 * يتعامل مع مشاكل الاتصال والكوكيز
 */

import React, { useEffect } from 'react';
import { supabase } from '../../lib/supabase';

export const NetworkErrorHandler: React.FC = () => {
  useEffect(() => {
    // معالجة أخطاء الشبكة والكوكيز
    const handleNetworkErrors = () => {
      // تنظيف الكوكيز المرفوضة
      if (typeof document !== 'undefined') {
        // حذف الكوكيز المشكوك فيها
        const cookiesToClear = ['__cf_bm', '_cfuvid'];
        cookiesToClear.forEach(cookieName => {
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.supabase.co;`;
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        });
      }

      // إعداد معالج أخطاء Supabase
      supabase.auth.onAuthStateChange((event, session) => {
        if (event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {
          // تنظيف إضافي عند تغيير حالة المصادقة
          if (typeof window !== 'undefined') {
            window.localStorage.removeItem('supabase.auth.token');
          }
        }
      });
    };

    handleNetworkErrors();

    // معالجة أخطاء WebSocket
    const handleWebSocketErrors = () => {
      if (typeof window !== 'undefined') {
        window.addEventListener('error', (event) => {
          if (event.message && event.message.includes('websocket')) {
            console.warn('WebSocket error detected, but continuing...');
            event.preventDefault();
          }
        });

        window.addEventListener('unhandledrejection', (event) => {
          if (event.reason && event.reason.message && event.reason.message.includes('websocket')) {
            console.warn('WebSocket promise rejection, but continuing...');
            event.preventDefault();
          }
        });
      }
    };

    handleWebSocketErrors();
  }, []);

  return null; // هذا المكون لا يعرض أي شيء
};

export default NetworkErrorHandler;
