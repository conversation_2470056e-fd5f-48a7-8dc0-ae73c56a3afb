import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Download, FileText, Calendar, User, Filter } from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import * as api from "../../lib/api";

export const AttendanceExport: React.FC = () => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const { students } = useDatabase();
  const [loading, setLoading] = useState(false);
  const [startDate, setStartDate] = useState(
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split("T")[0],
  );
  const [endDate, setEndDate] = useState(
    new Date().toISOString().split("T")[0],
  );
  const [selectedStudent, setSelectedStudent] = useState("");

  const exportAttendance = async () => {
    if (!tenant?.id) return;

    try {
      setLoading(true);
      const csvData = await api.exportAttendanceToCSV(
        tenant.id,
        startDate,
        endDate,
        selectedStudent || undefined,
      );

      // Create and download the file
      const blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);

      const filename = `attendance-export-${startDate}-to-${endDate}${selectedStudent ? `-student-${selectedStudent}` : ""}.csv`;
      link.setAttribute("download", filename);
      link.style.visibility = "hidden";

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error exporting attendance:", error);
      alert("Failed to export attendance data. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <FileText className="mr-2 h-5 w-5 text-primary-500" />
          Export Attendance Records
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Download attendance data in CSV format for reporting and analysis.
        </p>
      </div>

      <div className="p-6 space-y-6">
        {/* Date Range Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Calendar className="inline h-4 w-4 mr-1" />
              Start Date
            </label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Calendar className="inline h-4 w-4 mr-1" />
              End Date
            </label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        {/* Student Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <User className="inline h-4 w-4 mr-1" />
            Student Filter (Optional)
          </label>
          <select
            value={selectedStudent}
            onChange={(e) => setSelectedStudent(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="">All Students</option>
            {students.map((student) => (
              <option key={student.id} value={student.id}>
                {student.name} - Grade {student.grade}
              </option>
            ))}
          </select>
        </div>

        {/* Export Options */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            Export Information
          </h4>
          <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
            <li>• Date and time of attendance records</li>
            <li>• Student names and grades</li>
            <li>• Bus information</li>
            <li>• Pickup/dropoff status</li>
            <li>• Location data (if available)</li>
            <li>• Staff member who recorded attendance</li>
          </ul>
        </div>

        {/* Export Button */}
        <div className="flex justify-end">
          <Button
            onClick={exportAttendance}
            disabled={loading || !startDate || !endDate}
            className="flex items-center gap-2"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Download size={16} />
            )}
            {loading ? "Exporting..." : "Export to CSV"}
          </Button>
        </div>

        {/* Usage Instructions */}
        <div className="text-xs text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-600 pt-4">
          <p className="font-medium mb-1">Usage Tips:</p>
          <ul className="space-y-1">
            <li>• Select a date range to filter attendance records</li>
            <li>• Choose a specific student or leave blank for all students</li>
            <li>
              • The exported CSV file can be opened in Excel or Google Sheets
            </li>
            <li>• Large date ranges may take longer to process</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AttendanceExport;
