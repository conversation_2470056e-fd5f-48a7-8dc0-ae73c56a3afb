import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Users,
  CheckCircle,
  XCircle,
  MapPin,
  Clock,
  Bus,
  AlertTriangle,
  Navigation,
  Wifi,
  WifiOff,
  Battery,
} from "lucide-react";
import { Button } from "../ui/Button";
import { supabase } from "../../lib/supabase";
import { useAuth } from "../../contexts/AuthContext";
import type { Tables } from "../../lib/api";

interface StudentWithStop extends Tables<"students"> {
  route_stop?: {
    id: string;
    name: string;
    arrival_time?: string;
    order: number;
  };
  today_attendance?: {
    pickup?: boolean;
    dropoff?: boolean;
  };
}

interface DriverAttendanceInterfaceProps {
  className?: string;
}

export const DriverAttendanceInterface: React.FC<
  DriverAttendanceInterfaceProps
> = ({ className = "" }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [students, setStudents] = useState<StudentWithStop[]>([]);
  const [currentRoute, setCurrentRoute] = useState<Tables<"routes"> | null>(
    null,
  );
  const [currentBus, setCurrentBus] = useState<Tables<"buses"> | null>(null);
  const [location, setLocation] = useState<GeolocationPosition | null>(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [attendanceMode, setAttendanceMode] = useState<"pickup" | "dropoff">(
    "pickup",
  );
  const [currentStop, setCurrentStop] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState<string | null>(null);

  useEffect(() => {
    fetchDriverData();
    getCurrentLocation();

    // Listen for online/offline events
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, [user?.id]);

  useEffect(() => {
    // Update location every 30 seconds
    const interval = setInterval(getCurrentLocation, 30000);
    return () => clearInterval(interval);
  }, []);

  const getCurrentLocation = () => {
    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => setLocation(position),
        (error) => console.error("Error getting location:", error),
        { enableHighAccuracy: true, timeout: 10000, maximumAge: 60000 },
      );
    }
  };

  const fetchDriverData = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);

      // Get driver's assigned bus
      const { data: busData, error: busError } = await supabase
        .from("buses")
        .select("*")
        .eq("driver_id", user.id)
        .eq("is_active", true)
        .single();

      if (busError && busError.code !== "PGRST116") {
        throw busError;
      }

      setCurrentBus(busData);

      if (!busData) {
        setLoading(false);
        return;
      }

      // Get route assigned to this bus
      const { data: routeData, error: routeError } = await supabase
        .from("routes")
        .select(
          `
          *,
          stops:route_stops(*)
        `,
        )
        .eq("bus_id", busData.id)
        .single();

      if (routeError && routeError.code !== "PGRST116") {
        throw routeError;
      }

      setCurrentRoute(routeData);

      if (!routeData || !routeData.stops) {
        setLoading(false);
        return;
      }

      // Get students for this route
      const stopIds = routeData.stops.map((stop) => stop.id);
      const { data: studentsData, error: studentsError } = await supabase
        .from("students")
        .select("*")
        .in("route_stop_id", stopIds)
        .eq("is_active", true)
        .order("name");

      if (studentsError) throw studentsError;

      // Get today's attendance for these students
      const today = new Date().toISOString().split("T")[0];
      const { data: attendanceData } = await supabase
        .from("attendance")
        .select("*")
        .in("student_id", studentsData?.map((s) => s.id) || [])
        .gte("recorded_at", `${today}T00:00:00.000Z`)
        .lte("recorded_at", `${today}T23:59:59.999Z`);

      // Combine student data with route stop info and attendance
      const studentsWithDetails: StudentWithStop[] = (studentsData || []).map(
        (student) => {
          const routeStop = routeData.stops?.find(
            (stop) => stop.id === student.route_stop_id,
          );
          const studentAttendance =
            attendanceData?.filter((a) => a.student_id === student.id) || [];

          return {
            ...student,
            route_stop: routeStop
              ? {
                  id: routeStop.id,
                  name: routeStop.name,
                  arrival_time: routeStop.arrival_time,
                  order: routeStop.order,
                }
              : undefined,
            today_attendance: {
              pickup: studentAttendance.some((a) => a.type === "pickup"),
              dropoff: studentAttendance.some((a) => a.type === "dropoff"),
            },
          };
        },
      );

      // Sort by route stop order
      studentsWithDetails.sort((a, b) => {
        const orderA = a.route_stop?.order || 999;
        const orderB = b.route_stop?.order || 999;
        return orderA - orderB;
      });

      setStudents(studentsWithDetails);
    } catch (error) {
      console.error("Error fetching driver data:", error);
    } finally {
      setLoading(false);
    }
  };

  const recordAttendance = async (
    student: StudentWithStop,
    type: "pickup" | "dropoff",
    status: "present" | "absent",
  ) => {
    if (!currentBus || !location) return;

    setSubmitting(student.id);

    try {
      const attendanceData = {
        tenant_id: user?.tenant_id,
        student_id: student.id,
        bus_id: currentBus.id,
        type,
        status,
        location: `POINT(${location.coords.longitude} ${location.coords.latitude})`,
        recorded_at: new Date().toISOString(),
        recorded_by: user?.id,
        notes: currentStop ? `Stop: ${currentStop}` : undefined,
      };

      const { error } = await supabase
        .from("attendance")
        .insert([attendanceData]);

      if (error) throw error;

      // Refresh data to show updated attendance
      await fetchDriverData();

      // Show success feedback with sound
      const audio = new Audio("/sounds/notification.mp3");
      audio.play().catch(() => {});

      // Send notification to parent
      await sendAttendanceNotification(student, type, status);
    } catch (error) {
      console.error("Error recording attendance:", error);
      alert(t("attendance.recordError"));
    } finally {
      setSubmitting(null);
    }
  };

  const sendAttendanceNotification = async (
    student: StudentWithStop,
    type: "pickup" | "dropoff",
    status: "present" | "absent",
  ) => {
    try {
      if (student.parent_id) {
        const message = `${student.name} ${status === "present" ? "حضر" : "غاب"} في ${type === "pickup" ? "الصعود" : "النزول"}`;

        await supabase.from("notifications").insert({
          user_id: student.parent_id,
          title: "تحديث الحضور",
          message,
          tenant_id: user?.tenant_id,
          type: "attendance",
          metadata: {
            student_id: student.id,
            attendance_type: type,
            status,
          },
        });
      }
    } catch (error) {
      console.error("Error sending attendance notification:", error);
    }
  };

  const getStudentsByStop = () => {
    const stopGroups: {
      [stopId: string]: { stop: any; students: StudentWithStop[] };
    } = {};

    students.forEach((student) => {
      if (student.route_stop) {
        const stopId = student.route_stop.id;
        if (!stopGroups[stopId]) {
          stopGroups[stopId] = {
            stop: student.route_stop,
            students: [],
          };
        }
        stopGroups[stopId].students.push(student);
      }
    });

    return Object.values(stopGroups).sort(
      (a, b) => a.stop.order - b.stop.order,
    );
  };

  const getAttendanceStats = () => {
    const total = students.length;
    const pickup = students.filter((s) => s.today_attendance?.pickup).length;
    const dropoff = students.filter((s) => s.today_attendance?.dropoff).length;

    return { total, pickup, dropoff };
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
      </div>
    );
  }

  if (!currentBus) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <Bus size={48} className="mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {t("drivers.noBusAssigned")}
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          {t("drivers.contactAdminForBusAssignment")}
        </p>
      </div>
    );
  }

  if (!currentRoute) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <MapPin size={48} className="mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {t("drivers.noRouteAssigned")}
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          {t("drivers.contactAdminForRouteAssignment")}
        </p>
      </div>
    );
  }

  const stats = getAttendanceStats();
  const stopGroups = getStudentsByStop();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Bus and Route Info */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary-100 dark:bg-primary-800/20 rounded-lg">
              <Bus className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                {currentBus.plate_number}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {currentRoute.name} • {t("buses.capacity")}:{" "}
                {currentBus.capacity}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {isOnline ? (
              <Wifi size={20} className="text-green-600" />
            ) : (
              <WifiOff size={20} className="text-red-600" />
            )}
            {location && (
              <div className="flex items-center space-x-1 text-green-600">
                <Navigation size={16} />
                <span className="text-xs">GPS</span>
              </div>
            )}
          </div>
        </div>

        {/* Attendance Mode Toggle */}
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("attendance.mode")}:
          </span>
          <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setAttendanceMode("pickup")}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                attendanceMode === "pickup"
                  ? "bg-primary-500 text-white"
                  : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
              }`}
            >
              {t("attendance.pickup")}
            </button>
            <button
              onClick={() => setAttendanceMode("dropoff")}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                attendanceMode === "dropoff"
                  ? "bg-primary-500 text-white"
                  : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
              }`}
            >
              {t("attendance.dropoff")}
            </button>
          </div>
        </div>
      </div>

      {/* Attendance Statistics */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("students.total")}
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats.total}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("attendance.pickup")}
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats.pickup}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <XCircle className="h-8 w-8 text-purple-600 dark:text-purple-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("attendance.dropoff")}
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats.dropoff}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Students by Stop */}
      <div className="space-y-4">
        {stopGroups.map(({ stop, students: stopStudents }) => (
          <div
            key={stop.id}
            className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden"
          >
            <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <MapPin size={16} className="text-gray-500" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {stop.name}
                  </h3>
                  {stop.arrival_time && (
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      • {stop.arrival_time}
                    </span>
                  )}
                </div>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {stopStudents.length} {t("students.students")}
                </span>
              </div>
            </div>

            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {stopStudents.map((student) => {
                const hasAttendance =
                  attendanceMode === "pickup"
                    ? student.today_attendance?.pickup
                    : student.today_attendance?.dropoff;

                return (
                  <div key={student.id} className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0 h-10 w-10">
                          {student.photo_url ? (
                            <img
                              src={student.photo_url}
                              alt={student.name}
                              className="h-10 w-10 rounded-full object-cover"
                            />
                          ) : (
                            <div className="h-10 w-10 bg-primary-100 dark:bg-primary-800/20 rounded-full flex items-center justify-center text-primary-600 dark:text-primary-400">
                              <Users size={20} />
                            </div>
                          )}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {student.name}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {t("students.grade")} {student.grade}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {hasAttendance ? (
                          <div className="flex items-center space-x-1 text-green-600 dark:text-green-400">
                            <CheckCircle size={16} />
                            <span className="text-sm font-medium">
                              {t("attendance.recorded")}
                            </span>
                          </div>
                        ) : (
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              onClick={() =>
                                recordAttendance(
                                  student,
                                  attendanceMode,
                                  "present",
                                )
                              }
                              disabled={submitting === student.id || !location}
                              className="bg-green-600 hover:bg-green-700 text-white"
                            >
                              {submitting === student.id ? (
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                              ) : (
                                <CheckCircle size={14} />
                              )}
                              {t("attendance.present")}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() =>
                                recordAttendance(
                                  student,
                                  attendanceMode,
                                  "absent",
                                )
                              }
                              disabled={submitting === student.id || !location}
                              className="border-red-300 text-red-600 hover:bg-red-50"
                            >
                              <XCircle size={14} />
                              {t("attendance.absent")}
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>

      {/* Location Warning */}
      {!location && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
          <div className="flex items-center">
            <AlertTriangle
              size={20}
              className="text-yellow-600 dark:text-yellow-400"
            />
            <div className="ml-3">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                {t("attendance.locationRequired")}
              </p>
              <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                {t("attendance.enableLocationServices")}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DriverAttendanceInterface;
