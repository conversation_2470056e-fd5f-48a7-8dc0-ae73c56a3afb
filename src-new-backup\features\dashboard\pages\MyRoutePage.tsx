import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  MapPin,
  Clock,
  Users,
  CheckCircle,
  XCircle,
  Navigation,
  Bus,
  Home,
  User,
} from "lucide-react";
import { Navbar } from "../../../shared/layouts/Navbar";
import { Sidebar } from "../../components/layout/Sidebar";
import { LiveMap } from "../../components/map/LiveMap";
import { Button } from "../../components/ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/api";

interface RouteStudent {
  id: string;
  name: string;
  grade: string;
  stopName: string;
  stopOrder: number;
  arrivalTime?: string;
  attendanceStatus?: "present" | "absent" | "pending";
  photoUrl?: string;
}

interface RouteInfo {
  id: string;
  name: string;
  busId: string;
  busPlateNumber: string;
  stops: {
    id: string;
    name: string;
    order: number;
    arrivalTime?: string;
    location: { lat: number; lng: number };
    completed: boolean;
  }[];
  students: RouteStudent[];
}

export const MyRoutePage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { buses, routes, students } = useDatabase();
  const [routeInfo, setRouteInfo] = useState<RouteInfo | null>(null);
  const [currentStopIndex, setCurrentStopIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [attendanceMode, setAttendanceMode] = useState<"pickup" | "dropoff">(
    "pickup",
  );
  const [selectedStudents, setSelectedStudents] = useState<Set<string>>(
    new Set(),
  );
  const [isRecordingAttendance, setIsRecordingAttendance] = useState(false);
  const [isMobileView, setIsMobileView] = useState(false);
  const [issueReport, setIssueReport] = useState("");
  const [isReportingIssue, setIsReportingIssue] = useState(false);

  // Detect mobile view
  useEffect(() => {
    const checkMobile = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  useEffect(() => {
    if (user && buses.length > 0 && routes.length > 0) {
      fetchDriverRoute();
    }
  }, [user, buses, routes, students]);

  const fetchDriverRoute = async () => {
    try {
      setIsLoading(true);

      // Find the bus assigned to this driver
      const driverBus = buses.find((bus) => bus.driver_id === user?.id);
      if (!driverBus) {
        setIsLoading(false);
        return;
      }

      // Find the route for this bus
      const busRoute = routes.find((route) => route.bus_id === driverBus.id);
      if (!busRoute || !busRoute.stops) {
        setIsLoading(false);
        return;
      }

      // Get students for each stop
      const routeStudents: RouteStudent[] = [];
      const stopsWithCompletion = [];

      for (const stop of busRoute.stops) {
        // Find students for this stop
        const stopStudents = students.filter(
          (student) => student.route_stop_id === stop.id,
        );

        for (const student of stopStudents) {
          // Check today's attendance
          const today = new Date().toISOString().split("T")[0];
          const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000)
            .toISOString()
            .split("T")[0];

          const { data: attendanceData } = await supabase
            .from("attendance")
            .select("*")
            .eq("student_id", student.id)
            .eq("type", attendanceMode)
            .gte("recorded_at", today)
            .lt("recorded_at", tomorrow);

          let attendanceStatus: "present" | "absent" | "pending" = "pending";
          if (attendanceData && attendanceData.length > 0) {
            attendanceStatus = "present";
          }

          routeStudents.push({
            id: student.id,
            name: student.name,
            grade: student.grade,
            stopName: stop.name,
            stopOrder: stop.order,
            arrivalTime: stop.arrival_time,
            attendanceStatus,
            photoUrl: student.photo_url,
          });
        }

        // Check if stop is completed (all students have attendance recorded)
        const stopStudentIds = stopStudents.map((s) => s.id);
        const { data: stopAttendance } = await supabase
          .from("attendance")
          .select("student_id")
          .in("student_id", stopStudentIds)
          .eq("type", attendanceMode)
          .gte("recorded_at", new Date().toISOString().split("T")[0]);

        const completed =
          stopStudentIds.length > 0 &&
          stopAttendance &&
          stopAttendance.length === stopStudentIds.length;

        stopsWithCompletion.push({
          ...stop,
          completed,
        });
      }

      setRouteInfo({
        id: busRoute.id,
        name: busRoute.name,
        busId: driverBus.id,
        busPlateNumber: driverBus.plate_number,
        stops: stopsWithCompletion.sort((a, b) => a.order - b.order),
        students: routeStudents.sort((a, b) => a.stopOrder - b.stopOrder),
      });

      // Find current stop (first incomplete stop)
      const currentIncompleteIndex = stopsWithCompletion.findIndex(
        (stop) => !stop.completed,
      );
      if (currentIncompleteIndex !== -1) {
        setCurrentStopIndex(currentIncompleteIndex);
      }
    } catch (error) {
      console.error("Error fetching driver route:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStudentToggle = (studentId: string) => {
    const newSelected = new Set(selectedStudents);
    if (newSelected.has(studentId)) {
      newSelected.delete(studentId);
    } else {
      newSelected.add(studentId);
    }
    setSelectedStudents(newSelected);
  };

  const handleRecordAttendance = async (status: "present" | "absent") => {
    if (!routeInfo || selectedStudents.size === 0 || !user) return;

    setIsRecordingAttendance(true);

    try {
      const currentStop = routeInfo.stops[currentStopIndex];
      if (!currentStop) return;

      const attendanceRecords = Array.from(selectedStudents).map(
        (studentId) => ({
          student_id: studentId,
          bus_id: routeInfo.busId,
          type: attendanceMode,
          location: currentStop.location,
          recorded_at: new Date().toISOString(),
          recorded_by: user.id,
          tenant_id: user.tenant_id || "",
        }),
      );

      const { error } = await supabase
        .from("attendance")
        .insert(attendanceRecords);

      if (error) throw error;

      // Refresh route data
      await fetchDriverRoute();
      setSelectedStudents(new Set());
    } catch (error) {
      console.error("Error recording attendance:", error);
    } finally {
      setIsRecordingAttendance(false);
    }
  };

  const getCurrentStopStudents = () => {
    if (!routeInfo || !routeInfo.stops[currentStopIndex]) return [];

    const currentStop = routeInfo.stops[currentStopIndex];
    return routeInfo.students.filter(
      (student) => student.stopName === currentStop.name,
    );
  };

  const handleReportIssue = async () => {
    if (!issueReport.trim() || !user || !routeInfo) return;

    setIsReportingIssue(true);
    try {
      // Create a notification for supervisors about the issue
      const { error } = await supabase.from("notifications").insert({
        title: `Issue Reported - ${routeInfo.name}`,
        message: `Driver ${user.name} reported: ${issueReport}`,
        user_id: user.id, // This would typically go to supervisors
        tenant_id: user.tenant_id,
        metadata: {
          type: "issue_report",
          busId: routeInfo.busId,
          routeId: routeInfo.id,
          reportedBy: user.id,
        },
      });

      if (error) throw error;

      setIssueReport("");
      alert(t("routes.issueReported"));
    } catch (error) {
      console.error("Error reporting issue:", error);
      alert(t("routes.issueReportError"));
    } finally {
      setIsReportingIssue(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-pulse">
          <div className="bg-primary-500 h-12 w-12 rounded-xl flex items-center justify-center shadow-lg">
            <Bus className="text-white" size={30} />
          </div>
        </div>
      </div>
    );
  }

  if (!routeInfo) {
    return (
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        <Sidebar />

        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar />

          <main className="flex-1 overflow-y-auto p-4 md:p-6">
            <div className="max-w-7xl mx-auto">
              <div className="text-center py-12">
                <Bus size={48} className="mx-auto text-gray-400 mb-4" />
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {t("routes.noRouteAssigned")}
                </h2>
                <p className="text-gray-500 dark:text-gray-400">
                  {t("routes.contactAdminForRoute")}
                </p>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  const currentStopStudents = getCurrentStopStudents();

  // Mobile-optimized layout
  if (isMobileView) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Mobile Header */}
        <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 p-4 sticky top-0 z-10">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-lg font-bold text-gray-900 dark:text-white">
                {routeInfo?.name || t("routes.myRoute")}
              </h1>
              {routeInfo && (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t("buses.plateNumber")}: {routeInfo.busPlateNumber}
                </p>
              )}
            </div>
            <div className="flex space-x-2">
              <Button
                variant={attendanceMode === "pickup" ? "default" : "outline"}
                size="sm"
                onClick={() => setAttendanceMode("pickup")}
              >
                {t("attendance.pickup")}
              </Button>
              <Button
                variant={attendanceMode === "dropoff" ? "default" : "outline"}
                size="sm"
                onClick={() => setAttendanceMode("dropoff")}
              >
                {t("attendance.dropoff")}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Content */}
        <div className="p-4 space-y-4">
          {/* Current Stop Navigation */}
          {routeInfo && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                  <Navigation size={18} className="mr-2" />
                  {t("routes.currentStop")}
                </h2>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() =>
                      setCurrentStopIndex(Math.max(0, currentStopIndex - 1))
                    }
                    disabled={currentStopIndex === 0}
                  >
                    ←
                  </Button>
                  <span className="flex items-center px-2 text-sm">
                    {currentStopIndex + 1}/{routeInfo.stops.length}
                  </span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() =>
                      setCurrentStopIndex(
                        Math.min(
                          (routeInfo.stops.length || 1) - 1,
                          currentStopIndex + 1,
                        ),
                      )
                    }
                    disabled={
                      currentStopIndex === (routeInfo.stops.length || 1) - 1
                    }
                  >
                    →
                  </Button>
                </div>
              </div>

              {routeInfo.stops[currentStopIndex] && (
                <div className="p-3 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-md">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-500 text-white flex items-center justify-center mr-3">
                      <span className="text-xs font-medium">
                        {routeInfo.stops[currentStopIndex].order}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {routeInfo.stops[currentStopIndex].name}
                      </p>
                      {routeInfo.stops[currentStopIndex].arrivalTime && (
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          <Clock size={12} className="inline mr-1" />
                          {routeInfo.stops[currentStopIndex].arrivalTime}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Students at current stop */}
          {routeInfo && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex justify-between items-center">
                  <h2 className="text-base font-semibold text-gray-900 dark:text-white flex items-center">
                    <Users size={18} className="mr-2" />
                    {t("students.students")} ({currentStopStudents.length})
                  </h2>
                </div>
              </div>

              <div className="p-4">
                {currentStopStudents.length > 0 ? (
                  <div className="space-y-3">
                    {currentStopStudents.map((student) => (
                      <div
                        key={student.id}
                        className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                          selectedStudents.has(student.id)
                            ? "border-primary-500 bg-primary-50 dark:bg-primary-900/20"
                            : "border-gray-200 dark:border-gray-700"
                        } ${
                          student.attendanceStatus === "present"
                            ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800"
                            : student.attendanceStatus === "absent"
                              ? "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
                              : ""
                        }`}
                        onClick={() => {
                          if (student.attendanceStatus === "pending") {
                            handleStudentToggle(student.id);
                          }
                        }}
                      >
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            {student.photoUrl ? (
                              <img
                                src={student.photoUrl}
                                alt={student.name}
                                className="h-10 w-10 rounded-full object-cover"
                              />
                            ) : (
                              <div className="h-10 w-10 bg-primary-100 dark:bg-primary-800/20 rounded-full flex items-center justify-center text-primary-600 dark:text-primary-400">
                                <Users size={20} />
                              </div>
                            )}
                          </div>
                          <div className="ml-3 flex-1">
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {student.name}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {t("students.grade")} {student.grade}
                            </p>
                          </div>
                          <div className="flex-shrink-0">
                            {student.attendanceStatus === "present" && (
                              <CheckCircle
                                size={20}
                                className="text-green-500"
                              />
                            )}
                            {student.attendanceStatus === "absent" && (
                              <XCircle size={20} className="text-red-500" />
                            )}
                            {student.attendanceStatus === "pending" &&
                              selectedStudents.has(student.id) && (
                                <div className="w-4 h-4 bg-primary-500 rounded-full" />
                              )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6 text-gray-500 dark:text-gray-400">
                    <Users size={36} className="mx-auto mb-3" />
                    <p>{t("students.noStudentsAtStop")}</p>
                  </div>
                )}

                {/* Attendance Action Buttons */}
                {currentStopStudents.length > 0 && (
                  <div className="mt-4 flex space-x-2">
                    <Button
                      onClick={() => handleRecordAttendance("present")}
                      disabled={
                        selectedStudents.size === 0 || isRecordingAttendance
                      }
                      variant="success"
                      className="flex-1"
                    >
                      <CheckCircle size={16} className="mr-1" />
                      {t("students.markPresent")}
                    </Button>
                    <Button
                      onClick={() => handleRecordAttendance("absent")}
                      disabled={
                        selectedStudents.size === 0 || isRecordingAttendance
                      }
                      variant="destructive"
                      className="flex-1"
                    >
                      <XCircle size={16} className="mr-1" />
                      {t("students.markAbsent")}
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Mini Map */}
          {routeInfo && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
              <div className="p-3 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-base font-semibold text-gray-900 dark:text-white flex items-center">
                  <MapPin size={18} className="mr-2" />
                  {t("tracking.routeMap")}
                </h2>
              </div>
              <div className="h-64">
                <LiveMap selectedBusId={routeInfo.busId} />
              </div>
            </div>
          )}

          {/* Report Issue */}
          {routeInfo && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
              <h3 className="text-base font-semibold text-gray-900 dark:text-white mb-2">
                {t("routes.reportIssue")}
              </h3>
              <textarea
                value={issueReport}
                onChange={(e) => setIssueReport(e.target.value)}
                placeholder={t("routes.issueReportPlaceholder")}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
                rows={3}
              />
              <Button
                onClick={handleReportIssue}
                disabled={!issueReport.trim() || isReportingIssue}
                className="mt-2 w-full"
                variant="outline"
              >
                {isReportingIssue ? t("common.submitting") : t("common.submit")}
              </Button>
            </div>
          )}
        </div>

        {/* Bottom Navigation */}
        <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-2 flex justify-around">
          <a
            href="/dashboard"
            className="flex flex-col items-center p-2 text-gray-500 dark:text-gray-400"
          >
            <Home size={20} />
            <span className="text-xs mt-1">{t("common.home")}</span>
          </a>
          <a
            href="/dashboard/tracking"
            className="flex flex-col items-center p-2 text-primary-600 dark:text-primary-400"
          >
            <Navigation size={20} />
            <span className="text-xs mt-1">{t("routes.route")}</span>
          </a>
          <a
            href="/dashboard/profile"
            className="flex flex-col items-center p-2 text-gray-500 dark:text-gray-400"
          >
            <User size={20} />
            <span className="text-xs mt-1">{t("common.profile")}</span>
          </a>
        </div>
      </div>
    );
  }

  // Desktop layout
  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />

        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {routeInfo.name}
              </h1>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {t("buses.plateNumber")}: {routeInfo.busPlateNumber}
              </p>
            </div>

            {/* Attendance Mode Toggle */}
            <div className="mb-6">
              <div className="flex space-x-2">
                <Button
                  variant={attendanceMode === "pickup" ? "default" : "outline"}
                  onClick={() => setAttendanceMode("pickup")}
                >
                  {t("attendance.pickup")}
                </Button>
                <Button
                  variant={attendanceMode === "dropoff" ? "default" : "outline"}
                  onClick={() => setAttendanceMode("dropoff")}
                >
                  {t("attendance.dropoff")}
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Route Progress */}
              <div className="lg:col-span-1">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                      <Navigation size={20} className="mr-2" />
                      {t("routes.routeProgress")}
                    </h2>
                  </div>
                  <div className="p-4">
                    <div className="space-y-3">
                      {routeInfo.stops.map((stop, index) => (
                        <div
                          key={stop.id}
                          className={`flex items-center p-3 rounded-md cursor-pointer transition-colors ${
                            index === currentStopIndex
                              ? "bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800"
                              : "bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600"
                          }`}
                          onClick={() => setCurrentStopIndex(index)}
                        >
                          <div
                            className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-3 ${
                              stop.completed
                                ? "bg-green-500 text-white"
                                : index === currentStopIndex
                                  ? "bg-primary-500 text-white"
                                  : "bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-300"
                            }`}
                          >
                            {stop.completed ? (
                              <CheckCircle size={16} />
                            ) : (
                              <span className="text-xs font-medium">
                                {stop.order}
                              </span>
                            )}
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {stop.name}
                            </p>
                            {stop.arrivalTime && (
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {stop.arrivalTime}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Current Stop Students */}
              <div className="lg:col-span-2">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex justify-between items-center">
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                        <Users size={20} className="mr-2" />
                        {routeInfo.stops[currentStopIndex]?.name} -{" "}
                        {t("students.students")}
                      </h2>
                      <div className="flex space-x-2">
                        <Button
                          onClick={() => handleRecordAttendance("present")}
                          disabled={
                            selectedStudents.size === 0 || isRecordingAttendance
                          }
                          variant="success"
                          size="sm"
                        >
                          <CheckCircle size={16} className="mr-1" />
                          {t("students.markPresent")}
                        </Button>
                        <Button
                          onClick={() => handleRecordAttendance("absent")}
                          disabled={
                            selectedStudents.size === 0 || isRecordingAttendance
                          }
                          variant="destructive"
                          size="sm"
                        >
                          <XCircle size={16} className="mr-1" />
                          {t("students.markAbsent")}
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="p-4">
                    {currentStopStudents.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {currentStopStudents.map((student) => (
                          <div
                            key={student.id}
                            className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                              selectedStudents.has(student.id)
                                ? "border-primary-500 bg-primary-50 dark:bg-primary-900/20"
                                : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
                            } ${
                              student.attendanceStatus === "present"
                                ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800"
                                : student.attendanceStatus === "absent"
                                  ? "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
                                  : ""
                            }`}
                            onClick={() => {
                              if (student.attendanceStatus === "pending") {
                                handleStudentToggle(student.id);
                              }
                            }}
                          >
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                {student.photoUrl ? (
                                  <img
                                    src={student.photoUrl}
                                    alt={student.name}
                                    className="h-10 w-10 rounded-full object-cover"
                                  />
                                ) : (
                                  <div className="h-10 w-10 bg-primary-100 dark:bg-primary-800/20 rounded-full flex items-center justify-center text-primary-600 dark:text-primary-400">
                                    <Users size={20} />
                                  </div>
                                )}
                              </div>
                              <div className="ml-3 flex-1">
                                <p className="text-sm font-medium text-gray-900 dark:text-white">
                                  {student.name}
                                </p>
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  {t("students.grade")} {student.grade}
                                </p>
                              </div>
                              <div className="flex-shrink-0">
                                {student.attendanceStatus === "present" && (
                                  <CheckCircle
                                    size={20}
                                    className="text-green-500"
                                  />
                                )}
                                {student.attendanceStatus === "absent" && (
                                  <XCircle size={20} className="text-red-500" />
                                )}
                                {student.attendanceStatus === "pending" &&
                                  selectedStudents.has(student.id) && (
                                    <div className="w-4 h-4 bg-primary-500 rounded-full" />
                                  )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                        <Users size={48} className="mx-auto mb-4" />
                        <p>{t("students.noStudentsAtStop")}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Live Map */}
            <div className="mt-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                    <MapPin size={20} className="mr-2" />
                    {t("tracking.routeMap")}
                  </h2>
                </div>
                <div className="h-96">
                  <LiveMap selectedBusId={routeInfo.busId} />
                </div>
              </div>
            </div>

            {/* Report Issue */}
            <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow p-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                {t("routes.reportIssue")}
              </h3>
              <div className="flex">
                <textarea
                  value={issueReport}
                  onChange={(e) => setIssueReport(e.target.value)}
                  placeholder={t("routes.issueReportPlaceholder")}
                  className="flex-1 p-2 border border-gray-300 dark:border-gray-600 rounded-l-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  rows={2}
                />
                <Button
                  onClick={handleReportIssue}
                  disabled={!issueReport.trim() || isReportingIssue}
                  className="rounded-l-none"
                >
                  {isReportingIssue
                    ? t("common.submitting")
                    : t("common.submit")}
                </Button>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};
