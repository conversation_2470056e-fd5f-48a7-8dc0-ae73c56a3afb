import { User, UserRole } from "../types";
import { RBACHelper, ResourceType, Action } from "../lib/rbac";

/**
 * Simplified Auth Middleware
 * Basic permission checking for the school bus management system
 */
export class AuthMiddleware {
  /**
   * Check if user can perform action on resource
   */
  static checkResourceAccess(
    user: User,
    resource: ResourceType,
    action: Action,
  ): { allowed: boolean; error?: string } {
    if (!user) {
      return {
        allowed: false,
        error: "User not authenticated",
      };
    }

    const userRole = user.role as UserRole;
    const hasPermission = RBACHelper.canPerformAction(userRole, resource, action);

    if (!hasPermission) {
      return {
        allowed: false,
        error: `Access denied: ${userRole} cannot ${action} ${resource}`,
      };
    }

    return { allowed: true };
  }

  /**
   * Sanitize input to prevent XSS
   */
  static sanitizeInput(input: any): any {
    if (typeof input === "string") {
      return input
        .replace(/<script[^>]*>.*?<\/script>/gi, "")
        .replace(/<[^>]*>/g, "")
        .replace(/javascript:/gi, "")
        .trim();
    }

    if (Array.isArray(input)) {
      return input.map(item => this.sanitizeInput(item));
    }

    if (typeof input === "object" && input !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(input)) {
        sanitized[key] = this.sanitizeInput(value);
      }
      return sanitized;
    }

    return input;
  }
}
