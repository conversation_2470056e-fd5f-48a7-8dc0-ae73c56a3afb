import React, { useState, useEffect } from "react";
import {
  Bell,
  BellOff,
  Smartphone,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import {
  pushNotificationService,
  getUserPushSubscriptionStatus,
} from "../../lib/pushNotifications";
import { cn } from "../../utils/cn";

interface PushNotificationToggleProps {
  className?: string;
  showLabel?: boolean;
  size?: "sm" | "md" | "lg";
  variant?: "button" | "switch";
  onStatusChange?: (enabled: boolean) => void;
}

export const PushNotificationToggle: React.FC<PushNotificationToggleProps> = ({
  className = "",
  showLabel = true,
  size = "md",
  variant = "button",
  onStatusChange,
}) => {
  const { user } = useAuth();
  const [isEnabled, setIsEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [permission, setPermission] =
    useState<NotificationPermission>("default");
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    initializePushNotifications();
    checkPermissionStatus();
  }, [user]);

  const initializePushNotifications = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError(null);

      // Initialize push notification service
      const initialized = await pushNotificationService.initialize();
      setIsInitialized(initialized);

      if (initialized) {
        // Check current subscription status
        const subscriptionStatus = await getUserPushSubscriptionStatus(user.id);
        const isCurrentlySubscribed =
          await pushNotificationService.isSubscribed();

        setIsEnabled(
          subscriptionStatus.hasSubscription && isCurrentlySubscribed,
        );
      }
    } catch (err) {
      console.error("Error initializing push notifications:", err);
      setError("Failed to initialize push notifications");
    } finally {
      setIsLoading(false);
    }
  };

  const checkPermissionStatus = () => {
    if ("Notification" in window) {
      setPermission(Notification.permission);
    }
  };

  const handleToggle = async () => {
    if (!user || !isInitialized) {
      setError("User not authenticated or service not initialized");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      if (isEnabled) {
        // Unsubscribe from push notifications
        const success = await pushNotificationService.unsubscribeWithStorage(
          user.id,
        );
        if (success) {
          setIsEnabled(false);
          onStatusChange?.(false);
        } else {
          throw new Error("Failed to unsubscribe from push notifications");
        }
      } else {
        // Request permission first if not granted
        if (permission !== "granted") {
          const newPermission =
            await pushNotificationService.requestPermission();
          setPermission(newPermission);

          if (newPermission !== "granted") {
            throw new Error(
              "Push notification permission denied. Please enable notifications in your browser settings.",
            );
          }
        }

        // Subscribe to push notifications
        const subscription = await pushNotificationService.subscribeWithStorage(
          user.id,
          user.tenant_id || undefined,
        );

        if (subscription) {
          setIsEnabled(true);
          setPermission("granted");
          onStatusChange?.(true);
        } else {
          throw new Error("Failed to create push notification subscription");
        }
      }
    } catch (err: any) {
      console.error("Error toggling push notifications:", err);
      setError(err.message || "Failed to update push notification settings");

      // Reset permission status on error
      checkPermissionStatus();
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = () => {
    if (isLoading) {
      return (
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
      );
    }

    if (error) {
      return <AlertCircle size={16} className="text-red-500" />;
    }

    if (permission === "denied") {
      return <BellOff size={16} className="text-red-500" />;
    }

    if (isEnabled) {
      return <CheckCircle size={16} className="text-green-500" />;
    }

    return <Bell size={16} className="text-gray-500" />;
  };

  const getStatusText = () => {
    if (isLoading) return "Checking...";
    if (error) return "Error";
    if (permission === "denied") return "Blocked";
    if (isEnabled) return "Enabled";
    return "Disabled";
  };

  const getStatusColor = () => {
    if (error || permission === "denied")
      return "text-red-600 dark:text-red-400";
    if (isEnabled) return "text-green-600 dark:text-green-400";
    return "text-gray-600 dark:text-gray-400";
  };

  if (!isInitialized && !isLoading) {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <BellOff size={16} className="text-gray-400" />
        {showLabel && (
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Push notifications not supported
          </span>
        )}
      </div>
    );
  }

  if (variant === "switch") {
    return (
      <div className={cn("flex items-center space-x-3", className)}>
        {showLabel && (
          <div className="flex items-center space-x-2">
            <Smartphone
              size={16}
              className="text-gray-600 dark:text-gray-400"
            />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Push Notifications
            </span>
          </div>
        )}

        <div className="flex items-center space-x-2">
          <button
            onClick={handleToggle}
            disabled={isLoading || permission === "denied" || !isInitialized}
            className={cn(
              "relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",
              isEnabled ? "bg-primary-600" : "bg-gray-200 dark:bg-gray-700",
            )}
          >
            <span
              className={cn(
                "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                isEnabled ? "translate-x-6" : "translate-x-1",
              )}
            />
          </button>

          <div className="flex items-center space-x-1">
            {getStatusIcon()}
            {showLabel && (
              <span className={cn("text-xs", getStatusColor())}>
                {getStatusText()}
              </span>
            )}
          </div>
        </div>

        {error && (
          <div className="text-xs text-red-600 dark:text-red-400 mt-1">
            {error}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <Button
        variant={isEnabled ? "default" : "outline"}
        size={size}
        onClick={handleToggle}
        disabled={isLoading || permission === "denied" || !isInitialized}
        className={cn(
          "flex items-center space-x-2",
          permission === "denied" && "opacity-50 cursor-not-allowed",
        )}
      >
        {getStatusIcon()}
        {showLabel && (
          <span>
            {permission === "denied"
              ? "Notifications Blocked"
              : isEnabled
                ? "Push Notifications On"
                : "Enable Push Notifications"}
          </span>
        )}
      </Button>

      {error && (
        <div className="text-xs text-red-600 dark:text-red-400">{error}</div>
      )}

      {permission === "denied" && (
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Please enable notifications in your browser settings
        </div>
      )}
    </div>
  );
};

export default PushNotificationToggle;
