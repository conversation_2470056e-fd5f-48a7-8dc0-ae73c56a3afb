/**
 * Centralized Permission Guard Component
 * Uses the centralized permission service for consistent access control
 * Phase 1: RBAC Security Enhancement
 */

import React, { ReactNode, useMemo } from "react";
import { usePermissionService } from "../../hooks/usePermissionService";
import { ResourceType, Action, PermissionContext } from "../../lib/rbac";
import { Alert, AlertDescription } from "../ui/Alert";
import { Loader2 } from "lucide-react";

interface CentralizedPermissionGuardProps {
  children: ReactNode;
  resource: ResourceType;
  action: Action;
  context?: PermissionContext;
  fallback?: ReactNode;
  showError?: boolean;
  errorMessage?: string;
  loadingComponent?: ReactNode;
}

/**
 * Permission Guard that uses centralized permission service
 * Provides consistent permission checking across the application
 */
export const CentralizedPermissionGuard: React.FC<CentralizedPermissionGuardProps> = ({
  children,
  resource,
  action,
  context,
  fallback,
  showError = true,
  errorMessage,
  loadingComponent,
}) => {
  const { checkPermission, isLoading, hasValidUser } = usePermissionService();

  const permissionResult = useMemo(() => {
    if (!hasValidUser) {
      return {
        allowed: false,
        reason: "User not authenticated",
        securityLevel: "high" as const,
        auditRequired: true,
        riskScore: 90,
      };
    }

    return checkPermission(resource, action, context);
  }, [checkPermission, resource, action, context, hasValidUser]);

  // Show loading state
  if (isLoading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }
    
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Checking permissions...</span>
      </div>
    );
  }

  // Permission denied
  if (!permissionResult.allowed) {
    // Log high-risk permission denials
    if (permissionResult.securityLevel === "critical" || permissionResult.securityLevel === "high") {
      console.warn("🚨 High-risk permission denial:", {
        resource,
        action,
        reason: permissionResult.reason,
        securityLevel: permissionResult.securityLevel,
        riskScore: permissionResult.riskScore,
      });
    }

    // Return custom fallback if provided
    if (fallback) {
      return <>{fallback}</>;
    }

    // Show error message if enabled
    if (showError) {
      const displayMessage = errorMessage || 
        permissionResult.reason || 
        `You don't have permission to ${action.toLowerCase()} ${resource.toLowerCase()}`;

      return (
        <Alert variant="destructive" className="m-4">
          <AlertDescription>
            {displayMessage}
          </AlertDescription>
        </Alert>
      );
    }

    // Return null if no fallback and no error display
    return null;
  }

  // Permission granted - render children
  return <>{children}</>;
};

/**
 * Higher-order component for permission protection
 */
export const withCentralizedPermission = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  resource: ResourceType,
  action: Action,
  getContext?: (props: P) => PermissionContext | undefined
) => {
  const PermissionProtectedComponent: React.FC<P> = (props) => {
    const context = getContext ? getContext(props) : undefined;

    return (
      <CentralizedPermissionGuard
        resource={resource}
        action={action}
        context={context}
      >
        <WrappedComponent {...props} />
      </CentralizedPermissionGuard>
    );
  };

  PermissionProtectedComponent.displayName = 
    `withCentralizedPermission(${WrappedComponent.displayName || WrappedComponent.name})`;

  return PermissionProtectedComponent;
};

/**
 * Hook for conditional rendering based on permissions
 */
export const useConditionalRender = () => {
  const { checkPermission } = usePermissionService();

  return {
    renderIf: (
      resource: ResourceType,
      action: Action,
      context?: PermissionContext
    ) => (component: ReactNode) => {
      const result = checkPermission(resource, action, context);
      return result.allowed ? component : null;
    },
    
    renderUnless: (
      resource: ResourceType,
      action: Action,
      context?: PermissionContext
    ) => (component: ReactNode) => {
      const result = checkPermission(resource, action, context);
      return !result.allowed ? component : null;
    },
  };
};

/**
 * Component for rendering content based on multiple permissions
 */
interface MultiPermissionGuardProps {
  children: ReactNode;
  permissions: Array<{
    resource: ResourceType;
    action: Action;
    context?: PermissionContext;
  }>;
  requireAll?: boolean; // true = AND logic, false = OR logic
  fallback?: ReactNode;
  showError?: boolean;
}

export const MultiPermissionGuard: React.FC<MultiPermissionGuardProps> = ({
  children,
  permissions,
  requireAll = true,
  fallback,
  showError = true,
}) => {
  const { checkPermission, isLoading } = usePermissionService();

  const permissionResults = useMemo(() => {
    return permissions.map(({ resource, action, context }) =>
      checkPermission(resource, action, context)
    );
  }, [checkPermission, permissions]);

  const hasAccess = useMemo(() => {
    if (requireAll) {
      return permissionResults.every(result => result.allowed);
    } else {
      return permissionResults.some(result => result.allowed);
    }
  }, [permissionResults, requireAll]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Checking permissions...</span>
      </div>
    );
  }

  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (showError) {
      const deniedPermissions = permissionResults
        .filter(result => !result.allowed)
        .map((result, index) => ({
          ...permissions[index],
          reason: result.reason,
        }));

      return (
        <Alert variant="destructive" className="m-4">
          <AlertDescription>
            Access denied. Missing required permissions:
            <ul className="mt-2 list-disc list-inside">
              {deniedPermissions.map((perm, index) => (
                <li key={index}>
                  {perm.action} on {perm.resource}
                  {perm.reason && ` (${perm.reason})`}
                </li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      );
    }

    return null;
  }

  return <>{children}</>;
};

/**
 * Component for role-based rendering
 */
interface RoleGuardProps {
  children: ReactNode;
  allowedRoles: string[];
  fallback?: ReactNode;
  showError?: boolean;
}

export const RoleGuard: React.FC<RoleGuardProps> = ({
  children,
  allowedRoles,
  fallback,
  showError = true,
}) => {
  const { hasValidUser } = usePermissionService();
  // Note: We'll need to get user role from auth context
  // This is a simplified version for now

  if (!hasValidUser) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (showError) {
      return (
        <Alert variant="destructive" className="m-4">
          <AlertDescription>
            Authentication required
          </AlertDescription>
        </Alert>
      );
    }

    return null;
  }

  // TODO: Implement role checking logic
  // For now, we'll render children
  return <>{children}</>;
};
