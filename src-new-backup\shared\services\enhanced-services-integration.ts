/**
 * Enhanced Services Integration - تكامل الخدمات المحسنة
 * يدمج جميع التحسينات في الخدمات الموجودة
 */

import { StudentService } from './data/StudentService';
import { BusService } from './data/BusService';
import { AttendanceService } from './data/AttendanceService';
import EnhancedValidationService from '../lib/validation-enhanced';
import TransactionManager from '../lib/transaction-manager';
import EnhancedNotificationService from '../lib/notification-service-enhanced';
import CacheManager from '../lib/cache-manager';
import RBACEnhancedManager from '../lib/rbac-fixes';
import { APIResponse } from '../api/types';

/**
 * خدمة الطلاب المحسنة
 */
export class EnhancedStudentService extends StudentService {
  
  /**
   * إنشاء طالب مع التحقق الشامل والمعاملات
   */
  async createStudentEnhanced(studentData: {
    name: string;
    student_id: string;
    grade: string;
    class: string;
    parent_id: string;
    route_stop_id?: string;
    tenant_id: string;
    profile: {
      date_of_birth: string;
      address: any;
      emergency_contacts: any[];
      medical_info?: any;
      photo_url?: string;
    };
  }): Promise<APIResponse<any>> {
    
    try {
      // التحقق من صحة البيانات
      const validation = await EnhancedValidationService.validateStudentData({
        name: studentData.name,
        student_id: studentData.student_id,
        grade: studentData.grade,
        tenant_id: studentData.tenant_id,
        email: studentData.profile.emergency_contacts?.[0]?.email,
        phone: studentData.profile.emergency_contacts?.[0]?.phone,
        date_of_birth: studentData.profile.date_of_birth
      });
      
      if (!validation.isValid) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_FAILED',
            message: validation.errors.join(', ')
          }
        };
      }
      
      // إنشاء الطالب باستخدام المعاملات
      const result = await TransactionManager.createStudentWithTransaction(studentData);
      
      if (result.success) {
        // مسح Cache المتعلق
        CacheManager.deleteByPattern(/^students:/);
        
        // إرسال إشعار ترحيب
        await EnhancedNotificationService.sendNotificationWithRetry({
          title: 'مرحباً بالطالب الجديد',
          message: `تم تسجيل الطالب ${studentData.name} بنجاح في النظام`,
          type: 'announcements',
          tenantId: studentData.tenant_id,
          userIds: [studentData.parent_id],
          priority: 'normal'
        });
      }
      
      return result;
      
    } catch (error) {
      console.error('Error in createStudentEnhanced:', error);
      return {
        success: false,
        error: {
          code: 'SYSTEM_ERROR',
          message: 'حدث خطأ في النظام أثناء إنشاء الطالب'
        }
      };
    }
  }
  
  /**
   * الحصول على طالب مع Cache
   */
  async getStudentByIdCached(id: string): Promise<APIResponse<any>> {
    const cacheKey = `student:${id}`;
    
    try {
      return await CacheManager.getOrFetch(
        cacheKey,
        () => this.getStudentById(id),
        2 * 60 * 1000 // 2 minutes cache
      );
    } catch (error) {
      console.error('Error in getStudentByIdCached:', error);
      return this.getStudentById(id);
    }
  }
  
  /**
   * تحديث طالب مع التحقق والمعاملات
   */
  async updateStudentEnhanced(
    id: string,
    updateData: {
      name?: string;
      grade?: string;
      route_stop_id?: string;
      profile?: any;
    }
  ): Promise<APIResponse<any>> {
    
    try {
      // التحقق من صحة البيانات المحدثة
      if (updateData.name || updateData.grade) {
        const validation = await EnhancedValidationService.validateStudentData({
          name: updateData.name || 'temp',
          student_id: 'temp',
          grade: updateData.grade || 'temp',
          tenant_id: 'temp',
          excludeId: id
        });
        
        if (!validation.isValid) {
          return {
            success: false,
            error: {
              code: 'VALIDATION_FAILED',
              message: validation.errors.join(', ')
            }
          };
        }
      }
      
      // تحديث باستخدام المعاملات
      const result = await TransactionManager.updateStudentWithTransaction(id, updateData);
      
      if (result.success) {
        // مسح Cache
        CacheManager.delete(`student:${id}`);
        CacheManager.deleteByPattern(/^students:/);
      }
      
      return result;
      
    } catch (error) {
      console.error('Error in updateStudentEnhanced:', error);
      return {
        success: false,
        error: {
          code: 'SYSTEM_ERROR',
          message: 'حدث خطأ في النظام أثناء تحديث الطالب'
        }
      };
    }
  }
}

/**
 * خدمة الحافلات المحسنة
 */
export class EnhancedBusService extends BusService {
  
  /**
   * تعيين سائق للحافلة مع التحقق
   */
  async assignDriverEnhanced(busId: string, driverId: string, tenantId: string): Promise<APIResponse<any>> {
    
    try {
      // التحقق من صحة التعيين
      const driverValidation = await EnhancedValidationService.validateDriverAssignment(driverId, busId);
      
      if (!driverValidation.isValid) {
        return {
          success: false,
          error: {
            code: 'DRIVER_ASSIGNMENT_FAILED',
            message: driverValidation.error || 'فشل في تعيين السائق'
          }
        };
      }
      
      // تعيين باستخدام المعاملات
      const result = await TransactionManager.assignDriverToBusWithTransaction(busId, driverId, tenantId);
      
      if (result.success) {
        // مسح Cache
        CacheManager.deleteByPattern(/^bus:/);
        
        // إرسال إشعار
        await EnhancedNotificationService.sendNotificationWithRetry({
          title: 'تم تعيين سائق جديد',
          message: `تم تعيين سائق جديد للحافلة`,
          type: 'announcements',
          tenantId: tenantId,
          priority: 'normal'
        });
      }
      
      return result;
      
    } catch (error) {
      console.error('Error in assignDriverEnhanced:', error);
      return {
        success: false,
        error: {
          code: 'SYSTEM_ERROR',
          message: 'حدث خطأ في النظام أثناء تعيين السائق'
        }
      };
    }
  }
  
  /**
   * إنشاء حافلة مع التحقق الشامل
   */
  async createBusEnhanced(busData: {
    plate_number: string;
    capacity: number;
    driver_id?: string;
    tenant_id: string;
    notes?: string;
  }): Promise<APIResponse<any>> {
    
    try {
      // التحقق من صحة البيانات
      const validation = await EnhancedValidationService.validateBusData({
        plate_number: busData.plate_number,
        capacity: busData.capacity,
        driver_id: busData.driver_id
      });
      
      if (!validation.isValid) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_FAILED',
            message: validation.errors.join(', ')
          }
        };
      }
      
      // إنشاء الحافلة
      const result = await this.create(busData);
      
      if (result.success) {
        // مسح Cache
        CacheManager.deleteByPattern(/^bus:/);
      }
      
      return result;
      
    } catch (error) {
      console.error('Error in createBusEnhanced:', error);
      return {
        success: false,
        error: {
          code: 'SYSTEM_ERROR',
          message: 'حدث خطأ في النظام أثناء إنشاء الحافلة'
        }
      };
    }
  }
}

/**
 * خدمة الحضور المحسنة
 */
export class EnhancedAttendanceService extends AttendanceService {
  
  /**
   * تسجيل حضور مع إشعارات
   */
  async recordAttendanceEnhanced(attendanceData: {
    student_id: string;
    status: 'present' | 'absent' | 'late';
    recorded_by: string;
    tenant_id: string;
    notes?: string;
  }): Promise<APIResponse<any>> {
    
    try {
      // تسجيل الحضور
      const result = await this.recordAttendance(attendanceData);
      
      if (result.success && attendanceData.status === 'absent') {
        // إرسال إشعار لولي الأمر في حالة الغياب
        const { data: student } = await new EnhancedStudentService().getStudentById(attendanceData.student_id);
        
        if (student?.parent_id) {
          await EnhancedNotificationService.sendNotificationWithRetry({
            title: 'إشعار غياب',
            message: `الطالب ${student.name} غائب اليوم`,
            type: 'attendance',
            tenantId: attendanceData.tenant_id,
            userIds: [student.parent_id],
            priority: 'high'
          });
        }
      }
      
      // مسح Cache
      CacheManager.deleteByPattern(/^attendance:/);
      
      return result;
      
    } catch (error) {
      console.error('Error in recordAttendanceEnhanced:', error);
      return {
        success: false,
        error: {
          code: 'SYSTEM_ERROR',
          message: 'حدث خطأ في النظام أثناء تسجيل الحضور'
        }
      };
    }
  }
}

/**
 * خدمة التحقق من الصلاحيات المحسنة
 */
export class EnhancedPermissionService {
  
  /**
   * التحقق من الصلاحيات مع التسجيل
   */
  static async checkPermissionWithAudit(
    userId: string,
    userRole: any,
    resource: any,
    action: any,
    context?: any
  ): Promise<boolean> {
    
    try {
      const hasPermission = await RBACEnhancedManager.checkPermissionWithLogging(
        userId,
        userRole,
        resource,
        action,
        context
      );
      
      return hasPermission;
      
    } catch (error) {
      console.error('Error in checkPermissionWithAudit:', error);
      return false;
    }
  }
  
  /**
   * التحقق من صلاحيات متعددة
   */
  static async checkMultiplePermissions(
    userRole: any,
    permissions: Array<{ resource: any; action: any; context?: any }>,
    requireAll: boolean = true
  ): Promise<{ allowed: boolean; results: Array<{ allowed: boolean; reason?: string }> }> {
    
    try {
      return await RBACEnhancedManager.checkMultiplePermissions(userRole, permissions, requireAll);
    } catch (error) {
      console.error('Error in checkMultiplePermissions:', error);
      return {
        allowed: false,
        results: permissions.map(() => ({ allowed: false, reason: 'System error' }))
      };
    }
  }
}

/**
 * خدمة الإحصائيات المحسنة
 */
export class EnhancedAnalyticsService {
  
  /**
   * الحصول على إحصائيات شاملة مع Cache
   */
  static async getComprehensiveStats(tenantId: string): Promise<any> {
    const cacheKey = `analytics:comprehensive:${tenantId}`;
    
    try {
      return await CacheManager.getOrFetch(
        cacheKey,
        async () => {
          // جمع الإحصائيات من مصادر متعددة
          const [notificationStats, cacheStats] = await Promise.all([
            EnhancedNotificationService.getNotificationAnalytics(tenantId),
            Promise.resolve(CacheManager.getStats())
          ]);
          
          return {
            notifications: notificationStats,
            cache: cacheStats,
            timestamp: new Date().toISOString()
          };
        },
        5 * 60 * 1000 // 5 minutes cache
      );
    } catch (error) {
      console.error('Error in getComprehensiveStats:', error);
      return null;
    }
  }
}

// تصدير الخدمات المحسنة
export {
  EnhancedValidationService,
  TransactionManager,
  EnhancedNotificationService,
  CacheManager,
  RBACEnhancedManager
};
