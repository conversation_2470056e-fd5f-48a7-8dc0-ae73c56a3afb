import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Map,
  Bus,
  Users,
  Clock,
} from "lucide-react";
import { supabase } from "../../../shared/services/lib/supabase";
import { Route } from "../../types";
import { Button } from "../../components/ui/Button";
import { Pagination, usePagination } from "../../components/ui/Pagination";
import RouteModal from "../../components/routes/RouteModal";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { ResponsiveLayout } from "../../components/layout/ResponsiveLayout";
import { getAllRoutes, getRoutesByTenant } from "../../lib/api";

const RoutesPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { routes: dbRoutes, buses, loading: dbLoading } = useDatabase();
  const [routes, setRoutes] = useState<Route[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterActive, setFilterActive] = useState<boolean | null>(null);
  const [filterBusAssigned, setFilterBusAssigned] = useState<boolean | null>(
    null,
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentRoute, setCurrentRoute] = useState<Route | null>(null);
  const [routeStats, setRouteStats] = useState({
    total: 0,
    active: 0,
    withBuses: 0,
    totalStops: 0,
  });

  useEffect(() => {
    if (!dbLoading) {
      fetchRoutes();
    }
  }, [dbLoading, dbRoutes]);

  const fetchRoutes = async () => {
    try {
      setIsLoading(true);

      // Use the API function to avoid RLS recursion issues
      let routesData;
      if (user?.role === "admin") {
        routesData = await getAllRoutes();
      } else if (user?.tenant_id) {
        routesData = await getRoutesByTenant(user.tenant_id);
      } else {
        throw new Error("No tenant access");
      }

      const data = routesData;

      if (data) {
        const formattedRoutes: Route[] = data.map((route) => ({
          id: route.id,
          name: route.name,
          schoolId: route.tenant_id,
          busId: route.bus_id || undefined,
          isActive: route.is_active,
          stops:
            route.stops
              ?.map((stop) => ({
                id: stop.id,
                name: stop.name,
                routeId: route.id,
                order: stop.order,
                location: stop.location as { lat: number; lng: number },
                students: [],
                arrivalTime: stop.arrival_time || undefined,
              }))
              .sort((a, b) => a.order - b.order) || [],
          bus: route.bus
            ? {
                id: route.bus.id,
                plateNumber: route.bus.plate_number,
                capacity: route.bus.capacity,
                schoolId: route.tenant_id,
                driverId: route.bus.driver_id || undefined,
                isActive: true,
                driver: route.bus.driver,
              }
            : undefined,
        }));
        setRoutes(formattedRoutes);

        // Calculate stats
        const stats = {
          total: formattedRoutes.length,
          active: formattedRoutes.filter((r) => r.isActive).length,
          withBuses: formattedRoutes.filter((r) => r.busId).length,
          totalStops: formattedRoutes.reduce(
            (sum, r) => sum + r.stops.length,
            0,
          ),
        };
        setRouteStats(stats);
      }
    } catch (error) {
      console.error("Error fetching routes:", error);
      alert(t("routes.errorFetchingRoutes"));
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddRoute = () => {
    setCurrentRoute(null);
    setIsModalOpen(true);
  };

  const handleEditRoute = (route: Route) => {
    setCurrentRoute(route);
    setIsModalOpen(true);
  };

  const handleDeleteRoute = async (id: string) => {
    if (window.confirm(t("routes.deleteConfirmation"))) {
      try {
        // First delete all route stops
        const { error: stopsError } = await supabase
          .from("route_stops")
          .delete()
          .eq("route_id", id);

        if (stopsError) {
          console.error("Error deleting route stops:", stopsError);
        }

        // Then delete the route
        const { error } = await supabase.from("routes").delete().eq("id", id);
        if (error) throw error;

        alert(t("routes.routeDeleted"));
        fetchRoutes();
      } catch (error) {
        console.error("Error deleting route:", error);
        alert(t("routes.errorDeletingRoute"));
      }
    }
  };

  const handleModalClose = (refreshData: boolean = false) => {
    setIsModalOpen(false);
    if (refreshData) {
      fetchRoutes();
    }
  };

  const filteredRoutes = routes.filter((route) => {
    const matchesSearch =
      route.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      route.bus?.plateNumber
        ?.toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      route.stops.some((stop) =>
        stop.name.toLowerCase().includes(searchQuery.toLowerCase()),
      );

    const matchesActiveFilter =
      filterActive === null || route.isActive === filterActive;

    const matchesBusFilter =
      filterBusAssigned === null ||
      (filterBusAssigned ? !!route.busId : !route.busId);

    return matchesSearch && matchesActiveFilter && matchesBusFilter;
  });

  // Pagination
  const { currentPage, totalPages, startIndex, endIndex, goToPage } =
    usePagination(filteredRoutes.length, 10);

  const paginatedRoutes = filteredRoutes.slice(startIndex, endIndex);

  return (
    <ResponsiveLayout showSidebar={true}>
      <div className="container mx-auto px-4 py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t("routes.manageRoutes")}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t("routes.manageRoutesDescription")}
            </p>
          </div>
          <Button onClick={handleAddRoute} leftIcon={<Plus size={16} />}>
            {t("routes.addRoute")}
          </Button>
        </div>

        {/* Route Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Map className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("routes.totalRoutes")}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {routeStats.total}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <Clock className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("routes.activeRoutes")}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {routeStats.active}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                <Bus className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("routes.routesWithBuses")}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {routeStats.withBuses}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <Users className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("routes.totalStops")}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {routeStats.totalStops}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={18} className="text-gray-400" />
              </div>
              <input
                type="text"
                className="pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                placeholder={t("common.search")}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter size={18} className="text-gray-500" />
              <select
                className="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                value={
                  filterActive === null
                    ? ""
                    : filterActive
                      ? "active"
                      : "inactive"
                }
                onChange={(e) => {
                  if (e.target.value === "") setFilterActive(null);
                  else setFilterActive(e.target.value === "active");
                }}
              >
                <option value="">{t("routes.allStatus")}</option>
                <option value="active">{t("common.active")}</option>
                <option value="inactive">{t("common.inactive")}</option>
              </select>

              <select
                className="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                value={
                  filterBusAssigned === null
                    ? ""
                    : filterBusAssigned
                      ? "assigned"
                      : "unassigned"
                }
                onChange={(e) => {
                  if (e.target.value === "") setFilterBusAssigned(null);
                  else setFilterBusAssigned(e.target.value === "assigned");
                }}
              >
                <option value="">{t("routes.allBuses")}</option>
                <option value="assigned">{t("routes.busAssigned")}</option>
                <option value="unassigned">{t("routes.busUnassigned")}</option>
              </select>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          ) : filteredRoutes.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              {searchQuery || filterActive !== null
                ? t("common.noSearchResults")
                : t("routes.noRoutes")}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      {t("routes.name")}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      {t("routes.status")}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      {t("routes.assignedBus")}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      {t("routes.stops")}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      {t("routes.driver")}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      {t("common.actions")}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {paginatedRoutes.map((route) => (
                    <tr key={route.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                        {route.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            route.isActive
                              ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                              : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                          }`}
                        >
                          {route.isActive
                            ? t("common.active")
                            : t("common.inactive")}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {route.bus ? (
                          <div className="flex items-center">
                            <Bus size={16} className="mr-2 text-blue-500" />
                            <span className="font-medium">
                              {route.bus.plateNumber}
                            </span>
                            <span className="ml-2 text-xs text-gray-400">
                              ({route.bus.capacity} seats)
                            </span>
                          </div>
                        ) : (
                          <span className="text-red-500">
                            {t("routes.noBusAssigned")}
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        <div className="flex items-center">
                          <Map size={16} className="mr-2 text-green-500" />
                          <span>
                            {route.stops.length} {t("routes.stops")}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {route.bus?.driver ? (
                          <div className="flex items-center">
                            <Users size={16} className="mr-2 text-purple-500" />
                            <span>{route.bus.driver.name}</span>
                          </div>
                        ) : (
                          <span className="text-orange-500">
                            {t("routes.noDriverAssigned")}
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditRoute(route)}
                            aria-label={t("common.edit")}
                          >
                            <Edit
                              size={16}
                              className="text-gray-500 dark:text-gray-400"
                            />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteRoute(route.id)}
                            aria-label={t("common.delete")}
                          >
                            <Trash2 size={16} className="text-red-500" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mt-4">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {t("common.showing")} {startIndex + 1} - {endIndex}{" "}
                  {t("common.of")} {filteredRoutes.length}{" "}
                  {t("routes.manageRoutes").toLowerCase()}
                </div>
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={goToPage}
                />
              </div>
            </div>
          )}
        </div>

        {isModalOpen && (
          <RouteModal
            isOpen={isModalOpen}
            onClose={handleModalClose}
            route={currentRoute}
          />
        )}
      </div>
    </ResponsiveLayout>
  );
};

export default RoutesPage;
export { RoutesPage };
