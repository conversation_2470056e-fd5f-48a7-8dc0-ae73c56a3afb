import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import {
  Filter,
  X,
  Bell,
  MapPin,
  Users,
  Wrench,
  Megaphone,
  AlertTriangle,
} from "lucide-react";
import { Button } from "../ui/Button";
import { cn } from "../../utils/cn";

interface NotificationFilterProps {
  selectedCategory?: string;
  selectedPriority?: string;
  selectedStatus?: string;
  onCategoryChange: (category: string) => void;
  onPriorityChange: (priority: string) => void;
  onStatusChange: (status: string) => void;
  onClearFilters: () => void;
  availableCategories: string[];
  className?: string;
}

const NOTIFICATION_CATEGORIES = [
  { id: "all", name: "All", icon: Bell, color: "text-gray-600" },
  { id: "geofence", name: "Location", icon: MapPin, color: "text-blue-600" },
  {
    id: "attendance",
    name: "Attendance",
    icon: Users,
    color: "text-green-600",
  },
  {
    id: "maintenance",
    name: "Maintenance",
    icon: Wrench,
    color: "text-orange-600",
  },
  {
    id: "announcements",
    name: "Announcements",
    icon: Megaphone,
    color: "text-purple-600",
  },
  {
    id: "emergency",
    name: "Emergency",
    icon: AlertTriangle,
    color: "text-red-600",
  },
];

const PRIORITY_LEVELS = [
  { id: "all", name: "All Priorities" },
  { id: "low", name: "Low" },
  { id: "normal", name: "Normal" },
  { id: "high", name: "High" },
  { id: "urgent", name: "Urgent" },
];

const STATUS_OPTIONS = [
  { id: "all", name: "All" },
  { id: "unread", name: "Unread" },
  { id: "read", name: "Read" },
];

export const NotificationFilter: React.FC<NotificationFilterProps> = ({
  selectedCategory = "all",
  selectedPriority = "all",
  selectedStatus = "all",
  onCategoryChange,
  onPriorityChange,
  onStatusChange,
  onClearFilters,
  availableCategories,
  className = "",
}) => {
  const { t } = useTranslation();

  const hasActiveFilters =
    selectedCategory !== "all" ||
    selectedPriority !== "all" ||
    selectedStatus !== "all";

  const visibleCategories = React.useMemo(() => {
    return NOTIFICATION_CATEGORIES.filter(
      (category) =>
        category.id === "all" || availableCategories.includes(category.id),
    );
  }, [availableCategories]);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Filter Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Filter size={16} className="text-gray-600 dark:text-gray-400" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Filters
          </span>
        </div>
        {hasActiveFilters && (
          <Button
            size="sm"
            variant="ghost"
            onClick={onClearFilters}
            leftIcon={<X size={14} />}
            className="text-xs"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Category Filter */}
      <div className="space-y-2">
        <label className="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">
          Category
        </label>
        <div className="flex flex-wrap gap-2">
          {visibleCategories.map((category) => {
            const Icon = category.icon;
            const isSelected = selectedCategory === category.id;

            return (
              <button
                key={category.id}
                onClick={() => onCategoryChange(category.id)}
                className={cn(
                  "flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                  isSelected
                    ? "bg-primary-100 text-primary-700 dark:bg-primary-900/20 dark:text-primary-400 border border-primary-200 dark:border-primary-800"
                    : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border border-transparent",
                )}
              >
                <Icon
                  size={14}
                  className={cn(
                    isSelected
                      ? "text-primary-600 dark:text-primary-400"
                      : category.color,
                  )}
                />
                <span>{category.name}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Priority Filter */}
      <div className="space-y-2">
        <label className="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">
          Priority
        </label>
        <select
          value={selectedPriority}
          onChange={(e) => onPriorityChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
        >
          {PRIORITY_LEVELS.map((priority) => (
            <option key={priority.id} value={priority.id}>
              {priority.name}
            </option>
          ))}
        </select>
      </div>

      {/* Status Filter */}
      <div className="space-y-2">
        <label className="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">
          Status
        </label>
        <div className="flex space-x-2">
          {STATUS_OPTIONS.map((status) => {
            const isSelected = selectedStatus === status.id;

            return (
              <button
                key={status.id}
                onClick={() => onStatusChange(status.id)}
                className={cn(
                  "flex-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200",
                  isSelected
                    ? "bg-primary-100 text-primary-700 dark:bg-primary-900/20 dark:text-primary-400 border border-primary-200 dark:border-primary-800"
                    : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border border-transparent",
                )}
              >
                {status.name}
              </button>
            );
          })}
        </div>
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
          <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
            Active filters:
          </div>
          <div className="flex flex-wrap gap-1">
            {selectedCategory !== "all" && (
              <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400">
                {
                  NOTIFICATION_CATEGORIES.find((c) => c.id === selectedCategory)
                    ?.name
                }
              </span>
            )}
            {selectedPriority !== "all" && (
              <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400">
                {PRIORITY_LEVELS.find((p) => p.id === selectedPriority)?.name}
              </span>
            )}
            {selectedStatus !== "all" && (
              <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                {STATUS_OPTIONS.find((s) => s.id === selectedStatus)?.name}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationFilter;
