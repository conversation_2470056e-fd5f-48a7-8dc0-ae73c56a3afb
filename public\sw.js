// Service Worker for push notifications
const CACHE_NAME = "schoolbus-notifications-v5";
const urlsToCache = [
  "/",
  "/bus-icon.svg",
  "/sounds/alert.mp3",
  "/sounds/bell.mp3",
  "/sounds/chime.mp3",
  "/sounds/ding.mp3",
  "/sounds/notification.mp3",
];

// Install event
self.addEventListener("install", (event) => {
  console.log("Service Worker installing...");
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      return cache.addAll(urlsToCache).catch((error) => {
        console.warn("Failed to cache some resources:", error);
        // Don't fail the installation if some resources can't be cached
        return Promise.resolve();
      });
    }),
  );
  // Force the waiting service worker to become the active service worker
  self.skipWaiting();
});

// Activate event
self.addEventListener("activate", (event) => {
  console.log("Service Worker activating...");
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log("Deleting old cache:", cacheName);
            return caches.delete(cacheName);
          }
        }),
      );
    }),
  );
  // Ensure the service worker takes control immediately
  self.clients.claim();
});

// Fetch event
self.addEventListener("fetch", (event) => {
  // Skip service worker entirely for these requests - let browser handle them
  if (
    event.request.url.includes("/node_modules/") ||
    event.request.url.includes("?v=") ||
    event.request.url.includes(".js?") ||
    event.request.url.includes("lucide-react") ||
    event.request.url.includes("/api/") ||
    event.request.url.includes("supabase") ||
    event.request.url.includes("/@") ||
    event.request.url.includes("/src/") ||
    event.request.destination === "script" ||
    event.request.destination === "module" ||
    event.request.method !== "GET" ||
    event.request.url.includes(".tsx") ||
    event.request.url.includes(".ts") ||
    event.request.url.includes(".jsx")
  ) {
    // Don't intercept these requests at all - let browser handle natively
    return;
  }

  event.respondWith(
    caches.match(event.request).then((response) => {
      // Return cached version or fetch from network
      return (
        response ||
        fetch(event.request).catch((error) => {
          console.warn("Fetch failed for:", event.request.url, error.message);
          // For navigation requests, return a fallback
          if (event.request.mode === "navigate") {
            return caches.match("/");
          }
          // For development server requests, don't throw error
          if (event.request.url.includes('localhost') || event.request.url.includes('@vite')) {
            return new Response('', { status: 200 });
          }
          throw error;
        })
      );
    }),
  );
});

// Message event for handling test notifications
self.addEventListener("message", (event) => {
  console.log("Service Worker received message:", event.data);

  if (event.data.type === "TEST_NOTIFICATION") {
    const { payload } = event.data;
    self.registration.showNotification(payload.title, {
      body: payload.body,
      icon: payload.icon || "/bus-icon.svg",
      badge: payload.badge || "/bus-icon.svg",
      tag: payload.tag || "test",
      data: payload.data || {},
      actions: payload.actions || [],
      requireInteraction: payload.requireInteraction || false,
      vibrate: [100, 50, 100],
    });
  }

  if (event.data.type === "NOTIFICATION_RECEIVED") {
    const { notification } = event.data;
    self.registration.showNotification(notification.title, {
      body: notification.body,
      icon: notification.icon || "/bus-icon.svg",
      badge: notification.badge || "/bus-icon.svg",
      tag: notification.tag || "notification",
      data: notification.data || {},
      requireInteraction: notification.requireInteraction || false,
      vibrate: [100, 50, 100],
    });
  }
});

// Push event
self.addEventListener("push", (event) => {
  console.log("Push event received:", event);

  let notificationData = {
    title: "SchoolBus Notification",
    body: "You have a new notification",
    icon: "/bus-icon.svg",
    badge: "/bus-icon.svg",
    tag: "default",
    data: {
      timestamp: Date.now(),
      url: "/dashboard",
      notificationId: null,
      userId: null,
      tenantId: null,
    },
    actions: [],
    requireInteraction: false,
    vibrate: [100, 50, 100],
    image: null,
    dir: "auto",
    lang: "en",
  };

  // Parse push data if available
  if (event.data) {
    try {
      const pushData = event.data.json();
      notificationData = { ...notificationData, ...pushData };

      // Ensure data object has required fields
      notificationData.data = {
        timestamp: Date.now(),
        url: "/dashboard",
        ...notificationData.data,
        ...pushData.data,
      };
    } catch (error) {
      console.error("Error parsing push data:", error);
      notificationData.body = event.data.text() || notificationData.body;
    }
  }

  // Customize notification based on type
  const notificationType = notificationData.data?.type || notificationData.type;

  switch (notificationType) {
    case "geofence":
      notificationData.tag = "geofence";
      notificationData.requireInteraction = true;
      notificationData.actions = [
        {
          action: "view_location",
          title: "View Location",
          icon: "/bus-icon.svg",
        },
        {
          action: "dismiss",
          title: "Dismiss",
        },
      ];
      notificationData.vibrate = [200, 100, 200];
      break;

    case "attendance":
      notificationData.tag = "attendance";
      notificationData.actions = [
        {
          action: "view_attendance",
          title: "View Details",
          icon: "/bus-icon.svg",
        },
        {
          action: "acknowledge",
          title: "Acknowledge",
        },
        {
          action: "dismiss",
          title: "OK",
        },
      ];
      notificationData.vibrate = [100, 50, 100];
      break;

    case "maintenance":
      notificationData.tag = "maintenance";
      notificationData.requireInteraction = true;
      notificationData.actions = [
        {
          action: "view_maintenance",
          title: "View Details",
          icon: "/bus-icon.svg",
        },
        {
          action: "dismiss",
          title: "Later",
        },
      ];
      notificationData.vibrate = [300, 100, 300, 100, 300];
      break;

    case "announcement":
      notificationData.tag = "announcement";
      notificationData.actions = [
        {
          action: "read_more",
          title: "Read More",
          icon: "/bus-icon.svg",
        },
        {
          action: "dismiss",
          title: "Dismiss",
        },
      ];
      break;

    case "emergency":
      notificationData.tag = "emergency";
      notificationData.requireInteraction = true;
      notificationData.actions = [
        {
          action: "view_emergency",
          title: "View Details",
          icon: "/bus-icon.svg",
        },
        {
          action: "acknowledge",
          title: "Acknowledge",
        },
      ];
      notificationData.vibrate = [500, 200, 500, 200, 500];
      break;

    default:
      notificationData.actions = [
        {
          action: "view",
          title: "View",
          icon: "/bus-icon.svg",
        },
        {
          action: "dismiss",
          title: "Dismiss",
        },
      ];
  }

  // Add timestamp to data if not present
  if (!notificationData.data.timestamp) {
    notificationData.data.timestamp = Date.now();
  }

  // Set notification options
  const notificationOptions = {
    body: notificationData.body,
    icon: notificationData.icon,
    badge: notificationData.badge,
    tag: notificationData.tag,
    data: notificationData.data,
    actions: notificationData.actions,
    requireInteraction: notificationData.requireInteraction,
    vibrate: notificationData.vibrate,
    silent: false,
    renotify: true,
    timestamp: notificationData.data.timestamp,
  };

  // Add image if available
  if (notificationData.image) {
    notificationOptions.image = notificationData.image;
  }

  // Show notification
  event.waitUntil(
    self.registration.showNotification(
      notificationData.title,
      notificationOptions,
    ),
  );
});

// Notification click event
self.addEventListener("notificationclick", (event) => {
  console.log("Notification clicked:", event.action, event.notification.data);

  event.notification.close();

  const notificationData = event.notification.data || {};
  const notificationType = notificationData.type;
  let targetUrl = notificationData.url || "/dashboard";
  let shouldNavigate = true;

  // Handle different actions
  switch (event.action) {
    case "view_location":
      targetUrl = `/dashboard/tracking?bus=${notificationData.busId}`;
      break;

    case "view_attendance":
      targetUrl = `/dashboard/attendance?student=${notificationData.studentId}`;
      break;

    case "view_maintenance":
      targetUrl = `/dashboard/buses/${notificationData.busId}?tab=maintenance`;
      break;

    case "read_more":
      targetUrl = `/dashboard/notifications?id=${notificationData.notificationId || notificationData.announcementId}`;
      break;

    case "view_emergency":
      targetUrl = `/dashboard/emergency?id=${notificationData.emergencyId}`;
      break;

    case "acknowledge":
    case "dismiss":
      // Just close the notification, don't navigate
      shouldNavigate = false;
      break;

    default:
      // Default click behavior based on notification type
      switch (notificationType) {
        case "geofence":
          targetUrl = `/dashboard/tracking?bus=${notificationData.busId}`;
          break;
        case "attendance":
          targetUrl = `/dashboard/attendance${notificationData.studentId ? "?student=" + notificationData.studentId : ""}`;
          break;
        case "maintenance":
          targetUrl = `/dashboard/buses${notificationData.busId ? "/" + notificationData.busId : ""}`;
          break;
        case "announcement":
          targetUrl = `/dashboard/notifications${notificationData.announcementId ? "?id=" + notificationData.announcementId : ""}`;
          break;
        case "emergency":
          targetUrl = `/dashboard/emergency${notificationData.emergencyId ? "?id=" + notificationData.emergencyId : ""}`;
          break;
        default:
          targetUrl = "/dashboard";
      }
  }

  // Navigate to the target URL if needed
  if (shouldNavigate) {
    event.waitUntil(
      clients
        .matchAll({ type: "window", includeUncontrolled: true })
        .then((clientList) => {
          // Check if there's already a window/tab open with the target URL
          for (const client of clientList) {
            if (
              client.url.includes(targetUrl.split("?")[0]) &&
              "focus" in client
            ) {
              // If we have query parameters, navigate to the specific URL
              if (targetUrl.includes("?")) {
                return client.navigate(targetUrl).then(() => client.focus());
              }
              return client.focus();
            }
          }

          // If no existing window, open a new one
          if (clients.openWindow) {
            return clients.openWindow(targetUrl);
          }
        })
        .catch((error) => {
          console.warn(
            "Failed to handle notification click navigation:",
            error,
          );
        }),
    );
  }
});

// Handle notification close event
self.addEventListener("notificationclose", (event) => {
  console.log("Notification closed:", event.notification.data);
});

// Background sync for offline notifications
self.addEventListener("sync", (event) => {
  if (event.tag === "background-sync") {
    event.waitUntil(doBackgroundSync());
  }
});

function doBackgroundSync() {
  // Simple background sync without API calls that might fail
  return Promise.resolve().then(() => {
    console.log("Background sync completed");
  });
}
