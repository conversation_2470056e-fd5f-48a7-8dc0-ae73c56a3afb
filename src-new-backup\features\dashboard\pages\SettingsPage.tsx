import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Globe,
  Moon,
  Sun,
  Bell,
  Save,
  History,
  Settings as SettingsIcon,
} from "lucide-react";
import { supabase } from "../../../shared/services/lib/supabase";
import { useAuth } from "../../contexts/AuthContext";
import { Button } from "../../components/ui/Button";
import { ResponsiveLayout } from "../../components/layout/ResponsiveLayout";
import { NotificationPreferences } from "../../components/notifications/NotificationPreferences";
import { NotificationHistory } from "../../components/notifications/NotificationHistory";
import { NotificationTemplates } from "../../components/notifications/NotificationTemplates";

const SettingsPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { user } = useAuth();

  const [activeTab, setActiveTab] = useState<
    "general" | "notifications" | "history" | "templates"
  >("general");
  const [language, setLanguage] = useState(i18n.language || "en");
  const [theme, setTheme] = useState(localStorage.getItem("theme") || "light");
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    pushNotifications: true,
    routeUpdates: true,
    attendanceAlerts: true,
    systemAnnouncements: true,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);

  // Fetch user settings on component mount
  useEffect(() => {
    if (user) {
      fetchUserSettings();
    }
  }, [user]);

  const fetchUserSettings = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("users")
        .select("metadata")
        .eq("id", user.id)
        .single();

      if (error) throw error;

      if (data && data.metadata && data.metadata.settings) {
        const settings = data.metadata.settings;
        setNotificationSettings({
          emailNotifications: settings.emailNotifications ?? true,
          pushNotifications: settings.pushNotifications ?? true,
          routeUpdates: settings.routeUpdates ?? true,
          attendanceAlerts: settings.attendanceAlerts ?? true,
          systemAnnouncements: settings.systemAnnouncements ?? true,
        });
      }
    } catch (error) {
      console.error("Error fetching user settings:", error);
    }
  };

  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newLanguage = e.target.value;
    setLanguage(newLanguage);
    i18n.changeLanguage(newLanguage);
  };

  const handleThemeChange = (newTheme: string) => {
    setTheme(newTheme);
    localStorage.setItem("theme", newTheme);

    // Apply theme to document
    if (newTheme === "dark") {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  };

  const handleNotificationChange = (key: keyof typeof notificationSettings) => {
    setNotificationSettings((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const handleSaveSettings = async () => {
    if (!user) return;

    setIsLoading(true);
    setMessage(null);

    try {
      // Get current metadata to preserve other fields
      const { data: userData, error: fetchError } = await supabase
        .from("users")
        .select("metadata")
        .eq("id", user.id)
        .single();

      if (fetchError) throw fetchError;

      const currentMetadata = userData?.metadata || {};

      // Update user settings in the database
      const { error } = await supabase
        .from("users")
        .update({
          metadata: {
            ...currentMetadata,
            settings: notificationSettings,
            language,
            theme,
          },
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id);

      if (error) throw error;

      setMessage({
        type: "success",
        text: t("settings.saveSuccess"),
      });
    } catch (error) {
      console.error("Error saving settings:", error);
      setMessage({
        type: "error",
        text: t("settings.saveError"),
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Apply theme on initial load
  useEffect(() => {
    if (theme === "dark") {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, []);

  if (!user) {
    return (
      <ResponsiveLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            {t("common.loading")}
          </div>
        </div>
      </ResponsiveLayout>
    );
  }

  const tabs = [
    { id: "general", label: t("settings.general"), icon: SettingsIcon },
    { id: "notifications", label: t("settings.notifications"), icon: Bell },
    { id: "history", label: t("notifications.history"), icon: History },
    {
      id: "templates",
      label: t("notifications.templates"),
      icon: SettingsIcon,
    },
  ];

  return (
    <ResponsiveLayout>
      <div className="container mx-auto px-4 py-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          {t("settings.title")}
        </h1>

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? "border-primary-500 text-primary-600 dark:text-primary-400"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                    }`}
                  >
                    <Icon size={16} className="mr-2" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {message && (
          <div
            className={`mb-6 p-4 rounded-md ${
              message.type === "success"
                ? "bg-green-50 text-green-800 dark:bg-green-900 dark:text-green-100"
                : "bg-red-50 text-red-800 dark:bg-red-900 dark:text-red-100"
            }`}
          >
            {message.text}
          </div>
        )}

        {/* Tab Content */}
        {activeTab === "general" && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <Globe size={20} className="mr-2" />
                  {t("settings.language")}
                </h2>

                <div className="mb-6">
                  <label
                    htmlFor="language"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    {t("settings.selectLanguage")}
                  </label>
                  <select
                    id="language"
                    value={language}
                    onChange={handleLanguageChange}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="en">English</option>
                    <option value="ar">العربية (Arabic)</option>
                  </select>
                </div>

                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  {theme === "dark" ? (
                    <Moon size={20} className="mr-2" />
                  ) : (
                    <Sun size={20} className="mr-2" />
                  )}
                  {t("settings.theme")}
                </h2>

                <div className="flex space-x-4 mb-6">
                  <button
                    type="button"
                    onClick={() => handleThemeChange("light")}
                    className={`flex items-center justify-center px-4 py-2 rounded-md ${theme === "light" ? "bg-primary-100 text-primary-700 border border-primary-300" : "bg-gray-100 text-gray-700 border border-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600"}`}
                  >
                    <Sun size={18} className="mr-2" />
                    {t("settings.lightMode")}
                  </button>
                  <button
                    type="button"
                    onClick={() => handleThemeChange("dark")}
                    className={`flex items-center justify-center px-4 py-2 rounded-md ${theme === "dark" ? "bg-primary-100 text-primary-700 border border-primary-300 dark:bg-primary-900 dark:text-primary-300 dark:border-primary-700" : "bg-gray-100 text-gray-700 border border-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600"}`}
                  >
                    <Moon size={18} className="mr-2" />
                    {t("settings.darkMode")}
                  </button>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <Bell size={20} className="mr-2" />
                  {t("settings.notifications")}
                </h2>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <label
                      htmlFor="emailNotifications"
                      className="text-sm text-gray-700 dark:text-gray-300"
                    >
                      {t("settings.emailNotifications")}
                    </label>
                    <div className="relative inline-block w-10 mr-2 align-middle select-none">
                      <input
                        type="checkbox"
                        id="emailNotifications"
                        checked={notificationSettings.emailNotifications}
                        onChange={() =>
                          handleNotificationChange("emailNotifications")
                        }
                        className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                      />
                      <label
                        htmlFor="emailNotifications"
                        className={`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ${notificationSettings.emailNotifications ? "bg-primary-500" : "bg-gray-300 dark:bg-gray-600"}`}
                      ></label>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <label
                      htmlFor="pushNotifications"
                      className="text-sm text-gray-700 dark:text-gray-300"
                    >
                      {t("settings.pushNotifications")}
                    </label>
                    <div className="relative inline-block w-10 mr-2 align-middle select-none">
                      <input
                        type="checkbox"
                        id="pushNotifications"
                        checked={notificationSettings.pushNotifications}
                        onChange={() =>
                          handleNotificationChange("pushNotifications")
                        }
                        className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                      />
                      <label
                        htmlFor="pushNotifications"
                        className={`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ${notificationSettings.pushNotifications ? "bg-primary-500" : "bg-gray-300 dark:bg-gray-600"}`}
                      ></label>
                    </div>
                  </div>

                  <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      {t("settings.notificationTypes")}
                    </h3>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <label
                          htmlFor="routeUpdates"
                          className="text-sm text-gray-700 dark:text-gray-300"
                        >
                          {t("settings.routeUpdates")}
                        </label>
                        <div className="relative inline-block w-10 mr-2 align-middle select-none">
                          <input
                            type="checkbox"
                            id="routeUpdates"
                            checked={notificationSettings.routeUpdates}
                            onChange={() =>
                              handleNotificationChange("routeUpdates")
                            }
                            className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                          />
                          <label
                            htmlFor="routeUpdates"
                            className={`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ${notificationSettings.routeUpdates ? "bg-primary-500" : "bg-gray-300 dark:bg-gray-600"}`}
                          ></label>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <label
                          htmlFor="attendanceAlerts"
                          className="text-sm text-gray-700 dark:text-gray-300"
                        >
                          {t("settings.attendanceAlerts")}
                        </label>
                        <div className="relative inline-block w-10 mr-2 align-middle select-none">
                          <input
                            type="checkbox"
                            id="attendanceAlerts"
                            checked={notificationSettings.attendanceAlerts}
                            onChange={() =>
                              handleNotificationChange("attendanceAlerts")
                            }
                            className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                          />
                          <label
                            htmlFor="attendanceAlerts"
                            className={`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ${notificationSettings.attendanceAlerts ? "bg-primary-500" : "bg-gray-300 dark:bg-gray-600"}`}
                          ></label>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <label
                          htmlFor="systemAnnouncements"
                          className="text-sm text-gray-700 dark:text-gray-300"
                        >
                          {t("settings.systemAnnouncements")}
                        </label>
                        <div className="relative inline-block w-10 mr-2 align-middle select-none">
                          <input
                            type="checkbox"
                            id="systemAnnouncements"
                            checked={notificationSettings.systemAnnouncements}
                            onChange={() =>
                              handleNotificationChange("systemAnnouncements")
                            }
                            className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                          />
                          <label
                            htmlFor="systemAnnouncements"
                            className={`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ${notificationSettings.systemAnnouncements ? "bg-primary-500" : "bg-gray-300 dark:bg-gray-600"}`}
                          ></label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <Button
                onClick={handleSaveSettings}
                disabled={isLoading}
                leftIcon={<Save size={18} />}
              >
                {isLoading ? t("common.saving") : t("settings.saveSettings")}
              </Button>
            </div>
          </>
        )}

        {activeTab === "notifications" && <NotificationPreferences />}
        {activeTab === "history" && <NotificationHistory />}
        {activeTab === "templates" && user?.role === "admin" && (
          <NotificationTemplates />
        )}
        {activeTab === "templates" && user?.role !== "admin" && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-8 text-center">
            <Bell size={48} className="mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {t("notifications.adminOnly")}
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {t("notifications.adminOnlyDescription")}
            </p>
          </div>
        )}

        <style jsx>{`
          .toggle-checkbox:checked {
            right: 0;
            border-color: #fff;
          }
          .toggle-checkbox:checked + .toggle-label {
            background-color: #3b82f6;
          }
          .toggle-checkbox {
            right: 0;
            z-index: 1;
            border-color: #fff;
            transition: all 0.3s;
          }
          .toggle-checkbox + .toggle-label {
            transition: all 0.3s;
          }
        `}</style>
      </div>
    </ResponsiveLayout>
  );
};

export { SettingsPage };

export default SettingsPage;
