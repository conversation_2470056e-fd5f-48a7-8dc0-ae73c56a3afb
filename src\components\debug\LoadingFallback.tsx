import React, { useState, useEffect } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from '../ui/Button';

interface LoadingFallbackProps {
  title: string;
  description?: string;
  onRetry?: () => void;
  timeout?: number; // in milliseconds
}

export const LoadingFallback: React.FC<LoadingFallbackProps> = ({
  title,
  description = "Loading data...",
  onRetry,
  timeout = 10000 // 10 seconds default
}) => {
  const [showTimeout, setShowTimeout] = useState(false);
  const [timeLeft, setTimeLeft] = useState(timeout / 1000);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowTimeout(true);
    }, timeout);

    const countdown = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(countdown);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      clearTimeout(timer);
      clearInterval(countdown);
    };
  }, [timeout]);

  if (showTimeout) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Loading is taking longer than expected
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            The page is still loading. This might be due to network issues or server load.
          </p>
          {onRetry && (
            <Button onClick={onRetry} className="flex items-center gap-2">
              <RefreshCw className="w-4 h-4" />
              Try Again
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          {title}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-2">
          {description}
        </p>
        <p className="text-sm text-gray-500">
          {timeLeft > 0 ? `Timeout in ${timeLeft}s` : 'Loading...'}
        </p>
      </div>
    </div>
  );
};
