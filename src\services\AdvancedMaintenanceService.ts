/**
 * Advanced Maintenance Management Service
 * Phase 4: Core System Functionality Development
 */

import { supabase } from '../lib/supabase';
import { SmartNotificationService } from './SmartNotificationService';

export interface MaintenanceRecord {
  id: string;
  bus_id: string;
  tenant_id: string;
  type: 'preventive' | 'corrective' | 'emergency' | 'inspection';
  category: 'engine' | 'brakes' | 'tires' | 'electrical' | 'body' | 'safety' | 'other';
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'overdue';
  scheduled_date: string;
  started_date?: string;
  completed_date?: string;
  estimated_duration: number; // hours
  actual_duration?: number; // hours
  cost_estimate: number;
  actual_cost?: number;
  technician_id?: string;
  technician_name?: string;
  parts_used: MaintenancePart[];
  checklist: MaintenanceChecklistItem[];
  photos: string[];
  notes: string;
  next_maintenance_date?: string;
  warranty_info?: {
    covered: boolean;
    warranty_number?: string;
    expiry_date?: string;
  };
}

export interface MaintenancePart {
  id: string;
  name: string;
  part_number: string;
  quantity_used: number;
  unit_cost: number;
  total_cost: number;
  supplier: string;
  warranty_months?: number;
}

export interface MaintenanceChecklistItem {
  id: string;
  description: string;
  category: string;
  is_required: boolean;
  is_completed: boolean;
  notes?: string;
  photo_url?: string;
  completed_by?: string;
  completed_at?: string;
}

export interface MaintenanceSchedule {
  id: string;
  bus_id: string;
  tenant_id: string;
  maintenance_type: string;
  interval_type: 'mileage' | 'time' | 'usage_hours';
  interval_value: number;
  last_performed_date?: string;
  last_performed_mileage?: number;
  next_due_date: string;
  next_due_mileage?: number;
  is_active: boolean;
  auto_schedule: boolean;
}

export interface MaintenanceAlert {
  id: string;
  bus_id: string;
  alert_type: 'due_soon' | 'overdue' | 'critical' | 'inspection_required';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  due_date: string;
  days_overdue?: number;
  estimated_cost: number;
  auto_generated: boolean;
  acknowledged: boolean;
  acknowledged_by?: string;
  acknowledged_at?: string;
}

export interface MaintenanceReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    total_maintenance: number;
    completed_maintenance: number;
    pending_maintenance: number;
    overdue_maintenance: number;
    total_cost: number;
    average_cost_per_bus: number;
    total_downtime_hours: number;
    average_downtime_per_bus: number;
  };
  by_category: Record<string, {
    count: number;
    cost: number;
    avg_duration: number;
  }>;
  by_bus: Array<{
    bus_id: string;
    plate_number: string;
    maintenance_count: number;
    total_cost: number;
    total_downtime: number;
    reliability_score: number;
  }>;
  trends: {
    monthly_costs: number[];
    monthly_counts: number[];
    category_trends: Record<string, number[]>;
  };
}

export class AdvancedMaintenanceService {
  private static instance: AdvancedMaintenanceService;
  private notificationService = SmartNotificationService.getInstance();
  private alertCheckInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.startAlertMonitoring();
  }

  static getInstance(): AdvancedMaintenanceService {
    if (!AdvancedMaintenanceService.instance) {
      AdvancedMaintenanceService.instance = new AdvancedMaintenanceService();
    }
    return AdvancedMaintenanceService.instance;
  }

  /**
   * Create maintenance record
   */
  async createMaintenanceRecord(
    record: Omit<MaintenanceRecord, 'id'>
  ): Promise<string> {
    try {
      const { data, error } = await supabase
        .from('bus_maintenance')
        .insert([{
          bus_id: record.bus_id,
          tenant_id: record.tenant_id,
          type: record.type,
          category: record.category,
          title: record.title,
          description: record.description,
          priority: record.priority,
          status: record.status,
          scheduled_date: record.scheduled_date,
          estimated_duration: record.estimated_duration,
          cost_estimate: record.cost_estimate,
          technician_id: record.technician_id,
          technician_name: record.technician_name,
          parts_used: record.parts_used,
          checklist: record.checklist,
          photos: record.photos,
          notes: record.notes,
          next_maintenance_date: record.next_maintenance_date,
          warranty_info: record.warranty_info,
        }])
        .select()
        .single();

      if (error) throw error;

      const maintenanceId = data.id;

      // Create maintenance schedule if it's preventive
      if (record.type === 'preventive' && record.next_maintenance_date) {
        await this.createMaintenanceSchedule({
          bus_id: record.bus_id,
          tenant_id: record.tenant_id,
          maintenance_type: record.category,
          interval_type: 'time',
          interval_value: 30, // Default 30 days
          next_due_date: record.next_maintenance_date,
          is_active: true,
          auto_schedule: true,
        });
      }

      // Send notifications
      await this.notifyMaintenanceCreated(record);

      return maintenanceId;
    } catch (error) {
      console.error('Error creating maintenance record:', error);
      throw error;
    }
  }

  /**
   * Update maintenance status
   */
  async updateMaintenanceStatus(
    maintenanceId: string,
    status: MaintenanceRecord['status'],
    updates?: Partial<MaintenanceRecord>
  ): Promise<void> {
    try {
      const updateData: any = {
        status,
        updated_at: new Date().toISOString(),
        ...updates,
      };

      // Set timestamps based on status
      if (status === 'in_progress' && !updates?.started_date) {
        updateData.started_date = new Date().toISOString();
      } else if (status === 'completed' && !updates?.completed_date) {
        updateData.completed_date = new Date().toISOString();
        
        // Calculate actual duration
        const { data: record } = await supabase
          .from('bus_maintenance')
          .select('started_date')
          .eq('id', maintenanceId)
          .single();

        if (record?.started_date) {
          const startTime = new Date(record.started_date).getTime();
          const endTime = new Date().getTime();
          updateData.actual_duration = (endTime - startTime) / (1000 * 60 * 60); // hours
        }
      }

      const { error } = await supabase
        .from('bus_maintenance')
        .update(updateData)
        .eq('id', maintenanceId);

      if (error) throw error;

      // Send status update notifications
      await this.notifyMaintenanceStatusUpdate(maintenanceId, status);

      // Update bus availability if completed
      if (status === 'completed') {
        await this.updateBusAvailability(maintenanceId, true);
      } else if (status === 'in_progress') {
        await this.updateBusAvailability(maintenanceId, false);
      }
    } catch (error) {
      console.error('Error updating maintenance status:', error);
      throw error;
    }
  }

  /**
   * Get maintenance alerts
   */
  async getMaintenanceAlerts(tenantId: string): Promise<MaintenanceAlert[]> {
    try {
      // Get overdue maintenance
      const { data: overdue, error: overdueError } = await supabase
        .from('bus_maintenance')
        .select(`
          *,
          bus:buses!bus_id(id, plate_number)
        `)
        .eq('tenant_id', tenantId)
        .eq('status', 'scheduled')
        .lt('created_at', new Date().toISOString()); // استخدام created_at بدلاً من scheduled_date مؤقتاً

      if (overdueError) throw overdueError;

      // Get maintenance due soon (within 7 days)
      const sevenDaysFromNow = new Date();
      sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);

      const { data: dueSoon, error: dueSoonError } = await supabase
        .from('bus_maintenance')
        .select(`
          *,
          bus:buses!bus_id(id, plate_number)
        `)
        .eq('tenant_id', tenantId)
        .eq('status', 'scheduled')
        .gte('created_at', new Date().toISOString())
        .lte('created_at', sevenDaysFromNow.toISOString()); // استخدام created_at بدلاً من scheduled_date مؤقتاً

      if (dueSoonError) throw dueSoonError;

      const alerts: MaintenanceAlert[] = [];

      // Process overdue maintenance
      overdue?.forEach(maintenance => {
        const daysOverdue = Math.floor(
          (Date.now() - new Date(maintenance.created_at).getTime()) / (1000 * 60 * 60 * 24)
        );

        alerts.push({
          id: `overdue-${maintenance.id}`,
          bus_id: maintenance.bus_id,
          alert_type: 'overdue',
          priority: daysOverdue > 7 ? 'critical' : 'high',
          title: `Overdue Maintenance: ${maintenance.maintenance_type || 'General'}`,
          description: `${maintenance.bus?.plate_number || 'Unknown Bus'} - ${daysOverdue} days overdue`,
          due_date: maintenance.created_at, // استخدام created_at مؤقتاً
          days_overdue: daysOverdue,
          estimated_cost: maintenance.cost || 0,
          auto_generated: true,
          acknowledged: false,
        });
      });

      // Process due soon maintenance
      dueSoon?.forEach(maintenance => {
        const daysUntilDue = Math.floor(
          (new Date(maintenance.created_at).getTime() - Date.now()) / (1000 * 60 * 60 * 24)
        );

        alerts.push({
          id: `due-soon-${maintenance.id}`,
          bus_id: maintenance.bus_id,
          alert_type: 'due_soon',
          priority: daysUntilDue <= 3 ? 'high' : 'medium',
          title: `Maintenance Due Soon: ${maintenance.maintenance_type || 'General'}`,
          description: `${maintenance.bus?.plate_number || 'Unknown Bus'} - Due in ${Math.abs(daysUntilDue)} days`,
          due_date: maintenance.created_at, // استخدام created_at مؤقتاً
          estimated_cost: maintenance.cost || 0,
          auto_generated: true,
          acknowledged: false,
        });
      });

      return alerts;
    } catch (error) {
      console.error('Error getting maintenance alerts:', error);
      return [];
    }
  }

  /**
   * Generate maintenance report
   */
  async generateMaintenanceReport(
    tenantId: string,
    startDate: string,
    endDate: string
  ): Promise<MaintenanceReport> {
    try {
      const { data: maintenanceData, error } = await supabase
        .from('bus_maintenance')
        .select(`
          *,
          bus:buses!bus_id(id, plate_number)
        `)
        .eq('tenant_id', tenantId)
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      if (error) throw error;

      const data = maintenanceData || [];

      // Calculate summary
      const summary = {
        total_maintenance: data.length,
        completed_maintenance: data.filter(m => m.status === 'completed').length,
        pending_maintenance: data.filter(m => ['scheduled', 'in_progress'].includes(m.status)).length,
        overdue_maintenance: data.filter(m =>
          m.status === 'scheduled' && new Date(m.created_at) < new Date()
        ).length,
        total_cost: data.reduce((sum, m) => sum + (m.actual_cost || m.cost_estimate), 0),
        average_cost_per_bus: 0,
        total_downtime_hours: data.reduce((sum, m) => sum + (m.actual_duration || 0), 0),
        average_downtime_per_bus: 0,
      };

      // Get unique buses
      const uniqueBuses = new Set(data.map(m => m.bus_id));
      summary.average_cost_per_bus = uniqueBuses.size > 0 ? summary.total_cost / uniqueBuses.size : 0;
      summary.average_downtime_per_bus = uniqueBuses.size > 0 ? summary.total_downtime_hours / uniqueBuses.size : 0;

      // Calculate by category
      const by_category: Record<string, any> = {};
      data.forEach(maintenance => {
        const category = maintenance.category;
        if (!by_category[category]) {
          by_category[category] = { count: 0, cost: 0, total_duration: 0 };
        }
        by_category[category].count++;
        by_category[category].cost += maintenance.actual_cost || maintenance.cost_estimate;
        by_category[category].total_duration += maintenance.actual_duration || 0;
      });

      // Calculate average duration
      Object.keys(by_category).forEach(category => {
        by_category[category].avg_duration = by_category[category].count > 0 
          ? by_category[category].total_duration / by_category[category].count 
          : 0;
        delete by_category[category].total_duration;
      });

      // Calculate by bus
      const busByIdMap = new Map();
      data.forEach(maintenance => {
        const busId = maintenance.bus_id;
        if (!busByIdMap.has(busId)) {
          busByIdMap.set(busId, {
            bus_id: busId,
            plate_number: maintenance.bus?.plate_number || 'Unknown',
            maintenance_count: 0,
            total_cost: 0,
            total_downtime: 0,
          });
        }
        const busData = busByIdMap.get(busId);
        busData.maintenance_count++;
        busData.total_cost += maintenance.actual_cost || maintenance.cost_estimate;
        busData.total_downtime += maintenance.actual_duration || 0;
      });

      const by_bus = Array.from(busByIdMap.values()).map(bus => ({
        ...bus,
        reliability_score: this.calculateReliabilityScore(bus),
      }));

      return {
        period: { start: startDate, end: endDate },
        summary,
        by_category,
        by_bus,
        trends: {
          monthly_costs: [], // TODO: Implement trend calculation
          monthly_counts: [],
          category_trends: {},
        },
      };
    } catch (error) {
      console.error('Error generating maintenance report:', error);
      throw error;
    }
  }

  /**
   * Create maintenance schedule
   */
  async createMaintenanceSchedule(
    schedule: Omit<MaintenanceSchedule, 'id'>
  ): Promise<string> {
    try {
      const { data, error } = await supabase
        .from('maintenance_schedules')
        .insert([schedule])
        .select()
        .single();

      if (error) throw error;
      return data.id;
    } catch (error) {
      console.error('Error creating maintenance schedule:', error);
      throw error;
    }
  }

  /**
   * Auto-schedule maintenance based on schedules
   */
  async autoScheduleMaintenance(): Promise<void> {
    try {
      const { data: schedules, error } = await supabase
        .from('maintenance_schedules')
        .select('*')
        .eq('is_active', true)
        .eq('auto_schedule', true)
        .lte('next_due_date', new Date().toISOString());

      if (error) throw error;

      for (const schedule of schedules || []) {
        await this.createMaintenanceFromSchedule(schedule);
      }
    } catch (error) {
      console.error('Error auto-scheduling maintenance:', error);
    }
  }

  /**
   * Private helper methods
   */
  private startAlertMonitoring(): void {
    // Check for alerts every hour
    this.alertCheckInterval = setInterval(async () => {
      try {
        // Get all tenants and check alerts
        const { data: tenants } = await supabase
          .from('tenants')
          .select('id');

        for (const tenant of tenants || []) {
          const alerts = await this.getMaintenanceAlerts(tenant.id);
          await this.processAlerts(alerts, tenant.id);
        }

        // Auto-schedule maintenance
        await this.autoScheduleMaintenance();
      } catch (error) {
        console.error('Error in alert monitoring:', error);
      }
    }, 60 * 60 * 1000); // 1 hour
  }

  private async processAlerts(alerts: MaintenanceAlert[], tenantId: string): Promise<void> {
    for (const alert of alerts) {
      if (alert.priority === 'critical' || alert.priority === 'high') {
        await this.notificationService.sendSmartNotification(
          'maintenance_alert_template',
          [{ type: 'role', identifier: 'school_manager' }],
          {
            alertTitle: alert.title,
            alertDescription: alert.description,
            busId: alert.bus_id,
            dueDate: alert.due_date,
            estimatedCost: alert.estimated_cost,
          },
          tenantId,
          alert.priority
        );
      }
    }
  }

  private async notifyMaintenanceCreated(record: Omit<MaintenanceRecord, 'id'>): Promise<void> {
    // Send notification about new maintenance record
  }

  private async notifyMaintenanceStatusUpdate(
    maintenanceId: string,
    status: MaintenanceRecord['status']
  ): Promise<void> {
    // Send notification about status update
  }

  private async updateBusAvailability(maintenanceId: string, available: boolean): Promise<void> {
    // Update bus availability status
  }

  private calculateReliabilityScore(busData: any): number {
    // Simple reliability score calculation
    // Lower maintenance count and cost = higher reliability
    const maxScore = 100;
    const maintenancePenalty = busData.maintenance_count * 5;
    const costPenalty = (busData.total_cost / 1000) * 2;
    const downtimePenalty = busData.total_downtime * 1;

    return Math.max(0, maxScore - maintenancePenalty - costPenalty - downtimePenalty);
  }

  private async createMaintenanceFromSchedule(schedule: MaintenanceSchedule): Promise<void> {
    // Create maintenance record from schedule
  }
}

export default AdvancedMaintenanceService;
