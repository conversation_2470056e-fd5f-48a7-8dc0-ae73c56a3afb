import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RouterProvider } from "react-router-dom";
import "./i18n";
import { ThemeProvider } from "./contexts/ThemeContext";
import { CustomThemeProvider } from "./contexts/CustomThemeContext";
import { AuthProvider } from "./contexts/AuthContext";
import DatabaseProvider from "./contexts/DatabaseContext";
import NotificationsProvider from "./contexts/NotificationsContext";
import { TempoDevtools } from "tempo-devtools";
import App from "./App";
import "./index.css";
import { ErrorHandler } from "./utils/errorBoundary";
import { initializeApp } from "./config/app";

// Initialize error handling
try {
  // Initialize Tempo Devtools
  TempoDevtools.init();
} catch (error) {
  ErrorHandler.handleError(error as Error, "TempoDevtools");
}

// Register service worker for push notifications
if ("serviceWorker" in navigator) {
  window.addEventListener("load", () => {
    navigator.serviceWorker
      .register("/sw.js")
      .then((registration) => {
        console.log("SW registered: ", registration);
      })
      .catch((registrationError) => {
        console.log("SW registration failed: ", registrationError);
      });
  });
}

// Initialize app and render
async function startApp() {
  try {
    // Initialize application configuration
    await initializeApp();

    // Create a root element and render the app
    const rootElement = document.getElementById("root");
    if (rootElement) {
      const root = createRoot(rootElement);
      // إنشاء router مع future flags
      const router = createBrowserRouter([
        {
          path: "*",
          element: (
            <ThemeProvider>
              <AuthProvider>
                <CustomThemeProvider>
                  <DatabaseProvider>
                    <NotificationsProvider>
                      <App />
                    </NotificationsProvider>
                  </DatabaseProvider>
                </CustomThemeProvider>
              </AuthProvider>
            </ThemeProvider>
          ),
        },
      ], {
        future: {
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        },
      });

      root.render(
        <StrictMode>
          <RouterProvider router={router} />
        </StrictMode>,
      );
    } else {
      console.error("Root element not found");
      ErrorHandler.handleError(new Error("Root element not found"), "DOM");
    }
  } catch (error) {
    ErrorHandler.handleError(error as Error, "AppInitialization");
    showErrorFallback(error as Error);
  }
}

// Show error fallback UI
function showErrorFallback(error: Error) {
  const rootElement = document.getElementById("root");
  if (rootElement) {
    rootElement.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; min-height: 100vh; font-family: Arial, sans-serif;">
        <div style="text-align: center; padding: 2rem;">
          <h1 style="color: #dc2626; margin-bottom: 1rem;">Application Error</h1>
          <p style="color: #6b7280; margin-bottom: 1rem;">Failed to initialize application: ${error.message}</p>
          <button onclick="window.location.reload()" style="background: #3b82f6; color: white; padding: 0.5rem 1rem; border: none; border-radius: 0.25rem; cursor: pointer;">
            Refresh Page
          </button>
        </div>
      </div>
    `;
  }
}

// Start the application
startApp().catch((error) => {
  console.error("Failed to start application:", error);
  showErrorFallback(error);
});
