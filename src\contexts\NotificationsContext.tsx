import React, { createContext, useContext, useState, useEffect } from "react";
import { RealtimeChannel } from "@supabase/supabase-js";
import { useAuth } from "./AuthContext";
import { supabase } from "../lib/supabase";
import {
  createGeofenceNotification,
  isWithinGeofence,
  trackNotificationAnalytics,
} from "../lib/api";
import type { Tables } from "../lib/database.types";

interface NotificationsContextType {
  notifications: Tables<"notifications">[];
  unreadCount: number;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  loading: boolean;
  error: Error | null;
  createGeofenceAlert: (
    busId: string,
    stopId: string,
    type: "enter" | "exit",
  ) => Promise<void>;
  notificationIndicators?: Map<string, boolean>;
  clearIndicator: (key: string) => void;
  getNotificationsByCategory: (category?: string) => Tables<"notifications">[];
  getNotificationCategories: () => string[];
}

const NotificationsContext = createContext<
  NotificationsContextType | undefined
>(undefined);

function NotificationsProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Tables<"notifications">[]>(
    [],
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [subscription, setSubscription] = useState<RealtimeChannel | null>(
    null,
  );
  const [busSubscription, setBusSubscription] =
    useState<RealtimeChannel | null>(null);
  const [previousBusLocations, setPreviousBusLocations] = useState<
    Map<string, { lat: number; lng: number }>
  >(new Map());
  const [notificationIndicators, setNotificationIndicators] = useState<
    Map<string, boolean>
  >(new Map());
  const [notificationGroups, setNotificationGroups] = useState<
    Map<
      string,
      { notifications: Tables<"notifications">[]; lastUpdate: number }
    >
  >(new Map());

  const fetchNotifications = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from("notifications")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (fetchError) throw fetchError;

      setNotifications(data || []);
    } catch (err) {
      setError(
        err instanceof Error ? err : new Error("Failed to fetch notifications"),
      );
    } finally {
      setLoading(false);
    }
  };

  const setupSubscription = () => {
    if (!user) return;

    const newSubscription = supabase
      .channel(`user-notifications-${user.id}`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "notifications",
          filter: `user_id=eq.${user.id}`,
        },
        (payload) => {
          if (payload.eventType === "INSERT") {
            const newNotification = payload.new as Tables<"notifications">;

            // Check if notification should be grouped
            const shouldGroup = checkNotificationGrouping(newNotification);

            if (shouldGroup) {
              handleNotificationGrouping(newNotification);
            } else {
              setNotifications((prev) => [newNotification, ...prev]);
            }

            // Set indicator for new notification
            setNotificationIndicators(
              (prev) => new Map(prev.set("global", true)),
            );

            // Play notification sound if enabled
            playNotificationSound(newNotification);
          } else if (payload.eventType === "UPDATE") {
            setNotifications((prev) =>
              prev.map((n) =>
                n.id === payload.new.id
                  ? (payload.new as Tables<"notifications">)
                  : n,
              ),
            );
          } else if (payload.eventType === "DELETE") {
            setNotifications((prev) =>
              prev.filter((n) => n.id !== payload.old.id),
            );
          }
        },
      )
      .subscribe();

    // Set up geofencing subscription for bus location updates
    const newBusSubscription = supabase
      .channel("bus-geofencing")
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: "buses",
        },
        async (payload) => {
          const busUpdate = payload.new as Tables<"buses">;
          const oldBus = payload.old as Tables<"buses">;

          // Check if location actually changed
          if (
            busUpdate.last_location &&
            JSON.stringify(busUpdate.last_location) !==
              JSON.stringify(oldBus?.last_location)
          ) {
            await handleBusLocationUpdate(busUpdate);
          }
        },
      )
      .subscribe();

    setSubscription(newSubscription);
    setBusSubscription(newBusSubscription);
  };

  const handleBusLocationUpdate = async (bus: Tables<"buses">) => {
    if (!bus.last_location || !user?.tenant_id) return;

    try {
      // Get route stops for this bus
      const { data: route } = await supabase
        .from("routes")
        .select(
          `
          id,
          stops:route_stops(*)
        `,
        )
        .eq("bus_id", bus.id)
        .single();

      if (!route?.stops) return;

      const currentLocation = bus.last_location as any;
      const busLat = currentLocation.coordinates[1];
      const busLng = currentLocation.coordinates[0];

      const previousLocation = previousBusLocations.get(bus.id);

      // Check each stop for geofence entry/exit
      for (const stop of route.stops) {
        const stopLocation = stop.location as any;
        const stopLat = stopLocation.coordinates[1];
        const stopLng = stopLocation.coordinates[0];

        const currentlyInGeofence = isWithinGeofence(
          { lat: busLat, lng: busLng },
          { lat: stopLat, lng: stopLng },
          100, // 100 meter radius
        );

        const wasInGeofence = previousLocation
          ? isWithinGeofence(
              previousLocation,
              { lat: stopLat, lng: stopLng },
              100,
            )
          : false;

        // Detect entry
        if (currentlyInGeofence && !wasInGeofence) {
          await createGeofenceNotification(
            bus.id,
            stop.id,
            "enter",
            user.tenant_id!,
          );

          // Notify parents of students at this stop
          const { data: students } = await supabase
            .from("students")
            .select("parent_id, name")
            .eq("route_stop_id", stop.id);

          if (students) {
            for (const student of students) {
              if (student.parent_id) {
                await supabase.from("notifications").insert({
                  user_id: student.parent_id,
                  title: "Bus Arrived",
                  message: `The bus has arrived at ${stop.name} for ${student.name}`,
                  tenant_id: user.tenant_id,
                  metadata: {
                    type: "geofence",
                    busId: bus.id,
                    stopId: stop.id,
                    action: "enter",
                    studentId: student.name,
                  },
                });
              }
            }
          }
        }
        // Detect exit
        else if (!currentlyInGeofence && wasInGeofence) {
          await createGeofenceNotification(
            bus.id,
            stop.id,
            "exit",
            user.tenant_id!,
          );

          // Notify parents of students at this stop
          const { data: students } = await supabase
            .from("students")
            .select("parent_id, name")
            .eq("route_stop_id", stop.id);

          if (students) {
            for (const student of students) {
              if (student.parent_id) {
                await supabase.from("notifications").insert({
                  user_id: student.parent_id,
                  title: "Bus Departed",
                  message: `The bus has left ${stop.name} for ${student.name}`,
                  tenant_id: user.tenant_id,
                  metadata: {
                    type: "geofence",
                    busId: bus.id,
                    stopId: stop.id,
                    action: "exit",
                    studentId: student.name,
                  },
                });
              }
            }
          }
        }
      }

      // Update previous location
      setPreviousBusLocations(
        (prev) => new Map(prev.set(bus.id, { lat: busLat, lng: busLng })),
      );
    } catch (error) {
      console.error("Error handling bus location update:", error);
    }
  };

  useEffect(() => {
    if (user?.id) {
      fetchNotifications();
      setupSubscription();
    }

    return () => {
      if (subscription) {
        subscription.unsubscribe();
        setSubscription(null);
      }
      if (busSubscription) {
        busSubscription.unsubscribe();
        setBusSubscription(null);
      }
    };
  }, [user?.id]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
      if (busSubscription) {
        busSubscription.unsubscribe();
      }
    };
  }, []);

  const markAsRead = async (id: string) => {
    try {
      const { error: updateError } = await supabase
        .from("notifications")
        .update({ read: true })
        .eq("id", id);

      if (updateError) throw updateError;

      setNotifications((prev) =>
        prev.map((n) => (n.id === id ? { ...n, read: true } : n)),
      );
    } catch (err) {
      console.error("Error marking notification as read:", err);
    }
  };

  const markAllAsRead = async () => {
    try {
      const { error: updateError } = await supabase
        .from("notifications")
        .update({ read: true })
        .eq("user_id", user?.id)
        .eq("read", false);

      if (updateError) throw updateError;

      setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
    } catch (err) {
      console.error("Error marking all notifications as read:", err);
    }
  };

  const checkNotificationGrouping = (notification: Tables<"notifications">) => {
    const metadata = notification.metadata as any;
    if (!metadata?.type) return false;

    // Get user preferences for grouping
    const groupingEnabled = true; // TODO: Get from user preferences
    if (!groupingEnabled) return false;

    // Group similar notifications within configurable time window
    const timeWindow = 5 * 60 * 1000; // 5 minutes - TODO: Make configurable
    const maxGroupSize = 5; // TODO: Make configurable

    const groupKey = `${metadata.type}_${metadata.busId || metadata.studentId || "general"}`;
    const now = Date.now();
    const existingGroup = notificationGroups.get(groupKey);

    if (existingGroup && now - existingGroup.lastUpdate < timeWindow) {
      return existingGroup.notifications.length < maxGroupSize;
    }

    return false;
  };

  const handleNotificationGrouping = (
    notification: Tables<"notifications">,
  ) => {
    const metadata = notification.metadata as any;
    const groupKey = `${metadata.type}_${metadata.busId || metadata.studentId || "general"}`;
    const now = Date.now();

    setNotificationGroups((prev) => {
      const newGroups = new Map(prev);
      const existingGroup = newGroups.get(groupKey);

      if (existingGroup) {
        existingGroup.notifications.push(notification);
        existingGroup.lastUpdate = now;
      } else {
        newGroups.set(groupKey, {
          notifications: [notification],
          lastUpdate: now,
        });
      }

      return newGroups;
    });

    // Create or update grouped notification
    const groupedNotification = createGroupedNotification(
      groupKey,
      notification,
    );

    setNotifications((prev) => {
      // Remove any existing grouped notifications with the same key
      const filtered = prev.filter((n) => {
        const nMeta = n.metadata as any;
        return !(nMeta?.groupKey === groupKey && nMeta?.isGrouped);
      });
      return [groupedNotification, ...filtered];
    });

    // Track grouping analytics
    trackNotificationGrouping(groupKey, notification);
  };

  const createGroupedNotification = (
    groupKey: string,
    latestNotification: Tables<"notifications">,
  ) => {
    const group = notificationGroups.get(groupKey);
    if (!group) return latestNotification;

    const count = group.notifications.length;
    const metadata = latestNotification.metadata as any;

    return {
      ...latestNotification,
      id: `group_${groupKey}`,
      title:
        count > 1
          ? `${latestNotification.title} (+${count - 1} more)`
          : latestNotification.title,
      message:
        count > 1
          ? `${latestNotification.message} and ${count - 1} similar notifications`
          : latestNotification.message,
      metadata: {
        ...metadata,
        groupKey,
        groupCount: count,
        isGrouped: true,
      },
    } as Tables<"notifications">;
  };

  const playNotificationSound = async (
    notification: Tables<"notifications">,
  ) => {
    try {
      // Get user preferences
      const { data: userData } = await supabase
        .from("users")
        .select("metadata")
        .eq("id", user?.id)
        .single();

      const preferences = userData?.metadata?.notificationPreferences;
      if (!preferences?.sounds?.enabled) return;

      const metadata = notification.metadata as any;
      const soundType = preferences.sounds[metadata?.type] || "notification";
      const volume = preferences.sounds.volume || 0.7;

      if (soundType !== "none") {
        const audio = new Audio(`/sounds/${soundType}.mp3`);
        audio.volume = volume;
        audio.play().catch(() => {
          // Fallback to system notification
          if (
            "Notification" in window &&
            Notification.permission === "granted"
          ) {
            new Notification(notification.title, {
              body: notification.message,
              icon: "/bus-icon.svg",
              tag: notification.id,
            });
          }
        });
      }

      // Also send to service worker for background notifications
      if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: "NOTIFICATION_RECEIVED",
          notification: {
            title: notification.title,
            body: notification.message,
            icon: "/bus-icon.svg",
            tag: notification.id,
            data: metadata,
          },
        });
      }

      // Track notification analytics
      try {
        await trackNotificationAnalytics({
          type: "notification_opened",
          notificationId: notification.id,
          notificationData: {
            type: metadata?.type,
            title: notification.title,
            message: notification.message,
          },
          timestamp: new Date().toISOString(),
          metadata: {
            soundPlayed: soundType !== "none",
            soundType,
            volume,
            tenantId: user?.tenant_id,
          },
        });
      } catch (analyticsError) {
        console.warn("Failed to track notification analytics:", analyticsError);
      }
    } catch (error) {
      console.error("Error playing notification sound:", error);
    }
  };

  const clearIndicator = (key: string) => {
    setNotificationIndicators((prev) => {
      const newMap = new Map(prev);
      newMap.delete(key);
      return newMap;
    });
  };

  const trackNotificationGrouping = async (
    groupKey: string,
    notification: Tables<"notifications">,
  ) => {
    try {
      const group = notificationGroups.get(groupKey);
      if (!group) return;

      await trackNotificationAnalytics({
        type: "notification_opened", // Using existing type since notification_grouped doesn't exist
        notificationId: notification.id,
        notificationData: {
          type: (notification.metadata as any)?.type,
          groupKey,
          groupSize: group.notifications.length,
          title: notification.title,
          message: notification.message,
        },
        timestamp: new Date().toISOString(),
        metadata: {
          groupKey,
          groupSize: group.notifications.length,
          timeWindow: 5 * 60 * 1000, // 5 minutes
          tenantId: user?.tenant_id,
          isGrouped: true,
        },
      });
    } catch (error) {
      console.warn("Failed to track notification grouping:", error);
    }
  };

  const getNotificationsByCategory = (category?: string) => {
    if (!category || category === "all") {
      return notifications;
    }

    return notifications.filter((notification) => {
      const metadata = notification.metadata as any;
      return metadata?.type === category;
    });
  };

  const getNotificationCategories = () => {
    const categories = new Set<string>();
    notifications.forEach((notification) => {
      const metadata = notification.metadata as any;
      if (metadata?.type) {
        categories.add(metadata.type);
      }
    });
    return Array.from(categories);
  };

  const createGeofenceAlert = async (
    busId: string,
    stopId: string,
    type: "enter" | "exit",
  ) => {
    if (!user?.tenant_id) return;
    await createGeofenceNotification(busId, stopId, type, user.tenant_id);
  };

  const unreadCount = notifications.filter((n) => !n.read).length;

  return (
    <NotificationsContext.Provider
      value={{
        notifications,
        unreadCount,
        markAsRead,
        markAllAsRead,
        loading,
        error,
        createGeofenceAlert,
        notificationIndicators,
        clearIndicator,
        getNotificationsByCategory,
        getNotificationCategories,
      }}
    >
      {children}
    </NotificationsContext.Provider>
  );
}

export function useNotifications(): NotificationsContextType {
  const context = useContext(NotificationsContext);
  if (!context) {
    throw new Error(
      "useNotifications must be used within a NotificationsProvider",
    );
  }
  return context;
}

export default NotificationsProvider;
