import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { MapboxMap } from "../map/MapboxMap";
import { Marker, Source, Layer } from "react-map-gl";
import { Route, RouteStop } from "../../types";
import { Button } from "../ui/Button";
import { X, Navigation, Clock, MapPin } from "lucide-react";

interface RouteVisualizationProps {
  route: Route;
  isOpen: boolean;
  onClose: () => void;
}

const RouteVisualization: React.FC<RouteVisualizationProps> = ({
  route,
  isOpen,
  onClose,
}) => {
  const { t } = useTranslation();
  const [routeGeometry, setRouteGeometry] = useState<any>(null);
  const [selectedStop, setSelectedStop] = useState<RouteStop | null>(null);
  const [routeMetrics, setRouteMetrics] = useState({
    totalDistance: 0,
    estimatedDuration: 0,
  });

  useEffect(() => {
    if (route.stops.length > 1) {
      generateRouteGeometry();
      calculateRouteMetrics();
    }
  }, [route.stops]);

  const generateRouteGeometry = () => {
    const coordinates = route.stops
      .sort((a, b) => a.order - b.order)
      .map((stop) => [stop.location.lng, stop.location.lat]);

    setRouteGeometry({
      type: "Feature",
      properties: {},
      geometry: {
        type: "LineString",
        coordinates,
      },
    });
  };

  const calculateRouteMetrics = () => {
    let totalDistance = 0;
    const sortedStops = route.stops.sort((a, b) => a.order - b.order);

    for (let i = 0; i < sortedStops.length - 1; i++) {
      const distance = calculateDistance(
        sortedStops[i].location.lat,
        sortedStops[i].location.lng,
        sortedStops[i + 1].location.lat,
        sortedStops[i + 1].location.lng,
      );
      totalDistance += distance;
    }

    setRouteMetrics({
      totalDistance,
      estimatedDuration: Math.round((totalDistance / 30) * 60), // Assuming 30 km/h average speed
    });
  };

  const calculateDistance = (
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number,
  ): number => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLng = ((lng2 - lng1) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((lat1 * Math.PI) / 180) *
        Math.cos((lat2 * Math.PI) / 180) *
        Math.sin(dLng / 2) *
        Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  };

  const getStopColor = (order: number) => {
    if (order === 1) return "#10B981"; // Green for start
    if (order === route.stops.length) return "#EF4444"; // Red for end
    return "#3B82F6"; // Blue for intermediate stops
  };

  if (!isOpen) return null;

  const centerLat =
    route.stops.reduce((sum, stop) => sum + stop.location.lat, 0) /
    route.stops.length;
  const centerLng =
    route.stops.reduce((sum, stop) => sum + stop.location.lng, 0) /
    route.stops.length;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-6xl max-h-[90vh] overflow-hidden">
        <div className="flex justify-between items-center border-b border-gray-200 dark:border-gray-700 p-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {t("routes.routeVisualization")}: {route.name}
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {route.stops.length} {t("routes.stops")} •{" "}
              {routeMetrics.totalDistance.toFixed(1)} km •{" "}
              {routeMetrics.estimatedDuration} {t("routes.minutes")}
            </p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            aria-label="Close"
          >
            <X size={20} />
          </Button>
        </div>

        <div className="flex h-[600px]">
          {/* Map */}
          <div className="flex-1">
            <MapboxMap
              initialViewState={{
                latitude: centerLat,
                longitude: centerLng,
                zoom: 12,
              }}
              showNavigation={true}
            >
              {/* Route line */}
              {routeGeometry && (
                <Source id="route" type="geojson" data={routeGeometry}>
                  <Layer
                    id="route-line"
                    type="line"
                    paint={{
                      "line-color": "#3B82F6",
                      "line-width": 4,
                      "line-opacity": 0.8,
                    }}
                  />
                </Source>
              )}

              {/* Stop markers */}
              {route.stops.map((stop) => (
                <Marker
                  key={stop.id}
                  latitude={stop.location.lat}
                  longitude={stop.location.lng}
                  onClick={() => setSelectedStop(stop)}
                >
                  <div
                    className="rounded-full w-8 h-8 flex items-center justify-center text-xs font-bold text-white border-2 border-white shadow-lg cursor-pointer hover:scale-110 transition-transform"
                    style={{ backgroundColor: getStopColor(stop.order) }}
                  >
                    {stop.order}
                  </div>
                </Marker>
              ))}
            </MapboxMap>
          </div>

          {/* Sidebar */}
          <div className="w-80 border-l border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 overflow-y-auto">
            <div className="p-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                {t("routes.routeDetails")}
              </h3>

              {/* Route Metrics */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  {t("routes.routeMetrics")}
                </h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {t("routes.totalStops")}
                    </span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {route.stops.length}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {t("routes.totalDistance")}
                    </span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {routeMetrics.totalDistance.toFixed(1)} km
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {t("routes.estimatedDuration")}
                    </span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {routeMetrics.estimatedDuration} {t("routes.minutes")}
                    </span>
                  </div>
                </div>
              </div>

              {/* Bus Information */}
              {route.bus && (
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-4">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                    {t("routes.assignedBus")}
                  </h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {t("buses.plateNumber")}
                      </span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {route.bus.plateNumber}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {t("buses.capacity")}
                      </span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {route.bus.capacity} {t("buses.seats")}
                      </span>
                    </div>
                    {route.bus.driver && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {t("routes.driver")}
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {route.bus.driver.name}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Stops List */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  {t("routes.stops")}
                </h4>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {route.stops
                    .sort((a, b) => a.order - b.order)
                    .map((stop) => (
                      <div
                        key={stop.id}
                        className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                          selectedStop?.id === stop.id
                            ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                            : "border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                        }`}
                        onClick={() => setSelectedStop(stop)}
                      >
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center">
                            <div
                              className="w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white mr-2"
                              style={{
                                backgroundColor: getStopColor(stop.order),
                              }}
                            >
                              {stop.order}
                            </div>
                            <span className="text-sm font-medium text-gray-900 dark:text-white">
                              {stop.name}
                            </span>
                          </div>
                        </div>
                        {stop.arrivalTime && (
                          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 ml-8">
                            <Clock size={12} className="mr-1" />
                            {stop.arrivalTime}
                          </div>
                        )}
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RouteVisualization;
