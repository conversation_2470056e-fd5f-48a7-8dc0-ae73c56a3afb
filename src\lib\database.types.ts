export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      attendance: {
        Row: {
          bus_id: string;
          created_at: string;
          id: string;
          location: unknown;
          recorded_at: string;
          recorded_by: string;
          student_id: string;
          tenant_id: string;
          type: "pickup" | "dropoff";
        };
        Insert: {
          bus_id: string;
          created_at?: string;
          id?: string;
          location: unknown;
          recorded_at?: string;
          recorded_by: string;
          student_id: string;
          tenant_id: string;
          type: "pickup" | "dropoff";
        };
        Update: {
          bus_id?: string;
          created_at?: string;
          id?: string;
          location?: unknown;
          recorded_at?: string;
          recorded_by?: string;
          student_id?: string;
          tenant_id?: string;
          type?: "pickup" | "dropoff";
        };
      };
      buses: {
        Row: {
          capacity: number;
          created_at: string;
          driver_id: string | null;
          id: string;
          is_active: boolean;
          last_location: unknown | null;
          last_updated: string | null;
          metadata: Json;
          plate_number: string;
          tenant_id: string;
          updated_at: string;
        };
        Insert: {
          capacity: number;
          created_at?: string;
          driver_id?: string | null;
          id?: string;
          is_active?: boolean;
          last_location?: unknown | null;
          last_updated?: string | null;
          metadata?: Json;
          plate_number: string;
          tenant_id: string;
          updated_at?: string;
        };
        Update: {
          capacity?: number;
          created_at?: string;
          driver_id?: string | null;
          id?: string;
          is_active?: boolean;
          last_location?: unknown | null;
          last_updated?: string | null;
          metadata?: Json;
          plate_number?: string;
          tenant_id?: string;
          updated_at?: string;
        };
      };
      bus_maintenance: {
        Row: {
          id: string;
          bus_id: string;
          type: "routine" | "repair" | "inspection";
          description: string;
          cost: number | null;
          scheduled_date: string;
          completed_date: string | null;
          status: "scheduled" | "in_progress" | "completed" | "overdue";
          notes: string | null;
          created_at: string;
          updated_at: string;
          tenant_id: string;
        };
        Insert: {
          id?: string;
          bus_id: string;
          type: "routine" | "repair" | "inspection";
          description: string;
          cost?: number | null;
          scheduled_date: string;
          completed_date?: string | null;
          status?: "scheduled" | "in_progress" | "completed" | "overdue";
          notes?: string | null;
          created_at?: string;
          updated_at?: string;
          tenant_id: string;
        };
        Update: {
          id?: string;
          bus_id?: string;
          type?: "routine" | "repair" | "inspection";
          description?: string;
          cost?: number | null;
          scheduled_date?: string;
          completed_date?: string | null;
          status?: "scheduled" | "in_progress" | "completed" | "overdue";
          notes?: string | null;
          created_at?: string;
          updated_at?: string;
          tenant_id?: string;
        };
      };
      notifications: {
        Row: {
          created_at: string | null;
          id: string;
          message: string;
          metadata: Json;
          read: boolean | null;
          tenant_id: string | null;
          title: string;
          type: string | null;
          priority: "low" | "normal" | "high" | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          message: string;
          metadata?: Json;
          read?: boolean | null;
          tenant_id?: string | null;
          title: string;
          type?: string | null;
          priority?: "low" | "normal" | "high" | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          message?: string;
          metadata?: Json;
          read?: boolean | null;
          tenant_id?: string | null;
          title?: string;
          type?: string | null;
          priority?: "low" | "normal" | "high" | null;
          updated_at?: string | null;
          user_id?: string;
        };
      };
      push_subscriptions: {
        Row: {
          id: string;
          user_id: string;
          tenant_id: string | null;
          endpoint: string;
          p256dh_key: string;
          auth_key: string;
          user_agent: string | null;
          is_active: boolean | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          tenant_id?: string | null;
          endpoint: string;
          p256dh_key: string;
          auth_key: string;
          user_agent?: string | null;
          is_active?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          tenant_id?: string | null;
          endpoint?: string;
          p256dh_key?: string;
          auth_key?: string;
          user_agent?: string | null;
          is_active?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      notification_templates: {
        Row: {
          id: string;
          tenant_id: string;
          name: string;
          type: "geofence" | "attendance" | "maintenance" | "announcements";
          title: string;
          message: string;
          variables: string[] | null;
          is_active: boolean | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          tenant_id: string;
          name: string;
          type: "geofence" | "attendance" | "maintenance" | "announcements";
          title: string;
          message: string;
          variables?: string[] | null;
          is_active?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          tenant_id?: string;
          name?: string;
          type?: "geofence" | "attendance" | "maintenance" | "announcements";
          title?: string;
          message?: string;
          variables?: string[] | null;
          is_active?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      notification_actions: {
        Row: {
          id: string;
          notification_id: string;
          user_id: string;
          tenant_id: string | null;
          action_type: string;
          action_data: Json | null;
          result: string | null;
          created_at: string | null;
        };
        Insert: {
          id?: string;
          notification_id: string;
          user_id: string;
          tenant_id?: string | null;
          action_type: string;
          action_data?: Json | null;
          result?: string | null;
          created_at?: string | null;
        };
        Update: {
          id?: string;
          notification_id?: string;
          user_id?: string;
          tenant_id?: string | null;
          action_type?: string;
          action_data?: Json | null;
          result?: string | null;
          created_at?: string | null;
        };
      };
      notification_acknowledgments: {
        Row: {
          id: string;
          notification_id: string;
          user_id: string;
          tenant_id: string | null;
          acknowledged_at: string | null;
          notes: string | null;
        };
        Insert: {
          id?: string;
          notification_id: string;
          user_id: string;
          tenant_id?: string | null;
          acknowledged_at?: string | null;
          notes?: string | null;
        };
        Update: {
          id?: string;
          notification_id?: string;
          user_id?: string;
          tenant_id?: string | null;
          acknowledged_at?: string | null;
          notes?: string | null;
        };
      };
      notification_groups: {
        Row: {
          id: string;
          tenant_id: string;
          group_key: string;
          title: string;
          message: string;
          notification_count: number | null;
          last_notification_at: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          tenant_id: string;
          group_key: string;
          title: string;
          message: string;
          notification_count?: number | null;
          last_notification_at?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          tenant_id?: string;
          group_key?: string;
          title?: string;
          message?: string;
          notification_count?: number | null;
          last_notification_at?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      notification_analytics: {
        Row: {
          id: string;
          tenant_id: string | null;
          event_type:
            | "push_sent"
            | "notification_opened"
            | "notification_clicked"
            | "notification_dismissed";
          user_ids: string[] | null;
          notification_id: string | null;
          notification_data: Json | null;
          subscription_count: number | null;
          timestamp: string;
          metadata: Json | null;
          created_at: string | null;
        };
        Insert: {
          id?: string;
          tenant_id?: string | null;
          event_type:
            | "push_sent"
            | "notification_opened"
            | "notification_clicked"
            | "notification_dismissed";
          user_ids?: string[] | null;
          notification_id?: string | null;
          notification_data?: Json | null;
          subscription_count?: number | null;
          timestamp: string;
          metadata?: Json | null;
          created_at?: string | null;
        };
        Update: {
          id?: string;
          tenant_id?: string | null;
          event_type?:
            | "push_sent"
            | "notification_opened"
            | "notification_clicked"
            | "notification_dismissed";
          user_ids?: string[] | null;
          notification_id?: string | null;
          notification_data?: Json | null;
          subscription_count?: number | null;
          timestamp?: string;
          metadata?: Json | null;
          created_at?: string | null;
        };
      };
      route_stops: {
        Row: {
          arrival_time: string | null;
          created_at: string;
          id: string;
          location: unknown;
          name: string;
          order: number;
          route_id: string;
          updated_at: string;
        };
        Insert: {
          arrival_time?: string | null;
          created_at?: string;
          id?: string;
          location: unknown;
          name: string;
          order: number;
          route_id: string;
          updated_at?: string;
        };
        Update: {
          arrival_time?: string | null;
          created_at?: string;
          id?: string;
          location?: unknown;
          name?: string;
          order?: number;
          route_id?: string;
          updated_at?: string;
        };
      };
      routes: {
        Row: {
          bus_id: string | null;
          created_at: string;
          id: string;
          is_active: boolean;
          name: string;
          schedule: Json;
          tenant_id: string;
          updated_at: string;
        };
        Insert: {
          bus_id?: string | null;
          created_at?: string;
          id?: string;
          is_active?: boolean;
          name: string;
          schedule?: Json;
          tenant_id: string;
          updated_at?: string;
        };
        Update: {
          bus_id?: string | null;
          created_at?: string;
          id?: string;
          is_active?: boolean;
          name?: string;
          schedule?: Json;
          tenant_id?: string;
          updated_at?: string;
        };
      };
      students: {
        Row: {
          created_at: string;
          grade: string;
          id: string;
          is_active: boolean;
          metadata: Json;
          name: string;
          parent_id: string | null;
          photo_url: string | null;
          route_stop_id: string | null;
          tenant_id: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          grade: string;
          id?: string;
          is_active?: boolean;
          metadata?: Json;
          name: string;
          parent_id?: string | null;
          photo_url?: string | null;
          route_stop_id?: string | null;
          tenant_id: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          grade?: string;
          id?: string;
          is_active?: boolean;
          metadata?: Json;
          name?: string;
          parent_id?: string | null;
          photo_url?: string | null;
          route_stop_id?: string | null;
          tenant_id?: string;
          updated_at?: string;
        };
      };
      tenants: {
        Row: {
          address: string | null;
          contact_number: string | null;
          created_at: string;
          domain: string | null;
          id: string;
          is_active: boolean;
          logo_url: string | null;
          name: string;
          settings: Json;
          updated_at: string;
        };
        Insert: {
          address?: string | null;
          contact_number?: string | null;
          created_at?: string;
          domain?: string | null;
          id?: string;
          is_active?: boolean;
          logo_url?: string | null;
          name: string;
          settings?: Json;
          updated_at?: string;
        };
        Update: {
          address?: string | null;
          contact_number?: string | null;
          created_at?: string;
          domain?: string | null;
          id?: string;
          is_active?: boolean;
          logo_url?: string | null;
          name?: string;
          settings?: Json;
          updated_at?: string;
        };
      };
      users: {
        Row: {
          avatar_url: string | null;
          created_at: string;
          email: string;
          id: string;
          is_active: boolean;
          metadata: Json;
          name: string;
          phone: string | null;
          role:
            | "admin"
            | "school_manager"
            | "supervisor"
            | "driver"
            | "parent"
            | "student";
          tenant_id: string | null;
          updated_at: string;
        };
        Insert: {
          avatar_url?: string | null;
          created_at?: string;
          email: string;
          id: string;
          is_active?: boolean;
          metadata?: Json;
          name: string;
          phone?: string | null;
          role:
            | "admin"
            | "school_manager"
            | "supervisor"
            | "driver"
            | "parent"
            | "student";
          tenant_id?: string | null;
          updated_at?: string;
        };
        Update: {
          avatar_url?: string | null;
          created_at?: string;
          email?: string;
          id?: string;
          is_active?: boolean;
          metadata?: Json;
          name?: string;
          phone?: string | null;
          role?:
            | "admin"
            | "school_manager"
            | "supervisor"
            | "driver"
            | "parent"
            | "student";
          tenant_id?: string | null;
          updated_at?: string;
        };
      };
      driver_performance: {
        Row: {
          id: string;
          driver_id: string;
          tenant_id: string;
          date: string;
          distance_traveled: number | null;
          average_speed: number | null;
          max_speed: number | null;
          safety_score: number | null;
          on_time_percentage: number | null;
          fuel_consumption: number | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          driver_id: string;
          tenant_id: string;
          date: string;
          distance_traveled?: number | null;
          average_speed?: number | null;
          max_speed?: number | null;
          safety_score?: number | null;
          on_time_percentage?: number | null;
          fuel_consumption?: number | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          driver_id?: string;
          tenant_id?: string;
          date?: string;
          distance_traveled?: number | null;
          average_speed?: number | null;
          max_speed?: number | null;
          safety_score?: number | null;
          on_time_percentage?: number | null;
          fuel_consumption?: number | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      bus_utilization: {
        Row: {
          id: string;
          bus_id: string;
          tenant_id: string;
          date: string;
          total_trips: number | null;
          total_distance: number | null;
          total_students: number | null;
          utilization_percentage: number | null;
          fuel_consumption: number | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          bus_id: string;
          tenant_id: string;
          date: string;
          total_trips?: number | null;
          total_distance?: number | null;
          total_students?: number | null;
          utilization_percentage?: number | null;
          fuel_consumption?: number | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          bus_id?: string;
          tenant_id?: string;
          date?: string;
          total_trips?: number | null;
          total_distance?: number | null;
          total_students?: number | null;
          utilization_percentage?: number | null;
          fuel_consumption?: number | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      route_delays: {
        Row: {
          id: string;
          route_id: string;
          bus_id: string;
          tenant_id: string;
          date: string;
          delay_minutes: number | null;
          reason: string | null;
          affected_stops: string[] | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          route_id: string;
          bus_id: string;
          tenant_id: string;
          date: string;
          delay_minutes?: number | null;
          reason?: string | null;
          affected_stops?: string[] | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          route_id?: string;
          bus_id?: string;
          tenant_id?: string;
          date?: string;
          delay_minutes?: number | null;
          reason?: string | null;
          affected_stops?: string[] | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      spare_parts: {
        Row: {
          id: string;
          tenant_id: string;
          name: string;
          part_number: string | null;
          quantity: number;
          min_quantity: number | null;
          cost: number | null;
          supplier: string | null;
          last_ordered_date: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          tenant_id: string;
          name: string;
          part_number?: string | null;
          quantity?: number;
          min_quantity?: number | null;
          cost?: number | null;
          supplier?: string | null;
          last_ordered_date?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          tenant_id?: string;
          name?: string;
          part_number?: string | null;
          quantity?: number;
          min_quantity?: number | null;
          cost?: number | null;
          supplier?: string | null;
          last_ordered_date?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      maintenance_parts: {
        Row: {
          id: string;
          maintenance_id: string;
          part_id: string;
          quantity_used: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          maintenance_id: string;
          part_id: string;
          quantity_used: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          maintenance_id?: string;
          part_id?: string;
          quantity_used?: number;
          created_at?: string;
        };
      };
      evaluations: {
        Row: {
          id: string;
          tenant_id: string;
          user_id: string;
          target_type: string;
          target_id: string;
          rating: number;
          comment: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          tenant_id: string;
          user_id: string;
          target_type: string;
          target_id: string;
          rating: number;
          comment?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          tenant_id?: string;
          user_id?: string;
          target_type?: string;
          target_id?: string;
          rating?: number;
          comment?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      complaints: {
        Row: {
          id: string;
          tenant_id: string;
          user_id: string;
          type: string;
          target_id: string;
          title: string;
          description: string;
          status: string;
          response: string | null;
          resolved_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          tenant_id: string;
          user_id: string;
          type: string;
          target_id: string;
          title: string;
          description: string;
          status?: string;
          response?: string | null;
          resolved_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          tenant_id?: string;
          user_id?: string;
          type?: string;
          target_id?: string;
          title?: string;
          description?: string;
          status?: string;
          response?: string | null;
          resolved_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
