/**
 * نظام النسخ الاحتياطي الشامل - المرحلة الثانية
 * Comprehensive Backup System - Phase 2
 */

import { createClient } from '@supabase/supabase-js';
import { writeFileSync, mkdirSync, existsSync, readFileSync } from 'fs';
import { join } from 'path';
import * as dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export class ComprehensiveBackupService {
  private backupDir: string;
  private timestamp: string;

  constructor() {
    this.timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    this.backupDir = join(process.cwd(), 'backups', `phase2-backup-${this.timestamp}`);
    
    // إنشاء مجلد النسخ الاحتياطي
    if (!existsSync(this.backupDir)) {
      mkdirSync(this.backupDir, { recursive: true });
    }
  }

  /**
   * إنشاء نسخة احتياطية شاملة
   */
  async createComprehensiveBackup(): Promise<void> {
    console.log('🔄 بدء إنشاء النسخة الاحتياطية الشاملة...\n');

    try {
      // 1. نسخ احتياطي لقاعدة البيانات
      await this.backupDatabase();
      
      // 2. نسخ احتياطي للملفات المهمة
      await this.backupImportantFiles();
      
      // 3. نسخ احتياطي للإعدادات
      await this.backupConfigurations();
      
      // 4. إنشاء تقرير النسخة الاحتياطية
      await this.generateBackupReport();
      
      console.log(`\n✅ تم إنشاء النسخة الاحتياطية بنجاح في: ${this.backupDir}`);
      
    } catch (error) {
      console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
      throw error;
    }
  }

  /**
   * نسخ احتياطي لقاعدة البيانات
   */
  private async backupDatabase(): Promise<void> {
    console.log('📊 نسخ احتياطي لقاعدة البيانات...');

    const dbBackupDir = join(this.backupDir, 'database');
    mkdirSync(dbBackupDir, { recursive: true });

    // قائمة الجداول المهمة
    const importantTables = [
      'users', 'tenants', 'schools', 'buses', 'routes', 'students',
      'drivers', 'notifications', 'attendance_records', 'bus_locations',
      'login_attempts', 'user_blocks', 'ip_blocks', 'security_events',
      'user_sessions', 'user_2fa_config', 'user_behavior_patterns',
      'anomaly_alerts', 'email_verification_codes'
    ];

    for (const table of importantTables) {
      try {
        console.log(`  📋 نسخ جدول: ${table}`);
        
        // الحصول على بنية الجدول
        const { data: tableStructure, error: structureError } = await supabase.rpc('exec_sql', {
          sql: `
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = '${table}'
            ORDER BY ordinal_position;
          `
        });

        if (structureError) {
          console.warn(`    ⚠️ تحذير: لا يمكن الحصول على بنية جدول ${table}`);
          continue;
        }

        // الحصول على البيانات (عينة محدودة للجداول الكبيرة)
        const { data: tableData, error: dataError } = await supabase
          .from(table)
          .select('*')
          .limit(1000); // حد أقصى 1000 سجل لكل جدول

        if (dataError) {
          console.warn(`    ⚠️ تحذير: لا يمكن الحصول على بيانات جدول ${table}`);
          continue;
        }

        // حفظ البيانات
        const backupData = {
          table: table,
          structure: tableStructure,
          data: tableData,
          recordCount: tableData?.length || 0,
          timestamp: new Date().toISOString()
        };

        writeFileSync(
          join(dbBackupDir, `${table}.json`),
          JSON.stringify(backupData, null, 2)
        );

        console.log(`    ✅ تم نسخ ${backupData.recordCount} سجل من جدول ${table}`);

      } catch (error) {
        console.warn(`    ⚠️ خطأ في نسخ جدول ${table}:`, error);
      }
    }

    // نسخ احتياطي للدوال والإجراءات المخزنة
    await this.backupDatabaseFunctions(dbBackupDir);
    
    // نسخ احتياطي لسياسات RLS
    await this.backupRLSPolicies(dbBackupDir);
  }

  /**
   * نسخ احتياطي للدوال والإجراءات المخزنة
   */
  private async backupDatabaseFunctions(dbBackupDir: string): Promise<void> {
    console.log('  🔧 نسخ احتياطي للدوال والإجراءات...');

    try {
      const { data: functions, error } = await supabase.rpc('exec_sql', {
        sql: `
          SELECT routine_name, routine_definition, routine_type
          FROM information_schema.routines 
          WHERE routine_schema = 'public'
          AND routine_name NOT LIKE 'pg_%'
          ORDER BY routine_name;
        `
      });

      if (!error && functions) {
        writeFileSync(
          join(dbBackupDir, 'functions.json'),
          JSON.stringify(functions, null, 2)
        );
        console.log(`    ✅ تم نسخ ${functions.length} دالة وإجراء`);
      }
    } catch (error) {
      console.warn('    ⚠️ تحذير: لا يمكن نسخ الدوال والإجراءات');
    }
  }

  /**
   * نسخ احتياطي لسياسات RLS
   */
  private async backupRLSPolicies(dbBackupDir: string): Promise<void> {
    console.log('  🔒 نسخ احتياطي لسياسات RLS...');

    try {
      const { data: policies, error } = await supabase.rpc('exec_sql', {
        sql: `
          SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
          FROM pg_policies 
          WHERE schemaname = 'public'
          ORDER BY tablename, policyname;
        `
      });

      if (!error && policies) {
        writeFileSync(
          join(dbBackupDir, 'rls_policies.json'),
          JSON.stringify(policies, null, 2)
        );
        console.log(`    ✅ تم نسخ ${policies.length} سياسة RLS`);
      }
    } catch (error) {
      console.warn('    ⚠️ تحذير: لا يمكن نسخ سياسات RLS');
    }
  }

  /**
   * نسخ احتياطي للملفات المهمة
   */
  private async backupImportantFiles(): Promise<void> {
    console.log('📁 نسخ احتياطي للملفات المهمة...');

    const filesBackupDir = join(this.backupDir, 'files');
    mkdirSync(filesBackupDir, { recursive: true });

    const importantFiles = [
      'package.json',
      'vite.config.ts',
      'tsconfig.json',
      'tailwind.config.js',
      '.env.example',
      'README.md',
      'docs/project_phases.md',
      'docs/current_status_analysis.md',
      'docs/changelog.md',
      'src/types/index.ts',
      'src/lib/supabase.ts',
      'src/contexts/AuthContext.tsx',
      'src/services/CentralizedPermissionService.ts'
    ];

    for (const filePath of importantFiles) {
      try {
        if (existsSync(filePath)) {
          const content = readFileSync(filePath, 'utf8');
          const fileName = filePath.replace(/[\/\\]/g, '_');
          writeFileSync(join(filesBackupDir, fileName), content);
          console.log(`  ✅ تم نسخ: ${filePath}`);
        }
      } catch (error) {
        console.warn(`  ⚠️ تحذير: لا يمكن نسخ ${filePath}`);
      }
    }
  }

  /**
   * نسخ احتياطي للإعدادات
   */
  private async backupConfigurations(): Promise<void> {
    console.log('⚙️ نسخ احتياطي للإعدادات...');

    const configBackupDir = join(this.backupDir, 'configurations');
    mkdirSync(configBackupDir, { recursive: true });

    // إعدادات المشروع
    const projectConfig = {
      name: 'School Bus Management SaaS',
      version: '1.0.0',
      phase: 'Phase 2 - Cleanup and Reorganization',
      technologies: {
        frontend: 'React 18 + TypeScript + Vite',
        backend: 'Supabase (PostgreSQL + Auth)',
        styling: 'TailwindCSS + Radix UI',
        maps: 'Mapbox GL',
        charts: 'Recharts',
        i18n: 'i18next'
      },
      features: {
        phase1_completed: [
          'Password Strength Validation',
          'Brute-force Protection',
          'Two-Factor Authentication',
          'Anomaly Detection',
          'Advanced Session Management'
        ]
      },
      backup_timestamp: new Date().toISOString()
    };

    writeFileSync(
      join(configBackupDir, 'project_config.json'),
      JSON.stringify(projectConfig, null, 2)
    );

    console.log('  ✅ تم نسخ إعدادات المشروع');
  }

  /**
   * إنشاء تقرير النسخة الاحتياطية
   */
  private async generateBackupReport(): Promise<void> {
    console.log('📋 إنشاء تقرير النسخة الاحتياطية...');

    const report = {
      backup_info: {
        timestamp: this.timestamp,
        created_at: new Date().toISOString(),
        backup_directory: this.backupDir,
        phase: 'Phase 2 - Cleanup and Reorganization'
      },
      contents: {
        database_backup: 'Complete database structure and data backup',
        files_backup: 'Important project files and configurations',
        configurations: 'Project settings and metadata'
      },
      next_steps: [
        '1. Verify backup integrity',
        '2. Proceed with Phase 2 cleanup',
        '3. Database reorganization',
        '4. Code refactoring',
        '5. Performance optimization'
      ],
      restoration_notes: [
        'Use this backup to restore system state before Phase 2 changes',
        'Database backup includes structure and sample data',
        'RLS policies and functions are preserved',
        'Configuration files can be restored individually'
      ]
    };

    writeFileSync(
      join(this.backupDir, 'backup_report.json'),
      JSON.stringify(report, null, 2)
    );

    // إنشاء ملف README للنسخة الاحتياطية
    const readmeContent = `# نسخة احتياطية شاملة - المرحلة الثانية

## معلومات النسخة الاحتياطية
- **التاريخ**: ${new Date().toLocaleString('ar-SA')}
- **المرحلة**: المرحلة الثانية - التنظيف وإعادة التنظيم
- **النوع**: نسخة احتياطية شاملة

## محتويات النسخة الاحتياطية

### 📊 قاعدة البيانات (database/)
- بنية ومحتوى جميع الجداول المهمة
- الدوال والإجراءات المخزنة
- سياسات RLS

### 📁 الملفات (files/)
- ملفات التكوين الأساسية
- الوثائق المهمة
- ملفات الكود الحيوية

### ⚙️ الإعدادات (configurations/)
- إعدادات المشروع
- معلومات التقنيات المستخدمة
- حالة المراحل المكتملة

## كيفية الاستعادة
1. نسخ الملفات من مجلد files/ إلى مواقعها الأصلية
2. استعادة قاعدة البيانات من ملفات database/
3. تطبيق سياسات RLS من rls_policies.json
4. استعادة الدوال من functions.json

## ملاحظات مهمة
- هذه النسخة الاحتياطية تم إنشاؤها قبل بدء المرحلة الثانية
- تحتوي على حالة النظام بعد إكمال المرحلة الأولى بنجاح
- يمكن استخدامها للعودة إلى نقطة آمنة في حالة وجود مشاكل

---
تم إنشاؤها بواسطة: نظام النسخ الاحتياطي الشامل
`;

    writeFileSync(join(this.backupDir, 'README.md'), readmeContent);

    console.log('  ✅ تم إنشاء تقرير النسخة الاحتياطية');
  }

  /**
   * التحقق من سلامة النسخة الاحتياطية
   */
  async verifyBackup(): Promise<boolean> {
    console.log('\n🔍 التحقق من سلامة النسخة الاحتياطية...');

    try {
      const requiredFiles = [
        'backup_report.json',
        'README.md',
        'database',
        'files',
        'configurations'
      ];

      for (const file of requiredFiles) {
        const filePath = join(this.backupDir, file);
        if (!existsSync(filePath)) {
          console.error(`❌ ملف مفقود: ${file}`);
          return false;
        }
      }

      console.log('✅ جميع الملفات المطلوبة موجودة');
      
      // فحص حجم النسخة الاحتياطية
      const { size } = await import('fs/promises').then(fs => fs.stat(this.backupDir));
      console.log(`📊 حجم النسخة الاحتياطية: ${(size / 1024 / 1024).toFixed(2)} MB`);

      return true;

    } catch (error) {
      console.error('❌ خطأ في التحقق من النسخة الاحتياطية:', error);
      return false;
    }
  }
}

/**
 * الدالة الرئيسية
 */
async function main(): Promise<void> {
  console.log('🚀 بدء نظام النسخ الاحتياطي الشامل - المرحلة الثانية\n');

  try {
    const backupService = new ComprehensiveBackupService();
    
    // إنشاء النسخة الاحتياطية
    await backupService.createComprehensiveBackup();
    
    // التحقق من سلامة النسخة الاحتياطية
    const isValid = await backupService.verifyBackup();
    
    if (isValid) {
      console.log('\n🎉 تم إنشاء النسخة الاحتياطية الشاملة بنجاح!');
      console.log('\n📋 الخطوات التالية:');
      console.log('1. مراجعة محتويات النسخة الاحتياطية');
      console.log('2. البدء في تنظيف قاعدة البيانات');
      console.log('3. إعادة هيكلة الكود');
      console.log('4. تحسين الأداء');
    } else {
      console.error('\n❌ فشل في إنشاء نسخة احتياطية صحيحة');
      process.exit(1);
    }

  } catch (error) {
    console.error('💥 خطأ عام في نظام النسخ الاحتياطي:', error);
    process.exit(1);
  }
}

// تشغيل السكريبت
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { ComprehensiveBackupService };
