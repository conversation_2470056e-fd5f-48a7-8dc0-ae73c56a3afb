-- Update Existing Admin for New Security System
-- تحديث الأدمن الموجود للعمل مع النظام الأمني الجديد
-- يمكن تشغيله في Supabase SQL Editor

-- ===== 1. البحث عن حسابات الأدمن الموجودة =====

DO $$
DECLARE
  admin_record RECORD;
  admin_count INTEGER := 0;
  updated_count INTEGER := 0;
BEGIN
  RAISE NOTICE '=== البحث عن حسابات الأدمن الموجودة ===';
  
  -- البحث في جدول public.users عن الأدمن
  FOR admin_record IN 
    SELECT u.id, u.email, u.name, u.role, u.tenant_id, au.email_confirmed_at
    FROM public.users u
    LEFT JOIN auth.users au ON au.id = u.id
    WHERE u.role = 'admin' OR u.role = 'super_admin'
    ORDER BY u.created_at ASC
  LOOP
    admin_count := admin_count + 1;
    
    RAISE NOTICE 'تم العثور على أدمن #%: البريد: %, الاسم: %, المعرف: %', 
      admin_count, admin_record.email, admin_record.name, admin_record.id;
    
    -- ===== 2. إنشاء إعدادات 2FA إذا لم تكن موجودة =====
    INSERT INTO public.user_2fa_settings (
      user_id,
      is_enabled,
      method,
      created_at,
      updated_at
    ) VALUES (
      admin_record.id,
      false,
      'email',
      now(),
      now()
    )
    ON CONFLICT (user_id, method) DO NOTHING;
    
    -- ===== 3. إنشاء محاولة دخول أولية ناجحة =====
    INSERT INTO public.login_attempts (
      email,
      success,
      ip_address,
      user_agent,
      device_info,
      attempted_at
    ) VALUES (
      admin_record.email,
      true,
      '127.0.0.1'::inet,
      'Admin Security Update',
      jsonb_build_object(
        'source', 'security_update',
        'type', 'existing_admin',
        'updated_by', 'system'
      ),
      COALESCE(admin_record.email_confirmed_at, now())
    )
    ON CONFLICT DO NOTHING;
    
    -- ===== 4. إنشاء جلسة أولية إذا كان نشط مؤخراً =====
    IF admin_record.email_confirmed_at IS NOT NULL AND 
       admin_record.email_confirmed_at > now() - interval '30 days' THEN
      
      INSERT INTO public.active_sessions (
        user_id,
        session_token,
        ip_address,
        user_agent,
        device_info,
        is_active,
        last_activity,
        expires_at,
        created_at
      ) VALUES (
        admin_record.id,
        'admin_security_update_' || admin_record.id,
        '127.0.0.1'::inet,
        'Admin Security Update',
        jsonb_build_object(
          'source', 'security_update',
          'type', 'admin_session',
          'auto_created', true
        ),
        true,
        now(),
        now() + interval '8 hours',
        now()
      )
      ON CONFLICT (session_token) DO NOTHING;
    END IF;
    
    -- ===== 5. تسجيل عملية التحديث في سجل الأمان =====
    INSERT INTO public.security_audit_logs (
      user_id,
      tenant_id,
      action,
      resource_type,
      resource_id,
      old_values,
      new_values,
      ip_address,
      user_agent,
      risk_score,
      severity,
      metadata,
      timestamp
    ) VALUES (
      admin_record.id,
      admin_record.tenant_id,
      'admin_security_update',
      'user',
      admin_record.id,
      '{}'::jsonb,
      jsonb_build_object(
        'email', admin_record.email,
        'role', admin_record.role,
        'security_features_enabled', true,
        'update_type', 'existing_admin_migration'
      ),
      '127.0.0.1'::inet,
      'Admin Security Update',
      20, -- مخاطر منخفضة للتحديث الأمني
      'low',
      jsonb_build_object(
        'update_version', 'phase1_security',
        'auto_migration', true,
        'features_added', array['2fa_settings', 'login_tracking', 'session_management', 'audit_logging']
      ),
      now()
    );
    
    updated_count := updated_count + 1;
    
    RAISE NOTICE '✅ تم تحديث الأدمن: % بنجاح', admin_record.email;
  END LOOP;
  
  -- ===== 6. عرض النتائج =====
  IF admin_count = 0 THEN
    RAISE NOTICE '❌ لم يتم العثور على أي حسابات أدمن في النظام';
    RAISE NOTICE 'تحقق من وجود مستخدمين بدور admin أو super_admin في جدول public.users';
  ELSE
    RAISE NOTICE '=== تم إكمال التحديث بنجاح ===';
    RAISE NOTICE 'إجمالي حسابات الأدمن الموجودة: %', admin_count;
    RAISE NOTICE 'حسابات الأدمن المحدثة: %', updated_count;
    RAISE NOTICE '=====================================';
  END IF;
  
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'خطأ في تحديث حسابات الأدمن: %', SQLERRM;
END $$;

-- ===== 7. التحقق من نتائج التحديث =====

-- عرض حسابات الأدمن المحدثة
SELECT 
  'updated_admins' as info_type,
  u.id,
  u.email,
  u.name,
  u.role,
  u.is_active,
  au.email_confirmed_at,
  au.last_sign_in_at
FROM public.users u
LEFT JOIN auth.users au ON au.id = u.id
WHERE u.role IN ('admin', 'super_admin')
ORDER BY u.created_at;

-- عرض إعدادات 2FA للأدمن
SELECT 
  '2fa_settings' as info_type,
  u.email,
  tfa.is_enabled,
  tfa.method,
  tfa.created_at
FROM public.user_2fa_settings tfa
JOIN public.users u ON u.id = tfa.user_id
WHERE u.role IN ('admin', 'super_admin')
ORDER BY u.email;

-- عرض محاولات الدخول للأدمن
SELECT 
  'login_attempts' as info_type,
  la.email,
  la.success,
  la.attempted_at,
  la.device_info->>'source' as source
FROM public.login_attempts la
JOIN public.users u ON u.email = la.email
WHERE u.role IN ('admin', 'super_admin')
ORDER BY la.attempted_at DESC
LIMIT 10;

-- عرض الجلسات النشطة للأدمن
SELECT 
  'active_sessions' as info_type,
  u.email,
  s.session_token,
  s.is_active,
  s.last_activity,
  s.expires_at,
  s.device_info->>'source' as source
FROM public.active_sessions s
JOIN public.users u ON u.id = s.user_id
WHERE u.role IN ('admin', 'super_admin')
  AND s.is_active = true
ORDER BY s.last_activity DESC;

-- عرض سجل العمليات الأمنية للأدمن
SELECT 
  'security_logs' as info_type,
  u.email,
  sal.action,
  sal.severity,
  sal.risk_score,
  sal.timestamp,
  sal.metadata->>'update_type' as update_type
FROM public.security_audit_logs sal
JOIN public.users u ON u.id = sal.user_id
WHERE u.role IN ('admin', 'super_admin')
  AND sal.action = 'admin_security_update'
ORDER BY sal.timestamp DESC;

-- ===== 8. رسالة التأكيد النهائية =====

DO $$
DECLARE
  admin_count INTEGER;
  updated_features_count INTEGER;
BEGIN
  -- عدد حسابات الأدمن
  SELECT COUNT(*) INTO admin_count
  FROM public.users 
  WHERE role IN ('admin', 'super_admin');
  
  -- عدد الأدمن الذين لديهم إعدادات أمنية
  SELECT COUNT(*) INTO updated_features_count
  FROM public.users u
  JOIN public.user_2fa_settings tfa ON tfa.user_id = u.id
  WHERE u.role IN ('admin', 'super_admin');
  
  RAISE NOTICE '=== تقرير التحديث النهائي ===';
  RAISE NOTICE 'إجمالي حسابات الأدمن: %', admin_count;
  RAISE NOTICE 'حسابات الأدمن مع الميزات الأمنية: %', updated_features_count;
  
  IF admin_count > 0 AND updated_features_count = admin_count THEN
    RAISE NOTICE '✅ تم تحديث جميع حسابات الأدمن بنجاح!';
    RAISE NOTICE 'يمكن للأدمن الآن:';
    RAISE NOTICE '- تسجيل الدخول بالنظام الأمني الجديد';
    RAISE NOTICE '- الوصول إلى لوحة تحكم الأمان';
    RAISE NOTICE '- تفعيل التحقق الثنائي (2FA)';
    RAISE NOTICE '- مراقبة الجلسات والعمليات الأمنية';
  ELSIF admin_count = 0 THEN
    RAISE NOTICE '❌ لا توجد حسابات أدمن في النظام';
    RAISE NOTICE 'يرجى إنشاء حساب أدمن أولاً أو ترقية مستخدم موجود';
  ELSE
    RAISE NOTICE '⚠️ تم تحديث % من أصل % حساب أدمن', updated_features_count, admin_count;
    RAISE NOTICE 'قد تحتاج لمراجعة الحسابات غير المحدثة';
  END IF;
  
  RAISE NOTICE '=====================================';
END $$;
