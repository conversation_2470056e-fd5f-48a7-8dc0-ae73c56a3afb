import React, { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { X, MapPin, Search, Navigation } from "lucide-react";
import { supabase } from "../../lib/supabase";
import { RouteStop } from "../../types";
import { Button } from "../ui/Button";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";

interface RouteStopModalProps {
  isOpen: boolean;
  onClose: (refreshData?: boolean) => void;
  routeId: string;
  stop: RouteStop | null;
  existingStops: RouteStop[];
}

const RouteStopModal: React.FC<RouteStopModalProps> = ({
  isOpen,
  onClose,
  routeId,
  stop,
  existingStops,
}) => {
  const { t } = useTranslation();
  const [name, setName] = useState("");
  const [order, setOrder] = useState(1);
  const [arrivalTime, setArrivalTime] = useState("");
  const [location, setLocation] = useState<{ lat: number; lng: number }>({
    lat: 0,
    lng: 0,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const marker = useRef<mapboxgl.Marker | null>(null);
  const searchTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isOpen) {
      if (stop) {
        setName(stop.name);
        setOrder(stop.order);
        setArrivalTime(stop.arrivalTime || "");
        setLocation(stop.location);
        setSearchQuery(stop.name); // Pre-fill search with stop name
      } else {
        setName("");
        // Set order to be the next number after the highest existing order
        const nextOrder =
          existingStops.length > 0
            ? Math.max(...existingStops.map((s) => s.order)) + 1
            : 1;
        setOrder(nextOrder);
        setArrivalTime("");
        setSearchQuery("");
        // Default location (can be set to school location or center of the city)
        setLocation({ lat: 25.276987, lng: 55.296249 }); // Dubai coordinates as example
      }

      // Initialize map after a short delay to ensure the container is rendered
      setTimeout(() => {
        initializeMap();
      }, 100);
    }
  }, [isOpen, stop, existingStops]);

  const initializeMap = () => {
    if (!mapContainer.current) return;

    // Check if map is already initialized
    if (map.current) return;

    // Initialize Mapbox
    const mapboxToken =
      import.meta.env.VITE_MAPBOX_TOKEN ||
      import.meta.env.VITE_MAPBOX_ACCESS_TOKEN;
    mapboxgl.accessToken = mapboxToken;

    if (!mapboxToken || mapboxToken.includes("example")) {
      console.error("Mapbox access token is missing or invalid");
      // Show fallback message in map container
      if (mapContainer.current) {
        mapContainer.current.innerHTML = `
          <div class="flex items-center justify-center h-full bg-gray-100 dark:bg-gray-800 rounded-md">
            <div class="text-center p-4">
              <p class="text-gray-600 dark:text-gray-300 mb-2">${t("routes.mapNotAvailable")}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">${t("routes.configureMapbox")}</p>
            </div>
          </div>
        `;
      }
      return;
    }

    try {
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: "mapbox://styles/mapbox/streets-v11",
        center: [location.lng, location.lat],
        zoom: 13,
      });

      // Add navigation control
      map.current.addControl(new mapboxgl.NavigationControl(), "top-right");

      // Add marker
      marker.current = new mapboxgl.Marker({ draggable: true })
        .setLngLat([location.lng, location.lat])
        .addTo(map.current);

      // Update location when marker is dragged
      marker.current.on("dragend", () => {
        if (marker.current) {
          const lngLat = marker.current.getLngLat();
          setLocation({ lat: lngLat.lat, lng: lngLat.lng });
          reverseGeocode(lngLat.lat, lngLat.lng);
        }
      });

      // Allow clicking on the map to move the marker
      map.current.on("click", (e) => {
        if (marker.current && map.current) {
          marker.current.setLngLat(e.lngLat);
          setLocation({ lat: e.lngLat.lat, lng: e.lngLat.lng });
          // Reverse geocode to get address
          reverseGeocode(e.lngLat.lat, e.lngLat.lng);
        }
      });
    } catch (error) {
      console.error("Error initializing map:", error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!name.trim()) {
      alert(t("routes.stopNameRequired"));
      return;
    }

    if (order < 1) {
      alert(t("routes.orderMustBePositive"));
      return;
    }

    // Check if order already exists for this route (except for current stop)
    const existingStopWithOrder = existingStops.find(
      (s) => s.order === order && s.id !== stop?.id,
    );
    if (existingStopWithOrder) {
      alert(t("routes.orderAlreadyExists"));
      return;
    }

    if (!location.lat || !location.lng) {
      alert(t("routes.locationRequired"));
      return;
    }

    setIsLoading(true);

    try {
      const stopData = {
        name: name.trim(),
        route_id: routeId,
        order,
        location,
        arrival_time: arrivalTime || null,
      };

      let result;
      if (stop) {
        // Update existing stop
        result = await supabase
          .from("route_stops")
          .update(stopData)
          .eq("id", stop.id)
          .select();
      } else {
        // Create new stop
        result = await supabase.from("route_stops").insert([stopData]).select();
      }

      if (result.error) throw result.error;

      // Show success message
      alert(stop ? t("routes.stopUpdated") : t("routes.stopCreated"));
      onClose(true);
    } catch (error) {
      console.error("Error saving route stop:", error);
      alert(t("routes.errorSavingStop"));
    } finally {
      setIsLoading(false);
    }
  };

  // Clean up map when component unmounts
  useEffect(() => {
    return () => {
      if (map.current) {
        map.current.remove();
        map.current = null;
      }
    };
  }, []);

  // Search for locations using Mapbox Geocoding API
  const searchLocations = async (query: string) => {
    if (!query.trim() || query.length < 3) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const mapboxToken = import.meta.env.VITE_MAPBOX_TOKEN;
      if (!mapboxToken) {
        console.error("Mapbox token not available for geocoding");
        return;
      }

      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(query)}.json?access_token=${mapboxToken}&limit=5&country=AE&types=poi,address`,
      );

      if (response.ok) {
        const data = await response.json();
        setSearchResults(data.features || []);
      }
    } catch (error) {
      console.error("Error searching locations:", error);
    } finally {
      setIsSearching(false);
    }
  };

  // Reverse geocode to get address from coordinates
  const reverseGeocode = async (lat: number, lng: number) => {
    try {
      const mapboxToken = import.meta.env.VITE_MAPBOX_TOKEN;
      if (!mapboxToken) return;

      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${mapboxToken}&limit=1`,
      );

      if (response.ok) {
        const data = await response.json();
        if (data.features && data.features.length > 0) {
          const placeName = data.features[0].place_name;
          if (!name) {
            setName(placeName.split(",")[0]); // Use first part as stop name
          }
          setSearchQuery(placeName);
        }
      }
    } catch (error) {
      console.error("Error reverse geocoding:", error);
    }
  };

  // Handle search input change with debouncing
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);

    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }

    searchTimeout.current = setTimeout(() => {
      searchLocations(value);
    }, 500);
  };

  // Select a location from search results
  const selectLocation = (feature: any) => {
    const [lng, lat] = feature.center;
    setLocation({ lat, lng });
    setSearchQuery(feature.place_name);
    setSearchResults([]);

    if (!name) {
      setName(feature.text || feature.place_name.split(",")[0]);
    }

    // Update map and marker
    if (map.current && marker.current) {
      map.current.flyTo({ center: [lng, lat], zoom: 16 });
      marker.current.setLngLat([lng, lat]);
    }
  };

  // Get current location
  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const lat = position.coords.latitude;
          const lng = position.coords.longitude;
          setLocation({ lat, lng });

          if (map.current && marker.current) {
            map.current.flyTo({ center: [lng, lat], zoom: 16 });
            marker.current.setLngLat([lng, lat]);
          }

          reverseGeocode(lat, lng);
        },
        (error) => {
          console.error("Error getting current location:", error);
          alert(t("routes.locationError"));
        },
      );
    } else {
      alert(t("routes.geolocationNotSupported"));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center border-b border-gray-200 dark:border-gray-700 p-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {stop ? t("routes.editStop") : t("routes.addStop")}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onClose()}
            aria-label="Close"
          >
            <X size={20} />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="p-4">
          <div className="space-y-4">
            <div>
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                {t("routes.stopName")}
              </label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                required
              />
            </div>

            <div>
              <label
                htmlFor="order"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                {t("routes.order")}
              </label>
              <input
                type="number"
                id="order"
                value={order}
                onChange={(e) => setOrder(parseInt(e.target.value))}
                min="1"
                className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                required
              />
            </div>

            <div>
              <label
                htmlFor="arrivalTime"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                {t("routes.arrivalTime")}
              </label>
              <input
                type="time"
                id="arrivalTime"
                value={arrivalTime}
                onChange={(e) => setArrivalTime(e.target.value)}
                className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t("routes.location")}
              </label>

              {/* Location Search */}
              <div className="mb-3 relative">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search size={18} className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => handleSearchChange(e.target.value)}
                    placeholder={t("routes.searchLocation")}
                    className="pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                  <button
                    type="button"
                    onClick={getCurrentLocation}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    title={t("routes.getCurrentLocation")}
                  >
                    <Navigation
                      size={18}
                      className="text-gray-400 hover:text-primary-500"
                    />
                  </button>
                </div>

                {/* Search Results */}
                {searchResults.length > 0 && (
                  <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-48 overflow-y-auto">
                    {searchResults.map((result, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => selectLocation(result)}
                        className="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-600 last:border-b-0"
                      >
                        <div className="flex items-center">
                          <MapPin
                            size={16}
                            className="text-gray-400 mr-2 flex-shrink-0"
                          />
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {result.text}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {result.place_name}
                            </div>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                )}

                {isSearching && (
                  <div className="absolute right-12 top-2">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500"></div>
                  </div>
                )}
              </div>

              <div
                ref={mapContainer}
                className="w-full h-64 rounded-md border border-gray-300 dark:border-gray-600"
              />
              <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                {t("routes.clickMapToSetLocation")}
              </div>
              <div className="mt-1 grid grid-cols-2 gap-2">
                <div>
                  <label
                    htmlFor="latitude"
                    className="block text-xs font-medium text-gray-700 dark:text-gray-300"
                  >
                    {t("common.latitude")}
                  </label>
                  <input
                    type="text"
                    id="latitude"
                    value={location.lat.toFixed(6)}
                    readOnly
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1 text-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label
                    htmlFor="longitude"
                    className="block text-xs font-medium text-gray-700 dark:text-gray-300"
                  >
                    {t("common.longitude")}
                  </label>
                  <input
                    type="text"
                    id="longitude"
                    value={location.lng.toFixed(6)}
                    readOnly
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1 text-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => onClose()}
              disabled={isLoading}
            >
              {t("common.cancel")}
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin mr-2 h-4 w-4 border-b-2 border-white rounded-full"></div>
                  {t("common.saving")}
                </div>
              ) : stop ? (
                t("common.save")
              ) : (
                t("common.create")
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RouteStopModal;
