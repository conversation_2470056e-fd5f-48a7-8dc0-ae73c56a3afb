/**
 * RTL Provider
 * Comprehensive RTL support for Arabic and Hebrew languages
 * Phase 3: UI/UX Enhancement
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';

export interface RTLConfig {
  direction: 'ltr' | 'rtl';
  language: string;
  locale: string;
  textAlign: 'left' | 'right' | 'start' | 'end';
  numerals: 'western' | 'arabic' | 'persian';
  calendar: 'gregorian' | 'hijri' | 'persian';
}

export interface RTLContextValue {
  config: RTLConfig;
  isRTL: boolean;
  setDirection: (direction: 'ltr' | 'rtl') => void;
  setLanguage: (language: string) => void;
  toggleDirection: () => void;
  getLogicalProperty: (property: string) => string;
  getDirectionalValue: <T>(ltrValue: T, rtlValue: T) => T;
  formatNumber: (number: number) => string;
  formatDate: (date: Date) => string;
}

const RTLContext = createContext<RTLContextValue | null>(null);

export interface RTLProviderProps {
  children: ReactNode;
  defaultDirection?: 'ltr' | 'rtl';
  defaultLanguage?: string;
  autoDetect?: boolean;
}

/**
 * RTL Language Detection
 */
const RTL_LANGUAGES = new Set([
  'ar', 'he', 'fa', 'ur', 'ku', 'dv', 'ps', 'sd', 'ug', 'yi'
]);

const ARABIC_LOCALES = new Set([
  'ar-SA', 'ar-EG', 'ar-AE', 'ar-KW', 'ar-QA', 'ar-BH', 'ar-OM', 'ar-JO', 'ar-LB', 'ar-SY', 'ar-IQ', 'ar-LY', 'ar-MA', 'ar-TN', 'ar-DZ'
]);

const HEBREW_LOCALES = new Set(['he-IL']);
const PERSIAN_LOCALES = new Set(['fa-IR', 'fa-AF']);

/**
 * Detect if language is RTL
 */
function isRTLLanguage(language: string): boolean {
  const langCode = language.split('-')[0];
  return RTL_LANGUAGES.has(langCode);
}

/**
 * Get default RTL config for language
 */
function getDefaultRTLConfig(language: string): Partial<RTLConfig> {
  const langCode = language.split('-')[0];
  
  switch (langCode) {
    case 'ar':
      return {
        direction: 'rtl',
        textAlign: 'right',
        numerals: 'arabic',
        calendar: 'hijri',
      };
    case 'he':
      return {
        direction: 'rtl',
        textAlign: 'right',
        numerals: 'western',
        calendar: 'gregorian',
      };
    case 'fa':
      return {
        direction: 'rtl',
        textAlign: 'right',
        numerals: 'persian',
        calendar: 'persian',
      };
    default:
      return {
        direction: 'ltr',
        textAlign: 'left',
        numerals: 'western',
        calendar: 'gregorian',
      };
  }
}

/**
 * RTL Provider Component
 */
export const RTLProvider: React.FC<RTLProviderProps> = ({
  children,
  defaultDirection = 'ltr',
  defaultLanguage = 'en',
  autoDetect = true,
}) => {
  // Detect initial language and direction
  const detectInitialConfig = (): RTLConfig => {
    let language = defaultLanguage;
    let direction = defaultDirection;

    if (autoDetect) {
      // Try to detect from browser
      const browserLang = navigator.language || navigator.languages?.[0] || 'en';
      language = browserLang;
      
      if (isRTLLanguage(browserLang)) {
        direction = 'rtl';
      }
    }

    const defaultConfig = getDefaultRTLConfig(language);
    
    return {
      direction: direction,
      language,
      locale: language,
      textAlign: defaultConfig.textAlign || 'left',
      numerals: defaultConfig.numerals || 'western',
      calendar: defaultConfig.calendar || 'gregorian',
    };
  };

  const [config, setConfig] = useState<RTLConfig>(detectInitialConfig);

  /**
   * Update document direction
   */
  useEffect(() => {
    const html = document.documentElement;
    
    // Set direction
    html.setAttribute('dir', config.direction);
    html.setAttribute('lang', config.language);
    
    // Add/remove RTL class
    if (config.direction === 'rtl') {
      html.classList.add('rtl');
      html.classList.remove('ltr');
    } else {
      html.classList.add('ltr');
      html.classList.remove('rtl');
    }

    // Set CSS custom properties
    html.style.setProperty('--text-align', config.textAlign);
    html.style.setProperty('--direction', config.direction);
    
    // Set logical properties
    if (config.direction === 'rtl') {
      html.style.setProperty('--start', 'right');
      html.style.setProperty('--end', 'left');
    } else {
      html.style.setProperty('--start', 'left');
      html.style.setProperty('--end', 'right');
    }
  }, [config]);

  /**
   * Set direction
   */
  const setDirection = (direction: 'ltr' | 'rtl') => {
    setConfig(prev => ({
      ...prev,
      direction,
      textAlign: direction === 'rtl' ? 'right' : 'left',
    }));
  };

  /**
   * Set language
   */
  const setLanguage = (language: string) => {
    const defaultConfig = getDefaultRTLConfig(language);
    
    setConfig(prev => ({
      ...prev,
      language,
      locale: language,
      ...defaultConfig,
    }));
  };

  /**
   * Toggle direction
   */
  const toggleDirection = () => {
    setDirection(config.direction === 'rtl' ? 'ltr' : 'rtl');
  };

  /**
   * Get logical CSS property
   */
  const getLogicalProperty = (property: string): string => {
    const logicalMap: Record<string, string> = {
      'margin-left': 'margin-inline-start',
      'margin-right': 'margin-inline-end',
      'padding-left': 'padding-inline-start',
      'padding-right': 'padding-inline-end',
      'border-left': 'border-inline-start',
      'border-right': 'border-inline-end',
      'left': 'inset-inline-start',
      'right': 'inset-inline-end',
      'text-align-left': 'text-align: start',
      'text-align-right': 'text-align: end',
    };

    return logicalMap[property] || property;
  };

  /**
   * Get directional value
   */
  const getDirectionalValue = <T,>(ltrValue: T, rtlValue: T): T => {
    return config.direction === 'rtl' ? rtlValue : ltrValue;
  };

  /**
   * Format number according to locale
   */
  const formatNumber = (number: number): string => {
    const options: Intl.NumberFormatOptions = {};
    
    // Set numeral system
    if (config.numerals === 'arabic') {
      options.numberingSystem = 'arab';
    } else if (config.numerals === 'persian') {
      options.numberingSystem = 'arabext';
    }

    try {
      return new Intl.NumberFormat(config.locale, options).format(number);
    } catch {
      return number.toString();
    }
  };

  /**
   * Format date according to locale and calendar
   */
  const formatDate = (date: Date): string => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };

    // Set calendar system
    if (config.calendar === 'hijri') {
      options.calendar = 'islamic';
    } else if (config.calendar === 'persian') {
      options.calendar = 'persian';
    }

    try {
      return new Intl.DateTimeFormat(config.locale, options).format(date);
    } catch {
      return date.toLocaleDateString();
    }
  };

  const contextValue: RTLContextValue = {
    config,
    isRTL: config.direction === 'rtl',
    setDirection,
    setLanguage,
    toggleDirection,
    getLogicalProperty,
    getDirectionalValue,
    formatNumber,
    formatDate,
  };

  return (
    <RTLContext.Provider value={contextValue}>
      {children}
    </RTLContext.Provider>
  );
};

/**
 * Hook to use RTL context
 */
export const useRTL = (): RTLContextValue => {
  const context = useContext(RTLContext);
  if (!context) {
    throw new Error('useRTL must be used within an RTLProvider');
  }
  return context;
};

/**
 * HOC for RTL support
 */
export function withRTL<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  const RTLComponent = (props: P) => {
    const rtl = useRTL();
    return <Component {...props} rtl={rtl} />;
  };

  RTLComponent.displayName = `withRTL(${Component.displayName || Component.name})`;
  return RTLComponent;
}

/**
 * RTL-aware CSS-in-JS helper
 */
export const rtlCSS = {
  /**
   * Create RTL-aware styles
   */
  create: (styles: Record<string, any>) => {
    const rtlStyles: Record<string, any> = {};
    
    Object.entries(styles).forEach(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        rtlStyles[key] = rtlCSS.create(value);
      } else {
        rtlStyles[key] = value;
      }
    });

    return rtlStyles;
  },

  /**
   * Get directional style
   */
  directional: (ltrStyle: any, rtlStyle: any) => {
    return {
      '[dir="ltr"] &': ltrStyle,
      '[dir="rtl"] &': rtlStyle,
    };
  },

  /**
   * Convert directional properties to logical
   */
  logical: (styles: Record<string, any>) => {
    const logicalStyles: Record<string, any> = {};
    
    Object.entries(styles).forEach(([property, value]) => {
      switch (property) {
        case 'marginLeft':
          logicalStyles.marginInlineStart = value;
          break;
        case 'marginRight':
          logicalStyles.marginInlineEnd = value;
          break;
        case 'paddingLeft':
          logicalStyles.paddingInlineStart = value;
          break;
        case 'paddingRight':
          logicalStyles.paddingInlineEnd = value;
          break;
        case 'borderLeft':
          logicalStyles.borderInlineStart = value;
          break;
        case 'borderRight':
          logicalStyles.borderInlineEnd = value;
          break;
        case 'left':
          logicalStyles.insetInlineStart = value;
          break;
        case 'right':
          logicalStyles.insetInlineEnd = value;
          break;
        default:
          logicalStyles[property] = value;
      }
    });

    return logicalStyles;
  },
};

/**
 * RTL utilities
 */
export const rtlUtils = {
  /**
   * Check if language is RTL
   */
  isRTLLanguage,

  /**
   * Get text direction for language
   */
  getTextDirection: (language: string): 'ltr' | 'rtl' => {
    return isRTLLanguage(language) ? 'rtl' : 'ltr';
  },

  /**
   * Get appropriate font family for language
   */
  getFontFamily: (language: string): string[] => {
    const langCode = language.split('-')[0];
    
    switch (langCode) {
      case 'ar':
        return ['Noto Sans Arabic', 'Cairo', 'Amiri', 'Arial', 'sans-serif'];
      case 'he':
        return ['Noto Sans Hebrew', 'David', 'Arial Hebrew', 'Arial', 'sans-serif'];
      case 'fa':
        return ['Noto Sans Persian', 'Vazir', 'Tahoma', 'Arial', 'sans-serif'];
      default:
        return ['Inter', 'system-ui', 'sans-serif'];
    }
  },

  /**
   * Mirror icon for RTL
   */
  shouldMirrorIcon: (iconName: string): boolean => {
    const mirrorIcons = new Set([
      'arrow-left', 'arrow-right', 'chevron-left', 'chevron-right',
      'angle-left', 'angle-right', 'caret-left', 'caret-right',
      'forward', 'backward', 'next', 'previous', 'undo', 'redo'
    ]);
    
    return mirrorIcons.has(iconName.toLowerCase());
  },
};
