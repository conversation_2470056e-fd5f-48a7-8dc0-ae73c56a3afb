/**
 * Driver Dashboard Component
 * Provides driver-specific view with assigned bus and route information
 */

import React from "react";
import { useTranslation } from "react-i18next";
import {
  Bus,
  Route,
  Users,
  MapPin,
  Clock,
  CheckCircle,
  AlertTriangle,
  Navigation,
  Activity,
} from "lucide-react";
import { StatCard } from "../dashboard/StatCard";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { useRBACEnhancedSecurity } from "../../hooks/useRBACEnhancedSecurity";

interface DriverDashboardProps {
  className?: string;
}

export const DriverDashboard: React.FC<DriverDashboardProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { buses, routes, students } = useDatabase();
  const { filterDataByRole } = useRBACEnhancedSecurity();

  // Filter data to driver's assigned resources
  const filteredBuses = filterDataByRole(buses, "bus", "driver_id");
  const filteredRoutes = filterDataByRole(routes, "route");
  const filteredStudents = filterDataByRole(students, "student", "parent_id");

  // Get driver's assigned bus and route
  const myBus = filteredBuses.find((b) => b.driver_id === user?.id);
  const myRoute = myBus
    ? filteredRoutes.find((r) => r.bus_id === myBus.id)
    : null;
  const routeStudents = myRoute
    ? filteredStudents.filter((s) => {
        return myRoute.stops?.some((stop) => stop.id === s.route_stop_id);
      })
    : [];

  const stats = {
    assignedBus: myBus ? 1 : 0,
    assignedRoute: myRoute ? 1 : 0,
    studentsOnRoute: routeStudents.length,
    activeStudents: routeStudents.filter((s) => s.is_active).length,
    totalStops: myRoute?.stops?.length || 0,
    busCapacity: myBus?.capacity || 0,
    busStatus: myBus?.is_active ? "Active" : "Inactive",
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Driver Overview */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Welcome, {user?.name}
          </h2>
          <div className="flex items-center space-x-2">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                myBus
                  ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                  : "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100"
              }`}
            >
              {myBus ? "Bus Assigned" : "No Bus Assigned"}
            </span>
          </div>
        </div>
        <p className="text-gray-600 dark:text-gray-400">
          {myBus
            ? `You are assigned to bus ${myBus.plate_number}${myRoute ? ` on route ${myRoute.name}` : ""}`
            : "Please contact your supervisor for bus assignment."}
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="My Bus"
          value={myBus?.plate_number || "Not Assigned"}
          icon={<Bus size={24} />}
          className={`${
            myBus
              ? "bg-gradient-to-r from-green-500 to-green-600 text-white"
              : "bg-gradient-to-r from-gray-400 to-gray-500 text-white"
          }`}
        />
        <StatCard
          title="My Route"
          value={myRoute?.name || "Not Assigned"}
          icon={<Route size={24} />}
          className={`${
            myRoute
              ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white"
              : "bg-gradient-to-r from-gray-400 to-gray-500 text-white"
          }`}
        />
        <StatCard
          title="Students on Route"
          value={stats.studentsOnRoute}
          icon={<Users size={24} />}
          trend={{
            value: stats.activeStudents,
            isPositive: true,
            label: "Active",
          }}
          className="bg-gradient-to-r from-purple-500 to-purple-600 text-white"
        />
        <StatCard
          title="Route Stops"
          value={stats.totalStops}
          icon={<MapPin size={24} />}
          className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white"
        />
      </div>

      {/* Bus Information */}
      {myBus && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Bus Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div>
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  Plate Number
                </p>
                <p className="text-lg font-bold text-blue-700 dark:text-blue-300">
                  {myBus.plate_number}
                </p>
              </div>
              <Bus className="h-8 w-8 text-blue-500" />
            </div>
            <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div>
                <p className="text-sm text-green-600 dark:text-green-400">
                  Capacity
                </p>
                <p className="text-lg font-bold text-green-700 dark:text-green-300">
                  {myBus.capacity} seats
                </p>
              </div>
              <Users className="h-8 w-8 text-green-500" />
            </div>
            <div className="flex items-center justify-between p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <div>
                <p className="text-sm text-yellow-600 dark:text-yellow-400">
                  Status
                </p>
                <p className="text-lg font-bold text-yellow-700 dark:text-yellow-300">
                  {stats.busStatus}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-yellow-500" />
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Today's Tasks
          </h3>
          <div className="space-y-3">
            <button className="w-full flex items-center justify-between p-3 text-left bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  Record Attendance
                </span>
              </div>
            </button>
            <button className="w-full flex items-center justify-between p-3 text-left bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
              <div className="flex items-center">
                <Navigation className="h-5 w-5 text-blue-500 mr-3" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  View My Route
                </span>
              </div>
            </button>
            <button className="w-full flex items-center justify-between p-3 text-left bg-yellow-50 dark:bg-yellow-900/20 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-yellow-500 mr-3" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  Report Issue
                </span>
              </div>
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Route Schedule
          </h3>
          {myRoute && myRoute.stops ? (
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {myRoute.stops
                .sort((a, b) => a.order - b.order)
                .map((stop, index) => (
                  <div
                    key={stop.id}
                    className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded"
                  >
                    <div className="flex items-center">
                      <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded mr-2">
                        {index + 1}
                      </span>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {stop.name}
                      </span>
                    </div>
                    {stop.arrival_time && (
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {stop.arrival_time}
                      </span>
                    )}
                  </div>
                ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Clock size={32} className="mx-auto mb-2 opacity-50" />
              <p className="text-sm">No route assigned</p>
            </div>
          )}
        </div>
      </div>

      {/* Driver Performance Summary */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <Activity className="h-5 w-5 mr-2" />
          Performance Summary
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              95%
            </div>
            <div className="text-sm text-green-600 dark:text-green-400">
              Pickup Rate
            </div>
          </div>
          <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              92%
            </div>
            <div className="text-sm text-blue-600 dark:text-blue-400">
              Drop-off Rate
            </div>
          </div>
          <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {stats.totalStops}
            </div>
            <div className="text-sm text-purple-600 dark:text-purple-400">
              Total Stops
            </div>
          </div>
        </div>
      </div>

      {/* Safety and Emergency Information */}
      <div className="bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
        <h3 className="text-lg font-medium text-red-800 dark:text-red-200 mb-4 flex items-center">
          <AlertTriangle className="h-5 w-5 mr-2" />
          Safety & Emergency
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium text-red-700 dark:text-red-300">
              Emergency Contacts
            </h4>
            <div className="space-y-1 text-sm text-red-600 dark:text-red-400">
              <p>School Office: +1 (555) 123-4567</p>
              <p>Transport Supervisor: +1 (555) 987-6543</p>
              <p>Emergency Services: 911</p>
            </div>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium text-red-700 dark:text-red-300">
              Safety Reminders
            </h4>
            <ul className="space-y-1 text-sm text-red-600 dark:text-red-400">
              <li>• Ensure all students are seated before moving</li>
              <li>• Check mirrors and blind spots regularly</li>
              <li>• Report any incidents immediately</li>
              <li>• Follow designated routes only</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DriverDashboard;
