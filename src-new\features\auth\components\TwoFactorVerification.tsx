/**
 * مكون التحقق الثنائي أثناء تسجيل الدخول
 * Two-Factor Verification Component for Login
 * 
 * المرحلة 1: تأمين الدخول والسياسات الأمنية الأساسية
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Shield, Smartphone, Mail, RefreshCw, AlertCircle } from 'lucide-react';
import { Button } from '../ui/Button';
import { TwoFactorAuthService } from '../../services/security/TwoFactorAuth';

interface TwoFactorVerificationProps {
  userId: string;
  email: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export const TwoFactorVerification: React.FC<TwoFactorVerificationProps> = ({
  userId,
  email,
  onSuccess,
  onCancel
}) => {
  const { t } = useTranslation();
  const [method, setMethod] = useState<'totp' | 'email' | 'backup'>('totp');
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [emailSent, setEmailSent] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);

  const twoFactorService = TwoFactorAuthService.getInstance();

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (resendCooldown > 0) {
      interval = setInterval(() => {
        setResendCooldown(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendCooldown]);

  const handleVerification = async () => {
    if (!verificationCode.trim()) {
      setError('يرجى إدخال رمز التحقق');
      return;
    }

    setLoading(true);
    setError('');

    try {
      let success = false;

      if (method === 'totp' || method === 'backup') {
        // للتحقق من TOTP أو رموز النسخ الاحتياطي
        // يجب الحصول على إعدادات المستخدم أولاً
        success = twoFactorService.verifyTOTP('user-secret', verificationCode);
      } else if (method === 'email') {
        success = await twoFactorService.verifyEmailCode(userId, verificationCode);
      }

      if (success) {
        onSuccess();
      } else {
        setError('رمز التحقق غير صحيح');
      }

    } catch (error) {
      setError(error instanceof Error ? error.message : 'حدث خطأ في التحقق');
    } finally {
      setLoading(false);
    }
  };

  const sendEmailCode = async () => {
    if (resendCooldown > 0) return;

    setLoading(true);
    setError('');

    try {
      await twoFactorService.sendEmailVerificationCode(userId, email);
      setEmailSent(true);
      setResendCooldown(60); // 60 ثانية قبل إعادة الإرسال
    } catch (error) {
      setError(error instanceof Error ? error.message : 'فشل في إرسال رمز التحقق');
    } finally {
      setLoading(false);
    }
  };

  const handleMethodChange = (newMethod: 'totp' | 'email' | 'backup') => {
    setMethod(newMethod);
    setVerificationCode('');
    setError('');
    
    if (newMethod === 'email' && !emailSent) {
      sendEmailCode();
    }
  };

  return (
    <div className="max-w-md mx-auto bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
      <div className="text-center mb-6">
        <Shield className="w-12 h-12 text-primary-500 mx-auto mb-4" />
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          التحقق الثنائي مطلوب
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          يرجى إدخال رمز التحقق لإكمال تسجيل الدخول
        </p>
      </div>

      {/* اختيار طريقة التحقق */}
      <div className="mb-6">
        <div className="flex space-x-1 space-x-reverse bg-gray-100 dark:bg-gray-700 p-1 rounded-lg">
          <button
            onClick={() => handleMethodChange('totp')}
            className={`flex-1 flex items-center justify-center space-x-2 space-x-reverse py-2 px-3 rounded-md text-sm font-medium transition-colors ${
              method === 'totp'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'
            }`}
          >
            <Smartphone className="w-4 h-4" />
            <span>التطبيق</span>
          </button>
          
          <button
            onClick={() => handleMethodChange('email')}
            className={`flex-1 flex items-center justify-center space-x-2 space-x-reverse py-2 px-3 rounded-md text-sm font-medium transition-colors ${
              method === 'email'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'
            }`}
          >
            <Mail className="w-4 h-4" />
            <span>البريد</span>
          </button>
          
          <button
            onClick={() => handleMethodChange('backup')}
            className={`flex-1 flex items-center justify-center space-x-2 space-x-reverse py-2 px-3 rounded-md text-sm font-medium transition-colors ${
              method === 'backup'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'
            }`}
          >
            <AlertCircle className="w-4 h-4" />
            <span>احتياطي</span>
          </button>
        </div>
      </div>

      {/* وصف الطريقة المختارة */}
      <div className="mb-4">
        {method === 'totp' && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Smartphone className="w-4 h-4 text-blue-500" />
              <p className="text-sm text-blue-800 dark:text-blue-200">
                أدخل الرمز من تطبيق Google Authenticator
              </p>
            </div>
          </div>
        )}
        
        {method === 'email' && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-3">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Mail className="w-4 h-4 text-green-500" />
              <div>
                <p className="text-sm text-green-800 dark:text-green-200">
                  {emailSent 
                    ? `تم إرسال رمز التحقق إلى ${email}`
                    : 'سيتم إرسال رمز التحقق إلى بريدك الإلكتروني'
                  }
                </p>
                {emailSent && (
                  <button
                    onClick={sendEmailCode}
                    disabled={resendCooldown > 0 || loading}
                    className="text-xs text-green-600 dark:text-green-400 hover:underline mt-1 disabled:opacity-50"
                  >
                    {resendCooldown > 0 
                      ? `إعادة الإرسال خلال ${resendCooldown}ث`
                      : 'إعادة إرسال الرمز'
                    }
                  </button>
                )}
              </div>
            </div>
          </div>
        )}
        
        {method === 'backup' && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-3">
            <div className="flex items-center space-x-2 space-x-reverse">
              <AlertCircle className="w-4 h-4 text-yellow-500" />
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                أدخل أحد رموز النسخ الاحتياطي
              </p>
            </div>
          </div>
        )}
      </div>

      {/* حقل إدخال الرمز */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          رمز التحقق
        </label>
        <input
          type="text"
          value={verificationCode}
          onChange={(e) => setVerificationCode(e.target.value)}
          placeholder={method === 'backup' ? 'XXXXXXXX' : '000000'}
          maxLength={method === 'backup' ? 8 : 6}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-center font-mono text-lg tracking-widest focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
          autoComplete="one-time-code"
        />
      </div>

      {/* رسالة الخطأ */}
      {error && (
        <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}

      {/* أزرار العمل */}
      <div className="flex space-x-3 space-x-reverse">
        <Button
          variant="outline"
          onClick={onCancel}
          className="flex-1"
        >
          إلغاء
        </Button>
        <Button
          onClick={handleVerification}
          disabled={loading || !verificationCode.trim()}
          className="flex-1"
        >
          {loading ? (
            <div className="flex items-center space-x-2 space-x-reverse">
              <RefreshCw className="w-4 h-4 animate-spin" />
              <span>جاري التحقق...</span>
            </div>
          ) : (
            'تحقق'
          )}
        </Button>
      </div>

      {/* رابط المساعدة */}
      <div className="mt-4 text-center">
        <button
          onClick={() => handleMethodChange(method === 'backup' ? 'totp' : 'backup')}
          className="text-sm text-primary-600 dark:text-primary-400 hover:underline"
        >
          {method === 'backup' 
            ? 'العودة لتطبيق المصادقة'
            : 'لا يمكنك الوصول لتطبيق المصادقة؟'
          }
        </button>
      </div>
    </div>
  );
};
