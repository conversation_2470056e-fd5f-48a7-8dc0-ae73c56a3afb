/**
 * School Theme Settings Page
 * Main page for school theme customization
 * Phase 3: UI/UX Enhancement - School Pages
 */

import React from 'react';
import { ThemeCustomizer } from '../../components/theme/ThemeCustomizer';
import { Sidebar } from '../../components/layout/Sidebar';
import { Navbar } from '../../components/layout/Navbar';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { UserRole } from '../../types';

/**
 * School Theme Settings Page
 */
export const ThemeSettingsPage: React.FC = () => {
  const { direction } = useTheme();
  const isRTL = direction === 'rtl';

  return (
    <div className="min-h-screen bg-gray-50" dir={direction}>
      <Navbar />
      <div className="flex">
        <Sidebar />
        <main className="flex-1 overflow-y-auto">
          <div className="p-4 md:p-6 lg:p-8">
            <ThemeCustomizer type="school" />
          </div>
        </main>
      </div>
    </div>
  );
};

export default ThemeSettingsPage;
