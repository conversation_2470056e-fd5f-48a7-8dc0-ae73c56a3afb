/**
 * Transaction Manager - مدير المعاملات
 * يحل مشاكل إدارة المعاملات والعمليات المعقدة
 */

import { supabase } from './supabase';
import { APIResponse } from '../api/types';

export interface TransactionOperation {
  name: string;
  operation: () => Promise<any>;
  rollback?: () => Promise<any>;
}

export interface TransactionResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  completedOperations?: string[];
  failedOperation?: string;
}

export class TransactionManager {
  
  /**
   * تنفيذ عملية معقدة مع معاملة
   */
  static async executeTransaction<T>(
    operations: TransactionOperation[]
  ): Promise<TransactionResult<T>> {
    
    const completedOperations: string[] = [];
    const results: any[] = [];
    
    try {
      // تنفيذ العمليات بالتسلسل
      for (const operation of operations) {
        console.log(`Executing operation: ${operation.name}`);
        
        const result = await operation.operation();
        results.push(result);
        completedOperations.push(operation.name);
        
        console.log(`Operation ${operation.name} completed successfully`);
      }

      return { 
        success: true, 
        data: results as T,
        completedOperations 
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Transaction failed';
      console.error('Transaction failed:', errorMessage);

      // في حالة الفشل، تنفيذ عمليات التراجع
      await this.rollbackOperations(operations, completedOperations);

      return { 
        success: false, 
        error: errorMessage,
        completedOperations,
        failedOperation: operations[completedOperations.length]?.name
      };
    }
  }

  /**
   * تنفيذ عمليات التراجع
   */
  private static async rollbackOperations(
    operations: TransactionOperation[],
    completedOperations: string[]
  ): Promise<void> {
    
    console.log('Starting rollback operations...');
    
    // تنفيذ التراجع بالعكس
    for (let i = completedOperations.length - 1; i >= 0; i--) {
      const operationName = completedOperations[i];
      const operation = operations.find(op => op.name === operationName);
      
      if (operation?.rollback) {
        try {
          console.log(`Rolling back operation: ${operationName}`);
          await operation.rollback();
          console.log(`Rollback for ${operationName} completed`);
        } catch (rollbackError) {
          console.error(`Rollback failed for ${operationName}:`, rollbackError);
          // لا نتوقف عند فشل التراجع، نحاول باقي العمليات
        }
      }
    }
    
    console.log('Rollback operations completed');
  }

  /**
   * إنشاء طالب مع معاملة
   */
  static async createStudentWithTransaction(studentData: {
    name: string;
    student_id: string;
    grade: string;
    class: string;
    parent_id: string;
    route_stop_id?: string;
    tenant_id: string;
    profile: {
      date_of_birth: string;
      address: any;
      emergency_contacts: any[];
      medical_info?: any;
      photo_url?: string;
    };
  }): Promise<APIResponse<any>> {
    
    let createdUserId: string | null = null;
    let createdStudentId: string | null = null;
    
    const operations: TransactionOperation[] = [
      {
        name: 'create_user',
        operation: async () => {
          const { data: userResult, error: userError } = await supabase.functions.invoke(
            'create-user', 
            { 
              body: { 
                email: `${studentData.student_id}@${studentData.tenant_id}.student.local`,
                password: this.generateTemporaryPassword(),
                name: studentData.name,
                role: 'student',
                tenant_id: studentData.tenant_id
              } 
            }
          );
          
          if (userError) throw new Error(`User creation failed: ${userError.message}`);
          createdUserId = userResult.user.id;
          return userResult;
        },
        rollback: async () => {
          if (createdUserId) {
            await supabase.functions.invoke('delete-user', { 
              body: { userId: createdUserId } 
            });
          }
        }
      },
      
      {
        name: 'create_student',
        operation: async () => {
          const { data: studentResult, error: studentError } = await supabase
            .from('students')
            .insert({
              id: createdUserId,
              name: studentData.name,
              student_id: studentData.student_id,
              grade: studentData.grade,
              parent_id: studentData.parent_id,
              route_stop_id: studentData.route_stop_id || null,
              tenant_id: studentData.tenant_id,
              metadata: studentData.profile,
              is_active: true
            })
            .select()
            .single();
            
          if (studentError) throw new Error(`Student creation failed: ${studentError.message}`);
          createdStudentId = studentResult.id;
          return studentResult;
        },
        rollback: async () => {
          if (createdStudentId) {
            await supabase.from('students').delete().eq('id', createdStudentId);
          }
        }
      },

      {
        name: 'send_welcome_notification',
        operation: async () => {
          // إرسال إشعار ترحيب (اختياري)
          const { error: notificationError } = await supabase
            .from('notifications')
            .insert({
              user_id: studentData.parent_id,
              tenant_id: studentData.tenant_id,
              title: 'مرحباً بالطالب الجديد',
              message: `تم تسجيل الطالب ${studentData.name} بنجاح`,
              type: 'announcements',
              priority: 'normal'
            });
            
          if (notificationError) {
            console.warn('Failed to send welcome notification:', notificationError);
            // لا نفشل المعاملة بسبب الإشعار
          }
          
          return { notificationSent: !notificationError };
        }
        // لا نحتاج rollback للإشعار
      }
    ];
    
    const result = await this.executeTransaction<any>(operations);
    
    if (result.success) {
      return { 
        success: true, 
        data: result.data![1] // إرجاع بيانات الطالب
      };
    } else {
      return { 
        success: false, 
        error: { 
          code: 'TRANSACTION_FAILED', 
          message: result.error || 'Failed to create student',
          details: {
            completedOperations: result.completedOperations,
            failedOperation: result.failedOperation
          }
        } 
      };
    }
  }

  /**
   * تحديث بيانات الطالب مع معاملة
   */
  static async updateStudentWithTransaction(
    studentId: string,
    updateData: {
      name?: string;
      grade?: string;
      route_stop_id?: string;
      profile?: any;
    }
  ): Promise<APIResponse<any>> {
    
    let originalStudentData: any = null;
    let originalUserData: any = null;
    
    const operations: TransactionOperation[] = [
      {
        name: 'backup_original_data',
        operation: async () => {
          // نسخ احتياطي من البيانات الأصلية
          const { data: studentData, error: studentError } = await supabase
            .from('students')
            .select('*')
            .eq('id', studentId)
            .single();
            
          if (studentError) throw new Error(`Failed to fetch student data: ${studentError.message}`);
          originalStudentData = studentData;
          
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('id', studentId)
            .single();
            
          if (userError) throw new Error(`Failed to fetch user data: ${userError.message}`);
          originalUserData = userData;
          
          return { originalStudentData, originalUserData };
        }
      },

      {
        name: 'update_student',
        operation: async () => {
          const { data: updatedStudent, error: updateError } = await supabase
            .from('students')
            .update({
              name: updateData.name,
              grade: updateData.grade,
              route_stop_id: updateData.route_stop_id,
              metadata: updateData.profile,
              updated_at: new Date().toISOString()
            })
            .eq('id', studentId)
            .select()
            .single();
            
          if (updateError) throw new Error(`Student update failed: ${updateError.message}`);
          return updatedStudent;
        },
        rollback: async () => {
          if (originalStudentData) {
            await supabase
              .from('students')
              .update(originalStudentData)
              .eq('id', studentId);
          }
        }
      },

      {
        name: 'update_user',
        operation: async () => {
          if (updateData.name) {
            const { data: updatedUser, error: updateError } = await supabase
              .from('users')
              .update({
                name: updateData.name,
                updated_at: new Date().toISOString()
              })
              .eq('id', studentId)
              .select()
              .single();
              
            if (updateError) throw new Error(`User update failed: ${updateError.message}`);
            return updatedUser;
          }
          return null;
        },
        rollback: async () => {
          if (originalUserData && updateData.name) {
            await supabase
              .from('users')
              .update({
                name: originalUserData.name,
                updated_at: originalUserData.updated_at
              })
              .eq('id', studentId);
          }
        }
      }
    ];
    
    const result = await this.executeTransaction<any>(operations);
    
    if (result.success) {
      return { 
        success: true, 
        data: result.data![1] // إرجاع بيانات الطالب المحدثة
      };
    } else {
      return { 
        success: false, 
        error: { 
          code: 'UPDATE_TRANSACTION_FAILED', 
          message: result.error || 'Failed to update student',
          details: {
            completedOperations: result.completedOperations,
            failedOperation: result.failedOperation
          }
        } 
      };
    }
  }

  /**
   * حذف طالب مع معاملة
   */
  static async deleteStudentWithTransaction(studentId: string): Promise<APIResponse<{ message: string }>> {
    
    let studentData: any = null;
    let userData: any = null;
    let attendanceData: any[] = [];
    
    const operations: TransactionOperation[] = [
      {
        name: 'backup_student_data',
        operation: async () => {
          // نسخ احتياطي من جميع البيانات
          const { data: student, error: studentError } = await supabase
            .from('students')
            .select('*')
            .eq('id', studentId)
            .single();
            
          if (studentError) throw new Error(`Failed to fetch student: ${studentError.message}`);
          studentData = student;
          
          const { data: user, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('id', studentId)
            .single();
            
          if (userError) throw new Error(`Failed to fetch user: ${userError.message}`);
          userData = user;
          
          const { data: attendance, error: attendanceError } = await supabase
            .from('attendance')
            .select('*')
            .eq('student_id', studentId);
            
          if (!attendanceError) {
            attendanceData = attendance || [];
          }
          
          return { studentData, userData, attendanceData };
        }
      },

      {
        name: 'delete_attendance',
        operation: async () => {
          const { error: deleteError } = await supabase
            .from('attendance')
            .delete()
            .eq('student_id', studentId);
            
          if (deleteError) throw new Error(`Failed to delete attendance: ${deleteError.message}`);
          return { deletedAttendance: attendanceData.length };
        },
        rollback: async () => {
          if (attendanceData.length > 0) {
            await supabase.from('attendance').insert(attendanceData);
          }
        }
      },

      {
        name: 'delete_student',
        operation: async () => {
          const { error: deleteError } = await supabase
            .from('students')
            .delete()
            .eq('id', studentId);
            
          if (deleteError) throw new Error(`Failed to delete student: ${deleteError.message}`);
          return { deletedStudent: true };
        },
        rollback: async () => {
          if (studentData) {
            await supabase.from('students').insert(studentData);
          }
        }
      },

      {
        name: 'delete_user',
        operation: async () => {
          const { error: deleteError } = await supabase.functions.invoke('delete-user', {
            body: { userId: studentId }
          });
          
          if (deleteError) throw new Error(`Failed to delete user: ${deleteError.message}`);
          return { deletedUser: true };
        },
        rollback: async () => {
          if (userData) {
            // إعادة إنشاء المستخدم معقدة، نحتاج Edge Function خاص
            console.warn('User rollback not implemented - manual intervention required');
          }
        }
      }
    ];
    
    const result = await this.executeTransaction<any>(operations);
    
    if (result.success) {
      return { 
        success: true, 
        data: { message: 'Student deleted successfully' }
      };
    } else {
      return { 
        success: false, 
        error: { 
          code: 'DELETE_TRANSACTION_FAILED', 
          message: result.error || 'Failed to delete student',
          details: {
            completedOperations: result.completedOperations,
            failedOperation: result.failedOperation
          }
        } 
      };
    }
  }

  /**
   * توليد كلمة مرور مؤقتة
   */
  private static generateTemporaryPassword(): string {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }
}

export default TransactionManager;
