import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Star,
  TrendingUp,
  TrendingDown,
  Users,
  AlertTriangle,
  Download,
  FileText,
} from "lucide-react";
import { Button } from "../ui/Button";
import { getEvaluations, getComplaints } from "../../lib/api";
import { useAuth } from "../../contexts/AuthContext";
import type { Tables } from "../../lib/api";

interface UserSatisfactionReportProps {
  className?: string;
}

interface SatisfactionStats {
  totalEvaluations: number;
  averageRating: number;
  ratingDistribution: { [key: number]: number };
  totalComplaints: number;
  resolvedComplaints: number;
  pendingComplaints: number;
  satisfactionTrend: { date: string; rating: number }[];
}

export const UserSatisfactionReport: React.FC<UserSatisfactionReportProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [stats, setStats] = useState<SatisfactionStats>({
    totalEvaluations: 0,
    averageRating: 0,
    ratingDistribution: {},
    totalComplaints: 0,
    resolvedComplaints: 0,
    pendingComplaints: 0,
    satisfactionTrend: [],
  });
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
    targetType: "",
  });

  useEffect(() => {
    if (user?.tenant_id) {
      generateReport();
    }
  }, [user?.tenant_id, filters]);

  const generateReport = async () => {
    if (!user?.tenant_id) return;

    setLoading(true);
    try {
      const [evaluationsData, complaintsData] = await Promise.all([
        getEvaluations(user.tenant_id, filters.targetType || undefined),
        getComplaints(user.tenant_id),
      ]);

      // Filter by date range
      const startDate = new Date(filters.startDate);
      const endDate = new Date(filters.endDate);
      endDate.setHours(23, 59, 59, 999);

      const filteredEvaluations = (evaluationsData || []).filter(
        (evaluation) => {
          const evaluationDate = new Date(evaluation.created_at);
          return evaluationDate >= startDate && evaluationDate <= endDate;
        },
      );

      const filteredComplaints = (complaintsData || []).filter((complaint) => {
        const complaintDate = new Date(complaint.created_at);
        return complaintDate >= startDate && complaintDate <= endDate;
      });

      // Calculate statistics
      const totalEvaluations = filteredEvaluations.length;
      const averageRating =
        totalEvaluations > 0
          ? filteredEvaluations.reduce(
              (sum, evaluation) => sum + evaluation.rating,
              0,
            ) / totalEvaluations
          : 0;

      // Rating distribution
      const ratingDistribution: { [key: number]: number } = {};
      for (let i = 1; i <= 5; i++) {
        ratingDistribution[i] = filteredEvaluations.filter(
          (evaluation) => evaluation.rating === i,
        ).length;
      }

      // Complaints statistics
      const totalComplaints = filteredComplaints.length;
      const resolvedComplaints = filteredComplaints.filter(
        (c) => c.status === "resolved",
      ).length;
      const pendingComplaints = filteredComplaints.filter(
        (c) => c.status === "pending",
      ).length;

      // Satisfaction trend (weekly averages)
      const satisfactionTrend = calculateSatisfactionTrend(
        filteredEvaluations,
        startDate,
        endDate,
      );

      setStats({
        totalEvaluations,
        averageRating,
        ratingDistribution,
        totalComplaints,
        resolvedComplaints,
        pendingComplaints,
        satisfactionTrend,
      });
    } catch (error) {
      console.error("Error generating satisfaction report:", error);
    } finally {
      setLoading(false);
    }
  };

  const calculateSatisfactionTrend = (
    evaluations: Tables<"evaluations">[],
    startDate: Date,
    endDate: Date,
  ) => {
    const trend: { date: string; rating: number }[] = [];
    const current = new Date(startDate);

    while (current <= endDate) {
      const weekStart = new Date(current);
      const weekEnd = new Date(current);
      weekEnd.setDate(weekEnd.getDate() + 6);

      const weekEvaluations = evaluations.filter((evaluation) => {
        const evaluationDate = new Date(evaluation.created_at);
        return evaluationDate >= weekStart && evaluationDate <= weekEnd;
      });

      const weekAverage =
        weekEvaluations.length > 0
          ? weekEvaluations.reduce(
              (sum, evaluation) => sum + evaluation.rating,
              0,
            ) / weekEvaluations.length
          : 0;

      trend.push({
        date: weekStart.toISOString().split("T")[0],
        rating: weekAverage,
      });

      current.setDate(current.getDate() + 7);
    }

    return trend;
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            size={20}
            className={`${
              star <= Math.round(rating)
                ? "text-yellow-400 fill-current"
                : "text-gray-300 dark:text-gray-600"
            }`}
          />
        ))}
      </div>
    );
  };

  const getSatisfactionLevel = (rating: number) => {
    if (rating >= 4.5)
      return {
        label: t("evaluation.satisfaction.excellent"),
        color: "text-green-600",
      };
    if (rating >= 4.0)
      return {
        label: t("evaluation.satisfaction.good"),
        color: "text-blue-600",
      };
    if (rating >= 3.0)
      return {
        label: t("evaluation.satisfaction.average"),
        color: "text-yellow-600",
      };
    if (rating >= 2.0)
      return {
        label: t("evaluation.satisfaction.poor"),
        color: "text-orange-600",
      };
    return {
      label: t("evaluation.satisfaction.critical"),
      color: "text-red-600",
    };
  };

  const exportToCSV = () => {
    const headers = ["Metric", "Value"];

    const csvContent = [
      headers.join(","),
      `"Total Evaluations",${stats.totalEvaluations}`,
      `"Average Rating",${stats.averageRating.toFixed(2)}`,
      `"Total Complaints",${stats.totalComplaints}`,
      `"Resolved Complaints",${stats.resolvedComplaints}`,
      `"Pending Complaints",${stats.pendingComplaints}`,
      `"Resolution Rate",${stats.totalComplaints > 0 ? ((stats.resolvedComplaints / stats.totalComplaints) * 100).toFixed(1) : 0}%`,
      "",
      "Rating Distribution:",
      `"5 Stars",${stats.ratingDistribution[5] || 0}`,
      `"4 Stars",${stats.ratingDistribution[4] || 0}`,
      `"3 Stars",${stats.ratingDistribution[3] || 0}`,
      `"2 Stars",${stats.ratingDistribution[2] || 0}`,
      `"1 Star",${stats.ratingDistribution[1] || 0}`,
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `satisfaction_report_${filters.startDate}_to_${filters.endDate}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const satisfactionLevel = getSatisfactionLevel(stats.averageRating);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t("evaluation.satisfactionReport")}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t("evaluation.satisfactionReportDescription")}
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={exportToCSV}
            variant="outline"
            size="sm"
            leftIcon={<Download size={16} />}
            disabled={stats.totalEvaluations === 0}
          >
            {t("common.exportCSV")}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("common.startDate")}
            </label>
            <input
              type="date"
              value={filters.startDate}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, startDate: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("common.endDate")}
            </label>
            <input
              type="date"
              value={filters.endDate}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, endDate: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("evaluation.targetType")}
            </label>
            <select
              value={filters.targetType}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, targetType: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="">{t("common.all")}</option>
              <option value="driver">{t("evaluation.driver")}</option>
              <option value="service">{t("evaluation.service")}</option>
              <option value="route">{t("evaluation.route")}</option>
            </select>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
        </div>
      ) : (
        <>
          {/* Overall Satisfaction */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                {renderStars(stats.averageRating)}
              </div>
              <div className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
                {stats.averageRating.toFixed(1)}
              </div>
              <div
                className={`text-lg font-medium ${satisfactionLevel.color} mb-2`}
              >
                {satisfactionLevel.label}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {t("evaluation.basedOnEvaluations", {
                  count: stats.totalEvaluations,
                })}
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 dark:bg-blue-800/20 rounded-lg">
                  <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    {t("evaluation.totalEvaluations")}
                  </p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {stats.totalEvaluations}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 dark:bg-yellow-800/20 rounded-lg">
                  <Star className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    {t("evaluation.averageRating")}
                  </p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {stats.averageRating.toFixed(1)}/5
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 dark:bg-red-800/20 rounded-lg">
                  <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    {t("complaints.totalComplaints")}
                  </p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {stats.totalComplaints}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 dark:bg-green-800/20 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    {t("complaints.resolutionRate")}
                  </p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {stats.totalComplaints > 0
                      ? (
                          (stats.resolvedComplaints / stats.totalComplaints) *
                          100
                        ).toFixed(1)
                      : 0}
                    %
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Rating Distribution */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {t("evaluation.ratingDistribution")}
            </h3>
            <div className="space-y-3">
              {[5, 4, 3, 2, 1].map((rating) => {
                const count = stats.ratingDistribution[rating] || 0;
                const percentage =
                  stats.totalEvaluations > 0
                    ? (count / stats.totalEvaluations) * 100
                    : 0;

                return (
                  <div key={rating} className="flex items-center">
                    <div className="flex items-center w-16">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">
                        {rating}
                      </span>
                      <Star
                        size={16}
                        className="text-yellow-400 fill-current"
                      />
                    </div>
                    <div className="flex-1 mx-4">
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="w-16 text-right">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {count} ({percentage.toFixed(1)}%)
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Satisfaction Trend */}
          {stats.satisfactionTrend.length > 1 && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                {t("evaluation.satisfactionTrend")}
              </h3>
              <div className="h-64 flex items-end space-x-2">
                {stats.satisfactionTrend.map((point, index) => {
                  const height = (point.rating / 5) * 100;
                  return (
                    <div
                      key={index}
                      className="flex-1 flex flex-col items-center"
                    >
                      <div
                        className="w-full bg-primary-500 rounded-t transition-all duration-300"
                        style={{ height: `${height}%` }}
                        title={`${new Date(point.date).toLocaleDateString()}: ${point.rating.toFixed(1)}`}
                      ></div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-2 transform -rotate-45">
                        {new Date(point.date).toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default UserSatisfactionReport;
