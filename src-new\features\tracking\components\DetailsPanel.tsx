/**
 * Details Panel Component
 * مكون لوحة التفاصيل
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  X,
  Navigation,
  Users,
  Clock,
  MapPin,
  Route as RouteIcon,
  User,
  Activity,
  AlertTriangle,
  Phone,
  MessageCircle,
  Settings,
  Maximize2,
  TrendingUp,
  Gauge,
  Calendar,
} from 'lucide-react';
import { BusTrackingData } from './TrackingDashboard';

interface DetailsPanelProps {
  bus: BusTrackingData;
  onClose: () => void;
}

export const DetailsPanel: React.FC<DetailsPanelProps> = ({ bus, onClose }) => {
  const { t } = useTranslation();

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'stopped':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'emergency':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Navigation className="w-4 h-4 text-green-600" />;
      case 'stopped':
        return <MapPin className="w-4 h-4 text-gray-600" />;
      case 'maintenance':
        return <Settings className="w-4 h-4 text-yellow-600" />;
      case 'emergency':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <MapPin className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white shadow-2xl border-t border-gray-200 z-40 lg:relative lg:bottom-auto lg:left-auto lg:right-auto lg:shadow-lg lg:rounded-t-2xl">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            {getStatusIcon(bus.status)}
          </div>
          <div>
            <h3 className="text-lg font-bold text-gray-900">{bus.plateNumber}</h3>
            <p className="text-sm text-gray-600">{bus.driverName}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`px-3 py-1 text-sm font-medium rounded-full border ${getStatusColor(bus.status)}`}>
            {t(`tracking.status.${bus.status}`)}
          </span>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 max-h-96 overflow-y-auto lg:max-h-none">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Current Status */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold text-gray-900 uppercase tracking-wide">
              {t('tracking.currentStatus')}
            </h4>
            
            <div className="space-y-3">
              {/* Location */}
              {bus.currentLocation && (
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <MapPin className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {t('tracking.currentLocation')}
                    </p>
                    <p className="text-xs text-gray-600">
                      {bus.currentLocation.lat.toFixed(6)}, {bus.currentLocation.lng.toFixed(6)}
                    </p>
                    <p className="text-xs text-gray-500">
                      {t('tracking.accuracy')}: {bus.currentLocation.accuracy}m
                    </p>
                  </div>
                </div>
              )}

              {/* Speed */}
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <Gauge className="w-5 h-5 text-green-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {t('tracking.currentSpeed')}
                  </p>
                  <p className="text-lg font-bold text-green-600">
                    {bus.currentLocation?.speed || 0} km/h
                  </p>
                  <p className="text-xs text-gray-500">
                    {t('tracking.heading')}: {bus.currentLocation?.heading || 0}°
                  </p>
                </div>
              </div>

              {/* Students */}
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <Users className="w-5 h-5 text-purple-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {t('tracking.studentsOnBoard')}
                  </p>
                  <p className="text-lg font-bold text-purple-600">
                    {bus.studentsCount} / {bus.capacity}
                  </p>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                    <div
                      className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(bus.studentsCount / bus.capacity) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Route Information */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold text-gray-900 uppercase tracking-wide">
              {t('tracking.routeInformation')}
            </h4>
            
            <div className="space-y-3">
              {/* Route */}
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <RouteIcon className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {t('tracking.assignedRoute')}
                  </p>
                  <p className="text-lg font-bold text-blue-600">
                    {bus.route.name}
                  </p>
                  <div
                    className="w-4 h-4 rounded-full mt-1"
                    style={{ backgroundColor: bus.route.color }}
                  ></div>
                </div>
              </div>

              {/* Next Stop */}
              {bus.nextStop && (
                <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <Navigation className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-blue-900">
                      {t('tracking.nextStop')}
                    </p>
                    <p className="text-lg font-bold text-blue-700">
                      {bus.nextStop.name}
                    </p>
                    <p className="text-sm text-blue-600">
                      ETA: {bus.nextStop.eta}
                    </p>
                  </div>
                </div>
              )}

              {/* Route Stops */}
              {bus.route.stops.length > 0 && (
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm font-medium text-gray-900 mb-2">
                    {t('tracking.routeStops')}
                  </p>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {bus.route.stops.map((stop, index) => (
                      <div key={stop.id} className="flex items-center space-x-2">
                        <div className={`
                          w-3 h-3 rounded-full
                          ${stop.status === 'departed' ? 'bg-green-500' :
                            stop.status === 'arrived' ? 'bg-blue-500' : 'bg-gray-300'}
                        `}></div>
                        <span className="text-sm text-gray-700">{stop.name}</span>
                        <span className="text-xs text-gray-500">
                          ({stop.studentsCount} students)
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Alerts & Actions */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold text-gray-900 uppercase tracking-wide">
              {t('tracking.alertsAndActions')}
            </h4>
            
            <div className="space-y-3">
              {/* Last Update */}
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <Clock className="w-5 h-5 text-gray-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {t('tracking.lastUpdate')}
                  </p>
                  <p className="text-sm text-gray-600">
                    {formatDate(bus.lastUpdate)}
                  </p>
                  <p className="text-lg font-bold text-gray-700">
                    {formatTime(bus.lastUpdate)}
                  </p>
                </div>
              </div>

              {/* Alerts */}
              {bus.alerts.length > 0 ? (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-900">
                    {t('tracking.activeAlerts')}
                  </p>
                  {bus.alerts.map((alert, index) => (
                    <div
                      key={index}
                      className={`
                        p-3 rounded-lg border
                        ${alert.type === 'error' ? 'bg-red-50 border-red-200' :
                          alert.type === 'warning' ? 'bg-yellow-50 border-yellow-200' :
                          'bg-blue-50 border-blue-200'}
                      `}
                    >
                      <div className="flex items-start space-x-2">
                        <AlertTriangle className={`
                          w-4 h-4 mt-0.5
                          ${alert.type === 'error' ? 'text-red-600' :
                            alert.type === 'warning' ? 'text-yellow-600' :
                            'text-blue-600'}
                        `} />
                        <div>
                          <p className={`
                            text-sm font-medium
                            ${alert.type === 'error' ? 'text-red-900' :
                              alert.type === 'warning' ? 'text-yellow-900' :
                              'text-blue-900'}
                          `}>
                            {alert.message}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatTime(alert.timestamp)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <p className="text-sm text-green-700">
                      {t('tracking.noActiveAlerts')}
                    </p>
                  </div>
                </div>
              )}

              {/* Quick Actions */}
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-900">
                  {t('tracking.quickActions')}
                </p>
                <div className="grid grid-cols-2 gap-2">
                  <button className="flex items-center justify-center space-x-2 p-2 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                    <Phone className="w-4 h-4 text-blue-600" />
                    <span className="text-sm text-blue-700">{t('tracking.callDriver')}</span>
                  </button>
                  <button className="flex items-center justify-center space-x-2 p-2 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                    <MessageCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-green-700">{t('tracking.sendMessage')}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsPanel;
