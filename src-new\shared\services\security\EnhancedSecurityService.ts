/**
 * Enhanced Security Service
 * خدمة الأمان المحسنة - المرحلة الأولى
 * 
 * تتضمن:
 * - حماية من brute-force attacks
 * - تسجيل محاولات الدخول
 * - إدارة الجلسات المتقدمة
 * - نظام التحقق الثنائي (2FA)
 * - مراقبة العمليات الأمنية
 */

import { supabase } from '../../lib/supabase';
import { User } from '../../types';

// أنواع البيانات للأمان
export interface LoginAttempt {
  id: string;
  email: string;
  ip_address?: string;
  user_agent?: string;
  success: boolean;
  failure_reason?: string;
  location_data?: any;
  device_info?: any;
  attempted_at: string;
}

export interface SecurityAuditLog {
  id: string;
  user_id?: string;
  tenant_id?: string;
  action: string;
  resource_type?: string;
  resource_id?: string;
  old_values?: any;
  new_values?: any;
  ip_address?: string;
  user_agent?: string;
  risk_score: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata?: any;
  timestamp: string;
}

export interface ActiveSession {
  id: string;
  user_id: string;
  session_token: string;
  refresh_token?: string;
  ip_address?: string;
  user_agent?: string;
  device_info?: any;
  location_data?: any;
  is_active: boolean;
  last_activity: string;
  expires_at: string;
  created_at: string;
}

export interface User2FASettings {
  id: string;
  user_id: string;
  is_enabled: boolean;
  method: 'email' | 'sms' | 'totp';
  secret_key?: string;
  backup_codes?: string[];
  phone_number?: string;
  verified_at?: string;
  created_at: string;
  updated_at: string;
}

export interface BruteForceCheck {
  email_attempts: number;
  ip_attempts: number;
  email_blocked: boolean;
  ip_blocked: boolean;
  blocked: boolean;
  remaining_attempts: number;
  reset_time: string;
}

export interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  forbiddenPasswords: string[];
}

export class EnhancedSecurityService {
  private static instance: EnhancedSecurityService;
  
  // إعدادات الأمان الافتراضية
  private readonly DEFAULT_PASSWORD_POLICY: PasswordPolicy = {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    forbiddenPasswords: [
      'password', '123456', 'qwerty', 'admin', 'letmein',
      'welcome', 'monkey', 'dragon', 'password123', 'admin123'
    ]
  };

  private readonly MAX_LOGIN_ATTEMPTS = 5;
  private readonly LOCKOUT_DURATION = 15; // minutes
  private readonly SESSION_TIMEOUT = 30; // minutes

  public static getInstance(): EnhancedSecurityService {
    if (!EnhancedSecurityService.instance) {
      EnhancedSecurityService.instance = new EnhancedSecurityService();
    }
    return EnhancedSecurityService.instance;
  }

  /**
   * فحص محاولات تسجيل الدخول الفاشلة (Brute Force Protection)
   */
  async checkBruteForceAttempts(
    email: string, 
    ipAddress?: string
  ): Promise<BruteForceCheck> {
    try {
      const { data, error } = await supabase.rpc('check_failed_login_attempts', {
        p_email: email,
        p_ip_address: ipAddress,
        p_time_window: `${this.LOCKOUT_DURATION} minutes`,
        p_max_attempts: this.MAX_LOGIN_ATTEMPTS
      });

      if (error) {
        console.error('Error checking brute force attempts:', error);
        // في حالة الخطأ، نسمح بالمحاولة لتجنب منع المستخدمين الشرعيين
        return {
          email_attempts: 0,
          ip_attempts: 0,
          email_blocked: false,
          ip_blocked: false,
          blocked: false,
          remaining_attempts: this.MAX_LOGIN_ATTEMPTS,
          reset_time: new Date(Date.now() + this.LOCKOUT_DURATION * 60000).toISOString()
        };
      }

      return data as BruteForceCheck;
    } catch (error) {
      console.error('Exception in checkBruteForceAttempts:', error);
      throw new Error('فشل في فحص محاولات تسجيل الدخول');
    }
  }

  /**
   * تسجيل محاولة دخول
   */
  async logLoginAttempt(
    email: string,
    success: boolean,
    ipAddress?: string,
    userAgent?: string,
    failureReason?: string,
    deviceInfo?: any,
    locationData?: any
  ): Promise<string> {
    try {
      const { data, error } = await supabase.rpc('log_login_attempt', {
        p_email: email,
        p_success: success,
        p_ip_address: ipAddress,
        p_user_agent: userAgent,
        p_failure_reason: failureReason,
        p_device_info: deviceInfo || {},
        p_location_data: locationData || {}
      });

      if (error) {
        console.error('Error logging login attempt:', error);
        throw new Error('فشل في تسجيل محاولة الدخول');
      }

      return data as string;
    } catch (error) {
      console.error('Exception in logLoginAttempt:', error);
      throw new Error('فشل في تسجيل محاولة الدخول');
    }
  }

  /**
   * تسجيل عملية أمنية
   */
  async logSecurityEvent(
    userId: string,
    action: string,
    resourceType?: string,
    resourceId?: string,
    oldValues?: any,
    newValues?: any,
    ipAddress?: string,
    userAgent?: string,
    riskScore: number = 0,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'low',
    metadata?: any
  ): Promise<string> {
    try {
      const { data, error } = await supabase.rpc('log_security_event', {
        p_user_id: userId,
        p_action: action,
        p_resource_type: resourceType,
        p_resource_id: resourceId,
        p_old_values: oldValues || {},
        p_new_values: newValues || {},
        p_ip_address: ipAddress,
        p_user_agent: userAgent,
        p_risk_score: riskScore,
        p_severity: severity,
        p_metadata: metadata || {}
      });

      if (error) {
        console.error('Error logging security event:', error);
        throw new Error('فشل في تسجيل العملية الأمنية');
      }

      return data as string;
    } catch (error) {
      console.error('Exception in logSecurityEvent:', error);
      throw new Error('فشل في تسجيل العملية الأمنية');
    }
  }

  /**
   * التحقق من قوة كلمة المرور
   */
  validatePassword(password: string, policy?: Partial<PasswordPolicy>): {
    isValid: boolean;
    errors: string[];
    score: number;
  } {
    const activePolicy = { ...this.DEFAULT_PASSWORD_POLICY, ...policy };
    const errors: string[] = [];
    let score = 0;

    // فحص الطول الأدنى
    if (password.length < activePolicy.minLength) {
      errors.push(`كلمة المرور يجب أن تكون ${activePolicy.minLength} أحرف على الأقل`);
    } else {
      score += 20;
    }

    // فحص الأحرف الكبيرة
    if (activePolicy.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
    } else if (/[A-Z]/.test(password)) {
      score += 20;
    }

    // فحص الأحرف الصغيرة
    if (activePolicy.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل');
    } else if (/[a-z]/.test(password)) {
      score += 20;
    }

    // فحص الأرقام
    if (activePolicy.requireNumbers && !/\d/.test(password)) {
      errors.push('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
    } else if (/\d/.test(password)) {
      score += 20;
    }

    // فحص الرموز الخاصة
    if (activePolicy.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل');
    } else if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 20;
    }

    // فحص كلمات المرور المحظورة
    const lowerPassword = password.toLowerCase();
    if (activePolicy.forbiddenPasswords.some(forbidden => 
      lowerPassword.includes(forbidden.toLowerCase())
    )) {
      errors.push('كلمة المرور تحتوي على كلمات محظورة');
      score = Math.max(0, score - 40);
    }

    return {
      isValid: errors.length === 0,
      errors,
      score: Math.min(100, score)
    };
  }

  /**
   * الحصول على معلومات الجهاز
   */
  getDeviceInfo(): any {
    if (typeof window === 'undefined') return {};

    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth
      },
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * الحصول على عنوان IP (يتطلب خدمة خارجية)
   */
  async getClientIP(): Promise<string | null> {
    try {
      // يمكن استخدام خدمة مثل ipapi.co أو ipify
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch (error) {
      console.warn('Could not fetch client IP:', error);
      return null;
    }
  }

  /**
   * إنشاء جلسة جديدة
   */
  async createUserSession(
    userId: string,
    sessionToken: string,
    refreshToken?: string,
    ipAddress?: string,
    userAgent?: string,
    deviceInfo?: any,
    locationData?: any,
    expiresIn: number = 8 * 60 * 60 * 1000 // 8 hours in milliseconds
  ): Promise<string> {
    try {
      const { data, error } = await supabase.rpc('create_user_session', {
        p_user_id: userId,
        p_session_token: sessionToken,
        p_refresh_token: refreshToken,
        p_ip_address: ipAddress,
        p_user_agent: userAgent,
        p_device_info: deviceInfo || {},
        p_location_data: locationData || {},
        p_expires_in: `${Math.floor(expiresIn / 1000)} seconds`
      });

      if (error) {
        console.error('Error creating user session:', error);
        throw new Error('فشل في إنشاء الجلسة');
      }

      return data as string;
    } catch (error) {
      console.error('Exception in createUserSession:', error);
      throw new Error('فشل في إنشاء الجلسة');
    }
  }

  /**
   * الحصول على الجلسات النشطة للمستخدم
   */
  async getUserActiveSessions(userId: string): Promise<ActiveSession[]> {
    try {
      const { data, error } = await supabase
        .from('active_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('last_activity', { ascending: false });

      if (error) {
        console.error('Error fetching user sessions:', error);
        throw new Error('فشل في جلب الجلسات النشطة');
      }

      return data as ActiveSession[];
    } catch (error) {
      console.error('Exception in getUserActiveSessions:', error);
      throw new Error('فشل في جلب الجلسات النشطة');
    }
  }

  /**
   * إنهاء جلسة معينة
   */
  async terminateSession(sessionId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('active_sessions')
        .update({ is_active: false })
        .eq('id', sessionId)
        .eq('user_id', userId);

      if (error) {
        console.error('Error terminating session:', error);
        throw new Error('فشل في إنهاء الجلسة');
      }

      return true;
    } catch (error) {
      console.error('Exception in terminateSession:', error);
      throw new Error('فشل في إنهاء الجلسة');
    }
  }

  /**
   * إنهاء جميع الجلسات للمستخدم
   */
  async terminateAllUserSessions(userId: string, exceptSessionId?: string): Promise<boolean> {
    try {
      let query = supabase
        .from('active_sessions')
        .update({ is_active: false })
        .eq('user_id', userId);

      if (exceptSessionId) {
        query = query.neq('id', exceptSessionId);
      }

      const { error } = await query;

      if (error) {
        console.error('Error terminating all sessions:', error);
        throw new Error('فشل في إنهاء جميع الجلسات');
      }

      return true;
    } catch (error) {
      console.error('Exception in terminateAllUserSessions:', error);
      throw new Error('فشل في إنهاء جميع الجلسات');
    }
  }

  /**
   * تنظيف الجلسات المنتهية الصلاحية
   */
  async cleanupExpiredSessions(): Promise<boolean> {
    try {
      const { error } = await supabase.rpc('cleanup_expired_sessions');

      if (error) {
        console.error('Error cleaning up expired sessions:', error);
        throw new Error('فشل في تنظيف الجلسات المنتهية');
      }

      return true;
    } catch (error) {
      console.error('Exception in cleanupExpiredSessions:', error);
      throw new Error('فشل في تنظيف الجلسات المنتهية');
    }
  }

  /**
   * الحصول على إعدادات 2FA للمستخدم
   */
  async get2FASettings(userId: string): Promise<User2FASettings[]> {
    try {
      const { data, error } = await supabase
        .from('user_2fa_settings')
        .select('*')
        .eq('user_id', userId);

      if (error) {
        console.error('Error fetching 2FA settings:', error);
        throw new Error('فشل في جلب إعدادات التحقق الثنائي');
      }

      return data as User2FASettings[];
    } catch (error) {
      console.error('Exception in get2FASettings:', error);
      throw new Error('فشل في جلب إعدادات التحقق الثنائي');
    }
  }

  /**
   * تفعيل 2FA للمستخدم
   */
  async enable2FA(
    userId: string,
    method: 'email' | 'sms' | 'totp',
    phoneNumber?: string
  ): Promise<User2FASettings> {
    try {
      const settingsData: any = {
        user_id: userId,
        is_enabled: true,
        method: method,
        verified_at: new Date().toISOString()
      };

      if (method === 'sms' && phoneNumber) {
        settingsData.phone_number = phoneNumber;
      }

      if (method === 'totp') {
        // إنشاء مفتاح سري للـ TOTP
        settingsData.secret_key = this.generateTOTPSecret();
        settingsData.backup_codes = this.generateBackupCodes();
      }

      const { data, error } = await supabase
        .from('user_2fa_settings')
        .upsert(settingsData, {
          onConflict: 'user_id,method',
          ignoreDuplicates: false
        })
        .select()
        .single();

      if (error) {
        console.error('Error enabling 2FA:', error);
        throw new Error('فشل في تفعيل التحقق الثنائي');
      }

      return data as User2FASettings;
    } catch (error) {
      console.error('Exception in enable2FA:', error);
      throw new Error('فشل في تفعيل التحقق الثنائي');
    }
  }

  /**
   * إلغاء تفعيل 2FA للمستخدم
   */
  async disable2FA(userId: string, method: 'email' | 'sms' | 'totp'): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_2fa_settings')
        .update({ is_enabled: false })
        .eq('user_id', userId)
        .eq('method', method);

      if (error) {
        console.error('Error disabling 2FA:', error);
        throw new Error('فشل في إلغاء تفعيل التحقق الثنائي');
      }

      return true;
    } catch (error) {
      console.error('Exception in disable2FA:', error);
      throw new Error('فشل في إلغاء تفعيل التحقق الثنائي');
    }
  }

  /**
   * إنشاء مفتاح سري للـ TOTP
   */
  private generateTOTPSecret(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    let secret = '';
    for (let i = 0; i < 32; i++) {
      secret += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return secret;
  }

  /**
   * إنشاء رموز احتياطية
   */
  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();
      codes.push(code);
    }
    return codes;
  }

  /**
   * حساب درجة المخاطر للعملية
   */
  calculateRiskScore(
    action: string,
    userRole: string,
    resourceType?: string,
    timeOfDay?: number,
    ipAddress?: string,
    deviceInfo?: any
  ): number {
    let riskScore = 0;

    // المخاطر حسب نوع العملية
    const actionRisks: { [key: string]: number } = {
      'login': 10,
      'logout': 5,
      'password_change': 30,
      'user_create': 40,
      'user_delete': 60,
      'permission_change': 70,
      'data_export': 50,
      'system_config': 80
    };

    riskScore += actionRisks[action] || 20;

    // المخاطر حسب الدور
    const roleRisks: { [key: string]: number } = {
      'admin': 30,
      'school_manager': 20,
      'supervisor': 10,
      'driver': 5,
      'parent': 5,
      'student': 0
    };

    riskScore += roleRisks[userRole] || 15;

    // المخاطر حسب وقت العملية (خارج ساعات العمل)
    if (timeOfDay && (timeOfDay < 6 || timeOfDay > 22)) {
      riskScore += 20;
    }

    // المخاطر حسب نوع المورد
    const resourceRisks: { [key: string]: number } = {
      'users': 25,
      'tenants': 30,
      'permissions': 35,
      'system_settings': 40
    };

    if (resourceType) {
      riskScore += resourceRisks[resourceType] || 10;
    }

    return Math.min(100, Math.max(0, riskScore));
  }
}
