/**
 * نظام النشر السريع للهجرات
 * Quick Migration Deployment System
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 نظام النشر السريع للهجرات\n');

class QuickDeployer {
  constructor() {
    this.migrations = [];
    this.deploymentSteps = [];
    this.migrationsDir = path.join(process.cwd(), 'supabase', 'migrations');
  }

  /**
   * بدء النشر السريع
   */
  async startQuickDeployment() {
    console.log('🚀 بدء النشر السريع للهجرات...\n');

    try {
      // تحضير ملفات الهجرة
      await this.prepareMigrationFiles();
      
      // إنشاء سكريبت SQL موحد
      await this.createUnifiedSQLScript();
      
      // إنشاء سكريبت JavaScript للتطبيق
      await this.createJavaScriptDeployment();
      
      // إنشاء دليل النشر
      await this.createDeploymentGuide();
      
      // إنشاء بيانات اختبار
      await this.createTestData();
      
      console.log('\n✅ تم إكمال تحضير النشر السريع بنجاح!');
      this.showDeploymentSummary();
      
    } catch (error) {
      console.error('❌ خطأ في النشر السريع:', error);
    }
  }

  /**
   * تحضير ملفات الهجرة
   */
  async prepareMigrationFiles() {
    console.log('📦 تحضير ملفات الهجرة...');

    if (!fs.existsSync(this.migrationsDir)) {
      throw new Error('مجلد الهجرات غير موجود');
    }

    const files = fs.readdirSync(this.migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .filter(file => file.startsWith('2025-06-02')) // الهجرات الجديدة فقط
      .sort();

    for (const file of files) {
      const filePath = path.join(this.migrationsDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      this.migrations.push({
        name: file,
        path: filePath,
        content: content,
        order: this.getFileOrder(file)
      });
    }

    console.log(`  ✅ تم تحضير ${this.migrations.length} ملف هجرة`);
    
    this.migrations.forEach((migration, index) => {
      console.log(`    ${index + 1}. ${migration.name}`);
    });
  }

  /**
   * الحصول على ترتيب الملف
   */
  getFileOrder(filename) {
    if (filename.includes('000_create_base_tables')) return 1;
    if (filename.includes('001_create_roles')) return 2;
    if (filename.includes('002_create_security_functions')) return 3;
    if (filename.includes('003_create_rls_policies')) return 4;
    if (filename.includes('004_enable_rls')) return 5;
    return 999;
  }

  /**
   * إنشاء سكريبت SQL موحد
   */
  async createUnifiedSQLScript() {
    console.log('📄 إنشاء سكريبت SQL موحد...');

    let unifiedSQL = `-- نظام إدارة الحافلات المدرسية - هجرات RLS
-- School Bus Management System - RLS Migrations
-- تاريخ الإنشاء: ${new Date().toISOString()}

-- تحذير: يجب تطبيق هذا السكريبت على قاعدة بيانات فارغة
-- Warning: This script should be applied to an empty database

BEGIN;

`;

    // إضافة كل هجرة مع تعليقات
    for (const migration of this.migrations.sort((a, b) => a.order - b.order)) {
      unifiedSQL += `
-- ============================================================
-- ${migration.name}
-- ============================================================

${migration.content}

`;
    }

    unifiedSQL += `
-- ============================================================
-- إنشاء بيانات اختبار أساسية
-- Create Basic Test Data
-- ============================================================

-- إدراج مستأجر اختبار
INSERT INTO tenants (id, name, slug, settings) VALUES 
(
  'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  'مدرسة الأمل الابتدائية',
  'al-amal-primary',
  '{"timezone": "Asia/Riyadh", "language": "ar"}'
) ON CONFLICT (slug) DO NOTHING;

-- إدراج صلاحيات إضافية إذا لزم الأمر
INSERT INTO role_permissions (role, permission, resource) VALUES
('tenant_admin', 'manage', 'settings'),
('school_manager', 'read', 'analytics'),
('driver', 'update', 'location')
ON CONFLICT (role, permission, resource) DO NOTHING;

COMMIT;

-- ============================================================
-- التحقق من التطبيق
-- Verification Queries
-- ============================================================

-- فحص الجداول
SELECT 'Tables Created' as status, count(*) as count 
FROM information_schema.tables 
WHERE table_schema = 'public';

-- فحص RLS
SELECT 'RLS Enabled' as status, count(*) as count 
FROM pg_tables 
WHERE schemaname = 'public' AND rowsecurity = true;

-- فحص السياسات
SELECT 'Policies Created' as status, count(*) as count 
FROM pg_policies 
WHERE schemaname = 'public';

-- فحص الدوال
SELECT 'Functions Created' as status, count(*) as count 
FROM information_schema.routines 
WHERE routine_schema = 'public' AND routine_type = 'FUNCTION';

-- عرض رسالة النجاح
SELECT '🎉 تم تطبيق جميع الهجرات بنجاح!' as message;
`;

    const deployDir = path.join(process.cwd(), 'deployment');
    if (!fs.existsSync(deployDir)) {
      fs.mkdirSync(deployDir, { recursive: true });
    }

    const sqlPath = path.join(deployDir, 'complete-migration.sql');
    fs.writeFileSync(sqlPath, unifiedSQL);

    console.log(`  ✅ تم إنشاء السكريبت الموحد: ${sqlPath}`);
  }

  /**
   * إنشاء سكريبت JavaScript للتطبيق
   */
  async createJavaScriptDeployment() {
    console.log('⚙️ إنشاء سكريبت JavaScript للتطبيق...');

    const jsDeployment = `/**
 * سكريبت تطبيق الهجرات عبر JavaScript
 * JavaScript Migration Deployment Script
 */

import { createClient } from '@supabase/supabase-js'

// إعداد Supabase
const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('متغيرات البيئة مطلوبة: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

/**
 * تطبيق الهجرات
 */
async function deployMigrations() {
  console.log('🚀 بدء تطبيق الهجرات...')
  
  try {
    // قراءة السكريبت الموحد
    const fs = await import('fs')
    const path = await import('path')
    
    const sqlPath = path.join(process.cwd(), 'deployment', 'complete-migration.sql')
    const sqlContent = fs.readFileSync(sqlPath, 'utf8')
    
    // تقسيم السكريبت إلى أجزاء
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    console.log(\`📊 سيتم تطبيق \${statements.length} استعلام\`)
    
    // تطبيق كل استعلام
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      
      if (statement.includes('SELECT') && statement.includes('status')) {
        // استعلامات التحقق
        const { data, error } = await supabase.rpc('exec_sql', {
          sql: statement + ';'
        })
        
        if (error) {
          console.warn(\`⚠️ تحذير في التحقق: \${error.message}\`)
        } else {
          console.log(\`✅ التحقق: \${JSON.stringify(data)}\`)
        }
      } else {
        // استعلامات التطبيق
        const { error } = await supabase.rpc('exec_sql', {
          sql: statement + ';'
        })
        
        if (error) {
          console.error(\`❌ خطأ في الاستعلام \${i + 1}: \${error.message}\`)
          throw error
        } else {
          console.log(\`✅ تم تطبيق الاستعلام \${i + 1}\`)
        }
      }
    }
    
    console.log('🎉 تم تطبيق جميع الهجرات بنجاح!')
    
    // اختبار النظام
    await testSystem()
    
  } catch (error) {
    console.error('💥 خطأ في تطبيق الهجرات:', error)
    process.exit(1)
  }
}

/**
 * اختبار النظام
 */
async function testSystem() {
  console.log('🧪 اختبار النظام...')
  
  try {
    // اختبار الجداول
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
    
    if (tablesError) {
      console.warn('⚠️ لا يمكن اختبار الجداول:', tablesError.message)
    } else {
      console.log(\`✅ تم إنشاء \${tables?.length || 0} جدول\`)
    }
    
    // اختبار المستأجرين
    const { data: tenants, error: tenantsError } = await supabase
      .from('tenants')
      .select('*')
      .limit(1)
    
    if (tenantsError) {
      console.warn('⚠️ لا يمكن اختبار المستأجرين:', tenantsError.message)
    } else {
      console.log(\`✅ تم العثور على \${tenants?.length || 0} مستأجر اختبار\`)
    }
    
    // اختبار الدوال
    const { data: functions, error: functionsError } = await supabase
      .rpc('get_current_tenant_id')
    
    if (functionsError) {
      console.warn('⚠️ الدوال الأمنية تحتاج مستخدم مسجل:', functionsError.message)
    } else {
      console.log('✅ الدوال الأمنية تعمل')
    }
    
    console.log('🎯 اختبار النظام مكتمل')
    
  } catch (error) {
    console.warn('⚠️ بعض الاختبارات فشلت:', error.message)
  }
}

/**
 * تشغيل النشر
 */
if (import.meta.url === \`file://\${process.argv[1]}\`) {
  deployMigrations()
}

export { deployMigrations, testSystem }
`;

    const deployDir = path.join(process.cwd(), 'deployment');
    const jsPath = path.join(deployDir, 'deploy.mjs');
    fs.writeFileSync(jsPath, jsDeployment);

    console.log(`  ✅ تم إنشاء سكريبت JavaScript: ${jsPath}`);
  }

  /**
   * إنشاء دليل النشر
   */
  async createDeploymentGuide() {
    console.log('📖 إنشاء دليل النشر...');

    const guide = `# 🚀 دليل النشر السريع

## الطرق المتاحة للنشر

### 1. النشر عبر Supabase Dashboard (الأسهل)

1. اذهب إلى [app.supabase.com](https://app.supabase.com)
2. اختر مشروعك
3. اذهب إلى SQL Editor
4. انسخ محتوى \`deployment/complete-migration.sql\`
5. الصق المحتوى واضغط Run

### 2. النشر عبر JavaScript

\`\`\`bash
# تأكد من إعداد متغيرات البيئة
export SUPABASE_URL="your_supabase_url"
export SUPABASE_SERVICE_ROLE_KEY="your_service_role_key"

# تشغيل النشر
node deployment/deploy.mjs
\`\`\`

### 3. النشر عبر Supabase CLI

\`\`\`bash
# تثبيت CLI
npm install -g supabase

# تسجيل الدخول
supabase login

# ربط المشروع
supabase link --project-ref your-project-ref

# تطبيق الهجرات
supabase db push
\`\`\`

## ✅ التحقق من النشر

بعد النشر، تحقق من:

1. **الجداول**: يجب أن تجد ${this.migrations.length} جدول جديد
2. **RLS**: يجب أن يكون مُفعل على جميع الجداول
3. **السياسات**: يجب أن تجد 32+ سياسة RLS
4. **الدوال**: يجب أن تجد 8+ دالة أمنية

## 🧪 اختبار سريع

\`\`\`sql
-- فحص الجداول
SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';

-- فحص RLS
SELECT count(*) FROM pg_tables WHERE schemaname = 'public' AND rowsecurity = true;

-- فحص السياسات
SELECT count(*) FROM pg_policies WHERE schemaname = 'public';
\`\`\`

## 📞 في حالة المشاكل

1. تأكد من صحة متغيرات البيئة
2. تأكد من صلاحيات Service Role Key
3. راجع سجلات Supabase للأخطاء
4. راجع \`docs/migration-guide.md\` للتفاصيل

---
**🎉 مبروك! نظام RLS جاهز للاستخدام!**
`;

    const deployDir = path.join(process.cwd(), 'deployment');
    const guidePath = path.join(deployDir, 'README.md');
    fs.writeFileSync(guidePath, guide);

    console.log(`  ✅ تم إنشاء دليل النشر: ${guidePath}`);
  }

  /**
   * إنشاء بيانات اختبار
   */
  async createTestData() {
    console.log('🧪 إنشاء بيانات اختبار...');

    const testDataSQL = `-- بيانات اختبار للنظام
-- Test Data for the System

-- مستأجرين إضافيين
INSERT INTO tenants (id, name, slug, settings) VALUES 
(
  'b1ffbc99-9c0b-4ef8-bb6d-6bb9bd380a22',
  'مدرسة النور الثانوية',
  'al-noor-secondary',
  '{"timezone": "Asia/Riyadh", "language": "ar"}'
),
(
  'c2ffbc99-9c0b-4ef8-bb6d-6bb9bd380a33',
  'مدرسة الفجر المتوسطة',
  'al-fajr-middle',
  '{"timezone": "Asia/Riyadh", "language": "ar"}'
) ON CONFLICT (slug) DO NOTHING;

-- حافلات اختبار
INSERT INTO buses (tenant_id, bus_number, license_plate, capacity, model, year, color, status) VALUES 
(
  'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  'BUS-001',
  'ABC-1234',
  45,
  'Mercedes Sprinter',
  2023,
  'أصفر',
  'active'
),
(
  'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  'BUS-002',
  'DEF-5678',
  35,
  'Toyota Coaster',
  2022,
  'أزرق',
  'active'
) ON CONFLICT (tenant_id, bus_number) DO NOTHING;

-- مسارات اختبار
INSERT INTO routes (tenant_id, name, description, start_location, end_location, estimated_duration, distance_km) VALUES 
(
  'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  'مسار الشمال',
  'مسار يخدم الأحياء الشمالية',
  'المدرسة',
  'حي الملك فهد',
  45,
  12.5
),
(
  'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  'مسار الجنوب',
  'مسار يخدم الأحياء الجنوبية',
  'المدرسة',
  'حي الملك عبدالله',
  35,
  8.3
);

-- إشعارات اختبار
INSERT INTO notifications (tenant_id, title, message, type, priority) VALUES 
(
  'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  'مرحباً بكم في النظام',
  'تم تفعيل نظام إدارة الحافلات المدرسية بنجاح',
  'success',
  'normal'
),
(
  'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  'تذكير مهم',
  'يرجى تحديث بيانات الطلاب قبل بداية العام الدراسي',
  'warning',
  'high'
);
`;

    const deployDir = path.join(process.cwd(), 'deployment');
    const testDataPath = path.join(deployDir, 'test-data.sql');
    fs.writeFileSync(testDataPath, testDataSQL);

    console.log(`  ✅ تم إنشاء بيانات الاختبار: ${testDataPath}`);
  }

  /**
   * عرض ملخص النشر
   */
  showDeploymentSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('🚀 ملخص النشر السريع');
    console.log('='.repeat(60));
    console.log(`📦 ملفات الهجرة: ${this.migrations.length}`);
    console.log('📄 الملفات المنشأة:');
    console.log('  • deployment/complete-migration.sql - السكريبت الموحد');
    console.log('  • deployment/deploy.mjs - سكريبت JavaScript');
    console.log('  • deployment/README.md - دليل النشر');
    console.log('  • deployment/test-data.sql - بيانات اختبار');
    
    console.log('\n🎯 الخطوات التالية:');
    console.log('1. راجع ملف deployment/README.md');
    console.log('2. اختر طريقة النشر المناسبة');
    console.log('3. طبق الهجرات على Supabase');
    console.log('4. اختبر النظام');
    
    console.log('\n⚡ النشر السريع:');
    console.log('انسخ محتوى deployment/complete-migration.sql');
    console.log('والصقه في Supabase SQL Editor');
    
    console.log('='.repeat(60));
  }
}

/**
 * الدالة الرئيسية
 */
async function main() {
  try {
    const deployer = new QuickDeployer();
    await deployer.startQuickDeployment();
  } catch (error) {
    console.error('💥 خطأ في النشر السريع:', error);
    process.exit(1);
  }
}

// تشغيل النظام
main();
