/**
 * Profile Service
 * Handles user profile operations with proper error handling
 */

import { supabase } from '../lib/supabase';
import { User } from '../api/types';

export interface ProfileUpdateData {
  name?: string;
  phone?: string;
  email?: string;
}

export interface PasswordUpdateData {
  currentPassword: string;
  newPassword: string;
}

export class ProfileService {
  /**
   * Update user profile information
   */
  static async updateProfile(userId: string, data: ProfileUpdateData): Promise<{
    success: boolean;
    data?: User;
    error?: string;
  }> {
    try {
      console.log('Updating profile for user:', userId, 'with data:', data);

      // Try RPC function first
      try {
        const { data: result, error: rpcError } = await supabase.rpc('update_user_profile', {
          user_id: userId,
          user_name: data.name,
          user_phone: data.phone
        });

        if (!rpcError) {
          // Get updated user data
          const { data: updatedUser, error: fetchError } = await supabase
            .from('users')
            .select('id, name, email, phone, updated_at')
            .eq('id', userId)
            .single();

          if (!fetchError && updatedUser) {
            return {
              success: true,
              data: updatedUser
            };
          }
        }
      } catch (rpcError) {
        console.log('RPC method failed, trying direct update...');
      }

      // Fallback to direct update with minimal fields
      const updateData: any = {
        updated_at: new Date().toISOString(),
      };

      if (data.name) {
        updateData.name = data.name;
      }

      if (data.phone !== undefined) {
        updateData.phone = data.phone;
      }

      console.log('Attempting direct update with data:', updateData);

      const { data: updatedUser, error: updateError } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', userId)
        .select('id, name, email, phone, updated_at')
        .single();

      if (updateError) {
        console.error('Error updating profile directly:', updateError);
        return {
          success: false,
          error: `Update failed: ${updateError.message}`
        };
      }

      console.log('Profile updated successfully:', updatedUser);

      return {
        success: true,
        data: updatedUser
      };
    } catch (error) {
      console.error('Unexpected error updating profile:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred'
      };
    }
  }

  /**
   * Update user password
   */
  static async updatePassword(data: PasswordUpdateData): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // Update password using Supabase Auth
      const { error } = await supabase.auth.updateUser({
        password: data.newPassword
      });

      if (error) {
        console.error('Error updating password:', error);
        return {
          success: false,
          error: error.message || 'Failed to update password'
        };
      }

      return {
        success: true
      };
    } catch (error) {
      console.error('Unexpected error updating password:', error);
      return {
        success: false,
        error: 'An unexpected error occurred'
      };
    }
  }

  /**
   * Update user email
   */
  static async updateEmail(newEmail: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // Update email using Supabase Auth
      const { error } = await supabase.auth.updateUser({
        email: newEmail
      });

      if (error) {
        console.error('Error updating email:', error);
        return {
          success: false,
          error: error.message || 'Failed to update email'
        };
      }

      return {
        success: true
      };
    } catch (error) {
      console.error('Unexpected error updating email:', error);
      return {
        success: false,
        error: 'An unexpected error occurred'
      };
    }
  }

  /**
   * Get user profile
   */
  static async getProfile(userId: string): Promise<{
    success: boolean;
    data?: User;
    error?: string;
  }> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        return {
          success: false,
          error: error.message || 'Failed to fetch profile'
        };
      }

      return {
        success: true,
        data
      };
    } catch (error) {
      console.error('Unexpected error fetching profile:', error);
      return {
        success: false,
        error: 'An unexpected error occurred'
      };
    }
  }
}

export default ProfileService;
