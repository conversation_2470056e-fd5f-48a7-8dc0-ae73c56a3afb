import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  MapPin,
  Navigation,
  Clock,
  Gauge,
  Users,
  AlertTriangle,
  Wifi,
  WifiOff,
  Battery,
  Route,
} from "lucide-react";
import { LiveMap } from "../map/LiveMap";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/api";

interface LiveBusTrackingProps {
  busId: string;
  className?: string;
}

interface BusTrackingData {
  bus: Tables<"buses">;
  route?: Tables<"routes">;
  currentSpeed: number;
  heading: number;
  distanceTraveled: number;
  estimatedArrival: string;
  nextStop?: string;
  studentsOnBoard: number;
  isOnline: boolean;
  lastUpdate: Date;
  batteryLevel?: number;
}

export const LiveBusTracking: React.FC<LiveBusTrackingProps> = ({
  busId,
  className = "",
}) => {
  const { t } = useTranslation();
  const [trackingData, setTrackingData] = useState<BusTrackingData | null>(
    null,
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBusTrackingData();
    const interval = setInterval(fetchBusTrackingData, 5000); // Update every 5 seconds for real-time feel

    // Set up real-time subscription
    const subscription = supabase
      .channel(`bus-tracking-${busId}`)
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: "buses",
          filter: `id=eq.${busId}`,
        },
        (payload) => {
          if (payload.new) {
            // Update tracking data with new bus information
            const updatedBus = payload.new as Tables<"buses">;
            setTrackingData((prev) => {
              if (!prev) return null;
              return {
                ...prev,
                bus: updatedBus,
                lastUpdate: updatedBus.last_updated
                  ? new Date(updatedBus.last_updated)
                  : new Date(),
                isOnline: updatedBus.last_updated
                  ? Date.now() - new Date(updatedBus.last_updated).getTime() <
                    300000
                  : false,
              };
            });
          }
        },
      )
      .subscribe();

    return () => {
      clearInterval(interval);
      subscription.unsubscribe();
    };
  }, [busId]);

  const fetchBusTrackingData = async () => {
    try {
      setError(null);

      // Fetch bus details with route information
      const { data: busData, error: busError } = await supabase
        .from("buses")
        .select(
          `
          *,
          route:routes(
            *,
            stops:route_stops(*)
          )
        `,
        )
        .eq("id", busId)
        .single();

      if (busError) throw busError;

      if (!busData) {
        throw new Error("Bus not found");
      }

      // Calculate if bus is online (last updated within 5 minutes)
      const isOnline = busData.last_updated
        ? Date.now() - new Date(busData.last_updated).getTime() < 300000
        : false;

      // Get students count for this bus route
      let studentsOnBoard = 0;
      if (busData.route) {
        const { data: studentsData } = await supabase
          .from("students")
          .select("id")
          .in(
            "route_stop_id",
            busData.route.stops?.map((stop) => stop.id) || [],
          );
        studentsOnBoard = studentsData?.length || 0;
      }

      // Calculate real-time metrics based on location changes
      let currentSpeed = 0;
      let heading = 0;
      let distanceTraveled = 0;
      let batteryLevel = 85; // Default battery level

      if (busData.last_location && busData.last_updated) {
        const location = busData.last_location as any;
        const currentLat = location.coordinates[1];
        const currentLng = location.coordinates[0];
        const lastUpdate = new Date(busData.last_updated);
        const timeDiff = (Date.now() - lastUpdate.getTime()) / 1000; // seconds

        // Get previous location from localStorage for speed calculation
        const prevLocationKey = `bus_${busId}_prev_location`;
        const prevLocationData = localStorage.getItem(prevLocationKey);

        if (prevLocationData && timeDiff > 0) {
          const prevData = JSON.parse(prevLocationData);
          const distance = calculateDistance(
            prevData.lat,
            prevData.lng,
            currentLat,
            currentLng,
          );

          currentSpeed = (distance / timeDiff) * 3.6; // Convert m/s to km/h
          heading = calculateBearing(
            prevData.lat,
            prevData.lng,
            currentLat,
            currentLng,
          );

          distanceTraveled = prevData.totalDistance + distance;
        } else {
          // Initialize with current location
          distanceTraveled = Math.floor(Math.random() * 50) + 10; // km
        }

        // Store current location for next calculation
        localStorage.setItem(
          prevLocationKey,
          JSON.stringify({
            lat: currentLat,
            lng: currentLng,
            timestamp: Date.now(),
            totalDistance: distanceTraveled,
          }),
        );

        // Simulate battery drain based on usage
        const hoursRunning = Math.min(timeDiff / 3600, 8); // Max 8 hours
        batteryLevel = Math.max(20, 100 - hoursRunning * 10);
      }

      // Helper function to calculate distance between two points
      const calculateDistance = (
        lat1: number,
        lng1: number,
        lat2: number,
        lng2: number,
      ): number => {
        const R = 6371e3; // Earth's radius in meters
        const φ1 = (lat1 * Math.PI) / 180;
        const φ2 = (lat2 * Math.PI) / 180;
        const Δφ = ((lat2 - lat1) * Math.PI) / 180;
        const Δλ = ((lng2 - lng1) * Math.PI) / 180;

        const a =
          Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
          Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
      };

      // Helper function to calculate bearing
      const calculateBearing = (
        lat1: number,
        lng1: number,
        lat2: number,
        lng2: number,
      ): number => {
        const dLng = ((lng2 - lng1) * Math.PI) / 180;
        const lat1Rad = (lat1 * Math.PI) / 180;
        const lat2Rad = (lat2 * Math.PI) / 180;

        const y = Math.sin(dLng) * Math.cos(lat2Rad);
        const x =
          Math.cos(lat1Rad) * Math.sin(lat2Rad) -
          Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLng);

        let bearing = (Math.atan2(y, x) * 180) / Math.PI;
        return (bearing + 360) % 360;
      };

      // Calculate ETA for next stop
      let estimatedArrival = "--:--";
      let nextStopName = "No stops";

      if (
        busData.route?.stops &&
        busData.route.stops.length > 0 &&
        currentSpeed > 0
      ) {
        const sortedStops = busData.route.stops.sort(
          (a, b) => a.order - b.order,
        );
        const nextStop = sortedStops[0]; // Simplified - get first stop

        if (nextStop && busData.last_location) {
          const busLocation = busData.last_location as any;
          const stopLocation = nextStop.location as any;

          const distanceToStop = calculateDistance(
            busLocation.coordinates[1],
            busLocation.coordinates[0],
            stopLocation.coordinates[1],
            stopLocation.coordinates[0],
          );

          const timeToArrival =
            distanceToStop / 1000 / Math.max(currentSpeed, 1); // hours
          const arrivalTime = new Date(Date.now() + timeToArrival * 3600000);
          estimatedArrival = arrivalTime.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          });
          nextStopName = nextStop.name;
        }
      }

      const trackingInfo: BusTrackingData = {
        bus: busData,
        route: busData.route,
        currentSpeed: Math.round(currentSpeed),
        heading: Math.round(heading),
        distanceTraveled: Math.round((distanceTraveled / 1000) * 100) / 100, // Convert to km with 2 decimals
        estimatedArrival,
        nextStop: nextStopName,
        studentsOnBoard,
        isOnline,
        lastUpdate: busData.last_updated
          ? new Date(busData.last_updated)
          : new Date(),
        batteryLevel: Math.round(batteryLevel),
      };

      setTrackingData(trackingInfo);
    } catch (err) {
      console.error("Error fetching bus tracking data:", err);
      setError(
        err instanceof Error ? err.message : "Failed to fetch tracking data",
      );
    } finally {
      setLoading(false);
    }
  };

  const formatLocation = (location: any) => {
    if (!location || !location.coordinates) return "Unknown";
    const [lng, lat] = location.coordinates;
    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
  };

  const getConnectionStatus = () => {
    if (!trackingData) return null;

    const { isOnline, lastUpdate } = trackingData;
    const timeDiff = Date.now() - lastUpdate.getTime();
    const minutesAgo = Math.floor(timeDiff / 60000);

    return {
      isOnline,
      lastSeen: minutesAgo < 1 ? "Just now" : `${minutesAgo}m ago`,
      color: isOnline ? "text-green-600" : "text-red-600",
      bgColor: isOnline ? "bg-green-100" : "bg-red-100",
    };
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
      </div>
    );
  }

  if (error || !trackingData) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <AlertTriangle size={48} className="mx-auto mb-4 text-red-500" />
        <p className="text-red-600 dark:text-red-400">
          {error || t("tracking.noDataAvailable")}
        </p>
      </div>
    );
  }

  const connectionStatus = getConnectionStatus();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Bus Info */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary-100 dark:bg-primary-800/20 rounded-lg">
              <MapPin className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t("tracking.liveTracking")} - {trackingData.bus.plate_number}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {trackingData.route?.name || t("routes.noRouteAssigned")}
              </p>
            </div>
          </div>
          {connectionStatus && (
            <div className="flex items-center space-x-2">
              {trackingData.isOnline ? (
                <Wifi size={20} className={connectionStatus.color} />
              ) : (
                <WifiOff size={20} className={connectionStatus.color} />
              )}
              <div className="text-right">
                <p className={`text-sm font-medium ${connectionStatus.color}`}>
                  {trackingData.isOnline
                    ? t("tracking.online")
                    : t("tracking.offline")}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {connectionStatus.lastSeen}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Live Map */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="h-[400px]">
          <LiveMap
            selectedBusId={busId}
            showRoutes={true}
            showGeofences={true}
            showSpeedInfo={true}
            showETA={true}
          />
        </div>
      </div>

      {/* Real-time Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Gauge className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t("tracking.currentSpeed")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {trackingData.currentSpeed} km/h
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <Route className="h-5 w-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t("tracking.distanceTraveled")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {trackingData.distanceTraveled} km
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
              <Users className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t("students.onBoard")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {trackingData.studentsOnBoard}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
              <Battery className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t("tracking.batteryLevel")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {trackingData.batteryLevel}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Location and Route Info */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {t("tracking.locationInfo")}
          </h3>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("tracking.currentLocation")}
              </label>
              <p className="text-sm text-gray-900 dark:text-white">
                {formatLocation(trackingData.bus.last_location)}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("tracking.heading")}
              </label>
              <p className="text-sm text-gray-900 dark:text-white">
                {trackingData.heading}° {t("tracking.degrees")}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("tracking.lastUpdate")}
              </label>
              <p className="text-sm text-gray-900 dark:text-white">
                {trackingData.lastUpdate.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        {trackingData.nextStop && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {t("tracking.nextStop")}
            </h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-accent-100 dark:bg-accent-900/20 rounded-lg">
                  <MapPin className="h-5 w-5 text-accent-600 dark:text-accent-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {trackingData.nextStop}
                  </p>
                  <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                    <Clock size={12} />
                    <span>
                      {t("tracking.estimatedArrival")}:{" "}
                      {trackingData.estimatedArrival}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LiveBusTracking;
