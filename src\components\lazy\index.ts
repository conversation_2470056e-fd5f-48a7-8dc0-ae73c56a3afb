
import { lazy } from 'react'

// Lazy loading للصفحات الرئيسية
export const LazyDashboardPage = lazy(() => import('@pages/dashboard/DashboardPage'))
export const LazyBusesPage = lazy(() => import('@pages/dashboard/BusesPage'))
export const LazyStudentsPage = lazy(() => import('@pages/dashboard/StudentsPage'))
export const LazyRoutesPage = lazy(() => import('@pages/dashboard/RoutesPage'))
export const LazyReportsPage = lazy(() => import('@pages/dashboard/ReportsPage'))
export const LazySettingsPage = lazy(() => import('@pages/dashboard/SettingsPage'))

// Lazy loading للمكونات الثقيلة
export const LazyDataTable = lazy(() => import('@components/ui/DataTable'))
export const LazyChart = lazy(() => import('@components/ui/Chart'))
export const LazyMap = lazy(() => import('@components/ui/Map'))
