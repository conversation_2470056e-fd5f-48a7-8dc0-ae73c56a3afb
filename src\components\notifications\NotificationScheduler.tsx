import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Calendar,
  Clock,
  Users,
  Send,
  Plus,
  Edit,
  Trash2,
  Eye,
  Save,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { supabase } from "../../lib/supabase";
import { cn } from "../../utils/cn";

interface ScheduledNotification {
  id?: string;
  title: string;
  message: string;
  type:
    | "geofence"
    | "attendance"
    | "maintenance"
    | "announcements"
    | "emergency"
    | "route_changes";
  priority: "low" | "normal" | "high";
  targetRoles: string[];
  scheduledFor: string;
  isRecurring: boolean;
  recurringPattern?: {
    frequency: "daily" | "weekly" | "monthly";
    daysOfWeek?: number[];
    dayOfMonth?: number;
  };
  status: "pending" | "sent" | "cancelled";
  tenantId: string;
  createdBy: string;
}

interface NotificationSchedulerProps {
  className?: string;
}

const notificationTypes = [
  { value: "announcements", label: "Announcement", icon: "📢" },
  { value: "maintenance", label: "Maintenance", icon: "🔧" },
  { value: "route_changes", label: "Route Changes", icon: "🗺️" },
  { value: "geofence", label: "Location Alert", icon: "📍" },
  { value: "attendance", label: "Attendance", icon: "👥" },
];

const userRoles = [
  { value: "admin", label: "Administrators" },
  { value: "school_manager", label: "School Managers" },
  { value: "supervisor", label: "Supervisors" },
  { value: "driver", label: "Drivers" },
  { value: "parent", label: "Parents" },
  { value: "student", label: "Students" },
];

const priorityLevels = [
  { value: "low", label: "Low", color: "text-gray-600" },
  { value: "normal", label: "Normal", color: "text-blue-600" },
  { value: "high", label: "High", color: "text-red-600" },
];

export const NotificationScheduler: React.FC<NotificationSchedulerProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [scheduledNotifications, setScheduledNotifications] = useState<
    ScheduledNotification[]
  >([]);
  const [showForm, setShowForm] = useState(false);
  const [editingNotification, setEditingNotification] =
    useState<ScheduledNotification | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState<Partial<ScheduledNotification>>({
    title: "",
    message: "",
    type: "announcements",
    priority: "normal",
    targetRoles: [],
    scheduledFor: "",
    isRecurring: false,
    recurringPattern: {
      frequency: "weekly",
      daysOfWeek: [],
    },
  });

  useEffect(() => {
    fetchScheduledNotifications();
  }, [user]);

  const fetchScheduledNotifications = async () => {
    if (!user?.tenant_id) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("scheduled_notifications")
        .select("*")
        .eq("tenant_id", user.tenant_id)
        .order("scheduled_for", { ascending: true });

      if (error) throw error;
      setScheduledNotifications(data || []);
    } catch (err: any) {
      console.error("Error fetching scheduled notifications:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveNotification = async () => {
    if (
      !user?.tenant_id ||
      !formData.title ||
      !formData.message ||
      !formData.scheduledFor
    ) {
      setError("Please fill in all required fields");
      return;
    }

    setSaving(true);
    setError(null);

    try {
      const notificationData = {
        ...formData,
        tenant_id: user.tenant_id,
        created_by: user.id,
        status: "pending",
      };

      if (editingNotification) {
        const { error } = await supabase
          .from("scheduled_notifications")
          .update(notificationData)
          .eq("id", editingNotification.id);

        if (error) throw error;
      } else {
        const { error } = await supabase
          .from("scheduled_notifications")
          .insert([notificationData]);

        if (error) throw error;
      }

      await fetchScheduledNotifications();
      resetForm();
    } catch (err: any) {
      console.error("Error saving notification:", err);
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteNotification = async (id: string) => {
    if (
      !window.confirm(
        "Are you sure you want to delete this scheduled notification?",
      )
    ) {
      return;
    }

    try {
      const { error } = await supabase
        .from("scheduled_notifications")
        .delete()
        .eq("id", id);

      if (error) throw error;
      await fetchScheduledNotifications();
    } catch (err: any) {
      console.error("Error deleting notification:", err);
      setError(err.message);
    }
  };

  const handleEditNotification = (notification: ScheduledNotification) => {
    setEditingNotification(notification);
    setFormData(notification);
    setShowForm(true);
  };

  const resetForm = () => {
    setFormData({
      title: "",
      message: "",
      type: "announcements",
      priority: "normal",
      targetRoles: [],
      scheduledFor: "",
      isRecurring: false,
      recurringPattern: {
        frequency: "weekly",
        daysOfWeek: [],
      },
    });
    setEditingNotification(null);
    setShowForm(false);
    setError(null);
  };

  const updateFormData = (updates: Partial<ScheduledNotification>) => {
    setFormData((prev) => ({ ...prev, ...updates }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "text-yellow-600 bg-yellow-100";
      case "sent":
        return "text-green-600 bg-green-100";
      case "cancelled":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const formatDateTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
            <Calendar size={20} className="mr-2" />
            Scheduled Notifications
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Schedule notifications to be sent at specific times
          </p>
        </div>
        <Button onClick={() => setShowForm(true)} leftIcon={<Plus size={16} />}>
          Schedule Notification
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}

      {/* Notification Form */}
      {showForm && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {editingNotification ? "Edit" : "Schedule"} Notification
            </h3>
            <Button variant="ghost" onClick={resetForm}>
              ×
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Title */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Title *
              </label>
              <input
                type="text"
                value={formData.title || ""}
                onChange={(e) => updateFormData({ title: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="Notification title"
              />
            </div>

            {/* Message */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Message *
              </label>
              <textarea
                value={formData.message || ""}
                onChange={(e) => updateFormData({ message: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="Notification message"
              />
            </div>

            {/* Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Type
              </label>
              <select
                value={formData.type || "announcements"}
                onChange={(e) =>
                  updateFormData({ type: e.target.value as any })
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                {notificationTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.icon} {type.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Priority */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Priority
              </label>
              <select
                value={formData.priority || "normal"}
                onChange={(e) =>
                  updateFormData({ priority: e.target.value as any })
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                {priorityLevels.map((priority) => (
                  <option key={priority.value} value={priority.value}>
                    {priority.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Target Roles */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Target Recipients
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {userRoles.map((role) => (
                  <label
                    key={role.value}
                    className="flex items-center space-x-2"
                  >
                    <input
                      type="checkbox"
                      checked={
                        formData.targetRoles?.includes(role.value) || false
                      }
                      onChange={(e) => {
                        const currentRoles = formData.targetRoles || [];
                        if (e.target.checked) {
                          updateFormData({
                            targetRoles: [...currentRoles, role.value],
                          });
                        } else {
                          updateFormData({
                            targetRoles: currentRoles.filter(
                              (r) => r !== role.value,
                            ),
                          });
                        }
                      }}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {role.label}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Scheduled Date/Time */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Scheduled Date & Time *
              </label>
              <input
                type="datetime-local"
                value={formData.scheduledFor || ""}
                onChange={(e) =>
                  updateFormData({ scheduledFor: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* Recurring */}
            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.isRecurring || false}
                  onChange={(e) =>
                    updateFormData({ isRecurring: e.target.checked })
                  }
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Recurring Notification
                </span>
              </label>
            </div>

            {/* Recurring Pattern */}
            {formData.isRecurring && (
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Recurring Pattern
                </label>
                <select
                  value={formData.recurringPattern?.frequency || "weekly"}
                  onChange={(e) =>
                    updateFormData({
                      recurringPattern: {
                        ...formData.recurringPattern,
                        frequency: e.target.value as any,
                      },
                    })
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 mt-6">
            <Button variant="outline" onClick={resetForm}>
              Cancel
            </Button>
            <Button
              onClick={handleSaveNotification}
              disabled={saving}
              leftIcon={<Save size={16} />}
            >
              {saving
                ? "Saving..."
                : editingNotification
                  ? "Update"
                  : "Schedule"}
            </Button>
          </div>
        </div>
      )}

      {/* Scheduled Notifications List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        {scheduledNotifications.length === 0 ? (
          <div className="p-8 text-center text-gray-500 dark:text-gray-400">
            <Calendar size={48} className="mx-auto mb-4 opacity-50" />
            <p className="mb-2">No scheduled notifications</p>
            <p className="text-sm">
              Schedule your first notification to get started
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {scheduledNotifications.map((notification) => (
              <div
                key={notification.id}
                className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                        {notification.title}
                      </h4>
                      <span
                        className={cn(
                          "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
                          getStatusColor(notification.status),
                        )}
                      >
                        {notification.status}
                      </span>
                      {notification.isRecurring && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Recurring
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {notification.message}
                    </p>
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span className="flex items-center">
                        <Clock size={12} className="mr-1" />
                        {formatDateTime(notification.scheduledFor)}
                      </span>
                      <span className="flex items-center">
                        <Users size={12} className="mr-1" />
                        {notification.targetRoles?.length || 0} role(s)
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1 ml-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditNotification(notification)}
                      disabled={notification.status === "sent"}
                      className="p-1 h-auto"
                    >
                      <Edit size={14} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteNotification(notification.id!)}
                      disabled={notification.status === "sent"}
                      className="p-1 h-auto text-red-600 hover:text-red-700"
                    >
                      <Trash2 size={14} />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationScheduler;
