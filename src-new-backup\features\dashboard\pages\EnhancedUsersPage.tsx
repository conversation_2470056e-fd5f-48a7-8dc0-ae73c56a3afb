/**
 * صفحة إدارة المستخدمين المحسنة - Enhanced Users Management Page
 * تدعم النظام الجديد للصلاحيات مع جميع العمليات المطلوبة
 */

import React, { useState, useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import {
  User as UserIcon,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Plus,
  Loader2,
  MoreVertical,
  CheckSquare,
  Square,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Download,
  UserCheck,
  UserX,
  Shield,
  Settings,
} from "lucide-react";
import { Navbar } from "../../../shared/layouts/Navbar";
import { Sidebar } from "../../components/layout/Sidebar";
import { Button } from "../../components/ui/Button";
import { Pagination, usePagination } from "../../components/ui/Pagination";
import { UserModal } from "../../components/users/UserModal";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { usePermissions } from "../../hooks/usePermissions";
import { PermissionGuard } from "../../components/auth/PermissionGuard";
import { Permission } from "../../lib/rbac";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/api";
import { UserRole } from "../../types";

interface BulkAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  action: (selectedIds: string[]) => void;
  permission?: Permission;
  confirmMessage?: string;
}

export const EnhancedUsersPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { users, loading, error, refreshData } = useDatabase();
  const { 
    isAdmin, 
    isSchoolManager, 
    isSupervisor,
    hasPermission 
  } = usePermissions();

  // State management
  const [searchQuery, setSearchQuery] = useState("");
  const [filterRole, setFilterRole] = useState<UserRole | "all">("all");
  const [filterStatus, setFilterStatus] = useState<"all" | "active" | "inactive">("all");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any | undefined>();
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set());
  const [isProcessing, setIsProcessing] = useState(false);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [viewMode, setViewMode] = useState<"view" | "edit" | "create">("view");

  // تصفية المستخدمين حسب الصلاحيات
  const filteredUsers = useMemo(() => {
    let usersList = users;

    // تطبيق فلترة الصلاحيات
    if (!isAdmin && user?.tenant_id) {
      usersList = users.filter(u => u.tenant_id === user.tenant_id);
    }

    // تطبيق فلترة البحث والحالة والدور
    return usersList.filter((u) => {
      const matchesSearch = !searchQuery || 
        (u.name && u.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (u.email && u.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (u.phone && u.phone.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesRole = filterRole === "all" || u.role === filterRole;

      const matchesStatus = filterStatus === "all" ||
        (filterStatus === "active" && u.is_active) ||
        (filterStatus === "inactive" && !u.is_active);

      return matchesSearch && matchesRole && matchesStatus;
    });
  }, [users, user, isAdmin, searchQuery, filterRole, filterStatus]);

  // Pagination
  const { currentPage, totalPages, startIndex, endIndex, goToPage } = usePagination(filteredUsers.length, 10);
  const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

  // تحديد/إلغاء تحديد جميع المستخدمين
  const handleSelectAll = useCallback(() => {
    if (selectedUsers.size === paginatedUsers.length) {
      setSelectedUsers(new Set());
    } else {
      setSelectedUsers(new Set(paginatedUsers.map(user => user.id)));
    }
  }, [selectedUsers.size, paginatedUsers]);

  // تحديد/إلغاء تحديد مستخدم واحد
  const handleSelectUser = useCallback((userId: string) => {
    const newSelected = new Set(selectedUsers);
    if (newSelected.has(userId)) {
      newSelected.delete(userId);
    } else {
      newSelected.add(userId);
    }
    setSelectedUsers(newSelected);
  }, [selectedUsers]);

  // العمليات المجمعة
  const bulkActions: BulkAction[] = [
    {
      id: 'activate',
      label: 'تفعيل المحدد',
      icon: <UserCheck className="h-4 w-4" />,
      action: handleBulkActivate,
      permission: Permission.USERS_EDIT,
      confirmMessage: 'هل أنت متأكد من تفعيل المستخدمين المحددين؟'
    },
    {
      id: 'deactivate',
      label: 'إلغاء تفعيل المحدد',
      icon: <UserX className="h-4 w-4" />,
      action: handleBulkDeactivate,
      permission: Permission.USERS_EDIT,
      confirmMessage: 'هل أنت متأكد من إلغاء تفعيل المستخدمين المحددين؟'
    },
    {
      id: 'delete',
      label: 'حذف المحدد',
      icon: <Trash2 className="h-4 w-4" />,
      action: handleBulkDelete,
      permission: Permission.USERS_DELETE,
      confirmMessage: 'هل أنت متأكد من حذف المستخدمين المحددين؟ هذا الإجراء لا يمكن التراجع عنه.'
    },
    {
      id: 'export',
      label: 'تصدير المحدد',
      icon: <Download className="h-4 w-4" />,
      action: handleBulkExport,
    }
  ];

  // دوال العمليات المجمعة
  async function handleBulkActivate(selectedIds: string[]) {
    setIsProcessing(true);
    try {
      const { error } = await supabase
        .from('users')
        .update({ is_active: true })
        .in('id', selectedIds);

      if (error) throw error;
      
      await refreshData();
      setSelectedUsers(new Set());
      alert(`تم تفعيل ${selectedIds.length} مستخدم بنجاح!`);
    } catch (error) {
      console.error('Error activating users:', error);
      alert('حدث خطأ أثناء تفعيل المستخدمين.');
    } finally {
      setIsProcessing(false);
    }
  }

  async function handleBulkDeactivate(selectedIds: string[]) {
    setIsProcessing(true);
    try {
      const { error } = await supabase
        .from('users')
        .update({ is_active: false })
        .in('id', selectedIds);

      if (error) throw error;
      
      await refreshData();
      setSelectedUsers(new Set());
      alert(`تم إلغاء تفعيل ${selectedIds.length} مستخدم بنجاح!`);
    } catch (error) {
      console.error('Error deactivating users:', error);
      alert('حدث خطأ أثناء إلغاء تفعيل المستخدمين.');
    } finally {
      setIsProcessing(false);
    }
  }

  async function handleBulkDelete(selectedIds: string[]) {
    setIsProcessing(true);
    try {
      // استخدام دالة الحذف المجمع الآمن
      const { data, error } = await supabase.rpc('bulk_delete_users_final', {
        user_ids_to_delete: selectedIds
      });

      if (error) throw error;

      if (data.success) {
        alert(`تم حذف ${data.success_count} مستخدم من أصل ${data.total_requested} بنجاح!`);
      } else {
        alert(`فشل في حذف المستخدمين:\n${data.errors.join('\n')}`);
      }
      
      await refreshData();
      setSelectedUsers(new Set());
    } catch (error: any) {
      console.error('Error deleting users:', error);
      alert(`حدث خطأ أثناء حذف المستخدمين: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  }

  function handleBulkExport(selectedIds: string[]) {
    const selectedUsersData = filteredUsers.filter(user => selectedIds.includes(user.id));
    const csvContent = generateCSV(selectedUsersData);
    downloadCSV(csvContent, 'users.csv');
  }

  // دوال مساعدة للتصدير
  function generateCSV(users: any[]): string {
    const headers = ['الاسم', 'البريد الإلكتروني', 'الهاتف', 'الدور', 'الحالة', 'تاريخ الإنشاء'];
    const rows = users.map(user => [
      user.name || '',
      user.email || '',
      user.phone || '',
      getRoleLabel(user.role),
      user.is_active ? 'نشط' : 'غير نشط',
      new Date(user.created_at).toLocaleDateString('ar')
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  function downloadCSV(content: string, filename: string) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
  }

  function getRoleLabel(role: string): string {
    const roleLabels: Record<string, string> = {
      'admin': 'مدير النظام',
      'school_manager': 'مدير المدرسة',
      'supervisor': 'مشرف',
      'driver': 'سائق',
      'parent': 'ولي أمر'
    };
    return roleLabels[role] || role;
  }

  // دوال إدارة النماذج
  const handleOpenModal = useCallback((user?: any, mode: "view" | "edit" | "create" = "create") => {
    setSelectedUser(user);
    setViewMode(mode);
    setIsModalOpen(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setSelectedUser(undefined);
    setViewMode("view");
    setIsModalOpen(false);
  }, []);

  // دالة حفظ المستخدم
  const handleSubmit = async (data: any) => {
    try {
      console.log('🚀 EnhancedUsersPage: handleSubmit called', {
        viewMode,
        selectedUserId: selectedUser?.id,
        dataReceived: {
          ...data,
          password: data.password ? '***' : undefined
        }
      });

      if (selectedUser && viewMode === "edit") {
        // تحديث مستخدم موجود
        const updateData = { ...data };
        delete updateData.password; // إزالة كلمة المرور من بيانات التحديث

        const { error } = await supabase
          .from("users")
          .update(updateData)
          .eq("id", selectedUser.id);

        if (error) throw error;

        // تحديث كلمة المرور إذا تم توفيرها
        if (data.password && data.password.trim() !== "") {
          const { error: passwordError } = await supabase.rpc('update_user_password', {
            user_id: selectedUser.id,
            user_password: data.password
          });
          if (passwordError) throw passwordError;
        }

        alert('تم تحديث المستخدم بنجاح!');

      } else if (viewMode === "create") {
        // إنشاء مستخدم جديد باستخدام الدالة المحسنة
        console.log('📝 Creating new user with enhanced function');

        const { data: result, error } = await supabase.rpc('create_user_enhanced', {
          user_email: data.email,
          user_password: data.password,
          user_name: data.name,
          user_role: data.role,
          user_tenant_id: data.tenant_id,
          user_phone: data.phone || null
        });

        console.log('📊 Create user result:', result);
        console.log('❗ Create user error:', error);

        if (error) throw error;

        if (!result?.success) {
          throw new Error(result?.error || 'فشل في إنشاء المستخدم');
        }

        alert(result.message || 'تم إنشاء المستخدم بنجاح!');
      }

      await refreshData();
      handleCloseModal();

    } catch (error: any) {
      console.error("💥 Error saving user:", error);

      // تحسين رسائل الخطأ
      let errorMessage = "حدث خطأ غير متوقع";

      if (error?.message) {
        errorMessage = error.message;
      } else if (typeof error === "string") {
        errorMessage = error;
      } else if (error?.details) {
        errorMessage = error.details;
      }

      throw new Error(errorMessage);
    }
  };

  // دالة حذف مستخدم واحد
  const handleDelete = async (id: string) => {
    if (!confirm("هل أنت متأكد من حذف هذا المستخدم؟ سيتم حذف جميع البيانات المرتبطة به.")) return;

    setIsDeleting(true);
    try {
      // استخدام دالة الحذف الآمن
      const { data, error } = await supabase.rpc('delete_user_final', {
        user_id_to_delete: id
      });

      if (error) throw error;

      if (data && data.success) {
        alert(`تم حذف المستخدم بنجاح!\nالبيانات المحذوفة:\n- الجلسات: ${data.deleted_data.sessions}\n- الإشعارات: ${data.deleted_data.notifications}`);
        await refreshData();
      } else {
        alert(`فشل حذف المستخدم: ${data?.error || 'خطأ غير معروف'}`);
      }
    } catch (error: any) {
      console.error("Error deleting user:", error);
      alert(`حدث خطأ أثناء حذف المستخدم: ${error.message}`);
    } finally {
      setIsDeleting(false);
    }
  };

  // دالة تغيير حالة المستخدم
  const handleToggleStatus = async (user: any) => {
    try {
      const { error } = await supabase
        .from("users")
        .update({ is_active: !user.is_active })
        .eq("id", user.id);

      if (error) throw error;
      await refreshData();
    } catch (error) {
      console.error("Error toggling user status:", error);
    }
  };

  // التحقق من الصلاحيات للوصول للصفحة
  if (!isAdmin && !isSchoolManager && !isSupervisor) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            غير مصرح بالوصول
          </h2>
          <p className="text-gray-500 dark:text-gray-400">
            ليس لديك صلاحية للوصول إلى هذه الصفحة.
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin text-primary-500" />
          <span className="text-gray-600 dark:text-gray-300">جاري تحميل المستخدمين...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            خطأ في تحميل البيانات
          </h2>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            {error.message}
          </p>
          <Button onClick={refreshData} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            إعادة المحاولة
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />

        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            {/* رأس الصفحة */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  إدارة المستخدمين
                </h1>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {isAdmin
                    ? "إدارة جميع المستخدمين في النظام"
                    : isSchoolManager
                      ? "إدارة مستخدمي مدرستك"
                      : "عرض مستخدمي المدرسة"
                  }
                </p>
              </div>

              <div className="mt-4 md:mt-0 flex items-center gap-3">
                {/* زر تحديث */}
                <Button
                  variant="outline"
                  onClick={refreshData}
                  className="flex items-center gap-2"
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                  تحديث
                </Button>

                {/* زر إضافة مستخدم */}
                {(isAdmin || isSchoolManager) && (
                  <Button
                    onClick={() => handleOpenModal(undefined, "create")}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    إضافة مستخدم
                  </Button>
                )}
              </div>
            </div>

            {/* شريط العمليات المجمعة */}
            {selectedUsers.size > 0 && (
              <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                      تم تحديد {selectedUsers.size} مستخدم
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedUsers(new Set())}
                    >
                      إلغاء التحديد
                    </Button>
                  </div>

                  <div className="flex items-center gap-2">
                    {bulkActions.map((action) => {
                      // تحقق من الصلاحيات حسب نوع العملية
                      const hasPermissionForAction =
                        action.id === 'export' || // التصدير متاح للجميع
                        (action.id === 'delete' && isAdmin) || // الحذف للأدمن فقط
                        ((action.id === 'activate' || action.id === 'deactivate') && (isAdmin || isSchoolManager)); // التفعيل للأدمن ومدير المدرسة

                      if (!hasPermissionForAction) return null;

                      return (
                        <Button
                          key={action.id}
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (action.confirmMessage && !confirm(action.confirmMessage)) return;
                            action.action(Array.from(selectedUsers));
                          }}
                          disabled={isProcessing}
                          className="flex items-center gap-2"
                        >
                          {action.icon}
                          {action.label}
                        </Button>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}

            {/* البحث والفلترة */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                  <div className="relative max-w-xs w-full">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      placeholder="البحث في المستخدمين..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Filter className="h-5 w-5 text-gray-400" />
                      </div>
                      <select
                        className="block pl-10 pr-8 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                        value={filterRole}
                        onChange={(e) => setFilterRole(e.target.value as UserRole | "all")}
                      >
                        <option value="all">جميع الأدوار</option>
                        <option value="admin">مدير النظام</option>
                        <option value="school_manager">مدير المدرسة</option>
                        <option value="supervisor">مشرف</option>
                        <option value="driver">سائق</option>
                        <option value="parent">ولي أمر</option>
                      </select>
                    </div>

                    <div className="relative">
                      <select
                        className="block pr-8 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                        value={filterStatus}
                        onChange={(e) => setFilterStatus(e.target.value as "all" | "active" | "inactive")}
                      >
                        <option value="all">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                      </select>
                    </div>

                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {filteredUsers.length} مستخدم
                    </div>
                  </div>
                </div>
              </div>

              {/* جدول المستخدمين */}
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      {/* عمود التحديد */}
                      <th scope="col" className="px-6 py-3 text-left">
                        <button
                          onClick={handleSelectAll}
                          className="flex items-center justify-center w-5 h-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                          {selectedUsers.size === paginatedUsers.length && paginatedUsers.length > 0 ? (
                            <CheckSquare className="w-5 h-5" />
                          ) : (
                            <Square className="w-5 h-5" />
                          )}
                        </button>
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        المستخدم
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        الدور
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        تاريخ الإنشاء
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {paginatedUsers.length > 0 ? (
                      paginatedUsers.map((user) => (
                        <tr
                          key={user.id}
                          className={`hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                            selectedUsers.has(user.id) ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                          }`}
                        >
                          {/* عمود التحديد */}
                          <td className="px-6 py-4 whitespace-nowrap">
                            <button
                              onClick={() => handleSelectUser(user.id)}
                              className="flex items-center justify-center w-5 h-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                            >
                              {selectedUsers.has(user.id) ? (
                                <CheckSquare className="w-5 h-5 text-blue-600" />
                              ) : (
                                <Square className="w-5 h-5" />
                              )}
                            </button>
                          </td>

                          {/* معلومات المستخدم */}
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <div className="h-10 w-10 bg-primary-100 dark:bg-primary-800/20 rounded-full flex items-center justify-center text-primary-600 dark:text-primary-400">
                                  <UserIcon className="h-5 w-5" />
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {user.name}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  {user.email}
                                </div>
                                {user.phone && (
                                  <div className="text-sm text-gray-500 dark:text-gray-400">
                                    {user.phone}
                                  </div>
                                )}
                              </div>
                            </div>
                          </td>

                          {/* الدور */}
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              user.role === 'admin' ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' :
                              user.role === 'school_manager' ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100' :
                              user.role === 'supervisor' ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' :
                              user.role === 'driver' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100' :
                              'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                            }`}>
                              {user.role === 'admin' && <Shield className="w-3 h-3 mr-1" />}
                              {getRoleLabel(user.role)}
                            </span>
                          </td>

                          {/* الحالة */}
                          <td className="px-6 py-4 whitespace-nowrap">
                            <button
                              onClick={() => handleToggleStatus(user)}
                              disabled={!isAdmin && !isSchoolManager}
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors ${
                                user.is_active
                                  ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100 hover:bg-green-200 dark:hover:bg-green-700"
                                  : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 hover:bg-red-200 dark:hover:bg-red-700"
                              } ${!isAdmin && !isSchoolManager ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
                            >
                              {user.is_active ? (
                                <>
                                  <CheckCircle className="w-3 h-3 mr-1" />
                                  نشط
                                </>
                              ) : (
                                <>
                                  <XCircle className="w-3 h-3 mr-1" />
                                  غير نشط
                                </>
                              )}
                            </button>
                          </td>

                          {/* تاريخ الإنشاء */}
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {new Date(user.created_at).toLocaleDateString('ar')}
                          </td>

                          {/* الإجراءات */}
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex items-center justify-end gap-2">
                              {/* عرض */}
                              <button
                                className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 p-1"
                                title="عرض التفاصيل"
                                onClick={() => handleOpenModal(user, "view")}
                              >
                                <Eye className="h-4 w-4" />
                              </button>

                              {/* تعديل */}
                              {(isAdmin || isSchoolManager) && (
                                <button
                                  className="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300 p-1"
                                  title="تعديل"
                                  onClick={() => handleOpenModal(user, "edit")}
                                >
                                  <Edit className="h-4 w-4" />
                                </button>
                              )}

                              {/* حذف */}
                              {isAdmin && (
                                <button
                                  className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 p-1 transition-colors"
                                  title="حذف"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    handleDelete(user.id);
                                  }}
                                  disabled={isDeleting}
                                  style={{
                                    opacity: isDeleting ? 0.5 : 1,
                                    cursor: isDeleting ? 'not-allowed' : 'pointer'
                                  }}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                          {loading ? (
                            <div className="flex items-center justify-center">
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              جاري تحميل المستخدمين...
                            </div>
                          ) : searchQuery || filterRole !== "all" || filterStatus !== "all" ? (
                            "لا توجد نتائج مطابقة للبحث"
                          ) : (
                            <div className="py-8">
                              <UserIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                              <p className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                لا يوجد مستخدمون
                              </p>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                {isAdmin || isSchoolManager
                                  ? "ابدأ بإضافة أول مستخدم في النظام"
                                  : "لا توجد بيانات مستخدمين متاحة لحسابك"
                                }
                              </p>
                            </div>
                          )}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      عرض {startIndex + 1} - {endIndex} من {filteredUsers.length} مستخدم
                    </div>
                    <Pagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={goToPage}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>

      {/* النماذج */}
      <UserModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleSubmit}
        user={selectedUser}
        mode={viewMode}
      />
    </div>
  );
};
