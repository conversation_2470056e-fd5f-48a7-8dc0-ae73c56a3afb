import React from "react";
import { Navbar } from "../../components/layout/Navbar";
import { Sidebar } from "../../components/layout/Sidebar";
import { ProfessionalDashboard } from "../../components/dashboard/ProfessionalDashboard";

export const DashboardPage: React.FC = () => {

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />

        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            {/* لوحة التحكم الاحترافية الجديدة */}
            <ProfessionalDashboard />
          </div>
        </main>
      </div>
    </div>
  );
};
