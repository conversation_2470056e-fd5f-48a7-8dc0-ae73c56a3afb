/**
 * خدمة إدارة الجلسات المتقدمة
 * Advanced Session Management Service
 * 
 * المرحلة 1: تأمين الدخول والسياسات الأمنية الأساسية
 */

import { supabase } from '../../lib/supabase';

export interface UserSession {
  id: string;
  user_id: string;
  session_token: string;
  ip_address: string;
  user_agent: string;
  device_info: {
    browser: string;
    os: string;
    device_type: 'desktop' | 'mobile' | 'tablet';
    screen_resolution?: string;
    timezone?: string;
  };
  location?: {
    country: string;
    city: string;
    region: string;
  };
  is_active: boolean;
  last_activity: string;
  expires_at: string;
  created_at: string;
}

export interface SessionConfig {
  maxConcurrentSessions: number;
  sessionTimeoutMinutes: number;
  allowMultipleDevices: boolean;
  requireReauthForSensitive: boolean;
  trackDeviceFingerprint: boolean;
}

export class SessionManagementService {
  private static instance: SessionManagementService;

  private readonly DEFAULT_CONFIG: SessionConfig = {
    maxConcurrentSessions: 5,
    sessionTimeoutMinutes: 480, // 8 ساعات
    allowMultipleDevices: true,
    requireReauthForSensitive: true,
    trackDeviceFingerprint: true
  };

  private constructor() {}

  static getInstance(): SessionManagementService {
    if (!SessionManagementService.instance) {
      SessionManagementService.instance = new SessionManagementService();
    }
    return SessionManagementService.instance;
  }

  /**
   * إنشاء جلسة جديدة
   */
  async createSession(
    userId: string,
    sessionToken: string,
    ipAddress: string,
    userAgent: string,
    deviceFingerprint?: string
  ): Promise<UserSession> {
    try {
      // تحليل معلومات الجهاز
      const deviceInfo = this.parseDeviceInfo(userAgent);
      
      // الحصول على الموقع الجغرافي (محاكاة)
      const location = await this.getLocationFromIP(ipAddress);

      // حساب وقت انتهاء الجلسة
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + this.DEFAULT_CONFIG.sessionTimeoutMinutes);

      const session: Partial<UserSession> = {
        user_id: userId,
        session_token: sessionToken,
        ip_address: ipAddress,
        user_agent: userAgent,
        device_info: deviceInfo,
        location,
        is_active: true,
        last_activity: new Date().toISOString(),
        expires_at: expiresAt.toISOString(),
        created_at: new Date().toISOString()
      };

      // فحص الحد الأقصى للجلسات المتزامنة
      await this.enforceSessionLimits(userId);

      // حفظ الجلسة في قاعدة البيانات
      const { data, error } = await supabase
        .from('user_sessions')
        .insert([session])
        .select()
        .single();

      if (error) throw error;

      // تسجيل حدث أمني
      await this.logSecurityEvent(userId, 'SESSION_CREATED', 'New session created', {
        ip_address: ipAddress,
        device_info: deviceInfo,
        location
      });

      return data as UserSession;

    } catch (error) {
      console.error('Error creating session:', error);
      throw new Error('فشل في إنشاء الجلسة');
    }
  }

  /**
   * تحديث نشاط الجلسة
   */
  async updateSessionActivity(sessionToken: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_sessions')
        .update({ 
          last_activity: new Date().toISOString(),
          is_active: true
        })
        .eq('session_token', sessionToken)
        .eq('is_active', true);

      return !error;

    } catch (error) {
      console.error('Error updating session activity:', error);
      return false;
    }
  }

  /**
   * إنهاء جلسة محددة
   */
  async terminateSession(sessionToken: string, reason?: string): Promise<boolean> {
    try {
      // الحصول على معلومات الجلسة أولاً
      const { data: session } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('session_token', sessionToken)
        .single();

      if (!session) return false;

      // إنهاء الجلسة
      const { error } = await supabase
        .from('user_sessions')
        .update({ is_active: false })
        .eq('session_token', sessionToken);

      if (error) throw error;

      // تسجيل حدث أمني
      await this.logSecurityEvent(session.user_id, 'SESSION_TERMINATED', 
        `Session terminated: ${reason || 'User logout'}`, {
          session_token: sessionToken,
          reason
        });

      return true;

    } catch (error) {
      console.error('Error terminating session:', error);
      return false;
    }
  }

  /**
   * إنهاء جميع جلسات المستخدم
   */
  async terminateAllUserSessions(userId: string, exceptToken?: string): Promise<number> {
    try {
      let query = supabase
        .from('user_sessions')
        .update({ is_active: false })
        .eq('user_id', userId)
        .eq('is_active', true);

      if (exceptToken) {
        query = query.neq('session_token', exceptToken);
      }

      const { data, error } = await query.select();

      if (error) throw error;

      const terminatedCount = data?.length || 0;

      // تسجيل حدث أمني
      await this.logSecurityEvent(userId, 'ALL_SESSIONS_TERMINATED', 
        `All user sessions terminated (${terminatedCount} sessions)`, {
          terminated_count: terminatedCount,
          except_token: exceptToken ? 'yes' : 'no'
        });

      return terminatedCount;

    } catch (error) {
      console.error('Error terminating all user sessions:', error);
      return 0;
    }
  }

  /**
   * الحصول على جلسات المستخدم النشطة
   */
  async getUserActiveSessions(userId: string): Promise<UserSession[]> {
    try {
      const { data, error } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .gt('expires_at', new Date().toISOString())
        .order('last_activity', { ascending: false });

      if (error) throw error;
      return data as UserSession[];

    } catch (error) {
      console.error('Error getting user active sessions:', error);
      return [];
    }
  }

  /**
   * فحص صحة الجلسة
   */
  async validateSession(sessionToken: string): Promise<{
    isValid: boolean;
    session?: UserSession;
    reason?: string;
  }> {
    try {
      const { data: session, error } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('session_token', sessionToken)
        .eq('is_active', true)
        .single();

      if (error || !session) {
        return { isValid: false, reason: 'الجلسة غير موجودة' };
      }

      // فحص انتهاء الصلاحية
      if (new Date(session.expires_at) < new Date()) {
        await this.terminateSession(sessionToken, 'Session expired');
        return { isValid: false, reason: 'انتهت صلاحية الجلسة' };
      }

      // فحص عدم النشاط
      const lastActivity = new Date(session.last_activity);
      const inactiveMinutes = (Date.now() - lastActivity.getTime()) / (1000 * 60);
      
      if (inactiveMinutes > this.DEFAULT_CONFIG.sessionTimeoutMinutes) {
        await this.terminateSession(sessionToken, 'Session timeout due to inactivity');
        return { isValid: false, reason: 'انتهت الجلسة بسبب عدم النشاط' };
      }

      // تحديث نشاط الجلسة
      await this.updateSessionActivity(sessionToken);

      return { isValid: true, session: session as UserSession };

    } catch (error) {
      console.error('Error validating session:', error);
      return { isValid: false, reason: 'خطأ في التحقق من الجلسة' };
    }
  }

  /**
   * تطبيق حدود الجلسات المتزامنة
   */
  private async enforceSessionLimits(userId: string): Promise<void> {
    try {
      const activeSessions = await this.getUserActiveSessions(userId);
      
      if (activeSessions.length >= this.DEFAULT_CONFIG.maxConcurrentSessions) {
        // إنهاء أقدم الجلسات
        const sessionsToTerminate = activeSessions
          .sort((a, b) => new Date(a.last_activity).getTime() - new Date(b.last_activity).getTime())
          .slice(0, activeSessions.length - this.DEFAULT_CONFIG.maxConcurrentSessions + 1);

        for (const session of sessionsToTerminate) {
          await this.terminateSession(session.session_token, 'Session limit exceeded');
        }
      }

    } catch (error) {
      console.error('Error enforcing session limits:', error);
    }
  }

  /**
   * تحليل معلومات الجهاز
   */
  private parseDeviceInfo(userAgent: string): UserSession['device_info'] {
    const browser = this.extractBrowser(userAgent);
    const os = this.extractOS(userAgent);
    const deviceType = this.extractDeviceType(userAgent);

    return {
      browser,
      os,
      device_type: deviceType,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    };
  }

  /**
   * استخراج نوع المتصفح
   */
  private extractBrowser(userAgent: string): string {
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    if (userAgent.includes('Opera')) return 'Opera';
    return 'Unknown';
  }

  /**
   * استخراج نظام التشغيل
   */
  private extractOS(userAgent: string): string {
    if (userAgent.includes('Windows NT')) return 'Windows';
    if (userAgent.includes('Mac OS X')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iPhone') || userAgent.includes('iPad')) return 'iOS';
    return 'Unknown';
  }

  /**
   * استخراج نوع الجهاز
   */
  private extractDeviceType(userAgent: string): 'desktop' | 'mobile' | 'tablet' {
    if (userAgent.includes('Mobile') && !userAgent.includes('Tablet')) return 'mobile';
    if (userAgent.includes('Tablet') || userAgent.includes('iPad')) return 'tablet';
    return 'desktop';
  }

  /**
   * الحصول على الموقع من IP (محاكاة)
   */
  private async getLocationFromIP(ipAddress: string): Promise<UserSession['location'] | undefined> {
    try {
      // في التطبيق الحقيقي، استخدم خدمة مثل MaxMind أو IPGeolocation
      // هذه محاكاة بسيطة
      if (ipAddress === '127.0.0.1' || ipAddress === 'localhost') {
        return {
          country: 'Local',
          city: 'Local',
          region: 'Local'
        };
      }

      // محاكاة استدعاء API للموقع الجغرافي
      return {
        country: 'Saudi Arabia',
        city: 'Riyadh',
        region: 'Riyadh Region'
      };

    } catch (error) {
      console.error('Error getting location from IP:', error);
      return undefined;
    }
  }

  /**
   * تنظيف الجلسات المنتهية الصلاحية
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      const { data, error } = await supabase
        .from('user_sessions')
        .update({ is_active: false })
        .lt('expires_at', new Date().toISOString())
        .eq('is_active', true)
        .select();

      if (error) throw error;

      const cleanedCount = data?.length || 0;
      console.log(`Cleaned up ${cleanedCount} expired sessions`);

      return cleanedCount;

    } catch (error) {
      console.error('Error cleaning up expired sessions:', error);
      return 0;
    }
  }

  /**
   * الحصول على إحصائيات الجلسات
   */
  async getSessionStats(): Promise<{
    totalActiveSessions: number;
    uniqueActiveUsers: number;
    averageSessionDuration: number;
    deviceBreakdown: Record<string, number>;
    locationBreakdown: Record<string, number>;
  }> {
    try {
      const { data: sessions, error } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('is_active', true)
        .gt('expires_at', new Date().toISOString());

      if (error) throw error;

      const activeSessions = sessions || [];
      const uniqueUsers = new Set(activeSessions.map(s => s.user_id)).size;
      
      // حساب متوسط مدة الجلسة
      const totalDuration = activeSessions.reduce((sum, session) => {
        const duration = Date.now() - new Date(session.created_at).getTime();
        return sum + duration;
      }, 0);
      const averageDuration = activeSessions.length > 0 ? totalDuration / activeSessions.length : 0;

      // تحليل الأجهزة
      const deviceBreakdown: Record<string, number> = {};
      activeSessions.forEach(session => {
        const device = session.device_info?.device_type || 'unknown';
        deviceBreakdown[device] = (deviceBreakdown[device] || 0) + 1;
      });

      // تحليل المواقع
      const locationBreakdown: Record<string, number> = {};
      activeSessions.forEach(session => {
        const location = session.location?.country || 'unknown';
        locationBreakdown[location] = (locationBreakdown[location] || 0) + 1;
      });

      return {
        totalActiveSessions: activeSessions.length,
        uniqueActiveUsers: uniqueUsers,
        averageSessionDuration: Math.round(averageDuration / (1000 * 60)), // بالدقائق
        deviceBreakdown,
        locationBreakdown
      };

    } catch (error) {
      console.error('Error getting session stats:', error);
      return {
        totalActiveSessions: 0,
        uniqueActiveUsers: 0,
        averageSessionDuration: 0,
        deviceBreakdown: {},
        locationBreakdown: {}
      };
    }
  }

  /**
   * تسجيل حدث أمني
   */
  private async logSecurityEvent(
    userId: string, 
    eventType: string, 
    description: string, 
    metadata: any = {}
  ): Promise<void> {
    try {
      await supabase.rpc('log_security_event', {
        event_type: eventType,
        severity: 'INFO',
        description,
        user_id: userId,
        tenant_id: null,
        metadata
      });
    } catch (error) {
      console.error('Error logging security event:', error);
    }
  }
}
