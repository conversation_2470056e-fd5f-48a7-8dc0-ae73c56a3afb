import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  AlertTriangle,
  Send,
  MapPin,
  Phone,
  Users,
  Clock,
  Zap,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { createEmergencyNotification } from "../../lib/api";
import { cn } from "../../utils/cn";

interface EmergencyNotificationPanelProps {
  className?: string;
  onNotificationSent?: () => void;
}

const emergencyTypes = [
  {
    id: "accident",
    label: "Traffic Accident",
    icon: "🚗",
    color: "bg-red-500",
    description: "Vehicle collision or traffic incident",
  },
  {
    id: "breakdown",
    label: "Bus Breakdown",
    icon: "🔧",
    color: "bg-orange-500",
    description: "Mechanical failure or bus malfunction",
  },
  {
    id: "medical",
    label: "Medical Emergency",
    icon: "🏥",
    color: "bg-red-600",
    description: "Student or driver medical emergency",
  },
  {
    id: "security",
    label: "Security Incident",
    icon: "🚨",
    color: "bg-purple-500",
    description: "Safety or security concern",
  },
  {
    id: "weather",
    label: "Weather Emergency",
    icon: "⛈️",
    color: "bg-blue-500",
    description: "Severe weather conditions",
  },
];

export const EmergencyNotificationPanel: React.FC<
  EmergencyNotificationPanelProps
> = ({ className = "", onNotificationSent }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [selectedType, setSelectedType] = useState<string>("");
  const [title, setTitle] = useState("");
  const [message, setMessage] = useState("");
  const [location, setLocation] = useState<{ lat: number; lng: number } | null>(
    null,
  );
  const [busId, setBusId] = useState("");
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          });
        },
        (error) => {
          console.error("Error getting location:", error);
          setError("Unable to get current location");
        },
      );
    } else {
      setError("Geolocation is not supported by this browser");
    }
  };

  const handleSendEmergency = async () => {
    if (!selectedType || !title || !message || !user?.tenant_id) {
      setError("Please fill in all required fields");
      return;
    }

    setSending(true);
    setError(null);

    try {
      await createEmergencyNotification(
        title,
        message,
        user.tenant_id,
        selectedType as any,
        location || undefined,
        busId || undefined,
      );

      // Reset form
      setSelectedType("");
      setTitle("");
      setMessage("");
      setLocation(null);
      setBusId("");

      onNotificationSent?.();
    } catch (err: any) {
      console.error("Error sending emergency notification:", err);
      setError(err.message || "Failed to send emergency notification");
    } finally {
      setSending(false);
    }
  };

  const selectedEmergencyType = emergencyTypes.find(
    (type) => type.id === selectedType,
  );

  return (
    <div
      className={cn(
        "bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-red-200 dark:border-red-800",
        className,
      )}
    >
      {/* Header */}
      <div className="bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800 p-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-lg">
            <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-red-900 dark:text-red-100">
              Emergency Notification
            </h2>
            <p className="text-sm text-red-700 dark:text-red-300">
              Send immediate alerts to all staff and relevant personnel
            </p>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
            <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
          </div>
        )}

        {/* Emergency Type Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Emergency Type *
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {emergencyTypes.map((type) => (
              <button
                key={type.id}
                onClick={() => setSelectedType(type.id)}
                className={cn(
                  "p-4 rounded-lg border-2 transition-all text-left",
                  selectedType === type.id
                    ? "border-red-500 bg-red-50 dark:bg-red-900/20"
                    : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600",
                )}
              >
                <div className="flex items-center space-x-3">
                  <div
                    className={cn(
                      "w-10 h-10 rounded-lg flex items-center justify-center text-white",
                      type.color,
                    )}
                  >
                    <span className="text-lg">{type.icon}</span>
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900 dark:text-white">
                      {type.label}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {type.description}
                    </p>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Emergency Title *
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Brief description of the emergency"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-red-500 focus:border-red-500"
          />
        </div>

        {/* Message */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Detailed Message *
          </label>
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            rows={4}
            placeholder="Provide detailed information about the emergency, current status, and any immediate actions required..."
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-red-500 focus:border-red-500"
          />
        </div>

        {/* Location and Bus ID */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Location
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                value={
                  location
                    ? `${location.lat.toFixed(6)}, ${location.lng.toFixed(6)}`
                    : ""
                }
                placeholder="Click to get current location"
                readOnly
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-red-500 focus:border-red-500"
              />
              <Button
                type="button"
                variant="outline"
                onClick={getCurrentLocation}
                className="px-3"
              >
                <MapPin size={16} />
              </Button>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Bus ID (if applicable)
            </label>
            <input
              type="text"
              value={busId}
              onChange={(e) => setBusId(e.target.value)}
              placeholder="Enter bus ID if emergency involves a specific bus"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-red-500 focus:border-red-500"
            />
          </div>
        </div>

        {/* Preview */}
        {selectedEmergencyType && title && message && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Notification Preview
            </h4>
            <div className="bg-white dark:bg-gray-800 rounded-md p-3 border border-gray-200 dark:border-gray-600">
              <div className="flex items-start space-x-3">
                <div
                  className={cn(
                    "w-8 h-8 rounded-lg flex items-center justify-center text-white flex-shrink-0",
                    selectedEmergencyType.color,
                  )}
                >
                  <span>{selectedEmergencyType.icon}</span>
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900 dark:text-white text-sm">
                    🚨 EMERGENCY: {title}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {message}
                  </p>
                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                    <span className="flex items-center">
                      <Clock size={12} className="mr-1" />
                      {new Date().toLocaleTimeString()}
                    </span>
                    {location && (
                      <span className="flex items-center">
                        <MapPin size={12} className="mr-1" />
                        Location included
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Recipients Info */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
              This emergency notification will be sent to:
            </span>
          </div>
          <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
            <li>• All administrators and school managers</li>
            <li>• Transport supervisors</li>
            <li>• All active drivers</li>
            <li>• Parents (if student-related emergency)</li>
          </ul>
        </div>

        {/* Send Button */}
        <div className="flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={() => {
              setSelectedType("");
              setTitle("");
              setMessage("");
              setLocation(null);
              setBusId("");
              setError(null);
            }}
            disabled={sending}
          >
            Clear
          </Button>
          <Button
            onClick={handleSendEmergency}
            disabled={!selectedType || !title || !message || sending}
            className="bg-red-600 hover:bg-red-700 text-white"
            leftIcon={
              sending ? (
                <Zap className="animate-spin" size={16} />
              ) : (
                <Send size={16} />
              )
            }
          >
            {sending ? "Sending Emergency..." : "Send Emergency Alert"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default EmergencyNotificationPanel;
