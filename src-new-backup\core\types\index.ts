export type User = {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar_url?: string;
  tenant_id?: string;
  phone?: string;
  is_active: boolean;
  metadata?: any;
  created_at: string;
  updated_at: string;
};

export enum UserRole {
  ADMIN = "admin",
  SCHOOL_MANAGER = "school_manager",
  SUPERVISOR = "supervisor",
  DRIVER = "driver",
  PARENT = "parent",
  STUDENT = "student",
}

export type Tenant = {
  id: string;
  name: string;
  logo_url?: string;
  address?: string;
  contact_number?: string;
  domain?: string;
  is_active: boolean;
  settings: {
    theme?: {
      primaryColor?: string;
      logo?: string;
      favicon?: string;
    };
    features?: {
      gpsTracking?: boolean;
      notifications?: boolean;
      maintenance?: boolean;
      reports?: boolean;
      biometric?: boolean;
    };
    branding?: {
      schoolName?: string;
      tagline?: string;
      colors?: {
        primary?: string;
        secondary?: string;
        accent?: string;
      };
    };
  };
  created_at: string;
  updated_at: string;
};

export type Bus = {
  id: string;
  plate_number: string;
  capacity: number;
  tenant_id: string;
  driver_id?: string;
  is_active: boolean;
  last_location?: {
    type: string;
    coordinates: [number, number];
  };
  last_updated?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
};

export type Route = {
  id: string;
  name: string;
  tenant_id: string;
  bus_id?: string;
  is_active: boolean;
  schedule?: any;
  created_at: string;
  updated_at: string;
  stops?: RouteStop[];
};

export type RouteStop = {
  id: string;
  name: string;
  route_id: string;
  order: number;
  location: {
    type: string;
    coordinates: [number, number];
  };
  arrival_time?: string;
  created_at: string;
  updated_at: string;
};

export type Student = {
  id: string;
  name: string;
  tenant_id: string;
  grade: string;
  parent_id?: string;
  route_stop_id?: string;
  is_active: boolean;
  photo_url?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
};

export type Attendance = {
  id: string;
  student_id: string;
  bus_id: string;
  tenant_id: string;
  type: "pickup" | "dropoff";
  recorded_at: string;
  recorded_by: string;
  location: {
    type: string;
    coordinates: [number, number];
  };
  created_at: string;
};

export type Notification = {
  id: string;
  user_id: string;
  tenant_id?: string;
  title: string;
  message: string;
  type?: string;
  priority?: "low" | "normal" | "high";
  read?: boolean;
  metadata?: any;
  created_at?: string;
  updated_at?: string;
};

export type TenantBranding = {
  schoolName: string;
  logo?: string;
  primaryColor: string;
  secondaryColor?: string;
  favicon?: string;
  tagline?: string;
};

export type TenantFeatures = {
  gpsTracking: boolean;
  notifications: boolean;
  maintenance: boolean;
  reports: boolean;
  biometric: boolean;
  emergencyMode: boolean;
  routeOptimization: boolean;
  parentApp: boolean;
};
