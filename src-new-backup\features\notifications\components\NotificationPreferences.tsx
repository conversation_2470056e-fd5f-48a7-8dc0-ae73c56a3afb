import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Bell,
  Mail,
  Smartphone,
  Volume2,
  VolumeX,
  Settings,
  Save,
  TestTube,
  Send,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { supabase } from "../../lib/supabase";
import { PushNotificationToggle } from "./PushNotificationToggle";

interface NotificationPreferencesProps {
  className?: string;
}

interface NotificationSettings {
  email: {
    enabled: boolean;
    geofence: boolean;
    attendance: boolean;
    maintenance: boolean;
    announcements: boolean;
  };
  push: {
    enabled: boolean;
    geofence: boolean;
    attendance: boolean;
    maintenance: boolean;
    announcements: boolean;
  };
  sounds: {
    enabled: boolean;
    volume: number;
    geofence: string;
    attendance: string;
    maintenance: string;
    announcements: string;
  };
  grouping: {
    enabled: boolean;
    timeWindow: number;
    maxGroupSize: number;
  };
}

const defaultSettings: NotificationSettings = {
  email: {
    enabled: true,
    geofence: true,
    attendance: true,
    maintenance: true,
    announcements: true,
  },
  push: {
    enabled: true,
    geofence: true,
    attendance: true,
    maintenance: true,
    announcements: false,
  },
  sounds: {
    enabled: true,
    volume: 0.7,
    geofence: "bell",
    attendance: "chime",
    maintenance: "alert",
    announcements: "notification",
  },
  grouping: {
    enabled: true,
    timeWindow: 5,
    maxGroupSize: 5,
  },
};

const soundOptions = [
  { value: "none", label: "None" },
  { value: "bell", label: "Bell" },
  { value: "chime", label: "Chime" },
  { value: "ding", label: "Ding" },
  { value: "alert", label: "Alert" },
  { value: "notification", label: "Notification" },
];

export const NotificationPreferences: React.FC<
  NotificationPreferencesProps
> = ({ className = "" }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [settings, setSettings] =
    useState<NotificationSettings>(defaultSettings);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);
  const [pushPermission, setPushPermission] =
    useState<NotificationPermission>("default");
  const [testNotificationStatus, setTestNotificationStatus] = useState<{
    type: "success" | "error" | null;
    message: string;
  }>({ type: null, message: "" });
  const [sendingTest, setSendingTest] = useState(false);
  const [advancedSettings, setAdvancedSettings] = useState({
    quietHours: {
      enabled: false,
      start: "22:00",
      end: "07:00",
    },
    locationBased: {
      enabled: false,
      homeRadius: 1000, // meters
    },
    emergencyOverride: true,
  });

  useEffect(() => {
    loadSettings();
    checkPushPermission();
  }, [user]);

  const loadSettings = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("users")
        .select("metadata")
        .eq("id", user.id)
        .single();

      if (error) throw error;

      const userSettings = data?.metadata?.notificationPreferences;
      const userAdvancedSettings = data?.metadata?.advancedNotificationSettings;

      if (userSettings) {
        setSettings({ ...defaultSettings, ...userSettings });
      }

      if (userAdvancedSettings) {
        setAdvancedSettings({ ...advancedSettings, ...userAdvancedSettings });
      }
    } catch (error) {
      console.error("Error loading notification preferences:", error);
    } finally {
      setLoading(false);
    }
  };

  const checkPushPermission = () => {
    if ("Notification" in window) {
      setPushPermission(Notification.permission);
    }
  };

  const requestPushPermission = async () => {
    if ("Notification" in window) {
      const permission = await Notification.requestPermission();
      setPushPermission(permission);
      if (permission === "granted") {
        setSettings((prev) => ({
          ...prev,
          push: { ...prev.push, enabled: true },
        }));
      }
    }
  };

  const handlePushNotificationStatusChange = (enabled: boolean) => {
    setSettings((prev) => ({
      ...prev,
      push: { ...prev.push, enabled },
    }));
  };

  const sendTestNotification = async (type: string) => {
    if (!user) return;

    try {
      setSendingTest(true);
      setTestNotificationStatus({ type: null, message: "" });

      // Check if push notifications are enabled and permission is granted first
      if (!settings.push.enabled || pushPermission !== "granted") {
        setTestNotificationStatus({
          type: "error",
          message:
            "Push notifications are not enabled or permission not granted. Please enable them first.",
        });
        return;
      }

      const testPayloads = {
        geofence: {
          title: "🚌 Bus Location Update",
          body: "Bus #123 has arrived at Main Street Stop",
          icon: "/bus-icon.svg",
          badge: "/bus-icon.svg",
          tag: "geofence-test",
          data: {
            type: "geofence",
            busId: "test-bus-123",
            stopId: "test-stop-456",
            action: "enter",
            url: "/tracking?bus=test-bus-123",
            timestamp: Date.now(),
          },
          actions: [
            {
              action: "view_location",
              title: "View Location",
              icon: "/bus-icon.svg",
            },
            {
              action: "dismiss",
              title: "Dismiss",
            },
          ],
          requireInteraction: true,
        },
        attendance: {
          title: "👥 Student Attendance",
          body: "John Doe has been picked up at Main Street Stop",
          icon: "/bus-icon.svg",
          badge: "/bus-icon.svg",
          tag: "attendance-test",
          data: {
            type: "attendance",
            studentId: "test-student-789",
            studentName: "John Doe",
            busId: "test-bus-123",
            action: "pickup",
            url: "/attendance?student=test-student-789",
            timestamp: Date.now(),
          },
          actions: [
            {
              action: "view_attendance",
              title: "View Details",
              icon: "/bus-icon.svg",
            },
            {
              action: "dismiss",
              title: "OK",
            },
          ],
        },
        maintenance: {
          title: "🔧 Maintenance Alert",
          body: "Bus #123 is due for scheduled maintenance in 2 days",
          icon: "/bus-icon.svg",
          badge: "/bus-icon.svg",
          tag: "maintenance-test",
          data: {
            type: "maintenance",
            busId: "test-bus-123",
            maintenanceType: "routine",
            dueDate: new Date(
              Date.now() + 2 * 24 * 60 * 60 * 1000,
            ).toISOString(),
            url: "/buses/test-bus-123?tab=maintenance",
            timestamp: Date.now(),
          },
          actions: [
            {
              action: "view_maintenance",
              title: "View Details",
              icon: "/bus-icon.svg",
            },
            {
              action: "schedule",
              title: "Schedule",
            },
            {
              action: "dismiss",
              title: "Later",
            },
          ],
          requireInteraction: true,
        },
        announcement: {
          title: "📢 School Announcement",
          body: "School will be closed tomorrow due to weather conditions",
          icon: "/bus-icon.svg",
          badge: "/bus-icon.svg",
          tag: "announcement-test",
          data: {
            type: "announcement",
            announcementId: "test-announcement-101",
            priority: "high",
            url: "/notifications?id=test-announcement-101",
            timestamp: Date.now(),
          },
          actions: [
            {
              action: "read_more",
              title: "Read More",
              icon: "/bus-icon.svg",
            },
            {
              action: "dismiss",
              title: "Dismiss",
            },
          ],
        },
      };

      const payload = testPayloads[type as keyof typeof testPayloads];
      if (!payload) {
        throw new Error("Invalid notification type");
      }

      // Send test notification via service worker
      if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: "TEST_NOTIFICATION",
          payload,
        });
      } else {
        throw new Error("Service worker not available");
      }

      // Also log to notification history for testing
      try {
        await supabase.from("notifications").insert({
          user_id: user.id,
          tenant_id: user.tenant_id,
          title: payload.title,
          message: payload.body,
          metadata: {
            isTest: true,
            testType: type,
            type: type,
            priority: "normal",
            ...payload.data,
          },
        });
      } catch (dbError) {
        console.warn("Failed to log test notification to database:", dbError);
      }

      setTestNotificationStatus({
        type: "success",
        message: `Test ${type} notification sent successfully! Check your notifications.`,
      });

      // Clear status after 5 seconds
      setTimeout(() => {
        setTestNotificationStatus({ type: null, message: "" });
      }, 5000);
    } catch (error: any) {
      console.error("Error sending test notification:", error);
      setTestNotificationStatus({
        type: "error",
        message: error.message || "Failed to send test notification",
      });

      // Clear error after 5 seconds
      setTimeout(() => {
        setTestNotificationStatus({ type: null, message: "" });
      }, 5000);
    } finally {
      setSendingTest(false);
    }
  };

  const saveSettings = async () => {
    if (!user) return;

    try {
      setSaving(true);
      setMessage(null);

      const { error } = await supabase
        .from("users")
        .update({
          metadata: {
            ...user.metadata,
            notificationPreferences: settings,
            advancedNotificationSettings: advancedSettings,
          },
        })
        .eq("id", user.id);

      if (error) throw error;

      setMessage({
        type: "success",
        text: t("notifications.preferencesSaved"),
      });

      // Clear message after 3 seconds
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      console.error("Error saving notification preferences:", error);
      setMessage({
        type: "error",
        text: t("notifications.preferencesError"),
      });
    } finally {
      setSaving(false);
    }
  };

  const testSound = (soundType: string) => {
    if (soundType === "none") return;

    try {
      const audio = new Audio(`/sounds/${soundType}.mp3`);
      audio.volume = settings.sounds.volume;
      audio.play().catch(() => {
        console.warn(`Could not play sound: ${soundType}`);
      });
    } catch (error) {
      console.error("Error playing test sound:", error);
    }
  };

  const updateEmailSetting = (
    key: keyof NotificationSettings["email"],
    value: boolean,
  ) => {
    setSettings((prev) => ({
      ...prev,
      email: { ...prev.email, [key]: value },
    }));
  };

  const updatePushSetting = (
    key: keyof NotificationSettings["push"],
    value: boolean,
  ) => {
    setSettings((prev) => ({
      ...prev,
      push: { ...prev.push, [key]: value },
    }));
  };

  const updateSoundSetting = (
    key: keyof NotificationSettings["sounds"],
    value: string | number | boolean,
  ) => {
    setSettings((prev) => ({
      ...prev,
      sounds: { ...prev.sounds, [key]: value },
    }));
  };

  const updateGroupingSetting = (
    key: keyof NotificationSettings["grouping"],
    value: boolean | number,
  ) => {
    setSettings((prev) => ({
      ...prev,
      grouping: { ...prev.grouping, [key]: value },
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
          <Settings size={20} className="mr-2" />
          {t("notifications.preferences")}
        </h2>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          {t("notifications.preferencesDescription")}
        </p>
      </div>

      {/* Success/Error Message */}
      {message && (
        <div
          className={`p-4 rounded-md ${
            message.type === "success"
              ? "bg-green-50 text-green-800 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
              : "bg-red-50 text-red-800 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800"
          }`}
        >
          {message.text}
        </div>
      )}

      {/* Email Notifications */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center mb-4">
          <Mail size={20} className="mr-2 text-blue-600 dark:text-blue-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t("notifications.emailNotifications")}
          </h3>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("notifications.enablePush")}
            </span>
            <input
              type="checkbox"
              checked={settings.email.enabled}
              onChange={(e) => updateEmailSetting("enabled", e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          {settings.email.enabled && (
            <div className="ml-4 space-y-3">
              {[
                { key: "geofence", label: t("notifications.geofence") },
                { key: "attendance", label: t("notifications.attendance") },
                { key: "maintenance", label: t("notifications.maintenance") },
                {
                  key: "announcements",
                  label: t("notifications.announcements"),
                },
              ].map(({ key, label }) => (
                <div key={key} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {label}
                  </span>
                  <input
                    type="checkbox"
                    checked={
                      settings.email[
                        key as keyof typeof settings.email
                      ] as boolean
                    }
                    onChange={(e) =>
                      updateEmailSetting(
                        key as keyof typeof settings.email,
                        e.target.checked,
                      )
                    }
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Push Notifications */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center mb-4">
          <Smartphone
            size={20}
            className="mr-2 text-green-600 dark:text-green-400"
          />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t("notifications.pushNotifications")}
          </h3>
        </div>

        <div className="space-y-4">
          {/* Push Notification Toggle */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("notifications.enablePush")}
            </span>
            <PushNotificationToggle
              variant="switch"
              showLabel={false}
              onStatusChange={handlePushNotificationStatusChange}
            />
          </div>

          {settings.push.enabled && pushPermission === "granted" && (
            <div className="ml-4 space-y-3">
              {[
                { key: "geofence", label: t("notifications.geofence") },
                { key: "attendance", label: t("notifications.attendance") },
                { key: "maintenance", label: t("notifications.maintenance") },
                {
                  key: "announcements",
                  label: t("notifications.announcements"),
                },
              ].map(({ key, label }) => (
                <div key={key} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {label}
                  </span>
                  <input
                    type="checkbox"
                    checked={
                      settings.push[
                        key as keyof typeof settings.push
                      ] as boolean
                    }
                    onChange={(e) =>
                      updatePushSetting(
                        key as keyof typeof settings.push,
                        e.target.checked,
                      )
                    }
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Sound Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center mb-4">
          {settings.sounds.enabled ? (
            <Volume2
              size={20}
              className="mr-2 text-purple-600 dark:text-purple-400"
            />
          ) : (
            <VolumeX size={20} className="mr-2 text-gray-400" />
          )}
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t("notifications.soundSettings")}
          </h3>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Enable Sounds
            </span>
            <input
              type="checkbox"
              checked={settings.sounds.enabled}
              onChange={(e) => updateSoundSetting("enabled", e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          {settings.sounds.enabled && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t("notifications.volume")}:{" "}
                  {Math.round(settings.sounds.volume * 100)}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={settings.sounds.volume}
                  onChange={(e) =>
                    updateSoundSetting("volume", parseFloat(e.target.value))
                  }
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>

              {[
                { key: "geofence", label: t("notifications.geofence") },
                { key: "attendance", label: t("notifications.attendance") },
                { key: "maintenance", label: t("notifications.maintenance") },
                {
                  key: "announcements",
                  label: t("notifications.announcements"),
                },
              ].map(({ key, label }) => (
                <div key={key} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {label}
                  </span>
                  <div className="flex items-center space-x-2">
                    <select
                      value={
                        settings.sounds[
                          key as keyof typeof settings.sounds
                        ] as string
                      }
                      onChange={(e) =>
                        updateSoundSetting(
                          key as keyof typeof settings.sounds,
                          e.target.value,
                        )
                      }
                      className="text-sm border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white"
                    >
                      {soundOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() =>
                        testSound(
                          settings.sounds[
                            key as keyof typeof settings.sounds
                          ] as string,
                        )
                      }
                      disabled={
                        settings.sounds[key as keyof typeof settings.sounds] ===
                        "none"
                      }
                    >
                      <TestTube size={14} />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Notification Grouping */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center mb-4">
          <Bell
            size={20}
            className="mr-2 text-orange-600 dark:text-orange-400"
          />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t("notifications.grouping")}
          </h3>
        </div>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
          {t("notifications.groupingDescription")}
        </p>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Enable Grouping
            </span>
            <input
              type="checkbox"
              checked={settings.grouping.enabled}
              onChange={(e) =>
                updateGroupingSetting("enabled", e.target.checked)
              }
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          {settings.grouping.enabled && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t("notifications.timeWindow")}:{" "}
                  {settings.grouping.timeWindow} minutes
                </label>
                <input
                  type="range"
                  min="1"
                  max="30"
                  step="1"
                  value={settings.grouping.timeWindow}
                  onChange={(e) =>
                    updateGroupingSetting(
                      "timeWindow",
                      parseInt(e.target.value),
                    )
                  }
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t("notifications.maxGroupSize")}:{" "}
                  {settings.grouping.maxGroupSize}
                </label>
                <input
                  type="range"
                  min="2"
                  max="10"
                  step="1"
                  value={settings.grouping.maxGroupSize}
                  onChange={(e) =>
                    updateGroupingSetting(
                      "maxGroupSize",
                      parseInt(e.target.value),
                    )
                  }
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Notification Testing */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center mb-4">
          <TestTube
            size={20}
            className="mr-2 text-purple-600 dark:text-purple-400"
          />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Test Notifications
          </h3>
        </div>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
          Send test notifications to verify your settings are working correctly.
        </p>

        {/* Test Status Message */}
        {testNotificationStatus.type && (
          <div
            className={`mb-4 p-3 rounded-md flex items-center space-x-2 ${
              testNotificationStatus.type === "success"
                ? "bg-green-50 text-green-800 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
                : "bg-red-50 text-red-800 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800"
            }`}
          >
            {testNotificationStatus.type === "success" ? (
              <CheckCircle size={16} />
            ) : (
              <AlertCircle size={16} />
            )}
            <span className="text-sm">{testNotificationStatus.message}</span>
          </div>
        )}

        <div className="grid grid-cols-2 gap-3">
          {[
            { key: "geofence", label: "Bus Location", icon: "🚌" },
            { key: "attendance", label: "Attendance", icon: "👥" },
            { key: "maintenance", label: "Maintenance", icon: "🔧" },
            { key: "announcement", label: "Announcement", icon: "📢" },
          ].map(({ key, label, icon }) => (
            <Button
              key={key}
              variant="outline"
              size="sm"
              onClick={() => sendTestNotification(key)}
              disabled={
                sendingTest ||
                !settings.push.enabled ||
                pushPermission !== "granted"
              }
              className="flex items-center space-x-2 justify-start"
            >
              <span className="text-lg">{icon}</span>
              <span>Test {label}</span>
              {sendingTest && (
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-auto" />
              )}
            </Button>
          ))}
        </div>

        {/* Advanced Testing */}
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Advanced Testing
          </h4>
          <div className="grid grid-cols-1 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={async () => {
                try {
                  setSendingTest(true);
                  const emergencyPayload = {
                    title: "🚨 Emergency Alert",
                    body: "This is a test emergency notification",
                    icon: "/bus-icon.svg",
                    badge: "/bus-icon.svg",
                    tag: "emergency-test",
                    data: {
                      type: "emergency",
                      priority: "high",
                      timestamp: Date.now(),
                    },
                    requireInteraction: true,
                  };

                  if (
                    "serviceWorker" in navigator &&
                    navigator.serviceWorker.controller
                  ) {
                    navigator.serviceWorker.controller.postMessage({
                      type: "TEST_NOTIFICATION",
                      payload: emergencyPayload,
                    });
                    setTestNotificationStatus({
                      type: "success",
                      message: "Emergency test notification sent!",
                    });
                  } else {
                    throw new Error("Service worker not available");
                  }
                } catch (error: any) {
                  setTestNotificationStatus({
                    type: "error",
                    message: error.message,
                  });
                } finally {
                  setSendingTest(false);
                  setTimeout(() => {
                    setTestNotificationStatus({ type: null, message: "" });
                  }, 3000);
                }
              }}
              disabled={
                sendingTest ||
                !settings.push.enabled ||
                pushPermission !== "granted"
              }
              className="flex items-center space-x-2 justify-start text-red-600 border-red-300 hover:bg-red-50"
            >
              <span className="text-lg">🚨</span>
              <span>Test Emergency Alert</span>
              {sendingTest && (
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-auto" />
              )}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={async () => {
                try {
                  setSendingTest(true);
                  if ("serviceWorker" in navigator) {
                    const registration = await navigator.serviceWorker.ready;
                    const subscription =
                      await registration.pushManager.getSubscription();
                    if (subscription) {
                      setTestNotificationStatus({
                        type: "success",
                        message: `Subscription active. Endpoint: ${subscription.endpoint.substring(0, 50)}...`,
                      });
                    } else {
                      setTestNotificationStatus({
                        type: "error",
                        message: "No active subscription found",
                      });
                    }
                  } else {
                    setTestNotificationStatus({
                      type: "error",
                      message: "Service worker not supported",
                    });
                  }
                } catch (error: any) {
                  setTestNotificationStatus({
                    type: "error",
                    message: error.message,
                  });
                } finally {
                  setSendingTest(false);
                  setTimeout(() => {
                    setTestNotificationStatus({ type: null, message: "" });
                  }, 3000);
                }
              }}
              disabled={sendingTest}
              className="flex items-center space-x-2 justify-start"
            >
              <span className="text-lg">🔍</span>
              <span>Check Subscription Status</span>
              {sendingTest && (
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-auto" />
              )}
            </Button>
          </div>
        </div>

        {(!settings.push.enabled || pushPermission !== "granted") && (
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
            Enable push notifications to test them
          </p>
        )}
      </div>

      {/* Advanced Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center mb-4">
          <Settings
            size={20}
            className="mr-2 text-indigo-600 dark:text-indigo-400"
          />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Advanced Settings
          </h3>
        </div>

        <div className="space-y-6">
          {/* Quiet Hours */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Quiet Hours
              </span>
              <input
                type="checkbox"
                checked={advancedSettings.quietHours.enabled}
                onChange={(e) =>
                  setAdvancedSettings((prev) => ({
                    ...prev,
                    quietHours: {
                      ...prev.quietHours,
                      enabled: e.target.checked,
                    },
                  }))
                }
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>
            {advancedSettings.quietHours.enabled && (
              <div className="ml-4 grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Start Time
                  </label>
                  <input
                    type="time"
                    value={advancedSettings.quietHours.start}
                    onChange={(e) =>
                      setAdvancedSettings((prev) => ({
                        ...prev,
                        quietHours: {
                          ...prev.quietHours,
                          start: e.target.value,
                        },
                      }))
                    }
                    className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    End Time
                  </label>
                  <input
                    type="time"
                    value={advancedSettings.quietHours.end}
                    onChange={(e) =>
                      setAdvancedSettings((prev) => ({
                        ...prev,
                        quietHours: { ...prev.quietHours, end: e.target.value },
                      }))
                    }
                    className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Location-Based Notifications */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Location-Based Notifications
              </span>
              <input
                type="checkbox"
                checked={advancedSettings.locationBased.enabled}
                onChange={(e) =>
                  setAdvancedSettings((prev) => ({
                    ...prev,
                    locationBased: {
                      ...prev.locationBased,
                      enabled: e.target.checked,
                    },
                  }))
                }
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>
            {advancedSettings.locationBased.enabled && (
              <div className="ml-4">
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Home Radius (meters)
                </label>
                <input
                  type="number"
                  value={advancedSettings.locationBased.homeRadius}
                  onChange={(e) =>
                    setAdvancedSettings((prev) => ({
                      ...prev,
                      locationBased: {
                        ...prev.locationBased,
                        homeRadius: parseInt(e.target.value) || 1000,
                      },
                    }))
                  }
                  min="100"
                  max="5000"
                  step="100"
                  className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                />
              </div>
            )}
          </div>

          {/* Emergency Override */}
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Emergency Override
              </span>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Always receive emergency notifications regardless of other
                settings
              </p>
            </div>
            <input
              type="checkbox"
              checked={advancedSettings.emergencyOverride}
              onChange={(e) =>
                setAdvancedSettings((prev) => ({
                  ...prev,
                  emergencyOverride: e.target.checked,
                }))
              }
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={saveSettings}
          disabled={saving}
          leftIcon={<Save size={16} />}
        >
          {saving ? t("common.saving") : t("settings.saveSettings")}
        </Button>
      </div>
    </div>
  );
};

export default NotificationPreferences;
