/**
 * Theme Routes Configuration
 * Routes for theme management functionality
 * Phase 3: UI/UX Enhancement - Routing
 */

import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useThemePermissions } from '../hooks/useThemePermissions';
import { UserRole } from '../types';

// Pages
import ThemeManagementPage from '../pages/admin/ThemeManagementPage';
import ThemeSettingsPage from '../pages/school/ThemeSettingsPage';

// Components
import { Alert, AlertDescription } from '../components/common/ui/Alert';
import { AlertTriangle } from 'lucide-react';

/**
 * Protected Route Component for Theme Access
 */
interface ProtectedThemeRouteProps {
  children: React.ReactNode;
  requiredPermission: 'admin' | 'school';
}

const ProtectedThemeRoute: React.FC<ProtectedThemeRouteProps> = ({
  children,
  requiredPermission,
}) => {
  const { user } = useAuth();
  const permissions = useThemePermissions();

  // Check if user is authenticated
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Check permissions based on requirement
  const hasPermission = 
    requiredPermission === 'admin' 
      ? permissions.canManageAllThemes
      : permissions.canManageOwnTheme;

  if (!hasPermission) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              ليس لديك صلاحية للوصول إلى هذه الصفحة. يرجى التواصل مع مدير النظام.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

/**
 * Theme Routes Component
 */
export const ThemeRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Admin Theme Management */}
      <Route
        path="/admin/themes"
        element={
          <ProtectedThemeRoute requiredPermission="admin">
            <ThemeManagementPage />
          </ProtectedThemeRoute>
        }
      />

      {/* School Theme Settings */}
      <Route
        path="/school/theme"
        element={
          <ProtectedThemeRoute requiredPermission="school">
            <ThemeSettingsPage />
          </ProtectedThemeRoute>
        }
      />

      {/* Redirect based on user role */}
      <Route
        path="/themes"
        element={<ThemeRedirect />}
      />
    </Routes>
  );
};

/**
 * Theme Redirect Component
 * Redirects users to appropriate theme page based on their role
 */
const ThemeRedirect: React.FC = () => {
  const { user } = useAuth();
  const permissions = useThemePermissions();

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Redirect based on permissions
  if (permissions.canManageAllThemes) {
    return <Navigate to="/admin/themes" replace />;
  } else if (permissions.canManageOwnTheme) {
    return <Navigate to="/school/theme" replace />;
  } else {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              ليس لديك صلاحية لإدارة الثيمات.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }
};

export default ThemeRoutes;
