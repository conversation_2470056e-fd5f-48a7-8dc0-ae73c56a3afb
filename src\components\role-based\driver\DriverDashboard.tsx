/**
 * Driver Dashboard Component
 * Specialized dashboard for bus drivers
 * Phase 2: Application Structure Reorganization
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { busService } from '../../../services/data/BusService';
import { routeService } from '../../../services/data/RouteService';
import { attendanceService } from '../../../services/data/AttendanceService';
import { Bus, Route } from '../../../api/types';

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '../../common/ui/Card';
import { Button } from '../../common/ui/Button';
import { Badge } from '../../common/ui/Badge';
import { Alert, AlertDescription } from '../../common/ui/Alert';

// Icons
import { 
  Bus as BusIcon, 
  MapPin, 
  Users, 
  Clock, 
  Navigation,
  CheckCircle,
  AlertTriangle,
  Play,
  Square
} from 'lucide-react';

interface DriverDashboardState {
  myBuses: Bus[];
  myRoutes: Route[];
  todayAttendance: any[];
  currentTrip: any | null;
  loading: boolean;
  error: string | null;
}

/**
 * Driver Dashboard Component
 * Provides essential information and controls for bus drivers
 */
export const DriverDashboard: React.FC = () => {
  const { user } = useAuth();
  const [state, setState] = useState<DriverDashboardState>({
    myBuses: [],
    myRoutes: [],
    todayAttendance: [],
    currentTrip: null,
    loading: true,
    error: null,
  });

  /**
   * Load driver dashboard data
   */
  useEffect(() => {
    const loadDriverData = async () => {
      try {
        setState(prev => ({ ...prev, loading: true, error: null }));

        // Load driver's buses and routes
        const [busesResponse, routesResponse, attendanceResponse] = await Promise.all([
          busService.getMyBuses(),
          routeService.getMyRoutes(),
          attendanceService.getTodayAttendanceForDriver(),
        ]);

        if (busesResponse.success && routesResponse.success && attendanceResponse.success) {
          setState(prev => ({
            ...prev,
            myBuses: busesResponse.data!,
            myRoutes: routesResponse.data!,
            todayAttendance: attendanceResponse.data!,
            loading: false,
          }));
        } else {
          setState(prev => ({
            ...prev,
            error: 'Failed to load dashboard data',
            loading: false,
          }));
        }
      } catch (error) {
        setState(prev => ({
          ...prev,
          error: 'An unexpected error occurred',
          loading: false,
        }));
      }
    };

    loadDriverData();
  }, []);

  /**
   * Start route trip
   */
  const handleStartTrip = async (routeId: string, tripType: 'morning' | 'afternoon') => {
    try {
      const response = await routeService.startTrip(routeId, tripType);
      if (response.success) {
        setState(prev => ({
          ...prev,
          currentTrip: response.data,
        }));
      }
    } catch (error) {
      console.error('Failed to start trip:', error);
    }
  };

  /**
   * End route trip
   */
  const handleEndTrip = async () => {
    if (!state.currentTrip) return;

    try {
      const response = await routeService.endTrip(state.currentTrip.route_id, state.currentTrip.id);
      if (response.success) {
        setState(prev => ({
          ...prev,
          currentTrip: null,
        }));
      }
    } catch (error) {
      console.error('Failed to end trip:', error);
    }
  };

  /**
   * Get current time period
   */
  const getCurrentTimePeriod = (): 'morning' | 'afternoon' | 'off_hours' => {
    const hour = new Date().getHours();
    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 18) return 'afternoon';
    return 'off_hours';
  };

  if (state.loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (state.error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{state.error}</AlertDescription>
      </Alert>
    );
  }

  const currentPeriod = getCurrentTimePeriod();
  const activeBus = state.myBuses.find(bus => bus.is_active);
  const activeRoute = state.myRoutes.find(route => route.is_active);

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-green-600 to-green-800 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold">Good {currentPeriod === 'morning' ? 'Morning' : 'Afternoon'}, {user?.name}!</h1>
        <p className="mt-2 opacity-90">
          Ready for today's routes? Stay safe and have a great day!
        </p>
      </div>

      {/* Current Trip Status */}
      {state.currentTrip ? (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-green-800">
              <Navigation className="w-5 h-5" />
              <span>Trip in Progress</span>
              <Badge variant="default">ACTIVE</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-600">Route</p>
                <p className="font-medium">{activeRoute?.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Started</p>
                <p className="font-medium">
                  {new Date(state.currentTrip.started_at).toLocaleTimeString()}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Progress</p>
                <p className="font-medium">
                  {state.currentTrip.stops_completed} / {state.currentTrip.total_stops} stops
                </p>
              </div>
            </div>
            <div className="mt-4">
              <Button onClick={handleEndTrip} variant="destructive">
                <Square className="w-4 h-4 mr-2" />
                End Trip
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="w-5 h-5" />
              <span>Trip Control</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {activeRoute ? (
              <div className="space-y-4">
                <p className="text-gray-600">Ready to start your route?</p>
                <div className="flex space-x-2">
                  <Button 
                    onClick={() => handleStartTrip(activeRoute.id, 'morning')}
                    disabled={currentPeriod !== 'morning'}
                  >
                    <Play className="w-4 h-4 mr-2" />
                    Start Morning Route
                  </Button>
                  <Button 
                    onClick={() => handleStartTrip(activeRoute.id, 'afternoon')}
                    disabled={currentPeriod !== 'afternoon'}
                    variant="outline"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    Start Afternoon Route
                  </Button>
                </div>
              </div>
            ) : (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  No active route assigned. Please contact your supervisor.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Bus and Route Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* My Bus */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BusIcon className="w-5 h-5" />
              <span>My Bus</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {activeBus ? (
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="font-medium">{activeBus.plate_number}</span>
                  <Badge variant={activeBus.status === 'idle' ? 'secondary' : 'default'}>
                    {activeBus.status.replace('_', ' ').toUpperCase()}
                  </Badge>
                </div>
                <div className="text-sm text-gray-600">
                  <p>{activeBus.model} ({activeBus.year})</p>
                  <p>Capacity: {activeBus.capacity} seats</p>
                </div>
                {activeBus.location && (
                  <div className="text-sm">
                    <p className="text-gray-600">Last Location:</p>
                    <p>Lat: {activeBus.location.latitude.toFixed(4)}, Lng: {activeBus.location.longitude.toFixed(4)}</p>
                  </div>
                )}
              </div>
            ) : (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  No bus assigned. Please contact your supervisor.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* My Route */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="w-5 h-5" />
              <span>My Route</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {activeRoute ? (
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="font-medium">{activeRoute.name}</span>
                  <Badge variant="default">ASSIGNED</Badge>
                </div>
                <div className="text-sm text-gray-600">
                  <p>{activeRoute.stops.length} stops</p>
                  <p>Est. Duration: {Math.round(activeRoute.estimated_duration / 60)} minutes</p>
                  <p>Distance: {(activeRoute.distance / 1000).toFixed(1)} km</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Schedule:</p>
                  <p className="text-xs text-gray-600">
                    Morning: {activeRoute.schedule.morning_start} - {activeRoute.schedule.morning_end}
                  </p>
                  <p className="text-xs text-gray-600">
                    Afternoon: {activeRoute.schedule.afternoon_start} - {activeRoute.schedule.afternoon_end}
                  </p>
                </div>
              </div>
            ) : (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  No route assigned. Please contact your supervisor.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Today's Attendance Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="w-5 h-5" />
            <span>Today's Attendance</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {state.todayAttendance.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {state.todayAttendance.filter(a => a.pickup_status === 'present').length}
                </div>
                <div className="text-sm text-gray-600">Students Picked Up</div>
              </div>
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {state.todayAttendance.filter(a => a.dropoff_status === 'present').length}
                </div>
                <div className="text-sm text-gray-600">Students Dropped Off</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">
                  {state.todayAttendance.filter(a => a.pickup_status === 'absent').length}
                </div>
                <div className="text-sm text-gray-600">Absent Students</div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No attendance records for today</p>
              <p className="text-sm">Records will appear when you start taking attendance</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button className="h-16 flex flex-col space-y-2" disabled={!activeRoute}>
              <MapPin className="w-6 h-6" />
              <span>View Route Map</span>
            </Button>
            <Button variant="outline" className="h-16 flex flex-col space-y-2">
              <Users className="w-6 h-6" />
              <span>Take Attendance</span>
            </Button>
            <Button variant="outline" className="h-16 flex flex-col space-y-2">
              <AlertTriangle className="w-6 h-6" />
              <span>Report Issue</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
