import React, { createContext, useContext, useState, useEffect } from "react";
import { User as SupabaseUser, AuthError } from "@supabase/supabase-js";
import { supabase } from "../lib/supabase";
import { User, UserRole, Tenant } from "../types";
import { CentralizedPermissionService } from "../services/CentralizedPermissionService";
import { AuthMiddleware } from "../middleware/authMiddleware";

interface AuthContextType {
  user: User | null;
  tenant: Tenant | null;
  isLoading: boolean;
  login: (
    email: string,
    password: string,
  ) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  signUp: (
    email: string,
    password: string,
    name: string,
    role: UserRole,
    tenantId?: string,
  ) => Promise<boolean>;
  refreshUser: () => Promise<void>;
  refreshTenant: () => Promise<void>;
  switchTenant: (tenantId: string) => Promise<void>;
  // دوال الصلاحيات المحدثة للنظام الجديد
  isSystemAdmin: () => boolean;
  isSchoolManager: () => boolean;
  canEditUser: (targetUser: User) => boolean;
  canDeleteUser: (targetUser: User) => boolean;
  canCreateUserWithRole: (
    targetRole: UserRole,
    targetTenantId?: string,
  ) => boolean;
  filterDataByPermissions: <T extends { tenant_id?: string; id?: string }>(
    data: T[],
    ownerIdField?: keyof T,
  ) => T[];
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // خدمة الصلاحيات المركزية الجديدة
  const permissionService = CentralizedPermissionService.getInstance();
  const [userProfileCache, setUserProfileCache] = useState<{
    [key: string]: { data: User; timestamp: number };
  }>({});
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);

  useEffect(() => {
    let isMounted = true;

    const initializeAuth = async () => {
      try {
        console.log('🔄 AuthContext: Initializing auth...');

        // Check active session
        const { data: { session } } = await supabase.auth.getSession();

        if (!isMounted) return;

        if (session?.user) {
          console.log('✅ AuthContext: Found existing session');
          await fetchUserProfile(session.user);
        } else {
          console.log('❌ AuthContext: No existing session');
          setIsLoading(false);
        }
      } catch (error) {
        console.error('🚨 AuthContext: Error initializing auth:', error);
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    initializeAuth();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔄 AuthContext: Auth state changed:', event);

      if (!isMounted) return;

      try {
        if (session?.user) {
          console.log('✅ AuthContext: User signed in');
          await fetchUserProfile(session.user);
        } else {
          console.log('❌ AuthContext: User signed out');
          setUser(null);
          setTenant(null);
          setIsLoading(false);
        }
      } catch (error) {
        console.error('🚨 AuthContext: Error in auth state change:', error);
        setIsLoading(false);
      }
    });

    return () => {
      isMounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const fetchUserProfile = async (authUser: SupabaseUser) => {
    try {
      const now = Date.now();
      const cacheKey = authUser.id;
      const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
      const MIN_FETCH_INTERVAL = 2000; // 2 seconds minimum between fetches

      console.log('🔍 AuthContext: fetchUserProfile called for:', authUser.id);

      // Check if we have cached data that's still valid
      const cachedData = userProfileCache[cacheKey];
      if (cachedData && now - cachedData.timestamp < CACHE_DURATION) {
        console.log("✅ AuthContext: Using cached user profile");
        setUser(cachedData.data);

        // Fetch tenant if needed
        if (cachedData.data.tenant_id && !tenant) {
          await fetchTenantData(cachedData.data.tenant_id);
        }
        setIsLoading(false);
        return;
      }

      // Prevent rapid successive calls (but allow if no user is set)
      if (now - lastFetchTime < MIN_FETCH_INTERVAL && user) {
        console.log("⏸️ AuthContext: Skipping fetch due to rate limiting");
        setIsLoading(false);
        return;
      }

      setLastFetchTime(now);
      console.log("🔄 AuthContext: Fetching fresh user profile...");
      console.log("👤 AuthContext: Auth user ID:", authUser.id);
      console.log("📧 AuthContext: Auth user email:", authUser.email);

      // Use direct query with new permission system
      console.log("🔍 AuthContext: Querying users table for ID:", authUser.id);

      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("*")
        .eq("id", authUser.id)
        .single(); // Changed from maybeSingle to single for better error handling

      console.log("📊 AuthContext: User profile query result:", {
        hasData: !!userData,
        hasError: !!userError,
        errorMessage: userError?.message,
        errorCode: userError?.code,
        userRole: userData?.role,
        userTenantId: userData?.tenant_id,
        queryUserId: authUser.id
      });

      if (userError) {
        console.error("🚨 AuthContext: Error fetching user profile:", userError);
        console.error("🚨 AuthContext: Error details:", {
          code: userError.code,
          message: userError.message,
          details: userError.details,
          hint: userError.hint
        });

        // If user not found in users table, try to create it using safe function
        if (userError.code === 'PGRST116') { // No rows returned
          console.log("🔧 AuthContext: User not found in users table, creating safely...");

          try {
            // استخدام الدالة الآمنة لإنشاء المستخدم
            const { data: createResult, error: createError } = await supabase
              .rpc('safe_create_user', {
                user_id: authUser.id,
                user_email: authUser.email || '',
                user_name: authUser.user_metadata?.name || authUser.email || 'User',
                user_role: authUser.user_metadata?.role || 'student',
                user_tenant_id: authUser.user_metadata?.tenant_id || null
              });

            if (createError) {
              console.error("🚨 AuthContext: Error with safe_create_user:", createError);

              // إذا فشلت الدالة الآمنة، نحاول الطريقة المباشرة
              console.log("🔄 AuthContext: Trying direct insert as fallback...");

              const { data: directUser, error: directError } = await supabase
                .from("users")
                .insert([
                  {
                    id: authUser.id,
                    email: authUser.email || '',
                    name: authUser.user_metadata?.name || authUser.email || 'User',
                    role: authUser.user_metadata?.role || 'student',
                    tenant_id: authUser.user_metadata?.tenant_id || null,
                    phone: undefined,
                    is_active: true,
                    created_at: authUser.created_at,
                    updated_at: new Date().toISOString(),
                  }
                ])
                .select()
                .single();

              if (directError) {
                console.error("🚨 AuthContext: Direct insert also failed:", directError);
                if (directError.code === '42501') {
                  console.log("⚠️ AuthContext: RLS policy violation, using fallback user data");
                }
              } else if (directUser) {
                console.log("✅ AuthContext: User created via direct insert");
                setUser(directUser as User);

                // Cache the user data
                const now = Date.now();
                setUserProfileCache((prev) => ({
                  ...prev,
                  [authUser.id]: { data: directUser as User, timestamp: now },
                }));

                if (directUser.tenant_id) {
                  await fetchTenantData(directUser.tenant_id);
                }

                setIsLoading(false);
                return;
              }
            } else if (createResult) {
              console.log("✅ AuthContext: User profile created successfully via safe function");
              const newUser = createResult as User;
              setUser(newUser);

              // Cache the user data
              const now = Date.now();
              setUserProfileCache((prev) => ({
                ...prev,
                [authUser.id]: { data: newUser, timestamp: now },
              }));

              // Fetch tenant information if user has a tenant_id
              if (newUser.tenant_id) {
                await fetchTenantData(newUser.tenant_id);
              }

              setIsLoading(false);
              return;
            }
          } catch (createError) {
            console.error("🚨 AuthContext: Exception in user creation:", createError);
          }
        }

        // Fallback: create basic user object from auth data
        const fallbackUser: User = {
          id: authUser.id,
          email: authUser.email || '',
          name: authUser.user_metadata?.name || authUser.email || '',
          role: authUser.user_metadata?.role || 'student',
          tenant_id: authUser.user_metadata?.tenant_id || null,
          phone: undefined,
          is_active: true,
          created_at: authUser.created_at,
          updated_at: new Date().toISOString(),
        };

        console.log("⚠️ AuthContext: Using fallback user data:", fallbackUser);
        setUser(fallbackUser);
        setIsLoading(false);
        return;
      }

      if (userData) {
        console.log("✅ AuthContext: User profile fetched successfully:", {
          id: userData.id,
          email: userData.email,
          role: userData.role,
          tenant_id: userData.tenant_id,
        });

        const userObj = userData as User;
        setUser(userObj);

        // Cache the user data
        setUserProfileCache((prev) => ({
          ...prev,
          [cacheKey]: { data: userObj, timestamp: now },
        }));

        // Fetch tenant information if user has a tenant_id
        if (userData.tenant_id) {
          await fetchTenantData(userData.tenant_id);
        }
      } else {
        console.warn("⚠️ AuthContext: No user data returned");
      }
    } catch (error) {
      console.error("🚨 AuthContext: Error in fetchUserProfile:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchTenantData = async (tenantId: string) => {
    try {
      const { data: tenantData, error: tenantError } = await supabase
        .from("tenants")
        .select("*")
        .eq("id", tenantId)
        .maybeSingle();

      if (!tenantError && tenantData) {
        console.log("AuthContext: Tenant data fetched:", tenantData.name);
        setTenant(tenantData as Tenant);
      } else if (tenantError) {
        console.warn("AuthContext: Error fetching tenant data:", tenantError);
      }
    } catch (error) {
      console.error("AuthContext: Error fetching tenant:", error);
    }
  };

  const refreshUser = async () => {
    if (user) {
      try {
        console.log("AuthContext: Refreshing user profile for:", user.id);

        // Clear cache for this user to force fresh fetch
        setUserProfileCache((prev) => {
          const newCache = { ...prev };
          delete newCache[user.id];
          return newCache;
        });

        // Reset last fetch time to allow immediate fetch
        setLastFetchTime(0);

        // Use direct query with new permission system
        const { data: userData, error: userError } = await supabase
          .from("users")
          .select("*")
          .eq("id", user.id)
          .maybeSingle();

        if (userError) {
          console.error(
            "AuthContext: Error refreshing user profile:",
            userError,
          );
          return;
        }

        if (userData) {
          console.log("AuthContext: User profile refreshed successfully");
          const userObj = userData as User;
          setUser(userObj);

          // Update cache with fresh data
          const now = Date.now();
          setUserProfileCache((prev) => ({
            ...prev,
            [user.id]: { data: userObj, timestamp: now },
          }));

          // Refresh tenant information
          if (userData.tenant_id) {
            await fetchTenantData(userData.tenant_id);
          }
        }
      } catch (error) {
        console.error("AuthContext: Error in refreshUser:", error);
      }
    }
  };

  const login = async (
    email: string,
    password: string,
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      console.log("🔄 AuthContext: Starting login process...");
      setIsLoading(true);

      // تنظيف البيانات المدخلة
      const sanitizedEmail = AuthMiddleware.sanitizeInput(email.trim());
      const sanitizedPassword = password.trim();

      // التحقق من صحة البيانات الأساسي
      if (!sanitizedEmail || !sanitizedPassword) {
        setIsLoading(false);
        return { success: false, error: "البريد الإلكتروني وكلمة المرور مطلوبان" };
      }

      console.log("🔐 AuthContext: Calling Supabase signInWithPassword...");
      const { data, error } = await supabase.auth.signInWithPassword({
        email: sanitizedEmail,
        password: sanitizedPassword,
      });

      if (error) {
        console.error("❌ AuthContext: Supabase auth error:", error);
        setIsLoading(false);

        // تسجيل محاولة الدخول الفاشلة
        await permissionService.logSecurityEvent(
          'USER_LOGIN_FAILED',
          'WARNING',
          'Failed login attempt',
          undefined,
          undefined,
          { email: sanitizedEmail, error: error.message }
        );

        // Handle specific error cases
        if (error instanceof AuthError) {
          switch (error.message) {
            case "Invalid login credentials":
              return {
                success: false,
                error: "بريد إلكتروني أو كلمة مرور غير صحيحة",
              };
            case "Email not confirmed":
              return {
                success: false,
                error: "يرجى تأكيد البريد الإلكتروني",
              };
            default:
              return {
                success: false,
                error: "حدث خطأ أثناء تسجيل الدخول",
              };
          }
        }

        return {
          success: false,
          error: `خطأ في الاتصال: ${String(error)}`,
        };
      }

      if (!data.user) {
        setIsLoading(false);
        return { success: false, error: "لم يتم إرجاع بيانات المستخدم" };
      }

      // تسجيل عملية تسجيل الدخول الناجحة
      console.log("✅ AuthContext: Login successful");
      await permissionService.logSecurityEvent(
        'USER_LOGIN_SUCCESS',
        'INFO',
        'User logged in successfully',
        data.user.id,
        undefined,
        { email: sanitizedEmail, timestamp: new Date().toISOString() }
      );

      // The onAuthStateChange will handle fetching the user profile
      return { success: true };
    } catch (error) {
      console.error("🚨 AuthContext: Unexpected error during login:", error);
      setIsLoading(false);
      return { success: false, error: `حدث خطأ غير متوقع: ${error}` };
    }
  };

  const signUp = async (
    email: string,
    password: string,
    name: string,
    role: UserRole,
    tenantId?: string,
  ): Promise<boolean> => {
    try {
      setIsLoading(true);

      // تنظيف البيانات المدخلة
      const sanitizedData = {
        email: AuthMiddleware.sanitizeInput(email.trim()),
        password: password.trim(),
        name: AuthMiddleware.sanitizeInput(name.trim()),
        role,
        tenantId,
      };

      // التحقق من صحة البيانات الأساسي
      if (!sanitizedData.email || !sanitizedData.password || !sanitizedData.name) {
        console.error("Missing required fields");
        return false;
      }

      if (sanitizedData.password.length < 6) {
        console.error("Password too short");
        return false;
      }

      if (sanitizedData.name.length < 2) {
        console.error("Name too short");
        return false;
      }

      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: sanitizedData.email,
        password: sanitizedData.password,
        options: {
          data: {
            name: sanitizedData.name,
            role: sanitizedData.role,
          },
        },
      });

      if (authError) throw authError;
      if (!authData.user) throw new Error("No user returned from signup");

      // Create user profile
      const { error: profileError } = await supabase.from("users").insert([
        {
          id: authData.user.id,
          email: sanitizedData.email,
          name: sanitizedData.name,
          role: sanitizedData.role,
          tenant_id: sanitizedData.tenantId,
          is_active: true,
          metadata: {},
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ]);

      if (profileError) throw profileError;

      return true;
    } catch (error) {
      console.error("Error signing up:", error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      console.log("🔄 AuthContext: Starting logout process...");

      // تسجيل عملية تسجيل الخروج
      if (user) {
        await permissionService.logSecurityEvent(
          'USER_LOGOUT',
          'INFO',
          'User logged out successfully',
          user.id,
          user.tenant_id,
          {
            email: user.email,
            timestamp: new Date().toISOString()
          }
        );

        console.log("👋 User logged out:", {
          userId: user.id,
          email: user.email,
          timestamp: new Date().toISOString(),
        });
      }

      await supabase.auth.signOut();
      setUser(null);
      setTenant(null);

      console.log("✅ AuthContext: Logout completed successfully");
    } catch (error) {
      console.error("🚨 AuthContext: Error during logout:", error);
    }
  };

  const refreshTenant = async () => {
    if (user?.tenant_id) {
      try {
        console.log('AuthContext: Refreshing tenant data for:', user.tenant_id);
        await fetchTenantData(user.tenant_id);
      } catch (error) {
        console.error('AuthContext: Error refreshing tenant:', error);
      }
    }
  };

  const switchTenant = async (tenantId: string) => {
    if (!user) return;

    try {
      // فحص صلاحية تبديل المدرسة
      if (user.role !== UserRole.ADMIN) {
        throw new Error("غير مصرح بتبديل المدرسة");
      }

      // Update user's tenant_id
      const { error: updateError } = await supabase
        .from("users")
        .update({ tenant_id: tenantId })
        .eq("id", user.id);

      if (updateError) throw updateError;

      // Fetch new tenant data
      const { data: tenantData, error: tenantError } = await supabase
        .from("tenants")
        .select("*")
        .eq("id", tenantId)
        .maybeSingle();

      if (tenantError) throw tenantError;

      setTenant(tenantData as Tenant);
      setUser({ ...user, tenant_id: tenantId });

      // إنشاء سجل تدقيق لتبديل المدرسة
      console.log("Tenant switched:", {
        userId: user.id,
        previousTenantId: user.tenant_id,
        newTenantId: tenantId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Error switching tenant:", error);
    }
  };

  // دوال الصلاحيات المحدثة للنظام الجديد
  const isSystemAdmin = (): boolean => {
    return user?.role === UserRole.ADMIN;
  };

  const isSchoolManager = (): boolean => {
    return user?.role === UserRole.SCHOOL_MANAGER;
  };

  const canEditUser = (targetUser: User): boolean => {
    if (!user) return false;

    // User can edit their own profile
    if (user.id === targetUser.id) return true;

    // Only admin and school managers can edit other users
    if (user.role === 'admin') return true;

    if (user.role === 'school_manager') {
      // School managers can only edit users in their school
      return user.tenant_id === targetUser.tenant_id;
    }

    return false;
  };

  const canDeleteUser = (targetUser: User): boolean => {
    if (!user) return false;

    // User cannot delete themselves
    if (user.id === targetUser.id) return false;

    return canEditUser(targetUser);
  };

  const canCreateUserWithRole = (
    targetRole: UserRole,
    targetTenantId?: string,
  ): boolean => {
    if (!user) return false;

    // Only admin can create admin users
    if (targetRole === 'admin' && user.role !== 'admin') return false;

    // Admin can create any user
    if (user.role === 'admin') return true;

    // School managers can create users in their school
    if (user.role === 'school_manager' && targetTenantId === user.tenant_id) {
      return targetRole !== 'admin';
    }

    return false;
  };

  const filterDataByPermissions = <
    T extends { tenant_id?: string; id?: string },
  >(
    data: T[],
    ownerIdField?: keyof T,
  ): T[] => {
    if (!user) return [];

    // Admin sees all data
    if (user.role === 'admin') {
      return data;
    }

    // School managers and supervisors see only their school's data
    if (user.role === 'school_manager' || user.role === 'supervisor') {
      return data.filter(item => item.tenant_id === user.tenant_id);
    }

    // Drivers, parents, and students see only their own data
    if (ownerIdField) {
      return data.filter(item => item[ownerIdField] === user.id);
    }

    return data.filter(item => item.tenant_id === user.tenant_id);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        tenant,
        isLoading,
        login,
        logout,
        signUp,
        refreshUser,
        refreshTenant,
        switchTenant,
        isSystemAdmin,
        isSchoolManager,
        canEditUser,
        canDeleteUser,
        canCreateUserWithRole,
        filterDataByPermissions,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth: () => AuthContextType = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
