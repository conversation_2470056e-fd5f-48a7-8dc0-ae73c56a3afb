import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Star, User, Calendar, MessageSquare, Filter } from "lucide-react";
import { getEvaluations, getAverageRating } from "../../lib/api";
import { useAuth } from "../../contexts/AuthContext";
import type { Tables } from "../../lib/api";

interface EvaluationWithUser extends Tables<"evaluations"> {
  user: {
    id: string;
    name: string;
    avatar_url?: string;
  };
}

interface EvaluationsListProps {
  targetType?: "driver" | "service" | "route";
  targetId?: string;
  showFilters?: boolean;
}

export const EvaluationsList: React.FC<EvaluationsListProps> = ({
  targetType,
  targetId,
  showFilters = true,
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [evaluations, setEvaluations] = useState<EvaluationWithUser[]>([]);
  const [averageRating, setAverageRating] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    targetType: targetType || "",
    rating: "",
  });

  useEffect(() => {
    if (user?.tenant_id) {
      fetchEvaluations();
      if (targetType && targetId) {
        fetchAverageRating();
      }
    }
  }, [user?.tenant_id, targetType, targetId, filters]);

  const fetchEvaluations = async () => {
    if (!user?.tenant_id) return;

    setLoading(true);
    try {
      const data = await getEvaluations(
        user.tenant_id,
        filters.targetType || targetType,
        targetId,
      );

      let filteredData = data || [];

      // Apply rating filter
      if (filters.rating) {
        const ratingValue = parseInt(filters.rating);
        filteredData = filteredData.filter(
          (evaluation) => evaluation.rating === ratingValue,
        );
      }

      setEvaluations(filteredData as EvaluationWithUser[]);
    } catch (error) {
      console.error("Error fetching evaluations:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAverageRating = async () => {
    if (!user?.tenant_id || !targetType || !targetId) return;

    try {
      const avgRating = await getAverageRating(
        user.tenant_id,
        targetType,
        targetId,
      );
      setAverageRating(avgRating);
    } catch (error) {
      console.error("Error fetching average rating:", error);
    }
  };

  const renderStars = (rating: number, size: "sm" | "md" = "sm") => {
    const starSize = size === "sm" ? 16 : 20;
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            size={starSize}
            className={`${
              star <= rating
                ? "text-yellow-400 fill-current"
                : "text-gray-300 dark:text-gray-600"
            }`}
          />
        ))}
      </div>
    );
  };

  const getRatingText = (rating: number) => {
    switch (rating) {
      case 1:
        return t("evaluation.ratings.poor");
      case 2:
        return t("evaluation.ratings.fair");
      case 3:
        return t("evaluation.ratings.good");
      case 4:
        return t("evaluation.ratings.veryGood");
      case 5:
        return t("evaluation.ratings.excellent");
      default:
        return "";
    }
  };

  const getTargetTypeLabel = (type: string) => {
    switch (type) {
      case "driver":
        return t("evaluation.driver");
      case "service":
        return t("evaluation.service");
      case "route":
        return t("evaluation.route");
      default:
        return type;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t("evaluation.evaluations")} ({evaluations.length})
          </h3>
          {targetType && targetId && averageRating > 0 && (
            <div className="flex items-center space-x-2">
              {renderStars(Math.round(averageRating), "md")}
              <span className="text-lg font-semibold text-gray-900 dark:text-white">
                {averageRating.toFixed(1)}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                ({evaluations.length} {t("evaluation.reviews")})
              </span>
            </div>
          )}
        </div>

        {showFilters && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t("evaluation.targetType")}
              </label>
              <select
                value={filters.targetType}
                onChange={(e) =>
                  setFilters((prev) => ({
                    ...prev,
                    targetType: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
              >
                <option value="">{t("common.all")}</option>
                <option value="driver">{t("evaluation.driver")}</option>
                <option value="service">{t("evaluation.service")}</option>
                <option value="route">{t("evaluation.route")}</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t("evaluation.rating")}
              </label>
              <select
                value={filters.rating}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, rating: e.target.value }))
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
              >
                <option value="">{t("common.all")}</option>
                <option value="5">
                  {t("evaluation.ratings.excellent")} (5)
                </option>
                <option value="4">
                  {t("evaluation.ratings.veryGood")} (4)
                </option>
                <option value="3">{t("evaluation.ratings.good")} (3)</option>
                <option value="2">{t("evaluation.ratings.fair")} (2)</option>
                <option value="1">{t("evaluation.ratings.poor")} (1)</option>
              </select>
            </div>
          </div>
        )}
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              {t("common.loading")}
            </p>
          </div>
        ) : evaluations.length === 0 ? (
          <div className="p-8 text-center">
            <Star size={48} className="mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {t("evaluation.noEvaluations")}
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {t("evaluation.noEvaluationsDescription")}
            </p>
          </div>
        ) : (
          evaluations.map((evaluation) => (
            <div key={evaluation.id} className="p-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  {evaluation.user?.avatar_url ? (
                    <img
                      src={evaluation.user.avatar_url}
                      alt={evaluation.user.name}
                      className="h-10 w-10 rounded-full"
                    />
                  ) : (
                    <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                      <User
                        size={20}
                        className="text-gray-500 dark:text-gray-400"
                      />
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {evaluation.user?.name || t("common.anonymous")}
                      </p>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {getTargetTypeLabel(evaluation.target_type)}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {renderStars(evaluation.rating)}
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {getRatingText(evaluation.rating)}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mb-2">
                    <Calendar size={12} className="mr-1" />
                    {new Date(evaluation.created_at).toLocaleDateString()}
                  </div>
                  {evaluation.comment && (
                    <div className="mt-2">
                      <div className="flex items-start space-x-1">
                        <MessageSquare
                          size={14}
                          className="text-gray-400 mt-0.5 flex-shrink-0"
                        />
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          {evaluation.comment}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default EvaluationsList;
