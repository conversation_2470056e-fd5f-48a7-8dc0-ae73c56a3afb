/**
 * Enhanced Permission Guard Component
 * Improved version with better error handling and centralized configuration
 */

import React from "react";
import { useAuth } from "../../contexts/AuthContext";
import { usePermissions } from "../../hooks/usePermissions";
import { Permission, DataScope, ResourceType, Action } from "../../lib/rbac";
import { UserRole } from "../../types";
import {
  ENHANCED_COMPONENT_PERMISSIONS,
  ENHANCED_PERMISSION_ERROR_MESSAGES,
  ComponentPermissionConfig,
} from "../../lib/rbacCentralizedConfigEnhanced";

interface EnhancedPermissionGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;

  // Permission-based access
  permission?: Permission;
  permissions?: Permission[];
  requireAllPermissions?: boolean;

  // Role-based access
  roles?: UserRole[];
  excludeRoles?: UserRole[];

  // Component-based access
  componentKey?: string;

  // Action-based access
  resource?: ResourceType;
  action?: Action;
  context?: {
    resourceOwnerId?: string;
    resourceTenantId?: string;
    resourceData?: any;
  };

  // Data scope requirements
  dataScope?: DataScope;
  dataScopes?: DataScope[];

  // Error handling
  showError?: boolean;
  errorComponent?: React.ComponentType<{ error: string }>;
  onAccessDenied?: (error: string) => void;

  // Loading state
  loading?: boolean;
  loadingComponent?: React.ReactNode;

  // Security enhancements
  auditAccess?: boolean;
  rateLimit?: {
    maxRequests: number;
    windowMinutes: number;
  };
  requireMFA?: boolean;
}

/**
 * Default Error Component
 */
const DefaultErrorComponent: React.FC<{ error: string }> = ({ error }) => (
  <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
    <div className="flex items-center">
      <div className="flex-shrink-0">
        <svg
          className="h-5 w-5 text-red-400"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
            clipRule="evenodd"
          />
        </svg>
      </div>
      <div className="ml-3">
        <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
      </div>
    </div>
  </div>
);

/**
 * Default Loading Component
 */
const DefaultLoadingComponent: React.FC = () => (
  <div className="flex items-center justify-center p-4">
    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"></div>
    <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
      Checking permissions...
    </span>
  </div>
);

/**
 * Enhanced Permission Guard Component
 */
export const EnhancedPermissionGuard: React.FC<
  EnhancedPermissionGuardProps
> = ({
  children,
  fallback = null,
  permission,
  permissions = [],
  requireAllPermissions = false,
  roles = [],
  excludeRoles = [],
  componentKey,
  resource,
  action,
  context,
  dataScope,
  dataScopes = [],
  showError = false,
  errorComponent: ErrorComponent = DefaultErrorComponent,
  onAccessDenied,
  loading = false,
  loadingComponent = <DefaultLoadingComponent />,
  auditAccess = false,
  rateLimit,
  requireMFA = false,
}) => {
  const { user } = useAuth();
  const {
    hasPermission,
    hasDataScope,
    checkResourceAccess,
    userRole,
    currentUser,
    canPerformAction,
  } = usePermissions();

  // Show loading state
  if (loading) {
    return <>{loadingComponent}</>;
  }

  // If no user is logged in, deny access
  if (!user || !userRole) {
    const error = ENHANCED_PERMISSION_ERROR_MESSAGES.SESSION_EXPIRED;
    if (onAccessDenied) onAccessDenied(error);
    return showError ? <ErrorComponent error={error} /> : <>{fallback}</>;
  }

  // Check excluded roles first
  if (excludeRoles.length > 0 && excludeRoles.includes(userRole)) {
    const error = ENHANCED_PERMISSION_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS;
    if (onAccessDenied) onAccessDenied(error);
    return showError ? <ErrorComponent error={error} /> : <>{fallback}</>;
  }

  // Check required roles
  if (roles.length > 0 && !roles.includes(userRole)) {
    const error = ENHANCED_PERMISSION_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS;
    if (onAccessDenied) onAccessDenied(error);
    return showError ? <ErrorComponent error={error} /> : <>{fallback}</>;
  }

  // Component-based access check using centralized configuration
  if (componentKey) {
    const componentConfig = ENHANCED_COMPONENT_PERMISSIONS[componentKey];
    if (componentConfig) {
      // Check role restrictions
      if (componentConfig.roles && !componentConfig.roles.includes(userRole)) {
        const error =
          ENHANCED_PERMISSION_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS;
        if (onAccessDenied) onAccessDenied(error);
        return showError ? <ErrorComponent error={error} /> : <>{fallback}</>;
      }

      // Check permissions
      const hasRequiredPermissions = componentConfig.permissions.some((p) =>
        hasPermission(p),
      );
      if (!hasRequiredPermissions) {
        const error =
          ENHANCED_PERMISSION_ERROR_MESSAGES.COMPONENT_ACCESS_DENIED;
        if (onAccessDenied) onAccessDenied(error);
        return showError ? <ErrorComponent error={error} /> : <>{fallback}</>;
      }

      // Audit access if required
      if (auditAccess || componentConfig.auditRequired) {
        console.log("🔍 Component Access Audit:", {
          userId: user.id,
          userRole,
          componentKey,
          timestamp: new Date().toISOString(),
          securityLevel: componentConfig.securityLevel,
        });
      }
    }
  }

  // Action-based access check
  if (resource && action) {
    const actionResult = checkResourceAccess(resource, action, context);
    if (!actionResult.allowed) {
      const error =
        actionResult.error ||
        ENHANCED_PERMISSION_ERROR_MESSAGES.COMPONENT_ACCESS_DENIED;
      if (onAccessDenied) onAccessDenied(error);
      return showError ? <ErrorComponent error={error} /> : <>{fallback}</>;
    }
  }

  // Permission-based checks
  const allPermissions = [...(permission ? [permission] : []), ...permissions];
  if (allPermissions.length > 0) {
    const hasRequiredPermission = requireAllPermissions
      ? allPermissions.every((p) => hasPermission(p))
      : allPermissions.some((p) => hasPermission(p));

    if (!hasRequiredPermission) {
      const error = ENHANCED_PERMISSION_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS;
      if (onAccessDenied) onAccessDenied(error);
      return showError ? <ErrorComponent error={error} /> : <>{fallback}</>;
    }
  }

  // Data scope checks
  const allDataScopes = [...(dataScope ? [dataScope] : []), ...dataScopes];
  if (allDataScopes.length > 0) {
    const hasRequiredDataScope = allDataScopes.some((scope) =>
      hasDataScope(scope),
    );

    if (!hasRequiredDataScope) {
      const error = ENHANCED_PERMISSION_ERROR_MESSAGES.DATA_ACCESS_DENIED;
      if (onAccessDenied) onAccessDenied(error);
      return showError ? <ErrorComponent error={error} /> : <>{fallback}</>;
    }
  }

  // Rate limiting check (simplified implementation)
  if (rateLimit) {
    // In a real implementation, this would check against a rate limiting service
    // For now, we'll just log the attempt
    if (auditAccess) {
      console.log("🚦 Rate Limit Check:", {
        userId: user.id,
        maxRequests: rateLimit.maxRequests,
        windowMinutes: rateLimit.windowMinutes,
        timestamp: new Date().toISOString(),
      });
    }
  }

  // MFA requirement check (simplified implementation)
  if (requireMFA) {
    // In a real implementation, this would check if user has completed MFA
    const mfaCompleted = user.metadata?.mfaCompleted === true;
    if (!mfaCompleted) {
      const error = ENHANCED_PERMISSION_ERROR_MESSAGES.MFA_REQUIRED;
      if (onAccessDenied) onAccessDenied(error);
      return showError ? <ErrorComponent error={error} /> : <>{fallback}</>;
    }
  }

  // All checks passed, render children
  return <>{children}</>;
};

/**
 * Convenience components for common use cases
 */
export const AdminOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback }) => (
  <EnhancedPermissionGuard roles={[UserRole.ADMIN]} fallback={fallback}>
    {children}
  </EnhancedPermissionGuard>
);

export const SchoolManagerOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback }) => (
  <EnhancedPermissionGuard
    roles={[UserRole.ADMIN, UserRole.SCHOOL_MANAGER]}
    fallback={fallback}
  >
    {children}
  </EnhancedPermissionGuard>
);

export const DriverOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback }) => (
  <EnhancedPermissionGuard roles={[UserRole.DRIVER]} fallback={fallback}>
    {children}
  </EnhancedPermissionGuard>
);

export const ParentOnly: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback }) => (
  <EnhancedPermissionGuard roles={[UserRole.PARENT]} fallback={fallback}>
    {children}
  </EnhancedPermissionGuard>
);

/**
 * Component-based permission guard using centralized configuration
 */
export const ComponentPermissionGuard: React.FC<{
  children: React.ReactNode;
  componentKey: string;
  fallback?: React.ReactNode;
  auditAccess?: boolean;
}> = ({ children, componentKey, fallback, auditAccess = true }) => {
  return (
    <EnhancedPermissionGuard
      componentKey={componentKey}
      fallback={fallback}
      auditAccess={auditAccess}
    >
      {children}
    </EnhancedPermissionGuard>
  );
};

/**
 * Higher-Order Component for permission-based rendering
 */
export const withPermissions = (
  Component: React.ComponentType<any>,
  guardProps: Omit<EnhancedPermissionGuardProps, "children">,
) => {
  return (props: any) => (
    <EnhancedPermissionGuard {...guardProps}>
      <Component {...props} />
    </EnhancedPermissionGuard>
  );
};

/**
 * Enhanced HOC with component key support
 */
export const withComponentPermissions = (
  Component: React.ComponentType<any>,
  componentKey: string,
  options?: {
    fallback?: React.ReactNode;
    auditAccess?: boolean;
    showError?: boolean;
  },
) => {
  return (props: any) => (
    <ComponentPermissionGuard
      componentKey={componentKey}
      fallback={options?.fallback}
      auditAccess={options?.auditAccess}
    >
      <Component {...props} />
    </ComponentPermissionGuard>
  );
};

export default EnhancedPermissionGuard;
