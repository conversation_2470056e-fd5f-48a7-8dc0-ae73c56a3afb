/**
 * Protected Route Component
 * Centralized route-level permission checking with consistent error handling
 */

import React from "react";
import { Navigate } from "react-router-dom";
import { useRBACEnhanced } from "../../hooks/useRBACEnhanced";
import { Permission } from "../../lib/rbac";
import { UserRole } from "../../types";

interface ProtectedRouteProps {
  children: React.ReactNode;
  route?: string;
  permissions?: Permission[];
  roles?: UserRole[];
  requireAll?: boolean;
  fallbackRoute?: string;
  fallbackComponent?: React.ComponentType<{ error: string }>;
}

/**
 * Default Access Denied Component
 */
const DefaultAccessDenied: React.FC<{
  error: string;
  fallbackRoute?: string;
}> = ({ error, fallbackRoute = "/dashboard" }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="text-center p-8 max-w-md mx-auto">
        <div className="mb-6">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/20">
            <svg
              className="h-8 w-8 text-red-600 dark:text-red-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
        </div>

        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Access Denied
        </h1>

        <p className="text-gray-600 dark:text-gray-400 mb-6">{error}</p>

        <div className="space-y-3">
          <Navigate to={fallbackRoute} replace />

          <button
            onClick={() => window.history.back()}
            className="w-full px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Go Back
          </button>

          <a
            href="/dashboard"
            className="block w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 text-center"
          >
            Go to Dashboard
          </a>
        </div>
      </div>
    </div>
  );
};

/**
 * Protected Route Component
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  route,
  permissions = [],
  roles = [],
  requireAll = false,
  fallbackRoute = "/dashboard",
  fallbackComponent: FallbackComponent = DefaultAccessDenied,
}) => {
  const rbac = useRBACEnhanced();

  // If no user is logged in, redirect to login
  if (!rbac.currentUser) {
    return <Navigate to="/login" replace />;
  }

  // Check route-level permissions if route is specified
  if (route) {
    const routeResult = rbac.canAccessRoute(route);
    if (!routeResult.allowed) {
      if (routeResult.fallbackRoute) {
        return <Navigate to={routeResult.fallbackRoute} replace />;
      }
      return (
        <FallbackComponent
          error={routeResult.error || "Access denied"}
          fallbackRoute={fallbackRoute}
        />
      );
    }
  }

  // Check role-based access
  if (roles.length > 0 && rbac.userRole) {
    const hasRole = roles.includes(rbac.userRole);
    if (!hasRole) {
      return (
        <FallbackComponent
          error="Your role does not have access to this page."
          fallbackRoute={fallbackRoute}
        />
      );
    }
  }

  // Check permission-based access
  if (permissions.length > 0) {
    const hasPermission = requireAll
      ? rbac.hasAllPermissions(permissions)
      : rbac.hasAnyPermission(permissions);

    if (!hasPermission) {
      return (
        <FallbackComponent
          error="You do not have the required permissions to access this page."
          fallbackRoute={fallbackRoute}
        />
      );
    }
  }

  // All checks passed, render children
  return <>{children}</>;
};

/**
 * Higher-Order Component for protecting routes
 */
export const withProtectedRoute = (
  Component: React.ComponentType<any>,
  options: Omit<ProtectedRouteProps, "children">,
) => {
  return (props: any) => (
    <ProtectedRoute {...options}>
      <Component {...props} />
    </ProtectedRoute>
  );
};

/**
 * Hook for checking route access in components
 */
export const useRouteAccess = (route: string) => {
  const rbac = useRBACEnhanced();
  return rbac.canAccessRoute(route);
};

export default ProtectedRoute;
