/**
 * Theme Customizer Component
 * Advanced theme customization interface for tenants
 * Phase 3: UI/UX Enhancement
 */

import React, { useState, useCallback, useEffect } from 'react';
import { useTheme } from '../../../themes/providers/ThemeProvider';
import { TenantThemeConfig, tenantThemeManager } from '../../../design-system/themes/tenant/TenantTheme';
import { baseColors, colorUtils } from '../../../design-system/tokens/colors';
import { fontFamily } from '../../../design-system/tokens/typography';

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '../../common/ui/Card';
import { Button } from '../../ui/Button';
import { Badge } from '../../common/ui/Badge';
import { Alert, AlertDescription } from '../../common/ui/Alert';

// Icons
import {
  Palette,
  Type,
  Image,
  Settings,
  Download,
  Upload,
  Eye,
  Save,
  RotateCcw,
  Smartphone,
  Monitor,
  Sun,
  Moon
} from 'lucide-react';

interface ThemeCustomizerProps {
  tenantId: string;
  onSave?: (config: TenantThemeConfig) => void;
  onPreview?: (config: TenantThemeConfig) => void;
  className?: string;
}

interface CustomizationState {
  config: TenantThemeConfig;
  previewMode: boolean;
  activeTab: 'colors' | 'typography' | 'branding' | 'layout' | 'advanced';
  unsavedChanges: boolean;
}

/**
 * Theme Customizer Component
 */
export const ThemeCustomizer: React.FC<ThemeCustomizerProps> = ({
  tenantId,
  onSave,
  onPreview,
  className,
}) => {
  const { theme, setTenantTheme, mode, setMode } = useTheme();

  // Initialize state
  const [state, setState] = useState<CustomizationState>(() => {
    const existingTheme = tenantThemeManager.getTenantTheme(tenantId);

    const defaultConfig: TenantThemeConfig = {
      id: tenantId,
      name: 'Custom Theme',
      branding: {
        name: 'School Bus System',
        logo: {
          light: '/logos/default-light.svg',
          dark: '/logos/default-dark.svg',
          favicon: '/favicon.ico',
        },
        colors: {
          primary: baseColors.primary,
        },
        typography: {
          fontFamily: fontFamily.sans,
        },
        assets: {},
      },
      baseTheme: mode,
      customizations: {
        borderRadius: 'rounded',
        shadows: 'normal',
        animations: 'normal',
        density: 'normal',
      },
      rtl: false,
      locale: 'en',
    };

    return {
      config: existingTheme?.tenant || defaultConfig,
      previewMode: false,
      activeTab: 'colors',
      unsavedChanges: false,
    };
  });

  /**
   * Update configuration
   */
  const updateConfig = useCallback((updates: Partial<TenantThemeConfig>) => {
    setState(prev => ({
      ...prev,
      config: { ...prev.config, ...updates },
      unsavedChanges: true,
    }));
  }, []);

  /**
   * Update branding
   */
  const updateBranding = useCallback((updates: Partial<TenantThemeConfig['branding']>) => {
    setState(prev => ({
      ...prev,
      config: {
        ...prev.config,
        branding: { ...prev.config.branding, ...updates },
      },
      unsavedChanges: true,
    }));
  }, []);

  /**
   * Update customizations
   */
  const updateCustomizations = useCallback((updates: Partial<TenantThemeConfig['customizations']>) => {
    setState(prev => ({
      ...prev,
      config: {
        ...prev.config,
        customizations: { ...prev.config.customizations, ...updates },
      },
      unsavedChanges: true,
    }));
  }, []);

  /**
   * Preview theme
   */
  const handlePreview = useCallback(() => {
    const previewTheme = tenantThemeManager.createTenantTheme(state.config);
    setTenantTheme(tenantId, state.config);

    setState(prev => ({ ...prev, previewMode: true }));

    if (onPreview) {
      onPreview(state.config);
    }
  }, [state.config, tenantId, setTenantTheme, onPreview]);

  /**
   * Save theme
   */
  const handleSave = useCallback(() => {
    setTenantTheme(tenantId, state.config);
    setState(prev => ({ ...prev, unsavedChanges: false }));

    if (onSave) {
      onSave(state.config);
    }
  }, [state.config, tenantId, setTenantTheme, onSave]);

  /**
   * Reset to defaults
   */
  const handleReset = useCallback(() => {
    setState(prev => ({
      ...prev,
      config: {
        ...prev.config,
        branding: {
          ...prev.config.branding,
          colors: { primary: baseColors.primary },
          typography: { fontFamily: fontFamily.sans },
        },
        customizations: {
          borderRadius: 'rounded',
          shadows: 'normal',
          animations: 'normal',
          density: 'normal',
        },
      },
      unsavedChanges: true,
    }));
  }, []);

  /**
   * Export theme configuration
   */
  const handleExport = useCallback(() => {
    const dataStr = JSON.stringify(state.config, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `theme-${tenantId}.json`;
    link.click();

    URL.revokeObjectURL(url);
  }, [state.config, tenantId]);

  /**
   * Import theme configuration
   */
  const handleImport = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const config = JSON.parse(e.target?.result as string);
        setState(prev => ({
          ...prev,
          config: { ...config, id: tenantId },
          unsavedChanges: true,
        }));
      } catch (error) {
        console.error('Failed to import theme:', error);
      }
    };
    reader.readAsText(file);
  }, [tenantId]);

  /**
   * Color picker component
   */
  const ColorPicker: React.FC<{
    label: string;
    value: string;
    onChange: (color: string) => void;
  }> = ({ label, value, onChange }) => (
    <div className="space-y-2">
      <label className="text-sm font-medium">{label}</label>
      <div className="flex items-center space-x-2">
        <input
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-12 h-8 rounded border border-gray-300"
        />
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="flex-1 px-3 py-2 border border-gray-300 rounded text-sm"
          placeholder="#000000"
        />
      </div>
    </div>
  );

  /**
   * Render tab content
   */
  const renderTabContent = () => {
    switch (state.activeTab) {
      case 'colors':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Primary Colors</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ColorPicker
                  label="Primary 500"
                  value={state.config.branding.colors.primary[500]}
                  onChange={(color) => {
                    const newPrimary = { ...state.config.branding.colors.primary };
                    newPrimary[500] = color;
                    updateBranding({
                      colors: { ...state.config.branding.colors, primary: newPrimary }
                    });
                  }}
                />
                <ColorPicker
                  label="Primary 600"
                  value={state.config.branding.colors.primary[600]}
                  onChange={(color) => {
                    const newPrimary = { ...state.config.branding.colors.primary };
                    newPrimary[600] = color;
                    updateBranding({
                      colors: { ...state.config.branding.colors, primary: newPrimary }
                    });
                  }}
                />
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Color Palette Preview</h3>
              <div className="grid grid-cols-5 gap-2">
                {Object.entries(state.config.branding.colors.primary).map(([shade, color]) => (
                  <div key={shade} className="text-center">
                    <div
                      className="w-full h-12 rounded mb-1"
                      style={{ backgroundColor: color }}
                    />
                    <div className="text-xs text-gray-600">{shade}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'typography':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Font Family</h3>
              <select
                value={state.config.branding.typography.fontFamily[0]}
                onChange={(e) => {
                  const selectedFont = e.target.value;
                  const fontStack = selectedFont === 'Inter' ? fontFamily.sans :
                                   selectedFont === 'Cairo' ? fontFamily.arabic :
                                   [selectedFont, ...fontFamily.sans];

                  updateBranding({
                    typography: {
                      ...state.config.branding.typography,
                      fontFamily: fontStack,
                    }
                  });
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded"
              >
                <option value="Inter">Inter (Default)</option>
                <option value="Cairo">Cairo (Arabic)</option>
                <option value="Roboto">Roboto</option>
                <option value="Open Sans">Open Sans</option>
                <option value="Poppins">Poppins</option>
              </select>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Typography Preview</h3>
              <div className="space-y-4" style={{ fontFamily: state.config.branding.typography.fontFamily.join(', ') }}>
                <div className="text-4xl font-bold">Heading 1</div>
                <div className="text-2xl font-semibold">Heading 2</div>
                <div className="text-lg font-medium">Heading 3</div>
                <div className="text-base">Body text - Lorem ipsum dolor sit amet, consectetur adipiscing elit.</div>
                <div className="text-sm text-gray-600">Small text - Additional information and details.</div>
              </div>
            </div>
          </div>
        );

      case 'branding':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Organization Name</h3>
              <input
                type="text"
                value={state.config.branding.name}
                onChange={(e) => updateBranding({ name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded"
                placeholder="Organization Name"
              />
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Logo URLs</h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium mb-1">Light Mode Logo</label>
                  <input
                    type="url"
                    value={state.config.branding.logo.light}
                    onChange={(e) => updateBranding({
                      logo: { ...state.config.branding.logo, light: e.target.value }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded"
                    placeholder="https://example.com/logo-light.svg"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Dark Mode Logo</label>
                  <input
                    type="url"
                    value={state.config.branding.logo.dark}
                    onChange={(e) => updateBranding({
                      logo: { ...state.config.branding.logo, dark: e.target.value }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded"
                    placeholder="https://example.com/logo-dark.svg"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 'layout':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Border Radius</h3>
              <div className="grid grid-cols-3 gap-3">
                {(['sharp', 'rounded', 'pill'] as const).map((style) => (
                  <button
                    key={style}
                    onClick={() => updateCustomizations({ borderRadius: style })}
                    className={`p-4 border-2 rounded text-center capitalize ${
                      state.config.customizations.borderRadius === style
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-300'
                    }`}
                  >
                    {style}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Shadows</h3>
              <div className="grid grid-cols-3 gap-3">
                {(['minimal', 'normal', 'elevated'] as const).map((style) => (
                  <button
                    key={style}
                    onClick={() => updateCustomizations({ shadows: style })}
                    className={`p-4 border-2 rounded text-center capitalize ${
                      state.config.customizations.shadows === style
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-300'
                    }`}
                  >
                    {style}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Density</h3>
              <div className="grid grid-cols-3 gap-3">
                {(['compact', 'normal', 'comfortable'] as const).map((style) => (
                  <button
                    key={style}
                    onClick={() => updateCustomizations({ density: style })}
                    className={`p-4 border-2 rounded text-center capitalize ${
                      state.config.customizations.density === style
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-300'
                    }`}
                  >
                    {style}
                  </button>
                ))}
              </div>
            </div>
          </div>
        );

      case 'advanced':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Language & Direction</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="rtl-support"
                    checked={state.config.rtl}
                    onChange={(e) => updateConfig({ rtl: e.target.checked })}
                    className="rounded"
                  />
                  <label htmlFor="rtl-support" className="text-sm font-medium">
                    Enable RTL Support
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Locale</label>
                  <select
                    value={state.config.locale}
                    onChange={(e) => updateConfig({ locale: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded"
                  >
                    <option value="en">English</option>
                    <option value="ar">Arabic</option>
                    <option value="he">Hebrew</option>
                    <option value="fa">Persian</option>
                  </select>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Import/Export</h3>
              <div className="flex space-x-3">
                <Button onClick={handleExport} variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  Export Theme
                </Button>
                <label className="inline-flex">
                  <Button variant="outline" className="cursor-pointer">
                    <Upload className="w-4 h-4 mr-2" />
                    Import Theme
                  </Button>
                  <input
                    type="file"
                    accept=".json"
                    onChange={handleImport}
                    className="hidden"
                  />
                </label>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Theme Customizer</h2>
          <p className="text-gray-600">Customize your organization's theme and branding</p>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            onClick={() => setMode(mode === 'light' ? 'dark' : 'light')}
            variant="outline"
            size="sm"
          >
            {mode === 'light' ? <Moon className="w-4 h-4" /> : <Sun className="w-4 h-4" />}
          </Button>

          {state.unsavedChanges && (
            <Badge variant="warning">Unsaved Changes</Badge>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <Button onClick={handlePreview} variant="outline">
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
          <Button onClick={handleReset} variant="outline">
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </Button>
        </div>

        <Button onClick={handleSave} disabled={!state.unsavedChanges}>
          <Save className="w-4 h-4 mr-2" />
          Save Theme
        </Button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {[
            { id: 'colors', label: 'Colors', icon: Palette },
            { id: 'typography', label: 'Typography', icon: Type },
            { id: 'branding', label: 'Branding', icon: Image },
            { id: 'layout', label: 'Layout', icon: Monitor },
            { id: 'advanced', label: 'Advanced', icon: Settings },
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setState(prev => ({ ...prev, activeTab: id as any }))}
              className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                state.activeTab === id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span>{label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <Card>
        <CardContent className="p-6">
          {renderTabContent()}
        </CardContent>
      </Card>

      {/* Preview Mode Alert */}
      {state.previewMode && (
        <Alert>
          <Eye className="h-4 w-4" />
          <AlertDescription>
            Preview mode is active. Changes are temporarily applied. Save to make them permanent.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};
