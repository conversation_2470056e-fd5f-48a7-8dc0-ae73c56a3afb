{"test_info": {"timestamp": "2025-06-02T09-38-28-022Z", "total_tests": 15, "passed_tests": 15, "failed_tests": 0, "warning_tests": 0, "success_rate": "100.0"}, "test_suites": [{"id": "basic", "name": "الاختبارات الأساسية", "results": [{"suite": "basic", "test": "فحص البنية الأساسية", "action": "checkBasicStructure", "status": "passed", "message": "جميع المجلدات الأساسية موجودة", "details": "تم فحص 7 مجلد", "timestamp": "2025-06-02T09:38:27.987Z"}, {"suite": "basic", "test": "فحص ملفات التكوين", "action": "checkConfigFiles", "status": "passed", "message": "جميع ملفات التكوين موجودة", "details": "تم فحص 5 ملف", "timestamp": "2025-06-02T09:38:27.989Z"}, {"suite": "basic", "test": "فحص مسارات الاستيراد", "action": "checkImportPaths", "status": "passed", "message": "جميع مسارات الاستيراد صحيحة", "details": "51/51 مسار صحيح", "timestamp": "2025-06-02T09:38:27.993Z"}]}, {"id": "components", "name": "اختبار المكونات", "results": [{"suite": "components", "test": "فحص المكونات الأساسية", "action": "checkCoreComponents", "status": "passed", "message": "جميع المكونات الأساسية موجودة", "details": "3/3 مكون", "timestamp": "2025-06-02T09:38:27.994Z"}, {"suite": "components", "test": "فحص الصفحات الرئيسية", "action": "checkMainPages", "status": "passed", "message": "معظم الصفحات الرئيسية موجودة", "details": "3/3 صف<PERSON>ة", "timestamp": "2025-06-02T09:38:27.995Z"}, {"suite": "components", "test": "فحص مكونات UI", "action": "checkUIComponents", "status": "passed", "message": "مكونات UI موجودة", "details": "3/3 مجموعة مكونات", "timestamp": "2025-06-02T09:38:27.995Z"}]}, {"id": "services", "name": "اختبار الخدمات", "results": [{"suite": "services", "test": "فحص خدمات قاعدة البيانات", "action": "checkDatabaseServices", "status": "passed", "message": "خدمات قاعدة البيانات موجودة", "details": "2/2 خدمة", "timestamp": "2025-06-02T09:38:27.997Z"}, {"suite": "services", "test": "فح<PERSON> خدمات الأمان", "action": "checkSecurityServices", "status": "passed", "message": "خدمات الأمان موجودة", "details": "2/2 خدمة أمان", "timestamp": "2025-06-02T09:38:27.998Z"}, {"suite": "services", "test": "فح<PERSON> خدمات الإشعارات", "action": "checkNotificationServices", "status": "passed", "message": "خدمات الإشعارات موجودة", "details": "2/2 خدمة إشعارات", "timestamp": "2025-06-02T09:38:27.999Z"}]}, {"id": "functionality", "name": "اختبار الوظائف", "results": [{"suite": "functionality", "test": "اختبار تسجيل الدخول", "action": "testLogin", "status": "passed", "message": "مكونات تسجيل الدخول موجودة", "details": "صفحة تسجيل الدخول و AuthContext متوفران", "timestamp": "2025-06-02T09:38:28.000Z"}, {"suite": "functionality", "test": "اختبار لوحة التحكم", "action": "testDashboard", "status": "passed", "message": "لوحة التحكم موجودة", "details": "1/1 صفحة لوحة تحكم", "timestamp": "2025-06-02T09:38:28.001Z"}, {"suite": "functionality", "test": "اختبار إدارة الحافلات", "action": "testBusManagement", "status": "passed", "message": "مكونات إدارة الحافلات موجودة", "details": "2/2 مكون حافلات", "timestamp": "2025-06-02T09:38:28.002Z"}]}, {"id": "performance", "name": "اختبار الأداء", "results": [{"suite": "performance", "test": "فحص سرعة التحميل", "action": "checkLoadSpeed", "status": "passed", "message": "حجم الملفات الأساسية مناسب", "details": "21.99 KB إجمالي", "timestamp": "2025-06-02T09:38:28.003Z"}, {"suite": "performance", "test": "فحص استهلاك الذاكرة", "action": "checkMemoryUsage", "status": "passed", "message": "استهلاك الذاكرة مقبول", "details": "4.80 MB مستخدم", "timestamp": "2025-06-02T09:38:28.003Z"}, {"suite": "performance", "test": "فحص حجم الملفات", "action": "checkFileSize", "status": "passed", "message": "حجم مشروع مناسب", "details": "3.02 MB في 265 ملف", "timestamp": "2025-06-02T09:38:28.022Z"}]}], "detailed_results": [{"suite": "basic", "test": "فحص البنية الأساسية", "action": "checkBasicStructure", "status": "passed", "message": "جميع المجلدات الأساسية موجودة", "details": "تم فحص 7 مجلد", "timestamp": "2025-06-02T09:38:27.987Z"}, {"suite": "basic", "test": "فحص ملفات التكوين", "action": "checkConfigFiles", "status": "passed", "message": "جميع ملفات التكوين موجودة", "details": "تم فحص 5 ملف", "timestamp": "2025-06-02T09:38:27.989Z"}, {"suite": "basic", "test": "فحص مسارات الاستيراد", "action": "checkImportPaths", "status": "passed", "message": "جميع مسارات الاستيراد صحيحة", "details": "51/51 مسار صحيح", "timestamp": "2025-06-02T09:38:27.993Z"}, {"suite": "components", "test": "فحص المكونات الأساسية", "action": "checkCoreComponents", "status": "passed", "message": "جميع المكونات الأساسية موجودة", "details": "3/3 مكون", "timestamp": "2025-06-02T09:38:27.994Z"}, {"suite": "components", "test": "فحص الصفحات الرئيسية", "action": "checkMainPages", "status": "passed", "message": "معظم الصفحات الرئيسية موجودة", "details": "3/3 صف<PERSON>ة", "timestamp": "2025-06-02T09:38:27.995Z"}, {"suite": "components", "test": "فحص مكونات UI", "action": "checkUIComponents", "status": "passed", "message": "مكونات UI موجودة", "details": "3/3 مجموعة مكونات", "timestamp": "2025-06-02T09:38:27.995Z"}, {"suite": "services", "test": "فحص خدمات قاعدة البيانات", "action": "checkDatabaseServices", "status": "passed", "message": "خدمات قاعدة البيانات موجودة", "details": "2/2 خدمة", "timestamp": "2025-06-02T09:38:27.997Z"}, {"suite": "services", "test": "فح<PERSON> خدمات الأمان", "action": "checkSecurityServices", "status": "passed", "message": "خدمات الأمان موجودة", "details": "2/2 خدمة أمان", "timestamp": "2025-06-02T09:38:27.998Z"}, {"suite": "services", "test": "فح<PERSON> خدمات الإشعارات", "action": "checkNotificationServices", "status": "passed", "message": "خدمات الإشعارات موجودة", "details": "2/2 خدمة إشعارات", "timestamp": "2025-06-02T09:38:27.999Z"}, {"suite": "functionality", "test": "اختبار تسجيل الدخول", "action": "testLogin", "status": "passed", "message": "مكونات تسجيل الدخول موجودة", "details": "صفحة تسجيل الدخول و AuthContext متوفران", "timestamp": "2025-06-02T09:38:28.000Z"}, {"suite": "functionality", "test": "اختبار لوحة التحكم", "action": "testDashboard", "status": "passed", "message": "لوحة التحكم موجودة", "details": "1/1 صفحة لوحة تحكم", "timestamp": "2025-06-02T09:38:28.001Z"}, {"suite": "functionality", "test": "اختبار إدارة الحافلات", "action": "testBusManagement", "status": "passed", "message": "مكونات إدارة الحافلات موجودة", "details": "2/2 مكون حافلات", "timestamp": "2025-06-02T09:38:28.002Z"}, {"suite": "performance", "test": "فحص سرعة التحميل", "action": "checkLoadSpeed", "status": "passed", "message": "حجم الملفات الأساسية مناسب", "details": "21.99 KB إجمالي", "timestamp": "2025-06-02T09:38:28.003Z"}, {"suite": "performance", "test": "فحص استهلاك الذاكرة", "action": "checkMemoryUsage", "status": "passed", "message": "استهلاك الذاكرة مقبول", "details": "4.80 MB مستخدم", "timestamp": "2025-06-02T09:38:28.003Z"}, {"suite": "performance", "test": "فحص حجم الملفات", "action": "checkFileSize", "status": "passed", "message": "حجم مشروع مناسب", "details": "3.02 MB في 265 ملف", "timestamp": "2025-06-02T09:38:28.022Z"}], "summary": {"overall_status": "excellent", "recommendations": ["جميع الاختبارات نجحت - التطبيق جاهز للاستخدام"]}}