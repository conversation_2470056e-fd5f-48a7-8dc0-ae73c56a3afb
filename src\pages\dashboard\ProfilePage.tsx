import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { User, Mail, Phone, Lock, Save } from "lucide-react";
import { SimpleProfileService } from "../../services/SimpleProfileService";
import { FallbackProfileService } from "../../services/FallbackProfileService";
import { supabase } from "../../lib/supabase";
import { useAuth } from "../../contexts/AuthContext";
import { Button } from "../../components/ui/Button";
import { ResponsiveLayout } from "../../components/layout/ResponsiveLayout";

const ProfilePage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();

  const [name, setName] = useState(user?.name || "");
  const [email, setEmail] = useState(user?.email || "");
  const [phone, setPhone] = useState("");
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  // Fetch additional user data on component mount
  React.useEffect(() => {
    if (user) {
      fetchUserDetails();
    }
  }, [user]);

  const fetchUserDetails = async () => {
    if (!user) return;

    try {
      const response = await SimpleProfileService.getProfile(user.id);

      if (response.success && response.data) {
        setPhone(response.data.phone || "");
      } else {
        console.error("Error fetching user details:", response.error);
      }
    } catch (error) {
      console.error("Error fetching user details:", error);
    }
  };

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setIsLoading(true);
    setMessage(null);

    try {
      console.log('Starting profile update for user:', user.id);
      console.log('Update data:', { name, phone, email });

      let updateSuccess = false;
      let errorMessage = '';

      // Method 1: Try simple RPC function first (most reliable)
      try {
        const { data: rpcResult, error: rpcError } = await supabase.rpc('simple_update_user_profile', {
          p_user_id: user.id,
          p_name: name,
          p_phone: phone || null
        });

        if (!rpcError && rpcResult) {
          console.log('Simple RPC update successful:', rpcResult);
          updateSuccess = true;
        } else if (rpcError) {
          console.log('Simple RPC failed, trying alternative method:', rpcError.message);
        }
      } catch (rpcError) {
        console.log('Simple RPC method not available, trying alternative...');
      }

      // Method 2: If RPC failed, try direct update with auth
      if (!updateSuccess) {
        try {
          // Update auth metadata first
          const { error: authError } = await supabase.auth.updateUser({
            data: {
              name: name,
              phone: phone || null,
            }
          });

          if (authError) {
            console.log('Auth update failed:', authError.message);
          }

          // Try SimpleProfileService
          const profileResponse = await SimpleProfileService.updateProfile(user.id, {
            name,
            phone: phone || null,
          });

          if (profileResponse.success) {
            console.log('SimpleProfileService update successful');
            updateSuccess = true;
          } else {
            console.log('SimpleProfileService failed, trying FallbackProfileService...');

            // Method 3: Try FallbackProfileService as last resort
            const fallbackResponse = await FallbackProfileService.updateProfile(user.id, {
              name,
              phone: phone || null,
            });

            if (fallbackResponse.success) {
              console.log('FallbackProfileService update successful');
              updateSuccess = true;
            } else {
              errorMessage = fallbackResponse.error || 'All profile update methods failed';
            }
          }
        } catch (serviceError) {
          console.error('SimpleProfileService failed:', serviceError);
          errorMessage = serviceError instanceof Error ? serviceError.message : 'Service update failed';
        }
      }

      // Handle email update separately (always use auth)
      if (email !== user.email) {
        console.log('Updating email from', user.email, 'to', email);
        const { error: emailError } = await supabase.auth.updateUser({
          email: email,
        });

        if (emailError) {
          console.error('Email update error:', emailError);
          // Don't fail the whole operation for email update
          setMessage({
            type: "error",
            text: `Profile updated but email update failed: ${emailError.message}`,
          });
          return;
        }
      }

      if (updateSuccess) {
        console.log('Profile update completed successfully');
        setMessage({
          type: "success",
          text: t("profile.updateSuccess"),
        });
      } else {
        throw new Error(errorMessage || "All update methods failed");
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      setMessage({
        type: "error",
        text: error instanceof Error ? error.message : t("profile.updateError"),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    // Validate passwords
    if (newPassword !== confirmPassword) {
      setMessage({
        type: "error",
        text: t("profile.passwordsDoNotMatch"),
      });
      return;
    }

    setIsLoading(true);
    setMessage(null);

    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        throw new Error(error.message || "Failed to update password");
      }

      setMessage({
        type: "success",
        text: t("profile.passwordUpdateSuccess"),
      });

      // Reset password fields
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
      setIsChangingPassword(false);
    } catch (error) {
      console.error("Error updating password:", error);
      setMessage({
        type: "error",
        text: error instanceof Error ? error.message : t("profile.passwordUpdateError"),
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <ResponsiveLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            {t("common.loading")}
          </div>
        </div>
      </ResponsiveLayout>
    );
  }

  return (
    <ResponsiveLayout>
      <div className="container mx-auto px-4 py-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          {t("profile.title")}
        </h1>

        {message && (
          <div
            className={`mb-6 p-4 rounded-md ${
              message.type === "success"
                ? "bg-green-50 text-green-800 dark:bg-green-900 dark:text-green-100"
                : "bg-red-50 text-red-800 dark:bg-red-900 dark:text-red-100"
            }`}
          >
            {message.text}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex flex-col items-center">
                <div className="h-24 w-24 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-600 dark:text-primary-300 mb-4">
                  <User size={48} />
                </div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {user.name}
                </h2>
                <p className="text-gray-500 dark:text-gray-400">{user.role}</p>
              </div>
            </div>
          </div>

          <div className="md:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {t("profile.personalInfo")}
                </h2>

                <form onSubmit={handleProfileUpdate}>
                  <div className="space-y-4">
                    <div>
                      <label
                        htmlFor="name"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                      >
                        {t("profile.name")}
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <User size={18} className="text-gray-400" />
                        </div>
                        <input
                          type="text"
                          id="name"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          className="pl-10 w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <label
                        htmlFor="email"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                      >
                        {t("profile.email")}
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Mail size={18} className="text-gray-400" />
                        </div>
                        <input
                          type="email"
                          id="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="pl-10 w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <label
                        htmlFor="phone"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                      >
                        {t("profile.phone")}
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Phone size={18} className="text-gray-400" />
                        </div>
                        <input
                          type="tel"
                          id="phone"
                          value={phone}
                          onChange={(e) => setPhone(e.target.value)}
                          className="pl-10 w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                    </div>

                    <div className="pt-2">
                      <Button
                        type="submit"
                        disabled={isLoading}
                        leftIcon={<Save size={18} />}
                      >
                        {isLoading
                          ? t("common.saving")
                          : t("profile.saveChanges")}
                      </Button>
                    </div>
                  </div>
                </form>
              </div>

              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {t("profile.security")}
                  </h2>
                  {!isChangingPassword && (
                    <Button
                      variant="outline"
                      onClick={() => setIsChangingPassword(true)}
                      leftIcon={<Lock size={18} />}
                    >
                      {t("profile.changePassword")}
                    </Button>
                  )}
                </div>

                {isChangingPassword && (
                  <form onSubmit={handlePasswordChange}>
                    <div className="space-y-4">
                      <div>
                        <label
                          htmlFor="currentPassword"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          {t("profile.currentPassword")}
                        </label>
                        <input
                          type="password"
                          id="currentPassword"
                          value={currentPassword}
                          onChange={(e) => setCurrentPassword(e.target.value)}
                          className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                          required
                        />
                      </div>

                      <div>
                        <label
                          htmlFor="newPassword"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          {t("profile.newPassword")}
                        </label>
                        <input
                          type="password"
                          id="newPassword"
                          value={newPassword}
                          onChange={(e) => setNewPassword(e.target.value)}
                          className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                          required
                          minLength={8}
                        />
                      </div>

                      <div>
                        <label
                          htmlFor="confirmPassword"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          {t("profile.confirmPassword")}
                        </label>
                        <input
                          type="password"
                          id="confirmPassword"
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                          required
                          minLength={8}
                        />
                      </div>

                      <div className="pt-2 flex gap-3">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setIsChangingPassword(false)}
                          disabled={isLoading}
                        >
                          {t("common.cancel")}
                        </Button>
                        <Button type="submit" disabled={isLoading}>
                          {isLoading
                            ? t("common.saving")
                            : t("profile.updatePassword")}
                        </Button>
                      </div>
                    </div>
                  </form>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </ResponsiveLayout>
  );
};

export { ProfilePage };

export default ProfilePage;
