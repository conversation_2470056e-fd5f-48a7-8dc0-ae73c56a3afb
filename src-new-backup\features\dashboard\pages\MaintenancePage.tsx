/**
 * Advanced Maintenance Management Page
 * Phase 4: Core System Functionality
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Wrench,
  Plus,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  TrendingUp,
  Filter,
  Search,
  Download,
} from 'lucide-react';
import { ResponsiveLayout } from '../../components/layout/ResponsiveLayout';
import { Button } from '../../components/ui/Button';
import { useAuth } from '../../contexts/AuthContext';
import { AdvancedMaintenanceService } from '../../services/AdvancedMaintenanceService';
import { DatabaseService } from '../../services/DatabaseService';

interface MaintenanceStats {
  total: number;
  scheduled: number;
  inProgress: number;
  completed: number;
  overdue: number;
  totalCost: number;
  avgCostPerBus: number;
}

interface MaintenanceAlert {
  id: string;
  busId: string;
  busPlateNumber: string;
  type: 'due_soon' | 'overdue' | 'critical';
  title: string;
  description: string;
  dueDate: string;
  daysOverdue?: number;
  estimatedCost: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export const MaintenancePage: React.FC = () => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<MaintenanceStats>({
    total: 0,
    scheduled: 0,
    inProgress: 0,
    completed: 0,
    overdue: 0,
    totalCost: 0,
    avgCostPerBus: 0,
  });
  const [alerts, setAlerts] = useState<MaintenanceAlert[]>([]);
  const [activeTab, setActiveTab] = useState<'overview' | 'schedule' | 'alerts' | 'reports'>('overview');

  const maintenanceService = AdvancedMaintenanceService.getInstance();
  const databaseService = DatabaseService.getInstance();

  useEffect(() => {
    loadMaintenanceData();
  }, [tenant?.id]);

  const loadMaintenanceData = async () => {
    try {
      setIsLoading(true);
      console.log('🔄 MaintenancePage: Loading maintenance data for user:', user?.role, 'tenant:', tenant?.id);

      // For admin users, load global data
      if (user?.role === 'admin') {
        console.log('🔧 MaintenancePage: Loading admin maintenance data');

        // Use mock data for admin until proper admin maintenance functions are implemented
        const mockStats: MaintenanceStats = {
          total: 45,
          scheduled: 12,
          inProgress: 8,
          completed: 20,
          overdue: 5,
          totalCost: 25000,
          avgCostPerBus: 1250,
        };

        const mockAlerts: MaintenanceAlert[] = [
          {
            id: '1',
            busId: 'bus-1',
            busPlateNumber: 'ABC-123',
            type: 'overdue',
            title: 'Engine Oil Change',
            description: 'ABC-123 - 5 days overdue',
            dueDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            daysOverdue: 5,
            estimatedCost: 150,
            priority: 'high',
          },
          {
            id: '2',
            busId: 'bus-2',
            busPlateNumber: 'XYZ-456',
            type: 'due_soon',
            title: 'Brake Inspection',
            description: 'XYZ-456 - Due in 3 days',
            dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
            estimatedCost: 300,
            priority: 'medium',
          },
        ];

        setStats(mockStats);
        setAlerts(mockAlerts);
        console.log('✅ MaintenancePage: Admin data loaded successfully');
        return;
      }

      // For tenant users, load tenant-specific data
      if (!tenant?.id) {
        console.log('⚠️ MaintenancePage: No tenant ID available');
        setStats({
          total: 0,
          scheduled: 0,
          inProgress: 0,
          completed: 0,
          overdue: 0,
          totalCost: 0,
          avgCostPerBus: 0,
        });
        setAlerts([]);
        return;
      }

      console.log('🔄 MaintenancePage: Loading tenant maintenance data for:', tenant.id);

      try {
        // Load real maintenance statistics
        const realStats = await databaseService.getMaintenanceStats(tenant.id);

        // Transform to component format
        const transformedStats: MaintenanceStats = {
          total: realStats.total_maintenance,
          scheduled: realStats.scheduled_maintenance,
          inProgress: realStats.in_progress_maintenance,
          completed: realStats.completed_maintenance,
          overdue: realStats.overdue_maintenance,
          totalCost: realStats.total_cost,
          avgCostPerBus: realStats.average_cost,
        };

        setStats(transformedStats);

        // Load maintenance alerts
        const alertsData = await databaseService.getMaintenanceAlerts(tenant.id);

        // Transform alerts data
        const transformedAlerts: MaintenanceAlert[] = alertsData.map(alert => ({
          id: alert.id,
          busId: alert.bus_id,
          busPlateNumber: alert.bus_plate_number || 'Unknown',
          type: alert.days_overdue > 0 ? 'overdue' : 'due_soon',
          title: `${alert.maintenance_type}`,
          description: `${alert.bus_plate_number} - ${alert.days_overdue > 0 ? `${alert.days_overdue} days overdue` : 'Due soon'}`,
          dueDate: alert.scheduled_date,
          daysOverdue: alert.days_overdue,
          estimatedCost: 0, // سيتم إضافتها لاحقاً
          priority: alert.days_overdue > 7 ? 'critical' : alert.days_overdue > 3 ? 'high' : 'medium',
        }));

        setAlerts(transformedAlerts);
        console.log('✅ MaintenancePage: Tenant data loaded successfully');
      } catch (error) {
        console.error('❌ MaintenancePage: Error loading tenant maintenance data:', error);
        // Use fallback data
        setStats({
          total: 0,
          scheduled: 0,
          inProgress: 0,
          completed: 0,
          overdue: 0,
          totalCost: 0,
          avgCostPerBus: 0,
        });
        setAlerts([]);
      }
    } catch (error) {
      console.error('❌ MaintenancePage: Error loading maintenance data:', error);
      // Set fallback data
      setStats({
        total: 0,
        scheduled: 0,
        inProgress: 0,
        completed: 0,
        overdue: 0,
        totalCost: 0,
        avgCostPerBus: 0,
      });
      setAlerts([]);
    } finally {
      setIsLoading(false);
      console.log('✅ MaintenancePage: Loading completed');
    }
  };

  const getAlertColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default: return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'overdue': return <AlertTriangle className="w-5 h-5 text-red-500" />;
      case 'critical': return <AlertTriangle className="w-5 h-5 text-red-500" />;
      case 'due_soon': return <Clock className="w-5 h-5 text-yellow-500" />;
      default: return <Wrench className="w-5 h-5 text-blue-500" />;
    }
  };

  if (isLoading) {
    return (
      <ResponsiveLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </ResponsiveLayout>
    );
  }

  return (
    <ResponsiveLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('maintenance.title')}
            </h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {t('maintenance.description')}
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex space-x-3">
            <Button variant="outline" leftIcon={<Download />}>
              {t('maintenance.exportReport')}
            </Button>
            <Button leftIcon={<Plus />}>
              {t('maintenance.scheduleMaintenance')}
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Wrench className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('maintenance.totalMaintenance')}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {stats.total}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                <Clock className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('maintenance.scheduled')}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {stats.scheduled}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('maintenance.overdue')}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {stats.overdue}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <DollarSign className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('maintenance.totalCost')}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  ${stats.totalCost.toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'overview', label: t('maintenance.overview'), icon: TrendingUp },
              { key: 'schedule', label: t('maintenance.schedule'), icon: Calendar },
              { key: 'alerts', label: t('maintenance.alerts'), icon: AlertTriangle },
              { key: 'reports', label: t('maintenance.reports'), icon: Download },
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setActiveTab(key as any)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === key
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Maintenance */}
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  {t('maintenance.recentMaintenance')}
                </h3>
                <div className="space-y-4">
                  {[1, 2, 3].map((item) => (
                    <div key={item} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center">
                        <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            Engine Oil Change - Bus ABC-123
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            Completed 2 days ago
                          </p>
                        </div>
                      </div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        $150
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Upcoming Maintenance */}
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  {t('maintenance.upcomingMaintenance')}
                </h3>
                <div className="space-y-4">
                  {[1, 2, 3].map((item) => (
                    <div key={item} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center">
                        <Clock className="w-5 h-5 text-yellow-500 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            Brake Inspection - Bus XYZ-456
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            Due in 3 days
                          </p>
                        </div>
                      </div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        $300
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'alerts' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {t('maintenance.maintenanceAlerts')}
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {t('maintenance.alertsDescription')}
                </p>
              </div>
              <div className="p-6">
                {alerts.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400">
                      {t('maintenance.noAlerts')}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {alerts.map((alert) => (
                      <div
                        key={alert.id}
                        className={`p-4 rounded-lg border ${getAlertColor(alert.priority)}`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start">
                            {getAlertIcon(alert.type)}
                            <div className="ml-3">
                              <h4 className="text-sm font-medium">
                                {alert.title}
                              </h4>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {alert.description}
                              </p>
                              <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                                <span>Due: {new Date(alert.dueDate).toLocaleDateString()}</span>
                                {alert.daysOverdue && (
                                  <span className="ml-4 text-red-600">
                                    {alert.daysOverdue} days overdue
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <span className="text-sm font-medium">
                              ${alert.estimatedCost}
                            </span>
                            <div className="mt-2">
                              <Button size="sm" variant="outline">
                                {t('maintenance.schedule')}
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Other tabs content would go here */}
        </div>
      </div>
    </ResponsiveLayout>
  );
};

export default MaintenancePage;
