import { spawn } from 'child_process';

console.log('🚀 Supabase MCP Server Demo');
console.log('============================\n');

// Start the MCP server
const server = spawn('cmd', [
  '/c',
  'npx',
  '-y',
  '@supabase/mcp-server-supabase@latest',
  '--access-token',
  '********************************************'
], {
  stdio: ['pipe', 'pipe', 'pipe']
});

let messageId = 1;
let responseBuffer = '';

// Handle server output
server.stdout.on('data', (data) => {
  responseBuffer += data.toString();
  
  // Process complete JSON messages
  const lines = responseBuffer.split('\n');
  responseBuffer = lines.pop() || ''; // Keep incomplete line in buffer
  
  for (const line of lines) {
    if (line.trim()) {
      try {
        const response = JSON.parse(line.trim());
        handleResponse(response);
      } catch (e) {
        // Skip non-JSON output
      }
    }
  }
});

server.stderr.on('data', (data) => {
  console.error('❌ Server stderr:', data.toString());
});

function sendMessage(message) {
  message.id = messageId++;
  console.log(`📤 Sending: ${message.method}`);
  server.stdin.write(JSON.stringify(message) + '\n');
  return message.id;
}

function handleResponse(response) {
  console.log(`📨 Response (ID: ${response.id}):`);
  
  if (response.result) {
    if (response.id === 1) {
      // Initialize response
      console.log(`✅ Server initialized: ${response.result.serverInfo.name} v${response.result.serverInfo.version}`);
      
      // Step 2: List tools
      setTimeout(() => {
        sendMessage({
          jsonrpc: "2.0",
          method: "tools/list"
        });
      }, 1000);
      
    } else if (response.result.tools) {
      // Tools list response
      console.log(`\n🛠️  Available Tools (${response.result.tools.length}):`);
      response.result.tools.forEach((tool, index) => {
        console.log(`   ${index + 1}. ${tool.name}`);
        console.log(`      ${tool.description}`);
        if (tool.inputSchema && tool.inputSchema.properties) {
          const params = Object.keys(tool.inputSchema.properties);
          if (params.length > 0) {
            console.log(`      Parameters: ${params.join(', ')}`);
          }
        }
        console.log('');
      });
      
      // Step 3: Try a simple tool call
      setTimeout(() => {
        console.log('🔧 Testing list_projects tool...');
        sendMessage({
          jsonrpc: "2.0",
          method: "tools/call",
          params: {
            name: "list_projects",
            arguments: {}
          }
        });
      }, 1000);
      
    } else if (response.result.content) {
      // Tool call response
      console.log('\n📋 Tool Result:');
      response.result.content.forEach((content, index) => {
        if (content.type === 'text') {
          try {
            const data = JSON.parse(content.text);
            if (Array.isArray(data)) {
              console.log(`   Found ${data.length} projects:`);
              data.forEach((project, i) => {
                console.log(`   ${i + 1}. ${project.name} (${project.id})`);
                console.log(`      Status: ${project.status}`);
                console.log(`      Region: ${project.region}`);
                console.log(`      Created: ${new Date(project.created_at).toLocaleDateString()}`);
              });
            } else {
              console.log('   ', JSON.stringify(data, null, 2));
            }
          } catch (e) {
            console.log('   ', content.text);
          }
        }
      });
      
      // Cleanup
      setTimeout(() => {
        console.log('\n✅ Demo completed successfully!');
        server.kill();
        process.exit(0);
      }, 1000);
    }
  } else if (response.error) {
    console.log(`❌ Error: ${response.error.message}`);
    if (response.error.code) {
      console.log(`   Code: ${response.error.code}`);
    }
  }
}

// Step 1: Initialize
console.log('📡 Initializing MCP server...');
sendMessage({
  jsonrpc: "2.0",
  method: "initialize",
  params: {
    protocolVersion: "2024-11-05",
    capabilities: { tools: {} },
    clientInfo: { name: "supabase-demo", version: "1.0.0" }
  }
});

// Safety timeout
setTimeout(() => {
  console.log('\n⏰ Demo timeout reached');
  server.kill();
  process.exit(1);
}, 20000);