/**
 * أداة فحص مسارات الاستيراد بالتفصيل
 * Detailed Import Path Debugging Tool
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 فحص مسارات الاستيراد بالتفصيل...\n');

/**
 * استخراج مسارات الاستيراد من الملف
 */
function extractImports(content) {
  const importRegex = /import.*from\s+['"`]([^'"`]+)['"`]/g;
  const imports = [];
  let match;
  
  while ((match = importRegex.exec(content)) !== null) {
    imports.push({
      path: match[1],
      line: content.substring(0, match.index).split('\n').length,
      fullMatch: match[0]
    });
  }
  
  return imports;
}

/**
 * التحقق من صحة مسار الاستيراد
 */
function validateImportPath(importPath, fromFile) {
  // تجاهل المسارات الخارجية
  if (!importPath.startsWith('.')) {
    return { valid: true, reason: 'external module' };
  }

  const fromDir = path.dirname(fromFile);
  const resolvedPath = path.resolve(fromDir, importPath);
  
  // فحص الملف مع امتدادات مختلفة
  const extensions = ['', '.ts', '.tsx', '.js', '.jsx'];
  
  for (const ext of extensions) {
    const fullPath = resolvedPath + ext;
    if (fs.existsSync(fullPath)) {
      return { valid: true, reason: `found as ${fullPath}` };
    }
  }
  
  // فحص إذا كان مجلد مع index
  const indexExtensions = ['/index.ts', '/index.tsx', '/index.js', '/index.jsx'];
  for (const indexExt of indexExtensions) {
    const fullPath = resolvedPath + indexExt;
    if (fs.existsSync(fullPath)) {
      return { valid: true, reason: `found as ${fullPath}` };
    }
  }
  
  return { valid: false, reason: `not found at ${resolvedPath}` };
}

/**
 * فحص ملف واحد
 */
function checkFile(filePath) {
  console.log(`📄 فحص ملف: ${filePath}`);
  console.log('='.repeat(50));

  if (!fs.existsSync(filePath)) {
    console.log('❌ الملف غير موجود\n');
    return;
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const imports = extractImports(content);

    console.log(`📊 عدد الاستيرادات: ${imports.length}\n`);

    let validCount = 0;
    let invalidCount = 0;

    for (const importItem of imports) {
      const validation = validateImportPath(importItem.path, filePath);
      
      if (validation.valid) {
        validCount++;
        console.log(`✅ السطر ${importItem.line}: ${importItem.path}`);
        console.log(`   السبب: ${validation.reason}`);
      } else {
        invalidCount++;
        console.log(`❌ السطر ${importItem.line}: ${importItem.path}`);
        console.log(`   السبب: ${validation.reason}`);
        console.log(`   الاستيراد الكامل: ${importItem.fullMatch}`);
      }
      console.log('');
    }

    console.log(`📈 النتيجة: ${validCount} صحيح، ${invalidCount} خاطئ\n`);

  } catch (error) {
    console.log(`❌ خطأ في قراءة الملف: ${error.message}\n`);
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  const filesToCheck = [
    'src/main.tsx',
    'src/App.tsx'
  ];

  for (const file of filesToCheck) {
    checkFile(file);
  }
}

// تشغيل الفحص
main();
