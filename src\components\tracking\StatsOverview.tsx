/**
 * Stats Overview Component
 * مكون عرض الإحصائيات العامة
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Bus,
  Navigation,
  Users,
  Clock,
  AlertTriangle,
  Activity,
  TrendingUp,
  Zap,
  CheckCircle,
  XCircle,
  Wrench,
} from 'lucide-react';

export interface TrackingStats {
  totalBuses: number;
  activeBuses: number;
  stoppedBuses: number;
  maintenanceBuses: number;
  emergencyBuses: number;
  averageSpeed: number;
  totalStudents: number;
  onTimePercentage: number;
  activeAlerts: number;
}

interface StatsOverviewProps {
  stats: TrackingStats;
}

export const StatsOverview: React.FC<StatsOverviewProps> = ({ stats }) => {
  const { t } = useTranslation();

  const statCards = [
    {
      id: 'total',
      title: t('tracking.stats.totalBuses'),
      value: stats.totalBuses,
      icon: Bus,
      color: 'blue',
      bgColor: 'bg-blue-50',
      iconColor: 'text-blue-600',
      borderColor: 'border-blue-200',
    },
    {
      id: 'active',
      title: t('tracking.stats.activeBuses'),
      value: stats.activeBuses,
      icon: Navigation,
      color: 'green',
      bgColor: 'bg-green-50',
      iconColor: 'text-green-600',
      borderColor: 'border-green-200',
      percentage: stats.totalBuses > 0 ? Math.round((stats.activeBuses / stats.totalBuses) * 100) : 0,
    },
    {
      id: 'stopped',
      title: t('tracking.stats.stoppedBuses'),
      value: stats.stoppedBuses,
      icon: XCircle,
      color: 'gray',
      bgColor: 'bg-gray-50',
      iconColor: 'text-gray-600',
      borderColor: 'border-gray-200',
    },
    {
      id: 'maintenance',
      title: t('tracking.stats.maintenanceBuses'),
      value: stats.maintenanceBuses,
      icon: Wrench,
      color: 'yellow',
      bgColor: 'bg-yellow-50',
      iconColor: 'text-yellow-600',
      borderColor: 'border-yellow-200',
    },
    {
      id: 'emergency',
      title: t('tracking.stats.emergencyBuses'),
      value: stats.emergencyBuses,
      icon: AlertTriangle,
      color: 'red',
      bgColor: 'bg-red-50',
      iconColor: 'text-red-600',
      borderColor: 'border-red-200',
    },
    {
      id: 'speed',
      title: t('tracking.stats.averageSpeed'),
      value: `${Math.round(stats.averageSpeed)} km/h`,
      icon: Activity,
      color: 'purple',
      bgColor: 'bg-purple-50',
      iconColor: 'text-purple-600',
      borderColor: 'border-purple-200',
    },
    {
      id: 'students',
      title: t('tracking.stats.totalStudents'),
      value: stats.totalStudents,
      icon: Users,
      color: 'indigo',
      bgColor: 'bg-indigo-50',
      iconColor: 'text-indigo-600',
      borderColor: 'border-indigo-200',
    },
    {
      id: 'ontime',
      title: t('tracking.stats.onTimePercentage'),
      value: `${Math.round(stats.onTimePercentage)}%`,
      icon: CheckCircle,
      color: 'emerald',
      bgColor: 'bg-emerald-50',
      iconColor: 'text-emerald-600',
      borderColor: 'border-emerald-200',
      trend: stats.onTimePercentage >= 90 ? 'up' : 'down',
    },
  ];

  const getStatusColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getTrendIcon = (trend?: 'up' | 'down') => {
    if (trend === 'up') return <TrendingUp className="w-3 h-3 text-green-500" />;
    if (trend === 'down') return <TrendingUp className="w-3 h-3 text-red-500 rotate-180" />;
    return null;
  };

  return (
    <div className="px-6 py-4 bg-white border-b border-gray-100">
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
        {statCards.map((stat) => {
          const IconComponent = stat.icon;
          
          return (
            <div
              key={stat.id}
              className={`
                relative p-4 rounded-xl border transition-all duration-200 hover:shadow-md
                ${stat.bgColor} ${stat.borderColor}
              `}
            >
              {/* Icon */}
              <div className="flex items-center justify-between mb-2">
                <div className={`p-2 rounded-lg bg-white shadow-sm`}>
                  <IconComponent className={`w-4 h-4 ${stat.iconColor}`} />
                </div>
                {stat.trend && (
                  <div className="flex items-center">
                    {getTrendIcon(stat.trend)}
                  </div>
                )}
              </div>

              {/* Value */}
              <div className="mb-1">
                <div className="text-2xl font-bold text-gray-900">
                  {typeof stat.value === 'number' ? stat.value.toLocaleString() : stat.value}
                </div>
                {stat.percentage !== undefined && (
                  <div className={`text-xs font-medium ${getStatusColor(stat.percentage)}`}>
                    {stat.percentage}% of total
                  </div>
                )}
              </div>

              {/* Title */}
              <div className="text-xs font-medium text-gray-600 truncate">
                {stat.title}
              </div>

              {/* Progress bar for some stats */}
              {stat.id === 'active' && stats.totalBuses > 0 && (
                <div className="mt-2">
                  <div className="w-full bg-gray-200 rounded-full h-1">
                    <div
                      className="bg-green-500 h-1 rounded-full transition-all duration-300"
                      style={{ width: `${(stats.activeBuses / stats.totalBuses) * 100}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {stat.id === 'ontime' && (
                <div className="mt-2">
                  <div className="w-full bg-gray-200 rounded-full h-1">
                    <div
                      className={`h-1 rounded-full transition-all duration-300 ${
                        stats.onTimePercentage >= 90 ? 'bg-green-500' :
                        stats.onTimePercentage >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${stats.onTimePercentage}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="mt-4 flex items-center justify-between">
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Active</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
            <span>Stopped</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span>Maintenance</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <span>Emergency</span>
          </div>
        </div>

        {/* Alerts indicator */}
        {stats.activeAlerts > 0 && (
          <div className="flex items-center space-x-2 px-3 py-1 bg-red-50 border border-red-200 rounded-full">
            <AlertTriangle className="w-4 h-4 text-red-600" />
            <span className="text-sm font-medium text-red-700">
              {stats.activeAlerts} {t('tracking.stats.activeAlerts')}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default StatsOverview;
