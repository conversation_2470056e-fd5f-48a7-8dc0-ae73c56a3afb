/**
 * Enhanced Validation Service - خدمة التحقق المحسنة من صحة البيانات
 * يحل مشاكل التحقق من صحة البيانات والقيود التجارية
 */

import { supabase } from './supabase';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

export class EnhancedValidationService {
  
  /**
   * التحقق من صحة البريد الإلكتروني
   */
  static validateEmail(email: string): { isValid: boolean; error?: string } {
    if (!email || email.trim().length === 0) {
      return { isValid: false, error: 'Email is required' };
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      return { isValid: false, error: 'Invalid email format' };
    }

    // التحقق من طول البريد الإلكتروني
    if (email.length > 254) {
      return { isValid: false, error: 'Email is too long' };
    }

    return { isValid: true };
  }

  /**
   * التحقق من صحة رقم الهاتف السعودي
   */
  static validatePhone(phone: string): { isValid: boolean; error?: string } {
    if (!phone || phone.trim().length === 0) {
      return { isValid: false, error: 'Phone number is required' };
    }

    // إزالة المسافات والرموز
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    
    // التحقق من التنسيق السعودي
    const saudiPhoneRegex = /^(\+966|966|0)?[5][0-9]{8}$/;
    if (!saudiPhoneRegex.test(cleanPhone)) {
      return { isValid: false, error: 'Invalid Saudi phone number format. Should be like: 0501234567 or +966501234567' };
    }

    return { isValid: true };
  }

  /**
   * التحقق من صحة التاريخ
   */
  static validateDate(dateString: string, options?: {
    allowFuture?: boolean;
    minAge?: number;
    maxAge?: number;
  }): { isValid: boolean; error?: string } {
    
    if (!dateString || dateString.trim().length === 0) {
      return { isValid: false, error: 'Date is required' };
    }

    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return { isValid: false, error: 'Invalid date format' };
    }

    const now = new Date();
    
    // التحقق من التاريخ في المستقبل
    if (!options?.allowFuture && date > now) {
      return { isValid: false, error: 'Date cannot be in the future' };
    }

    // التحقق من العمر الأدنى
    if (options?.minAge) {
      const minDate = new Date();
      minDate.setFullYear(minDate.getFullYear() - options.minAge);
      if (date > minDate) {
        return { isValid: false, error: `Age must be at least ${options.minAge} years` };
      }
    }

    // التحقق من العمر الأقصى
    if (options?.maxAge) {
      const maxDate = new Date();
      maxDate.setFullYear(maxDate.getFullYear() - options.maxAge);
      if (date < maxDate) {
        return { isValid: false, error: `Age cannot exceed ${options.maxAge} years` };
      }
    }

    return { isValid: true };
  }

  /**
   * التحقق من تكرار رقم الطالب
   */
  static async validateUniqueStudentId(
    studentId: string, 
    tenantId: string, 
    excludeId?: string
  ): Promise<{ isValid: boolean; error?: string }> {
    
    if (!studentId || studentId.trim().length === 0) {
      return { isValid: false, error: 'Student ID is required' };
    }

    try {
      const { data, error } = await supabase.rpc('check_unique_student_id', {
        p_student_id: studentId.trim(),
        p_tenant_id: tenantId,
        p_exclude_id: excludeId || null
      });

      if (error) {
        console.error('Error checking student ID uniqueness:', error);
        return { isValid: false, error: 'Unable to verify student ID uniqueness' };
      }

      if (!data) {
        return { isValid: false, error: 'Student ID already exists in this school' };
      }

      return { isValid: true };
    } catch (error) {
      console.error('Error in validateUniqueStudentId:', error);
      return { isValid: false, error: 'System error while checking student ID' };
    }
  }

  /**
   * التحقق من القيود التجارية للحافلات
   */
  static async validateBusCapacity(
    busId: string, 
    newStudentsCount: number = 1
  ): Promise<{ isValid: boolean; error?: string; currentCapacity?: number; maxCapacity?: number }> {
    
    if (newStudentsCount < 1) {
      return { isValid: false, error: 'Number of students must be at least 1' };
    }

    try {
      const { data, error } = await supabase.rpc('check_bus_capacity', {
        p_bus_id: busId,
        p_additional_students: newStudentsCount
      });

      if (error) {
        console.error('Error checking bus capacity:', error);
        return { isValid: false, error: 'Unable to verify bus capacity' };
      }

      if (!data) {
        // الحصول على تفاصيل السعة للرسالة
        const { data: busData } = await supabase
          .from('buses')
          .select('capacity')
          .eq('id', busId)
          .single();

        const { data: currentStudents } = await supabase
          .from('students')
          .select('id')
          .eq('route_stop_id', busId); // تحتاج تعديل حسب هيكل قاعدة البيانات

        return { 
          isValid: false, 
          error: 'Bus capacity exceeded',
          currentCapacity: currentStudents?.length || 0,
          maxCapacity: busData?.capacity || 0
        };
      }

      return { isValid: true };
    } catch (error) {
      console.error('Error in validateBusCapacity:', error);
      return { isValid: false, error: 'System error while checking bus capacity' };
    }
  }

  /**
   * التحقق من أن السائق غير مُعين لحافلة أخرى
   */
  static async validateDriverAssignment(
    driverId: string, 
    excludeBusId?: string
  ): Promise<{ isValid: boolean; error?: string; conflictingBusId?: string }> {
    
    if (!driverId || driverId.trim().length === 0) {
      return { isValid: false, error: 'Driver ID is required' };
    }

    try {
      const { data, error } = await supabase.rpc('check_driver_assignment', {
        p_driver_id: driverId,
        p_exclude_bus_id: excludeBusId || null
      });

      if (error) {
        console.error('Error checking driver assignment:', error);
        return { isValid: false, error: 'Unable to verify driver assignment' };
      }

      if (!data) {
        // الحصول على معرف الحافلة المتضاربة
        const { data: conflictData } = await supabase
          .from('buses')
          .select('id, plate_number')
          .eq('driver_id', driverId)
          .eq('is_active', true)
          .neq('id', excludeBusId || '')
          .single();

        return { 
          isValid: false, 
          error: `Driver is already assigned to bus ${conflictData?.plate_number || 'Unknown'}`,
          conflictingBusId: conflictData?.id
        };
      }

      return { isValid: true };
    } catch (error) {
      console.error('Error in validateDriverAssignment:', error);
      return { isValid: false, error: 'System error while checking driver assignment' };
    }
  }

  /**
   * التحقق من صحة رقم لوحة السيارة
   */
  static validatePlateNumber(plateNumber: string): { isValid: boolean; error?: string } {
    if (!plateNumber || plateNumber.trim().length === 0) {
      return { isValid: false, error: 'Plate number is required' };
    }

    // التنسيق السعودي للوحات: ABC-1234 أو AB-1234
    const saudiPlateRegex = /^[A-Z]{1,3}-[0-9]{1,4}$/;
    const cleanPlate = plateNumber.trim().toUpperCase();
    
    if (!saudiPlateRegex.test(cleanPlate)) {
      return { isValid: false, error: 'Invalid plate number format. Should be like: ABC-1234' };
    }

    return { isValid: true };
  }

  /**
   * التحقق من صحة الصف الدراسي
   */
  static validateGrade(grade: string): { isValid: boolean; error?: string } {
    if (!grade || grade.trim().length === 0) {
      return { isValid: false, error: 'Grade is required' };
    }

    const validGrades = [
      'KG1', 'KG2', 'KG3', // روضة
      '1', '2', '3', '4', '5', '6', // ابتدائي
      '7', '8', '9', // متوسط
      '10', '11', '12' // ثانوي
    ];

    if (!validGrades.includes(grade.trim())) {
      return { isValid: false, error: 'Invalid grade. Must be KG1-KG3, 1-12' };
    }

    return { isValid: true };
  }

  /**
   * التحقق الشامل من بيانات الطالب
   */
  static async validateStudentData(data: {
    name: string;
    student_id: string;
    grade: string;
    email?: string;
    phone?: string;
    date_of_birth?: string;
    tenant_id: string;
    excludeId?: string;
  }): Promise<ValidationResult> {
    
    const errors: string[] = [];
    const warnings: string[] = [];

    // التحقق من الاسم
    if (!data.name || data.name.trim().length < 2) {
      errors.push('Student name must be at least 2 characters long');
    } else if (data.name.length > 100) {
      errors.push('Student name is too long (max 100 characters)');
    }

    // التحقق من رقم الطالب
    if (!data.student_id || data.student_id.trim().length < 3) {
      errors.push('Student ID must be at least 3 characters long');
    } else {
      const uniqueCheck = await this.validateUniqueStudentId(
        data.student_id, 
        data.tenant_id, 
        data.excludeId
      );
      if (!uniqueCheck.isValid) {
        errors.push(uniqueCheck.error || 'Student ID validation failed');
      }
    }

    // التحقق من الصف
    const gradeCheck = this.validateGrade(data.grade);
    if (!gradeCheck.isValid) {
      errors.push(gradeCheck.error || 'Invalid grade');
    }

    // التحقق من البريد الإلكتروني (اختياري)
    if (data.email) {
      const emailCheck = this.validateEmail(data.email);
      if (!emailCheck.isValid) {
        errors.push(emailCheck.error || 'Invalid email');
      }
    }

    // التحقق من رقم الهاتف (اختياري)
    if (data.phone) {
      const phoneCheck = this.validatePhone(data.phone);
      if (!phoneCheck.isValid) {
        errors.push(phoneCheck.error || 'Invalid phone number');
      }
    }

    // التحقق من تاريخ الميلاد (اختياري)
    if (data.date_of_birth) {
      const dateCheck = this.validateDate(data.date_of_birth, {
        allowFuture: false,
        minAge: 3,
        maxAge: 25
      });
      if (!dateCheck.isValid) {
        errors.push(dateCheck.error || 'Invalid birth date');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  /**
   * التحقق الشامل من بيانات الحافلة
   */
  static async validateBusData(data: {
    plate_number: string;
    capacity: number;
    driver_id?: string;
    excludeId?: string;
  }): Promise<ValidationResult> {
    
    const errors: string[] = [];

    // التحقق من رقم اللوحة
    const plateCheck = this.validatePlateNumber(data.plate_number);
    if (!plateCheck.isValid) {
      errors.push(plateCheck.error || 'Invalid plate number');
    }

    // التحقق من السعة
    if (!data.capacity || data.capacity < 1 || data.capacity > 100) {
      errors.push('Bus capacity must be between 1 and 100');
    }

    // التحقق من تعيين السائق (اختياري)
    if (data.driver_id) {
      const driverCheck = await this.validateDriverAssignment(data.driver_id, data.excludeId);
      if (!driverCheck.isValid) {
        errors.push(driverCheck.error || 'Driver assignment validation failed');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export default EnhancedValidationService;
