/**
 * School Service
 * Handles all school-related API operations
 * Phase 2: Application Structure Reorganization
 */

import { BaseService } from '../base/BaseService';
import {
  School,
  CreateSchoolRequest,
  UpdateSchoolRequest,
  APIResponse,
  PaginatedResponse,
  PaginationParams,
} from '../../api/types';

export interface SchoolListParams extends PaginationParams {
  is_active?: boolean;
  manager_id?: string;
  search?: string;
  search_fields?: string[];
}

/**
 * School Service Class
 * Implements comprehensive school management operations
 */
export class SchoolService extends BaseService {
  private readonly endpoint = '/schools';

  /**
   * Get paginated list of schools
   */
  async getSchools(params: SchoolListParams = {}): Promise<APIResponse<PaginatedResponse<School>>> {
    return this.getPaginated<School>(this.endpoint, params);
  }

  /**
   * Get school by ID
   */
  async getSchoolById(id: string): Promise<APIResponse<School>> {
    return this.get<School>(`${this.endpoint}/${id}`);
  }

  /**
   * Get current user's school
   */
  async getCurrentSchool(): Promise<APIResponse<School>> {
    return this.get<School>(`${this.endpoint}/current`);
  }

  /**
   * Create new school
   */
  async createSchool(schoolData: CreateSchoolRequest): Promise<APIResponse<School>> {
    const validation = this.validateCreateSchoolData(schoolData);
    if (!validation.isValid) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid school data',
          details: validation.errors,
        },
      };
    }

    return this.post<School>(this.endpoint, schoolData);
  }

  /**
   * Update school
   */
  async updateSchool(id: string, schoolData: UpdateSchoolRequest): Promise<APIResponse<School>> {
    const validation = this.validateUpdateSchoolData(schoolData);
    if (!validation.isValid) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid update data',
          details: validation.errors,
        },
      };
    }

    return this.put<School>(`${this.endpoint}/${id}`, schoolData);
  }

  /**
   * Delete school
   */
  async deleteSchool(id: string): Promise<APIResponse<{ message: string }>> {
    return this.delete<{ message: string }>(`${this.endpoint}/${id}`);
  }

  /**
   * Toggle school status
   */
  async toggleSchoolStatus(id: string, isActive: boolean): Promise<APIResponse<School>> {
    return this.patch<School>(`${this.endpoint}/${id}`, { is_active: isActive });
  }

  /**
   * Get school statistics
   */
  async getSchoolStats(id?: string): Promise<APIResponse<SchoolStats>> {
    const endpoint = id ? `${this.endpoint}/${id}/stats` : `${this.endpoint}/stats`;
    return this.get<SchoolStats>(endpoint);
  }

  /**
   * Assign manager to school
   */
  async assignManager(schoolId: string, managerId: string): Promise<APIResponse<School>> {
    return this.patch<School>(`${this.endpoint}/${schoolId}`, { manager_id: managerId });
  }

  /**
   * Update school settings
   */
  async updateSchoolSettings(id: string, settings: any): Promise<APIResponse<School>> {
    return this.patch<School>(`${this.endpoint}/${id}/settings`, settings);
  }

  /**
   * Upload school logo
   */
  async uploadSchoolLogo(id: string, file: File): Promise<APIResponse<{ logo_url: string }>> {
    const formData = new FormData();
    formData.append('logo', file);

    return this.request<{ logo_url: string }>(`${this.endpoint}/${id}/logo`, {
      method: 'POST',
      body: formData,
      headers: {}, // Let browser set Content-Type for FormData
    });
  }

  /**
   * Search schools
   */
  async searchSchools(
    query: string,
    fields: string[] = ['name', 'code']
  ): Promise<APIResponse<School[]>> {
    const params: SchoolListParams = {
      search: query,
      search_fields: fields,
    };

    const response = await this.getSchools(params);
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<School[]>;
  }

  /**
   * Get schools by manager
   */
  async getSchoolsByManager(managerId: string): Promise<APIResponse<School[]>> {
    const response = await this.getSchools({ manager_id: managerId });
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<School[]>;
  }

  /**
   * Validation methods
   */
  private validateCreateSchoolData(data: CreateSchoolRequest): ValidationResult {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length < 2) {
      errors.push('School name must be at least 2 characters long');
    }

    if (!data.code || data.code.trim().length < 2) {
      errors.push('School code must be at least 2 characters long');
    }

    if (!data.address || !data.address.street || !data.address.city) {
      errors.push('Complete address is required');
    }

    if (!data.contact || !data.contact.phone || !data.contact.email) {
      errors.push('Contact information is required');
    }

    if (data.contact?.email && !this.isValidEmail(data.contact.email)) {
      errors.push('Valid email is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private validateUpdateSchoolData(data: UpdateSchoolRequest): ValidationResult {
    const errors: string[] = [];

    if (data.name !== undefined && (!data.name || data.name.trim().length < 2)) {
      errors.push('School name must be at least 2 characters long');
    }

    if (data.code !== undefined && (!data.code || data.code.trim().length < 2)) {
      errors.push('School code must be at least 2 characters long');
    }

    if (data.contact?.email && !this.isValidEmail(data.contact.email)) {
      errors.push('Valid email is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

// ============================================================================
// Supporting Types
// ============================================================================

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

interface SchoolStats {
  total_students: number;
  total_buses: number;
  total_routes: number;
  total_drivers: number;
  total_parents: number;
  active_students: number;
  active_buses: number;
  active_routes: number;
  attendance_rate: number;
  last_updated: string;
}

// ============================================================================
// Service Instance
// ============================================================================

export const schoolService = new SchoolService();
