/**
 * API Types and Interfaces
 * Centralized type definitions for API communication
 * Phase 2: Application Structure Reorganization
 */

// ============================================================================
// Base Types
// ============================================================================

export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface TenantEntity extends BaseEntity {
  tenant_id: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: PaginationInfo;
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId: string;
    version: string;
  };
}

// ============================================================================
// Authentication Types
// ============================================================================

export interface LoginRequest {
  email: string;
  password: string;
  remember?: boolean;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresAt: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
  expiresAt: string;
}

// ============================================================================
// User Types
// ============================================================================

export enum UserRole {
  ADMIN = 'admin',
  SCHOOL_MANAGER = 'school_manager',
  SUPERVISOR = 'supervisor',
  DRIVER = 'driver',
  PARENT = 'parent',
  STUDENT = 'student',
}

export interface User extends TenantEntity {
  name: string;
  email: string;
  role: UserRole;
  is_active: boolean;
  phone?: string;
  avatar_url?: string;
  last_login?: string;
  preferences?: UserPreferences;
  profile?: UserProfile;
}

export interface UserPreferences {
  language: string;
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  theme: 'light' | 'dark' | 'auto';
}

export interface UserProfile {
  first_name: string;
  last_name: string;
  date_of_birth?: string;
  address?: Address;
  emergency_contact?: EmergencyContact;
}

export interface CreateUserRequest {
  name: string;
  email: string;
  role: UserRole;
  tenant_id: string;
  phone?: string;
  is_active?: boolean;
  profile?: Partial<UserProfile>;
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  phone?: string;
  is_active?: boolean;
  profile?: Partial<UserProfile>;
  preferences?: Partial<UserPreferences>;
}

export interface UserListParams extends PaginationParams {
  role?: UserRole;
  tenant_id?: string;
  is_active?: boolean;
  search?: string;
  search_fields?: string[];
}

// ============================================================================
// School Types
// ============================================================================

export interface School extends TenantEntity {
  name: string;
  code: string;
  address: Address;
  contact: ContactInfo;
  settings: SchoolSettings;
  is_active: boolean;
  manager_id?: string;
  manager?: User;
}

export interface SchoolSettings {
  branding: {
    logo_url?: string;
    primary_color?: string;
    secondary_color?: string;
    school_name?: string;
    tagline?: string;
  };
  operational: {
    start_time: string;
    end_time: string;
    timezone: string;
    academic_year_start: string;
    academic_year_end: string;
  };
  notifications: {
    delay_threshold_minutes: number;
    emergency_contacts: string[];
    auto_notifications: boolean;
  };
}

export interface CreateSchoolRequest {
  name: string;
  code: string;
  address: Address;
  contact: ContactInfo;
  manager_id?: string;
  settings?: Partial<SchoolSettings>;
}

export interface UpdateSchoolRequest {
  name?: string;
  code?: string;
  address?: Partial<Address>;
  contact?: Partial<ContactInfo>;
  manager_id?: string;
  settings?: Partial<SchoolSettings>;
  is_active?: boolean;
}

// ============================================================================
// Bus Types
// ============================================================================

export interface Bus extends TenantEntity {
  plate_number: string;
  model: string;
  year: number;
  capacity: number;
  driver_id?: string;
  driver?: User;
  route_id?: string;
  route?: Route;
  is_active: boolean;
  status: BusStatus;
  location?: Location;
  maintenance: MaintenanceInfo;
  fuel_efficiency?: number;
  last_updated?: string;
}

export enum BusStatus {
  IDLE = 'idle',
  IN_ROUTE = 'in_route',
  MAINTENANCE = 'maintenance',
  OUT_OF_SERVICE = 'out_of_service',
}

export interface Location {
  latitude: number;
  longitude: number;
  accuracy?: number;
  heading?: number;
  speed?: number;
  timestamp: string;
}

export interface MaintenanceInfo {
  last_service_date?: string;
  next_service_date?: string;
  mileage: number;
  service_history: MaintenanceRecord[];
}

export interface MaintenanceRecord {
  id: string;
  date: string;
  type: string;
  description: string;
  cost?: number;
  mechanic?: string;
}

export interface CreateBusRequest {
  plate_number: string;
  model: string;
  year: number;
  capacity: number;
  driver_id?: string;
  route_id?: string;
  tenant_id: string;
}

export interface UpdateBusRequest {
  plate_number?: string;
  model?: string;
  year?: number;
  capacity?: number;
  driver_id?: string;
  route_id?: string;
  is_active?: boolean;
  status?: BusStatus;
}

// ============================================================================
// Route Types
// ============================================================================

export interface Route extends TenantEntity {
  name: string;
  description?: string;
  bus_id?: string;
  bus?: Bus;
  stops: RouteStop[];
  is_active: boolean;
  estimated_duration: number;
  distance: number;
  schedule: RouteSchedule;
}

export interface RouteStop {
  id: string;
  name: string;
  location: Location;
  order: number;
  estimated_arrival_time: string;
  students: Student[];
}

export interface RouteSchedule {
  morning_start: string;
  morning_end: string;
  afternoon_start: string;
  afternoon_end: string;
  days_of_week: number[]; // 0-6, Sunday = 0
}

export interface CreateRouteRequest {
  name: string;
  description?: string;
  bus_id?: string;
  stops: Omit<RouteStop, 'id' | 'students'>[];
  schedule: RouteSchedule;
  tenant_id: string;
}

export interface UpdateRouteRequest {
  name?: string;
  description?: string;
  bus_id?: string;
  stops?: Omit<RouteStop, 'id' | 'students'>[];
  schedule?: Partial<RouteSchedule>;
  is_active?: boolean;
}

// ============================================================================
// Student Types
// ============================================================================

export interface Student extends TenantEntity {
  name: string;
  student_id: string;
  grade: string;
  class: string;
  parent_id: string;
  parent?: User;
  route_stop_id?: string;
  route_stop?: RouteStop;
  is_active: boolean;
  profile: StudentProfile;
  attendance_stats?: AttendanceStats;
}

export interface StudentProfile {
  date_of_birth: string;
  address: Address;
  emergency_contacts: EmergencyContact[];
  medical_info?: MedicalInfo;
  photo_url?: string;
}

export interface MedicalInfo {
  allergies?: string[];
  medications?: string[];
  medical_conditions?: string[];
  emergency_medical_contact?: string;
}

export interface AttendanceStats {
  total_days: number;
  present_days: number;
  absent_days: number;
  attendance_rate: number;
  last_attendance_date?: string;
}

// ============================================================================
// Common Types
// ============================================================================

export interface Address {
  street: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface ContactInfo {
  phone: string;
  email: string;
  website?: string;
  fax?: string;
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
  email?: string;
  is_primary: boolean;
}

// ============================================================================
// Filter and Search Types
// ============================================================================

export interface FilterOptions {
  [key: string]: any;
}

export interface SearchOptions {
  query: string;
  fields: string[];
  exact_match?: boolean;
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

// ============================================================================
// Webhook Types
// ============================================================================

export interface WebhookEvent {
  id: string;
  event: string;
  timestamp: string;
  data: any;
  tenant_id: string;
  version: string;
}

export interface WebhookSubscription {
  id: string;
  url: string;
  events: string[];
  is_active: boolean;
  secret: string;
  created_at: string;
  updated_at: string;
}
