/**
 * Real-Time Bus Tracking Dashboard
 * Phase 4: Advanced UI Components
 */

import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  MapPin,
  Navigation,
  Clock,
  AlertTriangle,
  Users,
  Route,
  Maximize2,
  Minimize2,
  RefreshCw,
  Settings,
  Zap
} from 'lucide-react';
import { RealTimeTrackingService, BusLocation } from '../../services/RealTimeTrackingService';
import { useAuth } from '../../contexts/AuthContext';
import { Button } from '../ui/Button';
import { InteractiveMap } from '../maps/InteractiveMap';
import { supabase } from '../../lib/supabase';
import { DatabaseService } from '../../services/DatabaseService';

interface BusTrackingData {
  bus: {
    id: string;
    plate_number: string;
    driver_name: string;
    capacity: number;
    current_students: number;
  };
  location: BusLocation | null;
  route: {
    id: string;
    name: string;
    stops: Array<{
      id: string;
      name: string;
      location: [number, number];
      estimated_arrival: string;
      status: 'pending' | 'arrived' | 'departed';
    }>;
  };
  status: 'active' | 'inactive' | 'maintenance' | 'emergency';
  lastUpdate: string;
}

interface TrackingDashboardProps {
  tenantId: string;
  selectedBusIds?: string[];
  onBusSelect?: (busId: string) => void;
  fullscreen?: boolean;
}

export const RealTimeTrackingDashboard: React.FC<TrackingDashboardProps> = ({
  tenantId,
  selectedBusIds = [],
  onBusSelect,
  fullscreen = false,
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [busData, setBusData] = useState<Map<string, BusTrackingData>>(new Map());
  const [isLoading, setIsLoading] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(fullscreen);
  const [selectedBus, setSelectedBus] = useState<string | null>(null);
  const [trackingConfig, setTrackingConfig] = useState({
    autoRefresh: true,
    refreshInterval: 5000,
    showTrails: true,
    showAlerts: true,
  });
  
  const trackingService = useRef(RealTimeTrackingService.getInstance());
  const databaseService = useRef(DatabaseService.getInstance());
  const mapRef = useRef<HTMLDivElement>(null);
  const refreshInterval = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    initializeTracking();
    return () => {
      cleanup();
    };
  }, [tenantId, selectedBusIds]);

  useEffect(() => {
    if (trackingConfig.autoRefresh) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }
    return () => stopAutoRefresh();
  }, [trackingConfig.autoRefresh, trackingConfig.refreshInterval]);

  const initializeTracking = async () => {
    try {
      setIsLoading(true);
      
      // Load initial bus data
      await loadBusData();
      
      // Subscribe to real-time updates
      await subscribeToUpdates();
      
      setIsLoading(false);
    } catch (error) {
      console.error('Error initializing tracking:', error);
      setIsLoading(false);
    }
  };

  const loadBusData = async () => {
    try {
      // Use DatabaseService to get buses with locations
      const busesWithLocations = await databaseService.current.getBusesWithLocations(tenantId);

      if (!busesWithLocations || busesWithLocations.length === 0) {
        setBusData(new Map());
        return;
      }

      // Transform to BusTrackingData format
      const busTrackingData: BusTrackingData[] = busesWithLocations.map(bus => ({
        bus: {
          id: bus.id,
          plate_number: bus.plate_number,
          driver_name: bus.driver_name || 'Unknown Driver',
          capacity: bus.capacity || 0,
          current_students: bus.current_students || 0,
        },
        location: bus.latitude && bus.longitude ? {
          id: `${bus.id}-location`,
          bus_id: bus.id,
          latitude: bus.latitude,
          longitude: bus.longitude,
          speed: bus.speed || 0,
          heading: bus.heading || 0,
          accuracy: bus.accuracy || 0,
          timestamp: bus.location_timestamp || new Date().toISOString(),
          tenant_id: bus.tenant_id,
        } : null,
        route: bus.route_id ? {
          id: bus.route_id,
          name: bus.route_name || 'Unknown Route',
          stops: [], // سيتم تحميلها من DatabaseService لاحقاً
        } : {
          id: 'no-route',
          name: 'No Route Assigned',
          stops: [],
        },
        status: bus.speed && bus.speed > 5 ? 'active' : 'inactive',
        lastUpdate: bus.location_timestamp || bus.last_updated || new Date().toISOString(),
      }));

      const dataMap = new Map();
      busTrackingData.forEach(bus => {
        dataMap.set(bus.bus.id, bus);
      });

      setBusData(dataMap);
    } catch (error) {
      console.error('Error loading bus data:', error);
      setBusData(new Map());
    }
  };

  const subscribeToUpdates = async () => {
    try {
      for (const busId of selectedBusIds) {
        await trackingService.current.subscribeToBusLocation(
          busId,
          (location: BusLocation) => {
            setBusData(prev => {
              const newData = new Map(prev);
              const busData = newData.get(busId);
              if (busData) {
                newData.set(busId, {
                  ...busData,
                  location,
                  lastUpdate: new Date().toISOString(),
                });
              }
              return newData;
            });
          },
          (error: Error) => {
            console.error(`Error tracking bus ${busId}:`, error);
          }
        );
      }
    } catch (error) {
      console.error('Error subscribing to updates:', error);
    }
  };

  const startAutoRefresh = () => {
    stopAutoRefresh();
    refreshInterval.current = setInterval(() => {
      loadBusData();
    }, trackingConfig.refreshInterval);
  };

  const stopAutoRefresh = () => {
    if (refreshInterval.current) {
      clearInterval(refreshInterval.current);
      refreshInterval.current = null;
    }
  };

  const cleanup = async () => {
    stopAutoRefresh();
    for (const busId of selectedBusIds) {
      await trackingService.current.unsubscribeFromBusLocation(busId);
    }
  };

  const handleBusClick = (busId: string) => {
    setSelectedBus(busId);
    onBusSelect?.(busId);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const formatLastUpdate = (timestamp: string) => {
    const diff = Date.now() - new Date(timestamp).getTime();
    const seconds = Math.floor(diff / 1000);
    
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m`;
    return `${Math.floor(seconds / 3600)}h`;
  };

  const getBusStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600';
      case 'inactive': return 'text-gray-600';
      case 'maintenance': return 'text-yellow-600';
      case 'emergency': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getBusStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Navigation className="w-4 h-4" />;
      case 'inactive': return <Clock className="w-4 h-4" />;
      case 'maintenance': return <Settings className="w-4 h-4" />;
      case 'emergency': return <AlertTriangle className="w-4 h-4" />;
      default: return <MapPin className="w-4 h-4" />;
    }
  };

  // Convert bus data to map format
  const getMapBusData = () => {
    return Array.from(busData.values())
      .filter(bus => bus.location)
      .map(bus => ({
        id: bus.bus.id,
        plateNumber: bus.bus.plate_number,
        latitude: bus.location!.latitude,
        longitude: bus.location!.longitude,
        speed: bus.location!.speed,
        heading: bus.location!.heading,
        status: bus.status === 'active' ? 'moving' :
                bus.status === 'inactive' ? 'stopped' :
                bus.status === 'maintenance' ? 'maintenance' : 'emergency',
        driver: bus.bus.driver_name,
        route: bus.route.name,
        studentsCount: bus.bus.current_students,
        lastUpdate: formatLastUpdate(bus.lastUpdate),
        nextStop: bus.route.stops.find(stop => stop.status === 'pending')?.name,
        eta: bus.route.stops.find(stop => stop.status === 'pending')?.estimated_arrival,
      }));
  };

  // Convert route data to map format
  const getMapRouteData = () => {
    return Array.from(busData.values()).map(bus => ({
      id: bus.route.id,
      name: bus.route.name,
      coordinates: bus.route.stops.map(stop => [stop.location[1], stop.location[0]] as [number, number]),
      stops: bus.route.stops.map(stop => ({
        id: stop.id,
        name: stop.name,
        latitude: stop.location[0],
        longitude: stop.location[1],
        order: 0, // You might want to add this to your data structure
        studentsCount: 0, // You might want to add this to your data structure
      })),
      color: '#3B82F6', // Blue color for routes
    }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <RefreshCw className="w-6 h-6 animate-spin" />
          <span>{t('tracking.loading')}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-3">
          <MapPin className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold">{t('tracking.realTimeTracking')}</h2>
          <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
            {busData.size} {t('tracking.activeBuses')}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setTrackingConfig(prev => ({ ...prev, autoRefresh: !prev.autoRefresh }))}
          >
            <RefreshCw className={`w-4 h-4 ${trackingConfig.autoRefresh ? 'animate-spin' : ''}`} />
            {trackingConfig.autoRefresh ? t('tracking.autoRefreshOn') : t('tracking.autoRefreshOff')}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={toggleFullscreen}
          >
            {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      <div className="flex h-96">
        {/* Bus List */}
        <div className="w-1/3 border-r overflow-y-auto">
          <div className="p-4">
            <h3 className="font-medium mb-3">{t('tracking.activeBuses')}</h3>
            <div className="space-y-2">
              {Array.from(busData.values()).map((bus) => (
                <div
                  key={bus.bus.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedBus === bus.bus.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleBusClick(bus.bus.id)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className={`${getBusStatusColor(bus.status)}`}>
                        {getBusStatusIcon(bus.status)}
                      </span>
                      <span className="font-medium">{bus.bus.plate_number}</span>
                    </div>
                    <span className="text-xs text-gray-500">
                      {formatLastUpdate(bus.lastUpdate)}
                    </span>
                  </div>
                  
                  <div className="text-sm text-gray-600 space-y-1">
                    <div className="flex items-center space-x-2">
                      <Users className="w-3 h-3" />
                      <span>{bus.bus.current_students}/{bus.bus.capacity}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Route className="w-3 h-3" />
                      <span>{bus.route.name}</span>
                    </div>
                    {bus.location && (
                      <div className="flex items-center space-x-2">
                        <Navigation className="w-3 h-3" />
                        <span>{bus.location.speed} km/h</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Interactive Map */}
        <div className="flex-1 relative">
          <InteractiveMap
            buses={getMapBusData()}
            routes={getMapRouteData()}
            selectedBus={selectedBus}
            onBusSelect={handleBusClick}
            showRoutes={true}
            showStops={true}
            showTrails={trackingConfig.showTrails}
            autoRefresh={trackingConfig.autoRefresh}
            className="h-full"
          />

          {/* Map Controls */}
          <div className="absolute top-4 right-4 space-y-2 z-10">
            <Button
              variant="outline"
              size="sm"
              className="bg-white shadow-lg"
              onClick={() => setTrackingConfig(prev => ({ ...prev, showTrails: !prev.showTrails }))}
            >
              {trackingConfig.showTrails ? t('tracking.hideTrails') : t('tracking.showTrails')}
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="bg-white shadow-lg"
              onClick={() => setTrackingConfig(prev => ({ ...prev, showAlerts: !prev.showAlerts }))}
            >
              {trackingConfig.showAlerts ? t('tracking.hideAlerts') : t('tracking.showAlerts')}
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="bg-white shadow-lg"
              onClick={() => loadBusData()}
            >
              <RefreshCw className="w-4 h-4" />
              {t('tracking.refreshData')}
            </Button>
          </div>
        </div>
      </div>

      {/* Selected Bus Details */}
      {selectedBus && busData.has(selectedBus) && (
        <div className="border-t p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Bus Info */}
            <div>
              <h4 className="font-medium mb-2">{t('tracking.busInformation')}</h4>
              <div className="space-y-1 text-sm">
                <div>Driver: {busData.get(selectedBus)?.bus.driver_name}</div>
                <div>Route: {busData.get(selectedBus)?.route.name}</div>
                <div>Students: {busData.get(selectedBus)?.bus.current_students}/{busData.get(selectedBus)?.bus.capacity}</div>
              </div>
            </div>

            {/* Location Info */}
            <div>
              <h4 className="font-medium mb-2">{t('tracking.currentLocation')}</h4>
              {busData.get(selectedBus)?.location && (
                <div className="space-y-1 text-sm">
                  <div>Lat: {busData.get(selectedBus)?.location?.latitude.toFixed(6)}</div>
                  <div>Lng: {busData.get(selectedBus)?.location?.longitude.toFixed(6)}</div>
                  <div>Speed: {busData.get(selectedBus)?.location?.speed} km/h</div>
                </div>
              )}
            </div>

            {/* Next Stops */}
            <div>
              <h4 className="font-medium mb-2">{t('tracking.nextStops')}</h4>
              <div className="space-y-1 text-sm">
                {busData.get(selectedBus)?.route.stops
                  .filter(stop => stop.status === 'pending')
                  .slice(0, 3)
                  .map(stop => (
                    <div key={stop.id} className="flex justify-between">
                      <span>{stop.name}</span>
                      <span className="text-gray-500">{stop.estimated_arrival}</span>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RealTimeTrackingDashboard;
