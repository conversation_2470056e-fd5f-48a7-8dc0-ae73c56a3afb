import React from "react";
import { useTranslation } from "react-i18next";
import { Navbar } from "../../components/layout/Navbar";
import { Sidebar } from "../../components/layout/Sidebar";
import { DriverAttendanceInterface } from "../../components/students/DriverAttendanceInterface";
import { useAuth } from "../../contexts/AuthContext";
import { UserRole } from "../../types";

export const DriverAttendancePage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();

  // Only allow drivers to access this page
  if (user?.role !== UserRole.DRIVER) {
    return (
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar />
          <main className="flex-1 overflow-y-auto p-4 md:p-6">
            <div className="max-w-7xl mx-auto">
              <div className="text-center py-12">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  {t("common.accessDenied")}
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  {t("drivers.onlyDriversCanAccess")}
                </p>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />

        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {t("attendance.driverInterface")}
              </h1>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {t("attendance.recordStudentAttendance")}
              </p>
            </div>

            <DriverAttendanceInterface />
          </div>
        </main>
      </div>
    </div>
  );
};

export default DriverAttendancePage;
