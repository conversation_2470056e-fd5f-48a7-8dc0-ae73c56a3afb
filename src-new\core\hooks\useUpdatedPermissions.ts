/**
 * Hook للصلاحيات المحدث - يستخدم النظام الأمني الجديد
 * Updated Permissions Hook - Uses the new security system
 * 
 * يستخدم النظام الأمني الجديد من المرحلة الأولى
 * Uses the new security system from Phase 1
 */

import { useAuth } from '../contexts/AuthContext';
import { useCentralizedPermissions } from './useCentralizedPermissions';
import { 
  ResourceType, 
  Action, 
  PermissionContext 
} from '../services/CentralizedPermissionService';
import { UserRole } from '../types';

export const useUpdatedPermissions = () => {
  const { user, isSystemAdmin, isSchoolManager } = useAuth();
  const {
    checkPermission,
    checkMultiplePermissions,
    canAccessTenant,
    canManageUsers,
    getPermissionSummary,
    logSecurityEvent,
    isLoading,
    error
  } = useCentralizedPermissions();

  // دوال مساعدة سريعة متزامنة
  const isAdmin = user?.role === UserRole.ADMIN;
  const isSchoolManagerSync = user?.role === UserRole.SCHOOL_MANAGER;
  const isSupervisor = user?.role === UserRole.SUPERVISOR;
  const isDriver = user?.role === UserRole.DRIVER;
  const isParent = user?.role === UserRole.PARENT;
  const isStudent = user?.role === UserRole.STUDENT;

  // دوال فحص الصلاحيات المحددة
  const canAccessStudent = async (studentId: string) => {
    const result = await checkPermission(
      ResourceType.STUDENT,
      Action.READ,
      { resourceId: studentId }
    );
    return result.allowed;
  };

  const canAccessBus = async (busId: string) => {
    const result = await checkPermission(
      ResourceType.BUS,
      Action.READ,
      { resourceId: busId }
    );
    return result.allowed;
  };

  const canRecordAttendance = async (studentId: string) => {
    const result = await checkPermission(
      ResourceType.ATTENDANCE,
      Action.CREATE,
      { studentId }
    );
    return result.allowed;
  };

  const canCreateUser = async (targetRole: UserRole, targetTenantId?: string) => {
    const result = await checkPermission(
      ResourceType.USER,
      Action.CREATE,
      { targetTenantId }
    );
    return result.allowed;
  };

  const canEditUser = async (targetUserId: string, targetTenantId?: string) => {
    const result = await checkPermission(
      ResourceType.USER,
      Action.UPDATE,
      { resourceId: targetUserId, resourceTenantId: targetTenantId }
    );
    return result.allowed;
  };

  const canDeleteUser = async (targetUserId: string, targetTenantId?: string) => {
    const result = await checkPermission(
      ResourceType.USER,
      Action.DELETE,
      { resourceId: targetUserId, resourceTenantId: targetTenantId }
    );
    return result.allowed;
  };

  // دوال فحص الصلاحيات للموارد المختلفة
  const canManageBuses = async () => {
    const summary = await getPermissionSummary(ResourceType.BUS);
    return summary.canCreate || summary.canUpdate || summary.canDelete;
  };

  const canManageRoutes = async () => {
    const summary = await getPermissionSummary(ResourceType.ROUTE);
    return summary.canCreate || summary.canUpdate || summary.canDelete;
  };

  const canManageStudents = async () => {
    const summary = await getPermissionSummary(ResourceType.STUDENT);
    return summary.canCreate || summary.canUpdate || summary.canDelete;
  };

  const canViewReports = async () => {
    // الأدمن ومدراء المدارس والمشرفين يمكنهم رؤية التقارير
    return isAdmin || isSchoolManagerSync || isSupervisor;
  };

  const canManageSchool = async (tenantId?: string) => {
    if (isAdmin) return true;
    if (isSchoolManagerSync && user?.tenant_id === tenantId) return true;
    return false;
  };

  // دوال فحص الصلاحيات المتقدمة
  const canCreateBus = async () => {
    const result = await checkPermission(ResourceType.BUS, Action.CREATE);
    return result.allowed;
  };

  const canEditBus = async (busId: string) => {
    const result = await checkPermission(
      ResourceType.BUS, 
      Action.UPDATE, 
      { resourceId: busId }
    );
    return result.allowed;
  };

  const canDeleteBus = async (busId: string) => {
    const result = await checkPermission(
      ResourceType.BUS, 
      Action.DELETE, 
      { resourceId: busId }
    );
    return result.allowed;
  };

  const canCreateRoute = async () => {
    const result = await checkPermission(ResourceType.ROUTE, Action.CREATE);
    return result.allowed;
  };

  const canEditRoute = async (routeId: string) => {
    const result = await checkPermission(
      ResourceType.ROUTE, 
      Action.UPDATE, 
      { resourceId: routeId }
    );
    return result.allowed;
  };

  const canDeleteRoute = async (routeId: string) => {
    const result = await checkPermission(
      ResourceType.ROUTE, 
      Action.DELETE, 
      { resourceId: routeId }
    );
    return result.allowed;
  };

  const canCreateStudent = async () => {
    const result = await checkPermission(ResourceType.STUDENT, Action.CREATE);
    return result.allowed;
  };

  const canEditStudent = async (studentId: string) => {
    const result = await checkPermission(
      ResourceType.STUDENT, 
      Action.UPDATE, 
      { resourceId: studentId }
    );
    return result.allowed;
  };

  const canDeleteStudent = async (studentId: string) => {
    const result = await checkPermission(
      ResourceType.STUDENT, 
      Action.DELETE, 
      { resourceId: studentId }
    );
    return result.allowed;
  };

  // دوال فحص الصلاحيات للإشعارات
  const canCreateNotification = async () => {
    const result = await checkPermission(ResourceType.NOTIFICATION, Action.CREATE);
    return result.allowed;
  };

  const canEditNotification = async (notificationId: string) => {
    const result = await checkPermission(
      ResourceType.NOTIFICATION, 
      Action.UPDATE, 
      { resourceId: notificationId }
    );
    return result.allowed;
  };

  const canDeleteNotification = async (notificationId: string) => {
    const result = await checkPermission(
      ResourceType.NOTIFICATION, 
      Action.DELETE, 
      { resourceId: notificationId }
    );
    return result.allowed;
  };

  return {
    // معلومات المستخدم
    user,
    userRole: user?.role,
    userTenantId: user?.tenant_id,
    hasValidUser: !!user,

    // حالة التحميل والأخطاء
    isLoading,
    error,

    // دوال فحص الصلاحيات الأساسية
    checkPermission,
    checkMultiplePermissions,
    canAccessTenant,
    canManageUsers,
    getPermissionSummary,

    // دوال فحص الصلاحيات المحددة
    canAccessStudent,
    canAccessBus,
    canRecordAttendance,
    canCreateUser,
    canEditUser,
    canDeleteUser,

    // دوال فحص إدارة الموارد
    canManageBuses,
    canManageRoutes,
    canManageStudents,
    canViewReports,
    canManageSchool,

    // دوال فحص الصلاحيات المتقدمة للحافلات
    canCreateBus,
    canEditBus,
    canDeleteBus,

    // دوال فحص الصلاحيات المتقدمة للمسارات
    canCreateRoute,
    canEditRoute,
    canDeleteRoute,

    // دوال فحص الصلاحيات المتقدمة للطلاب
    canCreateStudent,
    canEditStudent,
    canDeleteStudent,

    // دوال فحص الصلاحيات للإشعارات
    canCreateNotification,
    canEditNotification,
    canDeleteNotification,

    // تسجيل الأحداث
    logSecurityEvent,

    // دوال مساعدة سريعة متزامنة
    isAdmin,
    isSchoolManager: isSchoolManagerSync,
    isSupervisor,
    isDriver,
    isParent,
    isStudent,

    // دوال مساعدة غير متزامنة
    isSystemAdminAsync: isSystemAdmin,
    isSchoolManagerAsync: isSchoolManager,
  };
};

export default useUpdatedPermissions;
