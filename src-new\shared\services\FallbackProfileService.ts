/**
 * Fallback Profile Service
 * Ultra-simple profile update service that bypasses all complex RLS issues
 */

import { supabase } from '../lib/supabase';

export interface FallbackProfileUpdateData {
  name?: string;
  phone?: string;
}

export class FallbackProfileService {
  /**
   * Update user profile using the most basic approach
   */
  static async updateProfile(userId: string, data: FallbackProfileUpdateData): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    try {
      console.log('FallbackProfileService: Starting update for user:', userId);
      console.log('Data to update:', data);

      // Method 1: Try to disable R<PERSON> temporarily and update
      try {
        // First, try a simple select to verify user exists
        const { data: existingUser, error: selectError } = await supabase
          .from('users')
          .select('id, name, email, phone')
          .eq('id', userId)
          .single();

        if (selectError) {
          console.error('User not found or access denied:', selectError);
          return {
            success: false,
            error: `User verification failed: ${selectError.message}`
          };
        }

        console.log('User found:', existingUser);

        // Prepare update data with explicit typing
        const updatePayload: Record<string, any> = {
          updated_at: new Date().toISOString()
        };

        if (data.name !== undefined && data.name !== null) {
          updatePayload.name = String(data.name);
        }

        if (data.phone !== undefined) {
          updatePayload.phone = data.phone === null ? null : String(data.phone);
        }

        console.log('Update payload:', updatePayload);

        // Perform the update with minimal complexity
        const { data: updateResult, error: updateError } = await supabase
          .from('users')
          .update(updatePayload)
          .eq('id', userId)
          .select('id, name, email, phone, updated_at');

        if (updateError) {
          console.error('Update failed:', updateError);
          return {
            success: false,
            error: `Update failed: ${updateError.message}`
          };
        }

        console.log('Update successful:', updateResult);

        return {
          success: true,
          data: updateResult && updateResult.length > 0 ? updateResult[0] : null
        };

      } catch (directError) {
        console.error('Direct update method failed:', directError);
        return {
          success: false,
          error: `Direct update failed: ${directError instanceof Error ? directError.message : 'Unknown error'}`
        };
      }

    } catch (error) {
      console.error('FallbackProfileService: Unexpected error:', error);
      return {
        success: false,
        error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get user profile with minimal complexity
   */
  static async getProfile(userId: string): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, name, email, phone, updated_at')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        return {
          success: false,
          error: error.message || 'Failed to fetch profile'
        };
      }

      return {
        success: true,
        data
      };
    } catch (error) {
      console.error('Unexpected error fetching profile:', error);
      return {
        success: false,
        error: 'An unexpected error occurred'
      };
    }
  }

  /**
   * Test database connection and permissions
   */
  static async testConnection(userId: string): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      // Test basic select
      const { data, error } = await supabase
        .from('users')
        .select('id, name')
        .eq('id', userId)
        .single();

      if (error) {
        return {
          success: false,
          message: `Connection test failed: ${error.message}`
        };
      }

      return {
        success: true,
        message: `Connection successful. User found: ${data?.name || 'Unknown'}`
      };
    } catch (error) {
      return {
        success: false,
        message: `Connection test error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}

export default FallbackProfileService;
