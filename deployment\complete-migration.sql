-- نظام إدارة الحافلات المدرسية - هجرات RLS
-- School Bus Management System - RLS Migrations
-- تاريخ الإنشاء: 2025-06-02T10:33:10.927Z

-- تحذير: يجب تطبيق هذا السكريبت على قاعدة بيانات فارغة
-- Warning: This script should be applied to an empty database

BEGIN;


-- ============================================================
-- 2025-06-02_000_create_base_tables.sql
-- ============================================================

-- إنشاء الجداول الأساسية
-- Create Base Tables

-- إنشاء جدول المستأجرين (المدارس) أولاً
CREATE TABLE IF NOT EXISTS tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  settings JSONB DEFAULT '{}',
  contact_info JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المستخدمين (إضافة للـ auth.users)
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  phone TEXT,
  avatar_url TEXT,
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  is_active BOOLEAN DEFAULT true,
  last_login TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الحافلات
CREATE TABLE IF NOT EXISTS buses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE NOT NULL,
  bus_number TEXT NOT NULL,
  license_plate TEXT UNIQUE NOT NULL,
  capacity INTEGER NOT NULL CHECK (capacity > 0),
  driver_id UUID REFERENCES users(id) ON DELETE SET NULL,
  model TEXT,
  year INTEGER,
  color TEXT,
  gps_device_id TEXT,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'maintenance', 'inactive')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tenant_id, bus_number)
);

-- إنشاء جدول الطلاب
CREATE TABLE IF NOT EXISTS students (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  student_id TEXT NOT NULL,
  full_name TEXT NOT NULL,
  grade TEXT,
  class TEXT,
  parent_id UUID REFERENCES users(id) ON DELETE SET NULL,
  parent_phone TEXT,
  emergency_contact TEXT,
  address TEXT,
  medical_info JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tenant_id, student_id)
);

-- إنشاء جدول المسارات
CREATE TABLE IF NOT EXISTS routes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  start_location TEXT,
  end_location TEXT,
  estimated_duration INTEGER, -- بالدقائق
  distance_km DECIMAL(10,2),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول نقاط المسار
CREATE TABLE IF NOT EXISTS route_stops (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  route_id UUID REFERENCES routes(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  latitude DECIMAL(10,8),
  longitude DECIMAL(11,8),
  stop_order INTEGER NOT NULL,
  estimated_arrival_time TIME,
  is_pickup BOOLEAN DEFAULT true,
  is_dropoff BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول تخصيص الحافلات للمسارات
CREATE TABLE IF NOT EXISTS bus_routes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  bus_id UUID REFERENCES buses(id) ON DELETE CASCADE NOT NULL,
  route_id UUID REFERENCES routes(id) ON DELETE CASCADE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME,
  days_of_week INTEGER[] DEFAULT '{1,2,3,4,5}', -- 1=الاثنين, 7=الأحد
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(bus_id, route_id, start_time)
);

-- إنشاء جدول تخصيص الطلاب للحافلات
CREATE TABLE IF NOT EXISTS bus_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id) ON DELETE CASCADE NOT NULL,
  bus_id UUID REFERENCES buses(id) ON DELETE CASCADE NOT NULL,
  pickup_stop_id UUID REFERENCES route_stops(id) ON DELETE SET NULL,
  dropoff_stop_id UUID REFERENCES route_stops(id) ON DELETE SET NULL,
  assigned_date DATE DEFAULT CURRENT_DATE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(student_id, bus_id, assigned_date)
);

-- إنشاء جدول الحضور
CREATE TABLE IF NOT EXISTS attendance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id) ON DELETE CASCADE NOT NULL,
  bus_id UUID REFERENCES buses(id) ON DELETE CASCADE NOT NULL,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  pickup_time TIMESTAMP WITH TIME ZONE,
  pickup_location TEXT,
  dropoff_time TIMESTAMP WITH TIME ZONE,
  dropoff_location TEXT,
  status TEXT DEFAULT 'absent' CHECK (status IN ('present', 'absent', 'late', 'early_pickup')),
  notes TEXT,
  recorded_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(student_id, bus_id, date)
);

-- إنشاء جدول الإشعارات
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'error', 'success')),
  priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  is_read BOOLEAN DEFAULT false,
  read_at TIMESTAMP WITH TIME ZONE,
  data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول سجلات التدقيق
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  table_name TEXT NOT NULL,
  operation TEXT NOT NULL CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE', 'SELECT')),
  record_id UUID,
  old_values JSONB,
  new_values JSONB,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول حوادث الأمان
CREATE TABLE IF NOT EXISTS security_incidents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  incident_type TEXT NOT NULL,
  severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  description TEXT NOT NULL,
  ip_address TEXT,
  user_agent TEXT,
  resolved BOOLEAN DEFAULT false,
  resolved_at TIMESTAMP WITH TIME ZONE,
  resolved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول تنبيهات الأمان
CREATE TABLE IF NOT EXISTS security_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type TEXT NOT NULL,
  severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  message TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  ip_address TEXT,
  acknowledged BOOLEAN DEFAULT false,
  acknowledged_at TIMESTAMP WITH TIME ZONE,
  acknowledged_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_buses_tenant_id ON buses(tenant_id);
CREATE INDEX IF NOT EXISTS idx_buses_driver_id ON buses(driver_id);
CREATE INDEX IF NOT EXISTS idx_students_tenant_id ON students(tenant_id);
CREATE INDEX IF NOT EXISTS idx_students_parent_id ON students(parent_id);
CREATE INDEX IF NOT EXISTS idx_routes_tenant_id ON routes(tenant_id);
CREATE INDEX IF NOT EXISTS idx_route_stops_route_id ON route_stops(route_id);
CREATE INDEX IF NOT EXISTS idx_bus_routes_bus_id ON bus_routes(bus_id);
CREATE INDEX IF NOT EXISTS idx_bus_routes_route_id ON bus_routes(route_id);
CREATE INDEX IF NOT EXISTS idx_bus_assignments_student_id ON bus_assignments(student_id);
CREATE INDEX IF NOT EXISTS idx_bus_assignments_bus_id ON bus_assignments(bus_id);
CREATE INDEX IF NOT EXISTS idx_attendance_student_id ON attendance(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_bus_id ON attendance(bus_id);
CREATE INDEX IF NOT EXISTS idx_attendance_date ON attendance(date);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_tenant_id ON notifications(tenant_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_table_name ON audit_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_security_incidents_user_id ON security_incidents(user_id);
CREATE INDEX IF NOT EXISTS idx_security_incidents_severity ON security_incidents(severity);
CREATE INDEX IF NOT EXISTS idx_security_alerts_severity ON security_alerts(severity);

-- إنشاء triggers للـ updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_buses_updated_at BEFORE UPDATE ON buses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_students_updated_at BEFORE UPDATE ON students
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_routes_updated_at BEFORE UPDATE ON routes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bus_assignments_updated_at BEFORE UPDATE ON bus_assignments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_attendance_updated_at BEFORE UPDATE ON attendance
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- تفعيل Row Level Security على الجداول الحساسة
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE buses ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE routes ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_incidents ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_alerts ENABLE ROW LEVEL SECURITY;



-- ============================================================
-- 2025-06-02_001_create_roles.sql
-- ============================================================

-- إنشاء الأدوار والصلاحيات
-- Create Roles and Permissions

-- التأكد من وجود جدول tenants أولاً
CREATE TABLE IF NOT EXISTS tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الأدوار
CREATE TABLE IF NOT EXISTS user_roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('system_admin', 'tenant_admin', 'school_manager', 'driver', 'parent', 'student')),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, tenant_id)
);

-- إنشاء جدول صلاحيات الأدوار
CREATE TABLE IF NOT EXISTS role_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  role TEXT NOT NULL,
  permission TEXT NOT NULL,
  resource TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(role, permission, resource)
);

-- إدراج الصلاحيات الأساسية
INSERT INTO role_permissions (role, permission, resource) VALUES
-- صلاحيات مدير النظام
('system_admin', 'all', '*'),
-- صلاحيات مدير المدرسة
('tenant_admin', 'read', 'tenant'),
('tenant_admin', 'write', 'tenant'),
('tenant_admin', 'manage', 'users'),
('tenant_admin', 'manage', 'buses'),
('tenant_admin', 'manage', 'students'),
('tenant_admin', 'manage', 'routes'),
('tenant_admin', 'read', 'reports'),
-- صلاحيات مدير المدرسة
('school_manager', 'read', 'tenant'),
('school_manager', 'manage', 'students'),
('school_manager', 'read', 'buses'),
('school_manager', 'read', 'routes'),
('school_manager', 'read', 'attendance'),
-- صلاحيات السائق
('driver', 'read', 'bus'),
('driver', 'write', 'attendance'),
('driver', 'read', 'route'),
('driver', 'read', 'students'),
-- صلاحيات ولي الأمر
('parent', 'read', 'student'),
('parent', 'read', 'attendance'),
('parent', 'read', 'notifications'),
('parent', 'read', 'bus_location'),
-- صلاحيات الطالب
('student', 'read', 'self'),
('student', 'read', 'bus_location'),
('student', 'read', 'notifications')
ON CONFLICT (role, permission, resource) DO NOTHING;

-- إنشاء فهارس للأدوار
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_tenant_id ON user_roles(tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role ON user_roles(role);
CREATE INDEX IF NOT EXISTS idx_user_roles_active ON user_roles(is_active);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role ON role_permissions(role);

-- إنشاء trigger لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_roles_updated_at BEFORE UPDATE ON user_roles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();




-- ============================================================
-- 2025-06-02_002_create_security_functions.sql
-- ============================================================

-- إنشاء دوال الأمان
-- Create Security Functions

-- دالة الحصول على معرف المستأجر الحالي
CREATE OR REPLACE FUNCTION get_current_tenant_id()
RETURNS UUID
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN (
    SELECT tenant_id
    FROM user_roles
    WHERE user_id = auth.uid()
    AND is_active = true
    LIMIT 1
  );
END;
$$;

-- دالة الحصول على دور المستخدم الحالي
CREATE OR REPLACE FUNCTION get_current_user_role()
RETURNS TEXT
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN (
    SELECT role
    FROM user_roles
    WHERE user_id = auth.uid()
    AND is_active = true
    LIMIT 1
  );
END;
$$;

-- دالة التحقق من كون المستخدم مدير مستأجر
CREATE OR REPLACE FUNCTION is_tenant_admin()
RETURNS BOOLEAN
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN (
    SELECT role IN ('tenant_admin', 'system_admin')
    FROM user_roles
    WHERE user_id = auth.uid()
    AND is_active = true
    LIMIT 1
  );
END;
$$;

-- دالة التحقق من كون المستخدم مدير نظام
CREATE OR REPLACE FUNCTION is_system_admin()
RETURNS BOOLEAN
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN (
    SELECT role = 'system_admin'
    FROM user_roles
    WHERE user_id = auth.uid()
    AND is_active = true
    LIMIT 1
  );
END;
$$;

-- دالة التحقق من إمكانية الوصول لبيانات الطالب
CREATE OR REPLACE FUNCTION can_access_student(student_id UUID)
RETURNS BOOLEAN
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
DECLARE
  user_role TEXT;
  user_tenant_id UUID;
  student_tenant_id UUID;
BEGIN
  -- الحصول على دور المستخدم ومعرف المستأجر
  SELECT role, tenant_id INTO user_role, user_tenant_id
  FROM user_roles
  WHERE user_id = auth.uid() AND is_active = true;

  -- إذا كان مدير نظام، يمكنه الوصول لكل شيء
  IF user_role = 'system_admin' THEN
    RETURN true;
  END IF;

  -- الحصول على معرف مستأجر الطالب
  SELECT tenant_id INTO student_tenant_id
  FROM students
  WHERE id = student_id;

  -- التحقق من نفس المستأجر
  IF user_tenant_id != student_tenant_id THEN
    RETURN false;
  END IF;

  -- التحقق حسب الدور
  CASE user_role
    WHEN 'tenant_admin', 'school_manager' THEN
      RETURN true;
    WHEN 'driver' THEN
      -- السائق يمكنه رؤية الطلاب في حافلته فقط
      RETURN EXISTS (
        SELECT 1 FROM bus_assignments ba
        JOIN buses b ON ba.bus_id = b.id
        WHERE ba.student_id = student_id
        AND b.driver_id = auth.uid()
      );
    WHEN 'parent' THEN
      -- ولي الأمر يمكنه رؤية أطفاله فقط
      RETURN EXISTS (
        SELECT 1 FROM students s
        WHERE s.id = student_id
        AND s.parent_id = auth.uid()
      );
    WHEN 'student' THEN
      -- الطالب يمكنه رؤية بياناته فقط
      RETURN EXISTS (
        SELECT 1 FROM students s
        WHERE s.id = student_id
        AND s.user_id = auth.uid()
      );
    ELSE
      RETURN false;
  END CASE;
END;
$$;

-- دالة التحقق من إمكانية الوصول لبيانات الحافلة
CREATE OR REPLACE FUNCTION can_access_bus(bus_id UUID)
RETURNS BOOLEAN
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
DECLARE
  user_role TEXT;
  user_tenant_id UUID;
  bus_tenant_id UUID;
BEGIN
  -- الحصول على دور المستخدم ومعرف المستأجر
  SELECT role, tenant_id INTO user_role, user_tenant_id
  FROM user_roles
  WHERE user_id = auth.uid() AND is_active = true;

  -- إذا كان مدير نظام، يمكنه الوصول لكل شيء
  IF user_role = 'system_admin' THEN
    RETURN true;
  END IF;

  -- الحصول على معرف مستأجر الحافلة
  SELECT tenant_id INTO bus_tenant_id
  FROM buses
  WHERE id = bus_id;

  -- التحقق من نفس المستأجر
  IF user_tenant_id != bus_tenant_id THEN
    RETURN false;
  END IF;

  -- التحقق حسب الدور
  CASE user_role
    WHEN 'tenant_admin', 'school_manager' THEN
      RETURN true;
    WHEN 'driver' THEN
      -- السائق يمكنه رؤية حافلته فقط
      RETURN EXISTS (
        SELECT 1 FROM buses b
        WHERE b.id = bus_id
        AND b.driver_id = auth.uid()
      );
    WHEN 'parent', 'student' THEN
      -- ولي الأمر والطالب يمكنهما رؤية حافلة الطالب فقط
      RETURN EXISTS (
        SELECT 1 FROM bus_assignments ba
        JOIN students s ON ba.student_id = s.id
        WHERE ba.bus_id = bus_id
        AND (s.parent_id = auth.uid() OR s.user_id = auth.uid())
      );
    ELSE
      RETURN false;
  END CASE;
END;
$$;

-- دالة تسجيل محاولات الوصول للتدقيق
CREATE OR REPLACE FUNCTION audit_log_access(
  table_name TEXT,
  operation TEXT,
  record_id UUID
)
RETURNS VOID
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  INSERT INTO audit_logs (
    user_id,
    table_name,
    operation,
    record_id,
    ip_address,
    user_agent,
    created_at
  ) VALUES (
    auth.uid(),
    table_name,
    operation,
    record_id,
    current_setting('request.headers', true)::json->>'x-forwarded-for',
    current_setting('request.headers', true)::json->>'user-agent',
    NOW()
  );
END;
$$;

-- دالة التحقق من الصلاحيات
CREATE OR REPLACE FUNCTION has_permission(
  permission_name TEXT,
  resource_name TEXT DEFAULT NULL
)
RETURNS BOOLEAN
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- الحصول على دور المستخدم
  SELECT role INTO user_role
  FROM user_roles
  WHERE user_id = auth.uid() AND is_active = true;

  -- التحقق من وجود الصلاحية
  RETURN EXISTS (
    SELECT 1 FROM role_permissions rp
    WHERE rp.role = user_role
    AND (rp.permission = permission_name OR rp.permission = 'all')
    AND (rp.resource = resource_name OR rp.resource = '*' OR resource_name IS NULL)
  );
END;
$$;




-- ============================================================
-- 2025-06-02_003_create_rls_policies.sql
-- ============================================================

-- إنشاء سياسات RLS
-- Create RLS Policies

-- سياسات جدول المستأجرين (tenants)
CREATE POLICY "tenants_select_policy" ON tenants
  FOR SELECT
  USING (
    (get_current_user_role() = 'system_admin') OR
    (id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))
  );

CREATE POLICY "tenants_insert_policy" ON tenants
  FOR INSERT
  WITH CHECK (get_current_user_role() = 'system_admin');

CREATE POLICY "tenants_update_policy" ON tenants
  FOR UPDATE
  USING (
    (get_current_user_role() = 'system_admin') OR
    (id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin')
  );

CREATE POLICY "tenants_delete_policy" ON tenants
  FOR DELETE
  USING (get_current_user_role() = 'system_admin');

-- سياسات جدول المستخدمين (users)
CREATE POLICY "users_select_policy" ON users
  FOR SELECT
  USING (
    (get_current_user_role() = 'system_admin') OR
    (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')) OR
    (id = auth.uid())
  );

CREATE POLICY "users_insert_policy" ON users
  FOR INSERT
  WITH CHECK (
    (get_current_user_role() = 'system_admin') OR
    (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager'))
  );

CREATE POLICY "users_update_policy" ON users
  FOR UPDATE
  USING (
    (get_current_user_role() = 'system_admin') OR
    (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')) OR
    (id = auth.uid() AND get_current_user_role() IN ('parent', 'student', 'driver'))
  );

CREATE POLICY "users_delete_policy" ON users
  FOR DELETE
  USING (
    (get_current_user_role() = 'system_admin') OR
    (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin')
  );

-- سياسة سياسة قراءة جدول الحافلات
CREATE POLICY "buses_select_policy" ON buses
  FOR SELECT
  USING ((get_current_user_role() = 'system_admin') OR
          (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')) OR
          (can_access_bus(id)));

-- سياسة سياسة إدراج جدول الحافلات
CREATE POLICY "buses_insert_policy" ON buses
  FOR INSERT
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة تحديث جدول الحافلات
CREATE POLICY "buses_update_policy" ON buses
  FOR UPDATE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة حذف جدول الحافلات
CREATE POLICY "buses_delete_policy" ON buses
  FOR DELETE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin'));

-- سياسة سياسة قراءة جدول الطلاب
CREATE POLICY "students_select_policy" ON students
  FOR SELECT
  USING ((get_current_user_role() = 'system_admin') OR
          (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')) OR
          (can_access_student(id)));

-- سياسة سياسة إدراج جدول الطلاب
CREATE POLICY "students_insert_policy" ON students
  FOR INSERT
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة تحديث جدول الطلاب
CREATE POLICY "students_update_policy" ON students
  FOR UPDATE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة حذف جدول الطلاب
CREATE POLICY "students_delete_policy" ON students
  FOR DELETE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin'));

-- سياسة سياسة قراءة جدول المسارات
CREATE POLICY "routes_select_policy" ON routes
  FOR SELECT
  USING ((get_current_user_role() = 'system_admin') OR
          (tenant_id = get_current_tenant_id()));

-- سياسة سياسة إدراج جدول المسارات
CREATE POLICY "routes_insert_policy" ON routes
  FOR INSERT
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة تحديث جدول المسارات
CREATE POLICY "routes_update_policy" ON routes
  FOR UPDATE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة حذف جدول المسارات
CREATE POLICY "routes_delete_policy" ON routes
  FOR DELETE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin'));

-- سياسة سياسة قراءة جدول الحضور
CREATE POLICY "attendance_select_policy" ON attendance
  FOR SELECT
  USING ((get_current_user_role() = 'system_admin') OR
          (tenant_id = get_current_tenant_id()));

-- سياسة سياسة إدراج جدول الحضور
CREATE POLICY "attendance_insert_policy" ON attendance
  FOR INSERT
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة تحديث جدول الحضور
CREATE POLICY "attendance_update_policy" ON attendance
  FOR UPDATE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة حذف جدول الحضور
CREATE POLICY "attendance_delete_policy" ON attendance
  FOR DELETE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin'));

-- سياسة سياسة قراءة جدول الإشعارات
CREATE POLICY "notifications_select_policy" ON notifications
  FOR SELECT
  USING ((get_current_user_role() = 'system_admin') OR
          (tenant_id = get_current_tenant_id()));

-- سياسة سياسة إدراج جدول الإشعارات
CREATE POLICY "notifications_insert_policy" ON notifications
  FOR INSERT
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة تحديث جدول الإشعارات
CREATE POLICY "notifications_update_policy" ON notifications
  FOR UPDATE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة حذف جدول الإشعارات
CREATE POLICY "notifications_delete_policy" ON notifications
  FOR DELETE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin'));

-- سياسة سياسة قراءة جدول سجلات المراجعة
CREATE POLICY "audit_logs_select_policy" ON audit_logs
  FOR SELECT
  USING ((get_current_user_role() = 'system_admin') OR
          (tenant_id = get_current_tenant_id()));

-- سياسة سياسة إدراج جدول سجلات المراجعة
CREATE POLICY "audit_logs_insert_policy" ON audit_logs
  FOR INSERT
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة تحديث جدول سجلات المراجعة
CREATE POLICY "audit_logs_update_policy" ON audit_logs
  FOR UPDATE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() IN ('tenant_admin', 'school_manager')));

-- سياسة سياسة حذف جدول سجلات المراجعة
CREATE POLICY "audit_logs_delete_policy" ON audit_logs
  FOR DELETE
  USING ((get_current_user_role() = 'system_admin') OR
      (tenant_id = get_current_tenant_id() AND get_current_user_role() = 'tenant_admin'));




-- ============================================================
-- 2025-06-02_004_enable_rls.sql
-- ============================================================

-- تفعيل Row Level Security
-- Enable Row Level Security

-- تفعيل RLS على جدول tenants
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;

-- تفعيل RLS على جدول users
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- تفعيل RLS على جدول buses
ALTER TABLE buses ENABLE ROW LEVEL SECURITY;

-- تفعيل RLS على جدول students
ALTER TABLE students ENABLE ROW LEVEL SECURITY;

-- تفعيل RLS على جدول routes
ALTER TABLE routes ENABLE ROW LEVEL SECURITY;

-- تفعيل RLS على جدول attendance
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;

-- تفعيل RLS على جدول notifications
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- تفعيل RLS على جدول audit_logs
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;




-- ============================================================
-- إنشاء بيانات اختبار أساسية
-- Create Basic Test Data
-- ============================================================

-- إدراج مستأجر اختبار
INSERT INTO tenants (id, name, slug, settings) VALUES 
(
  'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  'مدرسة الأمل الابتدائية',
  'al-amal-primary',
  '{"timezone": "Asia/Riyadh", "language": "ar"}'
) ON CONFLICT (slug) DO NOTHING;

-- إدراج صلاحيات إضافية إذا لزم الأمر
INSERT INTO role_permissions (role, permission, resource) VALUES
('tenant_admin', 'manage', 'settings'),
('school_manager', 'read', 'analytics'),
('driver', 'update', 'location')
ON CONFLICT (role, permission, resource) DO NOTHING;

COMMIT;

-- ============================================================
-- التحقق من التطبيق
-- Verification Queries
-- ============================================================

-- فحص الجداول
SELECT 'Tables Created' as status, count(*) as count 
FROM information_schema.tables 
WHERE table_schema = 'public';

-- فحص RLS
SELECT 'RLS Enabled' as status, count(*) as count 
FROM pg_tables 
WHERE schemaname = 'public' AND rowsecurity = true;

-- فحص السياسات
SELECT 'Policies Created' as status, count(*) as count 
FROM pg_policies 
WHERE schemaname = 'public';

-- فحص الدوال
SELECT 'Functions Created' as status, count(*) as count 
FROM information_schema.routines 
WHERE routine_schema = 'public' AND routine_type = 'FUNCTION';

-- عرض رسالة النجاح
SELECT '🎉 تم تطبيق جميع الهجرات بنجاح!' as message;
