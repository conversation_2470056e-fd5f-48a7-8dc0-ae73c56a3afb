import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  UserCheck,
  UserX,
  Users,
  TrendingUp,
  Calendar,
  BarChart3,
  Clock,
} from "lucide-react";
import { supabase } from "../../lib/supabase";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";

interface AttendanceStatsProps {
  total?: number;
  present?: number;
  absent?: number;
  showAdvanced?: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
  className?: string;
}

interface EnhancedAttendanceData {
  totalStudents: number;
  presentToday: number;
  absentToday: number;
  attendanceRate: number;
  weeklyTrend: number[];
  topPerformingRoutes: Array<{
    routeName: string;
    attendanceRate: number;
  }>;
}

export const AttendanceStats: React.FC<AttendanceStatsProps> = ({
  total,
  present,
  absent,
  showAdvanced = false,
  dateRange,
  className = "",
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { students, routes } = useDatabase();
  const [enhancedStats, setEnhancedStats] = useState<EnhancedAttendanceData>({
    totalStudents: 0,
    presentToday: 0,
    absentToday: 0,
    attendanceRate: 0,
    weeklyTrend: [],
    topPerformingRoutes: [],
  });
  const [loading, setLoading] = useState(false);

  // Use props if provided, otherwise fetch from database
  const totalStudents = total ?? enhancedStats.totalStudents;
  const presentCount = present ?? enhancedStats.presentToday;
  const absentCount = absent ?? enhancedStats.absentToday;

  const presentPercentage =
    totalStudents > 0 ? Math.round((presentCount / totalStudents) * 100) : 0;
  const absentPercentage =
    totalStudents > 0 ? Math.round((absentCount / totalStudents) * 100) : 0;

  useEffect(() => {
    if (showAdvanced && !total) {
      fetchEnhancedStats();
    }
  }, [showAdvanced, students, dateRange, total]);

  const fetchEnhancedStats = async () => {
    if (!user?.tenant_id) return;

    try {
      setLoading(true);
      const today = new Date().toISOString().split("T")[0];
      const startDate = dateRange?.start || today;
      const endDate = dateRange?.end || today;

      // Get today's attendance
      const { data: todayAttendance } = await supabase
        .from("attendance")
        .select("student_id, status")
        .eq("tenant_id", user.tenant_id)
        .gte("recorded_at", `${today}T00:00:00.000Z`)
        .lte("recorded_at", `${today}T23:59:59.999Z`)
        .eq("type", "pickup");

      // Calculate basic stats
      const totalStudents = students.length;
      const presentToday =
        todayAttendance?.filter((a) => a.status === "present").length || 0;
      const absentToday =
        todayAttendance?.filter((a) => a.status === "absent").length || 0;
      const attendanceRate =
        totalStudents > 0 ? (presentToday / totalStudents) * 100 : 0;

      // Get weekly trend (last 7 days)
      const weeklyTrend = await getWeeklyTrend();

      // Get top performing routes
      const topPerformingRoutes = await getTopPerformingRoutes();

      setEnhancedStats({
        totalStudents,
        presentToday,
        absentToday,
        attendanceRate,
        weeklyTrend,
        topPerformingRoutes,
      });
    } catch (error) {
      console.error("Error fetching attendance stats:", error);
    } finally {
      setLoading(false);
    }
  };

  const getWeeklyTrend = async (): Promise<number[]> => {
    const trend = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split("T")[0];

      const { data } = await supabase
        .from("attendance")
        .select("status")
        .eq("tenant_id", user?.tenant_id)
        .gte("recorded_at", `${dateStr}T00:00:00.000Z`)
        .lte("recorded_at", `${dateStr}T23:59:59.999Z`)
        .eq("type", "pickup");

      const presentCount =
        data?.filter((a) => a.status === "present").length || 0;
      const rate =
        students.length > 0 ? (presentCount / students.length) * 100 : 0;
      trend.push(rate);
    }
    return trend;
  };

  const getTopPerformingRoutes = async () => {
    const routeStats = [];

    for (const route of routes) {
      if (!route.stops) continue;

      const routeStudents = students.filter((s) =>
        route.stops?.some((stop) => stop.id === s.route_stop_id),
      );

      if (routeStudents.length === 0) continue;

      const today = new Date().toISOString().split("T")[0];
      const { data: attendance } = await supabase
        .from("attendance")
        .select("status")
        .in(
          "student_id",
          routeStudents.map((s) => s.id),
        )
        .gte("recorded_at", `${today}T00:00:00.000Z`)
        .lte("recorded_at", `${today}T23:59:59.999Z`)
        .eq("type", "pickup");

      const presentCount =
        attendance?.filter((a) => a.status === "present").length || 0;
      const attendanceRate = (presentCount / routeStudents.length) * 100;

      routeStats.push({
        routeName: route.name,
        attendanceRate,
      });
    }

    return routeStats
      .sort((a, b) => b.attendanceRate - a.attendanceRate)
      .slice(0, 5);
  };

  if (loading && showAdvanced) {
    return (
      <div className={`flex items-center justify-center h-32 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Basic Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {showAdvanced && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("students.total")}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {totalStudents}
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-accent-100 dark:bg-accent-900/20 rounded-lg">
                <UserCheck className="h-6 w-6 text-accent-600 dark:text-accent-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("students.present")}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {presentCount}
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-accent-600 dark:text-accent-400">
                {presentPercentage}%
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {t("common.of")} {totalStudents}
              </div>
            </div>
          </div>
          <div className="mt-4">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-accent-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${presentPercentage}%` }}
              />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-error-100 dark:bg-error-900/20 rounded-lg">
                <UserX className="h-6 w-6 text-error-600 dark:text-error-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("students.absent")}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {absentCount}
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-error-600 dark:text-error-400">
                {absentPercentage}%
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {t("common.of")} {totalStudents}
              </div>
            </div>
          </div>
          <div className="mt-4">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-error-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${absentPercentage}%` }}
              />
            </div>
          </div>
        </div>

        {showAdvanced && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("attendance.rate")}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {enhancedStats.attendanceRate.toFixed(1)}%
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Advanced Analytics - Only show if showAdvanced is true */}
      {showAdvanced && (
        <>
          {/* Weekly Trend Chart */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center mb-4">
              <BarChart3 className="h-5 w-5 text-gray-500 mr-2" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {t("attendance.weeklyTrend")}
              </h3>
            </div>
            <div className="flex items-end space-x-2 h-32">
              {enhancedStats.weeklyTrend.map((rate, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div
                    className="w-full bg-primary-500 rounded-t transition-all duration-300 hover:bg-primary-600"
                    style={{ height: `${Math.max((rate / 100) * 100, 4)}%` }}
                    title={`${rate.toFixed(1)}%`}
                  />
                  <span className="text-xs text-gray-500 mt-1">
                    {new Date(
                      Date.now() - (6 - index) * 24 * 60 * 60 * 1000,
                    ).toLocaleDateString("ar", { weekday: "short" })}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Top Performing Routes */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {t("routes.topPerforming")}
            </h3>
            <div className="space-y-3">
              {enhancedStats.topPerformingRoutes.map((route, index) => (
                <div
                  key={route.routeName}
                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <div className="flex items-center">
                    <div
                      className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                        index === 0
                          ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
                          : index === 1
                            ? "bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200"
                            : index === 2
                              ? "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400"
                              : "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                      }`}
                    >
                      {index + 1}
                    </div>
                    <span className="ml-3 text-sm font-medium text-gray-900 dark:text-white">
                      {route.routeName}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${route.attendanceRate}%` }}
                      />
                    </div>
                    <span className="text-sm font-semibold text-green-600 dark:text-green-400 min-w-[3rem] text-right">
                      {route.attendanceRate.toFixed(1)}%
                    </span>
                  </div>
                </div>
              ))}
              {enhancedStats.topPerformingRoutes.length === 0 && (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                  <Calendar className="mx-auto h-8 w-8 mb-2 opacity-50" />
                  <p className="text-sm">{t("attendance.noDataAvailable")}</p>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};
