import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { UserPlus, X, Check } from "lucide-react";
import { Button } from "../ui/Button";
import { useDatabase } from "../../contexts/DatabaseContext";
import type { Tenant, User } from "../../types";

interface SchoolManagerAssignmentProps {
  tenant: Tenant;
  onClose: () => void;
  onSuccess: () => void;
}

export const SchoolManagerAssignment: React.FC<
  SchoolManagerAssignmentProps
> = ({ tenant, onClose, onSuccess }) => {
  const { t } = useTranslation();
  const { users, assignSchoolManager } = useDatabase();
  const [selectedUserId, setSelectedUserId] = useState("");
  const [isAssigning, setIsAssigning] = useState(false);

  // Get users who can be assigned as school managers
  // (users without tenant_id or users who are not already school managers)
  const availableUsers = users.filter((user) => {
    // Don't show admin users as they shouldn't be assigned to schools
    if (user.role === "admin") return false;

    // Show users without tenant_id (unassigned users)
    if (!user.tenant_id) return true;

    // Show users from other tenants who are not school managers
    if (user.tenant_id !== tenant.id && user.role !== "school_manager")
      return true;

    return false;
  });

  // Get current school manager for this tenant
  const currentManager = users.find(
    (user) => user.tenant_id === tenant.id && user.role === "school_manager",
  );

  const handleAssign = async () => {
    if (!selectedUserId) return;

    setIsAssigning(true);
    try {
      console.log(
        `Assigning user ${selectedUserId} as manager for school ${tenant.id}`,
      );
      const success = await assignSchoolManager(tenant.id, selectedUserId);
      if (success) {
        onSuccess();
        onClose();
      } else {
        console.error("Assignment returned false");
        alert("Failed to assign school manager");
      }
    } catch (error) {
      console.error("Error assigning school manager:", error);
      alert(
        "Error assigning school manager: " +
          (error instanceof Error ? error.message : "Unknown error"),
      );
    } finally {
      setIsAssigning(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
            <UserPlus size={20} className="mr-2 text-primary-500" />
            Assign School Manager
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X size={24} />
          </button>
        </div>

        <div className="p-6">
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {tenant.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Select a user to assign as the school manager for this school.
            </p>
          </div>

          {/* Current Manager */}
          {currentManager && (
            <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
              <div className="flex items-center">
                <Check size={16} className="text-green-600 mr-2" />
                <span className="text-sm font-medium text-green-800 dark:text-green-200">
                  Current Manager:
                </span>
              </div>
              <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                {currentManager.name} ({currentManager.email})
              </p>
            </div>
          )}

          {/* User Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Select User
            </label>
            <select
              value={selectedUserId}
              onChange={(e) => setSelectedUserId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              required
            >
              <option value="">Select a user...</option>
              {availableUsers.map((user) => (
                <option key={user.id} value={user.id}>
                  {user.name} ({user.email}) -{" "}
                  {user.role === "school_manager"
                    ? "School Manager"
                    : user.role}
                  {user.tenant_id && " (Currently assigned to another school)"}
                </option>
              ))}
            </select>
            {availableUsers.length === 0 && (
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                No available users to assign. Create a new user first.
              </p>
            )}
          </div>

          {/* Warning */}
          {selectedUserId && (
            <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                <strong>Note:</strong> This will assign the selected user to
                this school and change their role to "School Manager". If they
                are currently assigned to another school, they will be moved to
                this school.
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isAssigning}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleAssign}
              disabled={
                !selectedUserId || isAssigning || availableUsers.length === 0
              }
              leftIcon={isAssigning ? undefined : <UserPlus size={16} />}
            >
              {isAssigning ? "Assigning..." : "Assign Manager"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SchoolManagerAssignment;
