{"test_info": {"timestamp": "2025-06-02T09-35-30-337Z", "total_tests": 15, "passed_tests": 14, "failed_tests": 1, "warning_tests": 0, "success_rate": "93.3"}, "test_suites": [{"id": "basic", "name": "الاختبارات الأساسية", "results": [{"suite": "basic", "test": "فحص البنية الأساسية", "action": "checkBasicStructure", "status": "passed", "message": "جميع المجلدات الأساسية موجودة", "details": "تم فحص 7 مجلد", "timestamp": "2025-06-02T09:35:30.305Z"}, {"suite": "basic", "test": "فحص ملفات التكوين", "action": "checkConfigFiles", "status": "passed", "message": "جميع ملفات التكوين موجودة", "details": "تم فحص 5 ملف", "timestamp": "2025-06-02T09:35:30.306Z"}, {"suite": "basic", "test": "فحص مسارات الاستيراد", "action": "checkImportPaths", "status": "failed", "message": "بعض مسارات الاستيراد غير صحيحة", "details": "49/51 مسار صحيح", "timestamp": "2025-06-02T09:35:30.311Z"}]}, {"id": "components", "name": "اختبار المكونات", "results": [{"suite": "components", "test": "فحص المكونات الأساسية", "action": "checkCoreComponents", "status": "passed", "message": "جميع المكونات الأساسية موجودة", "details": "3/3 مكون", "timestamp": "2025-06-02T09:35:30.312Z"}, {"suite": "components", "test": "فحص الصفحات الرئيسية", "action": "checkMainPages", "status": "passed", "message": "معظم الصفحات الرئيسية موجودة", "details": "3/3 صف<PERSON>ة", "timestamp": "2025-06-02T09:35:30.313Z"}, {"suite": "components", "test": "فحص مكونات UI", "action": "checkUIComponents", "status": "passed", "message": "مكونات UI موجودة", "details": "3/3 مجموعة مكونات", "timestamp": "2025-06-02T09:35:30.313Z"}]}, {"id": "services", "name": "اختبار الخدمات", "results": [{"suite": "services", "test": "فحص خدمات قاعدة البيانات", "action": "checkDatabaseServices", "status": "passed", "message": "خدمات قاعدة البيانات موجودة", "details": "2/2 خدمة", "timestamp": "2025-06-02T09:35:30.314Z"}, {"suite": "services", "test": "فح<PERSON> خدمات الأمان", "action": "checkSecurityServices", "status": "passed", "message": "خدمات الأمان موجودة", "details": "2/2 خدمة أمان", "timestamp": "2025-06-02T09:35:30.314Z"}, {"suite": "services", "test": "فح<PERSON> خدمات الإشعارات", "action": "checkNotificationServices", "status": "passed", "message": "خدمات الإشعارات موجودة", "details": "2/2 خدمة إشعارات", "timestamp": "2025-06-02T09:35:30.315Z"}]}, {"id": "functionality", "name": "اختبار الوظائف", "results": [{"suite": "functionality", "test": "اختبار تسجيل الدخول", "action": "testLogin", "status": "passed", "message": "مكونات تسجيل الدخول موجودة", "details": "صفحة تسجيل الدخول و AuthContext متوفران", "timestamp": "2025-06-02T09:35:30.316Z"}, {"suite": "functionality", "test": "اختبار لوحة التحكم", "action": "testDashboard", "status": "passed", "message": "لوحة التحكم موجودة", "details": "1/1 صفحة لوحة تحكم", "timestamp": "2025-06-02T09:35:30.316Z"}, {"suite": "functionality", "test": "اختبار إدارة الحافلات", "action": "testBusManagement", "status": "passed", "message": "مكونات إدارة الحافلات موجودة", "details": "2/2 مكون حافلات", "timestamp": "2025-06-02T09:35:30.317Z"}]}, {"id": "performance", "name": "اختبار الأداء", "results": [{"suite": "performance", "test": "فحص سرعة التحميل", "action": "checkLoadSpeed", "status": "passed", "message": "حجم الملفات الأساسية مناسب", "details": "21.98 KB إجمالي", "timestamp": "2025-06-02T09:35:30.318Z"}, {"suite": "performance", "test": "فحص استهلاك الذاكرة", "action": "checkMemoryUsage", "status": "passed", "message": "استهلاك الذاكرة مقبول", "details": "4.80 MB مستخدم", "timestamp": "2025-06-02T09:35:30.318Z"}, {"suite": "performance", "test": "فحص حجم الملفات", "action": "checkFileSize", "status": "passed", "message": "حجم مشروع مناسب", "details": "3.02 MB في 265 ملف", "timestamp": "2025-06-02T09:35:30.337Z"}]}], "detailed_results": [{"suite": "basic", "test": "فحص البنية الأساسية", "action": "checkBasicStructure", "status": "passed", "message": "جميع المجلدات الأساسية موجودة", "details": "تم فحص 7 مجلد", "timestamp": "2025-06-02T09:35:30.305Z"}, {"suite": "basic", "test": "فحص ملفات التكوين", "action": "checkConfigFiles", "status": "passed", "message": "جميع ملفات التكوين موجودة", "details": "تم فحص 5 ملف", "timestamp": "2025-06-02T09:35:30.306Z"}, {"suite": "basic", "test": "فحص مسارات الاستيراد", "action": "checkImportPaths", "status": "failed", "message": "بعض مسارات الاستيراد غير صحيحة", "details": "49/51 مسار صحيح", "timestamp": "2025-06-02T09:35:30.311Z"}, {"suite": "components", "test": "فحص المكونات الأساسية", "action": "checkCoreComponents", "status": "passed", "message": "جميع المكونات الأساسية موجودة", "details": "3/3 مكون", "timestamp": "2025-06-02T09:35:30.312Z"}, {"suite": "components", "test": "فحص الصفحات الرئيسية", "action": "checkMainPages", "status": "passed", "message": "معظم الصفحات الرئيسية موجودة", "details": "3/3 صف<PERSON>ة", "timestamp": "2025-06-02T09:35:30.313Z"}, {"suite": "components", "test": "فحص مكونات UI", "action": "checkUIComponents", "status": "passed", "message": "مكونات UI موجودة", "details": "3/3 مجموعة مكونات", "timestamp": "2025-06-02T09:35:30.313Z"}, {"suite": "services", "test": "فحص خدمات قاعدة البيانات", "action": "checkDatabaseServices", "status": "passed", "message": "خدمات قاعدة البيانات موجودة", "details": "2/2 خدمة", "timestamp": "2025-06-02T09:35:30.314Z"}, {"suite": "services", "test": "فح<PERSON> خدمات الأمان", "action": "checkSecurityServices", "status": "passed", "message": "خدمات الأمان موجودة", "details": "2/2 خدمة أمان", "timestamp": "2025-06-02T09:35:30.314Z"}, {"suite": "services", "test": "فح<PERSON> خدمات الإشعارات", "action": "checkNotificationServices", "status": "passed", "message": "خدمات الإشعارات موجودة", "details": "2/2 خدمة إشعارات", "timestamp": "2025-06-02T09:35:30.315Z"}, {"suite": "functionality", "test": "اختبار تسجيل الدخول", "action": "testLogin", "status": "passed", "message": "مكونات تسجيل الدخول موجودة", "details": "صفحة تسجيل الدخول و AuthContext متوفران", "timestamp": "2025-06-02T09:35:30.316Z"}, {"suite": "functionality", "test": "اختبار لوحة التحكم", "action": "testDashboard", "status": "passed", "message": "لوحة التحكم موجودة", "details": "1/1 صفحة لوحة تحكم", "timestamp": "2025-06-02T09:35:30.316Z"}, {"suite": "functionality", "test": "اختبار إدارة الحافلات", "action": "testBusManagement", "status": "passed", "message": "مكونات إدارة الحافلات موجودة", "details": "2/2 مكون حافلات", "timestamp": "2025-06-02T09:35:30.317Z"}, {"suite": "performance", "test": "فحص سرعة التحميل", "action": "checkLoadSpeed", "status": "passed", "message": "حجم الملفات الأساسية مناسب", "details": "21.98 KB إجمالي", "timestamp": "2025-06-02T09:35:30.318Z"}, {"suite": "performance", "test": "فحص استهلاك الذاكرة", "action": "checkMemoryUsage", "status": "passed", "message": "استهلاك الذاكرة مقبول", "details": "4.80 MB مستخدم", "timestamp": "2025-06-02T09:35:30.318Z"}, {"suite": "performance", "test": "فحص حجم الملفات", "action": "checkFileSize", "status": "passed", "message": "حجم مشروع مناسب", "details": "3.02 MB في 265 ملف", "timestamp": "2025-06-02T09:35:30.337Z"}], "summary": {"overall_status": "needs_improvement", "recommendations": ["إصلاح الاختبارات الفاشلة أولاً"]}}