/**
 * سكريبت التبديل بين البنية القديمة والجديدة
 * Switch Between Old and New Structure Script
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 سكريبت التبديل بين البنية القديمة والجديدة\n');

class StructureSwitcher {
  constructor() {
    this.currentStructure = this.detectCurrentStructure();
  }

  /**
   * كشف البنية الحالية
   */
  detectCurrentStructure() {
    const srcExists = fs.existsSync('src');
    const srcNewExists = fs.existsSync('src-new');
    const srcOldExists = fs.existsSync('src-old');

    if (srcExists && srcNewExists) {
      return 'both'; // كلا البنيتين موجودتان
    } else if (srcExists && !srcNewExists) {
      return 'old'; // البنية القديمة فقط
    } else if (!srcExists && srcNewExists) {
      return 'new-only'; // البنية الجديدة فقط
    } else if (srcOldExists && !srcExists) {
      return 'switched'; // تم التبديل مسبقاً
    } else {
      return 'unknown';
    }
  }

  /**
   * إنشاء نسخة احتياطية آمنة
   */
  createSafeBackup(source, backup) {
    if (fs.existsSync(backup)) {
      console.log(`⚠️ النسخة الاحتياطية موجودة مسبقاً: ${backup}`);
      return false;
    }

    try {
      // نسخ المجلد
      this.copyDirectory(source, backup);
      console.log(`✅ تم إنشاء نسخة احتياطية: ${source} → ${backup}`);
      return true;
    } catch (error) {
      console.error(`❌ فشل في إنشاء النسخة الاحتياطية: ${error.message}`);
      return false;
    }
  }

  /**
   * نسخ مجلد بشكل تكراري
   */
  copyDirectory(source, destination) {
    if (!fs.existsSync(destination)) {
      fs.mkdirSync(destination, { recursive: true });
    }

    const items = fs.readdirSync(source);

    for (const item of items) {
      const sourcePath = path.join(source, item);
      const destPath = path.join(destination, item);
      const stat = fs.statSync(sourcePath);

      if (stat.isDirectory()) {
        this.copyDirectory(sourcePath, destPath);
      } else {
        fs.copyFileSync(sourcePath, destPath);
      }
    }
  }

  /**
   * حذف مجلد بشكل تكراري
   */
  removeDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      return;
    }

    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        this.removeDirectory(fullPath);
      } else {
        fs.unlinkSync(fullPath);
      }
    }

    fs.rmdirSync(dirPath);
  }

  /**
   * إعادة تسمية مجلد
   */
  renameDirectory(oldName, newName) {
    if (!fs.existsSync(oldName)) {
      console.log(`⚠️ المجلد غير موجود: ${oldName}`);
      return false;
    }

    if (fs.existsSync(newName)) {
      console.log(`⚠️ المجلد الوجهة موجود مسبقاً: ${newName}`);
      return false;
    }

    try {
      fs.renameSync(oldName, newName);
      console.log(`✅ تم إعادة تسمية: ${oldName} → ${newName}`);
      return true;
    } catch (error) {
      console.error(`❌ فشل في إعادة التسمية: ${error.message}`);
      return false;
    }
  }

  /**
   * التبديل إلى البنية الجديدة
   */
  switchToNew() {
    console.log('🔄 التبديل إلى البنية الجديدة...\n');

    switch (this.currentStructure) {
      case 'both':
        // إنشاء نسخة احتياطية من البنية القديمة
        if (!this.createSafeBackup('src', 'src-old')) {
          console.log('❌ فشل في إنشاء النسخة الاحتياطية');
          return false;
        }

        // حذف البنية القديمة
        this.removeDirectory('src');

        // إعادة تسمية البنية الجديدة
        if (this.renameDirectory('src-new', 'src')) {
          console.log('✅ تم التبديل إلى البنية الجديدة بنجاح!');
          return true;
        }
        break;

      case 'new-only':
        if (this.renameDirectory('src-new', 'src')) {
          console.log('✅ تم تفعيل البنية الجديدة!');
          return true;
        }
        break;

      case 'switched':
        console.log('✅ البنية الجديدة مفعلة مسبقاً!');
        return true;

      default:
        console.log('❌ لا يمكن التبديل - البنية الجديدة غير موجودة');
        return false;
    }

    return false;
  }

  /**
   * العودة إلى البنية القديمة
   */
  switchToOld() {
    console.log('🔄 العودة إلى البنية القديمة...\n');

    if (!fs.existsSync('src-old')) {
      console.log('❌ البنية القديمة غير موجودة (src-old)');
      return false;
    }

    // إنشاء نسخة احتياطية من البنية الحالية
    if (fs.existsSync('src')) {
      if (!this.createSafeBackup('src', 'src-new-backup')) {
        console.log('❌ فشل في إنشاء نسخة احتياطية من البنية الحالية');
        return false;
      }
      this.removeDirectory('src');
    }

    // استعادة البنية القديمة
    if (this.renameDirectory('src-old', 'src')) {
      console.log('✅ تم العودة إلى البنية القديمة بنجاح!');
      return true;
    }

    return false;
  }

  /**
   * عرض حالة البنية الحالية
   */
  showStatus() {
    console.log('📊 حالة البنية الحالية:\n');

    const structures = {
      'src': fs.existsSync('src'),
      'src-new': fs.existsSync('src-new'),
      'src-old': fs.existsSync('src-old'),
      'src-new-backup': fs.existsSync('src-new-backup')
    };

    for (const [name, exists] of Object.entries(structures)) {
      const status = exists ? '✅ موجود' : '❌ غير موجود';
      console.log(`  ${name}: ${status}`);
    }

    console.log(`\n🔍 البنية المكتشفة: ${this.currentStructure}`);

    // توصيات
    console.log('\n💡 التوصيات:');
    switch (this.currentStructure) {
      case 'both':
        console.log('  • يمكنك التبديل إلى البنية الجديدة بأمان');
        console.log('  • سيتم إنشاء نسخة احتياطية تلقائياً');
        break;
      case 'old':
        console.log('  • البنية الجديدة غير موجودة');
        console.log('  • قم بتشغيل الترحيل أولاً');
        break;
      case 'new-only':
        console.log('  • يمكنك تفعيل البنية الجديدة');
        break;
      case 'switched':
        console.log('  • البنية الجديدة مفعلة');
        console.log('  • يمكنك العودة للقديمة إذا لزم الأمر');
        break;
      default:
        console.log('  • حالة غير معروفة - تحقق من الملفات');
    }
  }

  /**
   * تنظيف الملفات الاحتياطية القديمة
   */
  cleanup() {
    console.log('🧹 تنظيف الملفات الاحتياطية...\n');

    const backupDirs = ['src-old', 'src-new-backup'];
    let cleaned = 0;

    for (const dir of backupDirs) {
      if (fs.existsSync(dir)) {
        console.log(`🗑️ حذف: ${dir}`);
        this.removeDirectory(dir);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`✅ تم تنظيف ${cleaned} مجلد احتياطي`);
    } else {
      console.log('✅ لا توجد ملفات احتياطية للتنظيف');
    }
  }
}

/**
 * الدالة الرئيسية
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  const switcher = new StructureSwitcher();

  switch (command) {
    case 'new':
    case 'switch-new':
      switcher.switchToNew();
      break;

    case 'old':
    case 'switch-old':
      switcher.switchToOld();
      break;

    case 'status':
      switcher.showStatus();
      break;

    case 'cleanup':
      switcher.cleanup();
      break;

    default:
      console.log('📋 الأوامر المتاحة:');
      console.log('  node scripts/switch-structure.cjs new     - التبديل للبنية الجديدة');
      console.log('  node scripts/switch-structure.cjs old     - العودة للبنية القديمة');
      console.log('  node scripts/switch-structure.cjs status  - عرض الحالة الحالية');
      console.log('  node scripts/switch-structure.cjs cleanup - تنظيف الملفات الاحتياطية');
      console.log('\n💡 مثال:');
      console.log('  node scripts/switch-structure.cjs new');
      
      // عرض الحالة الحالية
      console.log('\n');
      switcher.showStatus();
  }
}

// تشغيل النظام
main();
