import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  FileText,
  Plus,
  Edit,
  Trash2,
  Copy,
  Download,
  Upload,
  Search,
  Filter,
  Users,
  Bus,
  TrendingUp,
  Clock,
  Grid3X3,
  Globe,
  Lock,
  Share2,
  UserPlus,
  Mail,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import {
  reportGeneratorService,
  ReportTemplate,
  ReportConfig,
} from "../../lib/reportGeneratorService";

interface ReportTemplatesProps {
  className?: string;
}

export const ReportTemplates: React.FC<ReportTemplatesProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const { buses, routes } = useDatabase();
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<ReportTemplate | null>(
    null,
  );
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  const [showShareModal, setShowShareModal] = useState(false);
  const [sharingTemplate, setSharingTemplate] = useState<ReportTemplate | null>(
    null,
  );
  const [shareEmails, setShareEmails] = useState<string[]>([""]);
  const [shareMessage, setShareMessage] = useState("");
  const [formData, setFormData] = useState<{
    name: string;
    description: string;
    is_public: boolean;
    config: ReportConfig;
  }>({
    name: "",
    description: "",
    is_public: false,
    config: {
      type: "attendance",
      format: "pdf",
      filters: {
        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          .toISOString()
          .split("T")[0],
        endDate: new Date().toISOString().split("T")[0],
        tenantId: "",
      },
      options: {
        includeCharts: true,
        includeStats: true,
        groupBy: "day",
      },
    },
  });

  useEffect(() => {
    if (tenant?.id) {
      loadTemplates();
      setFormData((prev) => ({
        ...prev,
        config: {
          ...prev.config,
          filters: {
            ...prev.config.filters,
            tenantId: tenant.id,
          },
        },
      }));
    }
  }, [tenant?.id]);

  const loadTemplates = async () => {
    if (!tenant?.id || !user?.id) return;

    try {
      setLoading(true);
      const templateList = await reportGeneratorService.getReportTemplates(
        tenant.id,
        user.id,
      );
      setTemplates(templateList);
    } catch (error) {
      console.error("Error loading templates:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!tenant?.id || !user?.id) return;

    try {
      const templateData = {
        name: formData.name,
        description: formData.description,
        config: formData.config,
        created_by: user.id,
        tenant_id: tenant.id,
        is_public: formData.is_public,
      };

      if (editingTemplate) {
        // Update existing template (would need to implement update method)
        console.log("Update template:", templateData);
      } else {
        await reportGeneratorService.saveReportTemplate(templateData);
      }

      await loadTemplates();
      resetForm();
    } catch (error) {
      console.error("Error saving template:", error);
      alert("Failed to save template. Please try again.");
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      is_public: false,
      config: {
        type: "attendance",
        format: "pdf",
        filters: {
          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            .toISOString()
            .split("T")[0],
          endDate: new Date().toISOString().split("T")[0],
          tenantId: tenant?.id || "",
        },
        options: {
          includeCharts: true,
          includeStats: true,
          groupBy: "day",
        },
      },
    });
    setShowForm(false);
    setEditingTemplate(null);
  };

  const deleteTemplate = async (templateId: string) => {
    if (!confirm("Are you sure you want to delete this template?")) return;

    try {
      await reportGeneratorService.deleteReportTemplate(templateId);
      await loadTemplates();
    } catch (error) {
      console.error("Error deleting template:", error);
      alert("Failed to delete template. Please try again.");
    }
  };

  const duplicateTemplate = async (template: ReportTemplate) => {
    try {
      const duplicatedTemplate = {
        name: `${template.name} (Copy)`,
        description: template.description,
        config: template.config,
        created_by: user?.id || "",
        tenant_id: tenant?.id || "",
        is_public: false,
      };

      await reportGeneratorService.saveReportTemplate(duplicatedTemplate);
      await loadTemplates();
    } catch (error) {
      console.error("Error duplicating template:", error);
      alert("Failed to duplicate template. Please try again.");
    }
  };

  const generateReportFromTemplate = async (
    template: ReportTemplate,
    format: "csv" | "pdf",
  ) => {
    try {
      const config = {
        ...template.config,
        format,
        filters: {
          ...template.config.filters,
          tenantId: tenant?.id || "",
        },
      };

      if (format === "csv") {
        const csvData = await reportGeneratorService.exportToCSV(config);
        const blob = new Blob([csvData], { type: "text/csv" });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${template.name.replace(/\s+/g, "_").toLowerCase()}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
      } else {
        const pdfBlob = await reportGeneratorService.exportToPDF(config);
        const url = window.URL.createObjectURL(pdfBlob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${template.name.replace(/\s+/g, "_").toLowerCase()}.pdf`;
        a.click();
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Error generating report from template:", error);
      alert("Failed to generate report. Please try again.");
    }
  };

  const getReportTypeIcon = (type: string) => {
    switch (type) {
      case "attendance":
        return Users;
      case "bus_utilization":
        return Bus;
      case "driver_performance":
        return TrendingUp;
      case "route_delays":
        return Clock;
      case "combined":
        return Grid3X3;
      default:
        return FileText;
    }
  };

  const getReportTypeColor = (type: string) => {
    switch (type) {
      case "attendance":
        return "blue";
      case "bus_utilization":
        return "green";
      case "driver_performance":
        return "yellow";
      case "route_delays":
        return "red";
      case "combined":
        return "purple";
      default:
        return "gray";
    }
  };

  const filteredTemplates = templates.filter((template) => {
    const matchesSearch = template.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesFilter =
      filterType === "all" || template.config.type === filterType;
    return matchesSearch && matchesFilter;
  });

  const shareTemplate = async (template: ReportTemplate) => {
    setSharingTemplate(template);
    setShowShareModal(true);
  };

  const handleShareSubmit = async () => {
    if (!sharingTemplate || !tenant?.id) return;

    const validEmails = shareEmails.filter(
      (email) => email.trim() !== "" && email.includes("@"),
    );
    if (validEmails.length === 0) {
      alert("Please enter at least one valid email address.");
      return;
    }

    try {
      // For now, just show success message since we don't have supabase import
      // In a real implementation, you would save to database here
      console.log(
        "Sharing template:",
        sharingTemplate.name,
        "with:",
        validEmails,
      );

      alert(
        `Template shared successfully with ${validEmails.length} recipient(s)!`,
      );
      setShowShareModal(false);
      setSharingTemplate(null);
      setShareEmails([""]);
      setShareMessage("");
    } catch (error) {
      console.error("Error sharing template:", error);
      alert("Failed to share template. Please try again.");
    }
  };

  const addShareEmail = () => {
    setShareEmails((prev) => [...prev, ""]);
  };

  const updateShareEmail = (index: number, email: string) => {
    setShareEmails((prev) => prev.map((e, i) => (i === index ? email : e)));
  };

  const removeShareEmail = (index: number) => {
    setShareEmails((prev) => prev.filter((_, i) => i !== index));
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
            <FileText className="mr-2 h-5 w-5 text-primary-500" />
            Report Templates
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Save and manage report configurations for quick access and reuse.
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Upload size={16} />
            Import
          </Button>
          <Button
            onClick={() => setShowForm(true)}
            className="flex items-center gap-2"
          >
            <Plus size={16} />
            Create Template
          </Button>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Types</option>
              <option value="attendance">Attendance</option>
              <option value="bus_utilization">Bus Utilization</option>
              <option value="driver_performance">Driver Performance</option>
              <option value="route_delays">Route Delays</option>
              <option value="combined">Combined</option>
            </select>
          </div>
        </div>
      </div>

      {/* Template Form */}
      {showForm && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {editingTemplate ? "Edit" : "Create New"} Template
          </h3>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Template Name
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, name: e.target.value }))
                  }
                  required
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  placeholder="e.g., Weekly Attendance Summary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Report Type
                </label>
                <select
                  value={formData.config.type}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      config: {
                        ...prev.config,
                        type: e.target.value as ReportConfig["type"],
                      },
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="attendance">Attendance Report</option>
                  <option value="bus_utilization">Bus Utilization</option>
                  <option value="driver_performance">Driver Performance</option>
                  <option value="route_delays">Route Delays</option>
                  <option value="combined">Combined Report</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                placeholder="Describe what this template is used for..."
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Default Format
                </label>
                <select
                  value={formData.config.format}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      config: {
                        ...prev.config,
                        format: e.target.value as "csv" | "pdf",
                      },
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="pdf">PDF</option>
                  <option value="csv">CSV</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Group By
                </label>
                <select
                  value={formData.config.options?.groupBy || "day"}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      config: {
                        ...prev.config,
                        options: {
                          ...prev.config.options,
                          groupBy: e.target.value as "day" | "week" | "month",
                        },
                      },
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="day">Daily</option>
                  <option value="week">Weekly</option>
                  <option value="month">Monthly</option>
                </select>
              </div>

              <div className="flex items-center gap-4 pt-6">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.config.options?.includeCharts || false}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        config: {
                          ...prev.config,
                          options: {
                            ...prev.config.options,
                            includeCharts: e.target.checked,
                          },
                        },
                      }))
                    }
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Include Charts
                  </span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.is_public}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        is_public: e.target.checked,
                      }))
                    }
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Public Template
                  </span>
                </label>
              </div>
            </div>

            <div className="flex justify-end gap-3">
              <Button type="button" variant="outline" onClick={resetForm}>
                Cancel
              </Button>
              <Button type="submit">
                {editingTemplate ? "Update" : "Create"} Template
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* Templates Grid */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Templates ({filteredTemplates.length})
          </h3>
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
        ) : filteredTemplates.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6">
            {filteredTemplates.map((template) => {
              const Icon = getReportTypeIcon(template.config.type);
              const color = getReportTypeColor(template.config.type);

              return (
                <div
                  key={template.id}
                  className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div
                        className={`p-2 rounded-lg bg-${color}-100 dark:bg-${color}-900/20`}
                      >
                        <Icon
                          className={`h-5 w-5 text-${color}-600 dark:text-${color}-400`}
                        />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {template.name}
                        </h4>
                        <div className="flex items-center gap-2 mt-1">
                          <span
                            className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-${color}-100 text-${color}-800 dark:bg-${color}-900/20 dark:text-${color}-400`}
                          >
                            {template.config.type.replace("_", " ")}
                          </span>
                          {template.is_public ? (
                            <Globe className="h-3 w-3 text-green-500" />
                          ) : (
                            <Lock className="h-3 w-3 text-gray-400" />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <p className="text-xs text-gray-500 dark:text-gray-400 mb-4 line-clamp-2">
                    {template.description || "No description provided."}
                  </p>

                  <div className="text-xs text-gray-400 mb-4">
                    Created:{" "}
                    {new Date(template.created_at).toLocaleDateString()}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          generateReportFromTemplate(template, "pdf")
                        }
                        className="text-xs"
                      >
                        PDF
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          generateReportFromTemplate(template, "csv")
                        }
                        className="text-xs"
                      >
                        CSV
                      </Button>
                    </div>

                    <div className="flex items-center gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => duplicateTemplate(template)}
                        className="p-1"
                      >
                        <Copy size={12} />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setEditingTemplate(template);
                          setFormData({
                            name: template.name,
                            description: template.description,
                            is_public: template.is_public,
                            config: template.config,
                          });
                          setShowForm(true);
                        }}
                        className="p-1"
                      >
                        <Edit size={12} />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => shareTemplate(template)}
                        className="p-1"
                        title="Share template"
                      >
                        <Share2 size={12} />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => deleteTemplate(template.id)}
                        className="p-1 text-red-600 border-red-300 hover:bg-red-50"
                      >
                        <Trash2 size={12} />
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <FileText size={48} className="mx-auto mb-4 text-gray-300" />
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm || filterType !== "all"
                ? "No templates match your search criteria."
                : "No templates found."}
            </p>
            <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">
              Create your first template to save report configurations for quick
              access.
            </p>
          </div>
        )}
      </div>

      {/* Share Template Modal */}
      {showShareModal && sharingTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                  <Share2 className="mr-2 h-5 w-5 text-primary-500" />
                  Share Template
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowShareModal(false)}
                  className="p-1"
                >
                  ×
                </Button>
              </div>

              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    Sharing: <strong>{sharingTemplate.name}</strong>
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email Recipients
                  </label>
                  <div className="space-y-2">
                    {shareEmails.map((email, index) => (
                      <div key={index} className="flex gap-2">
                        <input
                          type="email"
                          value={email}
                          onChange={(e) =>
                            updateShareEmail(index, e.target.value)
                          }
                          placeholder="<EMAIL>"
                          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
                        />
                        {shareEmails.length > 1 && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => removeShareEmail(index)}
                            className="p-2"
                          >
                            <Trash2 size={14} />
                          </Button>
                        )}
                      </div>
                    ))}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={addShareEmail}
                      className="flex items-center gap-2"
                    >
                      <UserPlus size={14} />
                      Add Recipient
                    </Button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Message (Optional)
                  </label>
                  <textarea
                    value={shareMessage}
                    onChange={(e) => setShareMessage(e.target.value)}
                    placeholder="Add a personal message..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
                  />
                </div>

                <div className="flex justify-end gap-3 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowShareModal(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleShareSubmit}
                    className="flex items-center gap-2"
                  >
                    <Mail size={16} />
                    Share Template
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportTemplates;
