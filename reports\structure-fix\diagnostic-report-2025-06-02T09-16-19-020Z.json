{"diagnostic_info": {"timestamp": "2025-06-02T09-16-19-020Z", "total_issues": 8, "high_severity": 8, "medium_severity": 0, "low_severity": 0}, "issues_by_type": {"missing_files": 1, "invalid_imports": 4, "wrong_import_paths": 3}, "detailed_issues": [{"type": "missing_file", "file": "src-new/core/contexts/CustomThemeProvider.tsx", "severity": "high", "description": "ملف مهم مفقود: src-new/core/contexts/CustomThemeProvider.tsx"}, {"type": "invalid_import", "file": "src-new/main.tsx", "import": "./core/contexts/CustomThemeProvider", "severity": "high", "description": "مسار استيراد غير صحيح: ./core/contexts/CustomThemeProvider في src-new/main.tsx"}, {"type": "invalid_import", "file": "src-new/App.tsx", "import": "./features/testing/TestPage", "severity": "high", "description": "مسار استيراد غير صحيح: ./features/testing/TestPage في src-new/App.tsx"}, {"type": "invalid_import", "file": "src-new/App.tsx", "import": "./shared/services/lib/rbacCentralizedConfigEnhanced", "severity": "high", "description": "مسار استيراد غير صحيح: ./shared/services/lib/rbacCentralizedConfigEnhanced في src-new/App.tsx"}, {"type": "invalid_import", "file": "src-new/App.tsx", "import": "./core/hooks/useThemePermissions", "severity": "high", "description": "مسار استيراد غير صحيح: ./core/hooks/useThemePermissions في src-new/App.tsx"}, {"type": "wrong_import_path", "file": "src-new/core/contexts/AuthContext.tsx", "wrongPath": "../lib/supabase", "correctPath": "../shared/services/lib/supabase", "severity": "high", "description": "مسار استيراد خاطئ في src-new/core/contexts/AuthContext.tsx"}, {"type": "wrong_import_path", "file": "src-new/core/contexts/DatabaseContext.tsx", "wrongPath": "../lib/supabase", "correctPath": "../shared/services/lib/supabase", "severity": "high", "description": "مسار استيراد خاطئ في src-new/core/contexts/DatabaseContext.tsx"}, {"type": "wrong_import_path", "file": "src-new/core/contexts/NotificationsContext.tsx", "wrongPath": "../lib/supabase", "correctPath": "../shared/services/lib/supabase", "severity": "high", "description": "مسار استيراد خاطئ في src-new/core/contexts/NotificationsContext.tsx"}], "recommended_fixes": ["إنشاء الملفات المفقودة", "تصحيح مسارات الاستيراد", "إنشاء ملفات index.ts مناسبة", "تحديث التبعيات"]}