import { useState, useCallback } from "react";
import { AppError, handleSupabaseError, logError } from "../utils/errorHandler";

export const useErrorHandler = () => {
  const [error, setError] = useState<AppError | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleError = useCallback((error: any, context?: string) => {
    const appError =
      error instanceof AppError ? error : handleSupabaseError(error);
    logError(appError, context);
    setError(appError);
    setIsLoading(false);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const executeAsync = useCallback(
    async <T>(
      asyncFn: () => Promise<T>,
      context?: string,
    ): Promise<T | null> => {
      try {
        setIsLoading(true);
        setError(null);
        const result = await asyncFn();
        setIsLoading(false);
        return result;
      } catch (err) {
        handleError(err, context);
        return null;
      }
    },
    [handleError],
  );

  return {
    error,
    isLoading,
    handleError,
    clearError,
    executeAsync,
  };
};
