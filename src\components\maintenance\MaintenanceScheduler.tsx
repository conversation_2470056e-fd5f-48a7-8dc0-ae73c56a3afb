import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Calendar,
  Wrench,
  Plus,
  Bell,
  AlertTriangle,
  CheckCircle,
  Edit,
  Trash2,
  Grid3X3,
  List,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";
import * as api from "../../lib/api";

interface MaintenanceRecord {
  id: string;
  bus_id: string;
  type: string;
  description: string;
  scheduled_date: string;
  status: string;
  cost?: number;
  next_maintenance_date?: string;
  bus?: {
    id: string;
    plate_number: string;
  };
}

export const MaintenanceScheduler: React.FC = () => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const { buses } = useDatabase();
  const [maintenanceRecords, setMaintenanceRecords] = useState<
    MaintenanceRecord[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingRecord, setEditingRecord] = useState<MaintenanceRecord | null>(
    null,
  );
  const [viewMode, setViewMode] = useState<"list" | "calendar">("list");
  const [currentDate, setCurrentDate] = useState(new Date());
  const [formData, setFormData] = useState({
    bus_id: "",
    type: "routine",
    description: "",
    scheduled_date: "",
    cost: "",
    notes: "",
    technician_name: "",
    next_maintenance_date: "",
  });

  const maintenanceTypes = [
    { value: "routine", label: "Routine Maintenance" },
    { value: "repair", label: "Repair" },
    { value: "inspection", label: "Safety Inspection" },
    { value: "oil_change", label: "Oil Change" },
    { value: "tire_replacement", label: "Tire Replacement" },
    { value: "brake_service", label: "Brake Service" },
    { value: "engine_service", label: "Engine Service" },
    { value: "other", label: "Other" },
  ];

  const fetchMaintenanceRecords = async () => {
    if (!tenant?.id) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("bus_maintenance")
        .select(
          `
          *,
          bus:buses!bus_id(
            id,
            plate_number
          )
        `,
        )
        .eq("tenant_id", tenant.id)
        .order("scheduled_date", { ascending: true });

      if (error) throw error;
      setMaintenanceRecords(data || []);
    } catch (error) {
      console.error("Error fetching maintenance records:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMaintenanceRecords();
  }, [tenant?.id]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!tenant?.id) return;

    try {
      await api.createMaintenanceRecord(
        formData.bus_id,
        formData.type,
        formData.description,
        formData.scheduled_date,
        tenant.id,
        parseFloat(formData.cost) || 0,
        formData.notes,
        formData.technician_name,
        formData.next_maintenance_date || undefined,
      );

      // Create notification for upcoming maintenance
      try {
        const bus = buses.find((b) => b.id === formData.bus_id);
        const scheduledDate = new Date(formData.scheduled_date);
        const reminderDate = new Date(
          scheduledDate.getTime() - 24 * 60 * 60 * 1000,
        ); // 1 day before

        if (reminderDate > new Date()) {
          await supabase.from("notifications").insert({
            tenant_id: tenant.id,
            title: "Maintenance Reminder",
            message: `Maintenance scheduled for bus ${bus?.plate_number} tomorrow: ${formData.description}`,
            type: "maintenance_reminder",
            priority: "normal",
            metadata: {
              type: "maintenance",
              busId: formData.bus_id,
              maintenanceType: formData.type,
              scheduledDate: formData.scheduled_date,
            },
          });
        }
      } catch (notificationError) {
        console.warn(
          "Failed to create maintenance notification:",
          notificationError,
        );
      }

      // Reset form and refresh data
      setFormData({
        bus_id: "",
        type: "routine",
        description: "",
        scheduled_date: "",
        cost: "",
        notes: "",
        technician_name: "",
        next_maintenance_date: "",
      });
      setShowForm(false);
      setEditingRecord(null);
      fetchMaintenanceRecords();
    } catch (error) {
      console.error("Error creating maintenance record:", error);
      alert("Failed to schedule maintenance. Please try again.");
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "in_progress":
        return <Wrench className="h-4 w-4 text-blue-500" />;
      case "overdue":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Calendar className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "in_progress":
        return "bg-blue-100 text-blue-800";
      case "overdue":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const isOverdue = (scheduledDate: string, status: string) => {
    if (status === "completed") return false;
    return new Date(scheduledDate) < new Date();
  };

  const updateMaintenanceStatus = async (
    recordId: string,
    newStatus: string,
  ) => {
    try {
      const { error } = await supabase
        .from("bus_maintenance")
        .update({
          status: newStatus,
          completed_at:
            newStatus === "completed" ? new Date().toISOString() : null,
          updated_at: new Date().toISOString(),
        })
        .eq("id", recordId);

      if (error) throw error;
      fetchMaintenanceRecords();
    } catch (error) {
      console.error("Error updating maintenance status:", error);
      alert("Failed to update maintenance status. Please try again.");
    }
  };

  const deleteMaintenanceRecord = async (recordId: string) => {
    if (!confirm("Are you sure you want to delete this maintenance record?"))
      return;

    try {
      const { error } = await supabase
        .from("bus_maintenance")
        .delete()
        .eq("id", recordId);

      if (error) throw error;
      fetchMaintenanceRecords();
    } catch (error) {
      console.error("Error deleting maintenance record:", error);
      alert("Failed to delete maintenance record. Please try again.");
    }
  };

  const upcomingMaintenance = maintenanceRecords.filter(
    (record) =>
      record.status === "scheduled" &&
      new Date(record.scheduled_date) <=
        new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
  );

  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    const endDate = new Date(lastDay);
    endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()));

    for (
      let date = new Date(startDate);
      date <= endDate;
      date.setDate(date.getDate() + 1)
    ) {
      days.push(new Date(date));
    }

    return days;
  };

  const getMaintenanceForDate = (date: Date) => {
    const dateStr = date.toISOString().split("T")[0];
    return maintenanceRecords.filter(
      (record) => record.scheduled_date === dateStr,
    );
  };

  const navigateMonth = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() + (direction === "next" ? 1 : -1));
    setCurrentDate(newDate);
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentDate.getMonth();
  };

  const renderCalendarView = () => {
    const days = generateCalendarDays();
    const weekDays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        {/* Calendar Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateMonth("prev")}
            className="flex items-center gap-2"
          >
            <ChevronLeft size={16} />
            Previous
          </Button>

          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {currentDate.toLocaleDateString("en-US", {
              month: "long",
              year: "numeric",
            })}
          </h3>

          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateMonth("next")}
            className="flex items-center gap-2"
          >
            Next
            <ChevronRight size={16} />
          </Button>
        </div>

        {/* Calendar Grid */}
        <div className="p-4">
          {/* Week Headers */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {weekDays.map((day) => (
              <div
                key={day}
                className="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400"
              >
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Days */}
          <div className="grid grid-cols-7 gap-1">
            {days.map((date, index) => {
              const dayMaintenance = getMaintenanceForDate(date);
              const hasOverdue = dayMaintenance.some((m) =>
                isOverdue(m.scheduled_date, m.status),
              );
              const hasScheduled = dayMaintenance.some(
                (m) => m.status === "scheduled",
              );

              return (
                <div
                  key={index}
                  className={`min-h-[80px] p-1 border border-gray-100 dark:border-gray-700 rounded ${
                    isCurrentMonth(date)
                      ? "bg-white dark:bg-gray-800"
                      : "bg-gray-50 dark:bg-gray-900"
                  } ${isToday(date) ? "ring-2 ring-primary-500" : ""}`}
                >
                  <div
                    className={`text-sm font-medium mb-1 ${
                      isCurrentMonth(date)
                        ? "text-gray-900 dark:text-white"
                        : "text-gray-400"
                    } ${
                      isToday(date)
                        ? "text-primary-600 dark:text-primary-400"
                        : ""
                    }`}
                  >
                    {date.getDate()}
                  </div>

                  {/* Maintenance Items */}
                  <div className="space-y-1">
                    {dayMaintenance.slice(0, 2).map((maintenance) => {
                      const isOverdueItem = isOverdue(
                        maintenance.scheduled_date,
                        maintenance.status,
                      );
                      return (
                        <div
                          key={maintenance.id}
                          className={`text-xs p-1 rounded truncate cursor-pointer ${
                            isOverdueItem
                              ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                              : maintenance.status === "completed"
                                ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                                : "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                          }`}
                          title={`${maintenance.bus?.plate_number} - ${maintenance.description}`}
                          onClick={() => {
                            // Could open a detail modal here
                            console.log("Maintenance clicked:", maintenance);
                          }}
                        >
                          {maintenance.bus?.plate_number}
                        </div>
                      );
                    })}

                    {dayMaintenance.length > 2 && (
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        +{dayMaintenance.length - 2} more
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Legend */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-4 text-xs">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-100 dark:bg-blue-900/20 rounded"></div>
              <span className="text-gray-600 dark:text-gray-400">
                Scheduled
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-100 dark:bg-red-900/20 rounded"></div>
              <span className="text-gray-600 dark:text-gray-400">Overdue</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-100 dark:bg-green-900/20 rounded"></div>
              <span className="text-gray-600 dark:text-gray-400">
                Completed
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Wrench className="mr-2 h-5 w-5 text-primary-500" />
              Maintenance Scheduler
            </h3>
            {upcomingMaintenance.length > 0 && (
              <div className="flex items-center gap-2 mt-2">
                <Bell className="h-4 w-4 text-orange-500" />
                <span className="text-sm text-orange-600">
                  {upcomingMaintenance.length} maintenance task
                  {upcomingMaintenance.length !== 1 ? "s" : ""} due this week
                </span>
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            <div className="flex border border-gray-300 dark:border-gray-600 rounded-lg">
              <button
                onClick={() => setViewMode("list")}
                className={`px-3 py-2 text-sm font-medium transition-colors ${
                  viewMode === "list"
                    ? "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300"
                    : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
                title="List View"
              >
                <List size={16} />
              </button>
              <button
                onClick={() => setViewMode("calendar")}
                className={`px-3 py-2 text-sm font-medium transition-colors ${
                  viewMode === "calendar"
                    ? "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300"
                    : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
                title="Calendar View"
              >
                <Grid3X3 size={16} />
              </button>
            </div>

            <Button
              onClick={() => setShowForm(!showForm)}
              className="flex items-center gap-2"
            >
              <Plus size={16} />
              Schedule Maintenance
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {showForm && (
          <div className="mb-6 p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
              Schedule New Maintenance
            </h4>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Bus
                  </label>
                  <select
                    value={formData.bus_id}
                    onChange={(e) =>
                      setFormData({ ...formData, bus_id: e.target.value })
                    }
                    required
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Select a bus</option>
                    {buses.map((bus) => (
                      <option key={bus.id} value={bus.id}>
                        {bus.plate_number}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Maintenance Type
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) =>
                      setFormData({ ...formData, type: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  >
                    {maintenanceTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  required
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Describe the maintenance work to be performed..."
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Scheduled Date
                  </label>
                  <input
                    type="date"
                    value={formData.scheduled_date}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        scheduled_date: e.target.value,
                      })
                    }
                    required
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Estimated Cost
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.cost}
                    onChange={(e) =>
                      setFormData({ ...formData, cost: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Technician
                  </label>
                  <input
                    type="text"
                    value={formData.technician_name}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        technician_name: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Technician name"
                  />
                </div>
              </div>

              <div className="flex justify-end gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowForm(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Schedule Maintenance</Button>
              </div>
            </form>
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
        ) : maintenanceRecords.length === 0 ? (
          <div className="text-center py-8">
            <Wrench size={48} className="mx-auto mb-4 text-gray-300" />
            <p className="text-gray-500 dark:text-gray-400">
              No maintenance records found. Schedule your first maintenance
              task.
            </p>
          </div>
        ) : viewMode === "calendar" ? (
          renderCalendarView()
        ) : (
          <div className="space-y-4">
            {maintenanceRecords.map((record) => {
              const overdue = isOverdue(record.scheduled_date, record.status);
              const actualStatus = overdue ? "overdue" : record.status;

              return (
                <div
                  key={record.id}
                  className={`p-4 border rounded-lg ${
                    overdue
                      ? "border-red-200 bg-red-50"
                      : "border-gray-200 dark:border-gray-600"
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        {getStatusIcon(actualStatus)}
                        <span className="font-medium text-gray-900 dark:text-white">
                          {record.bus?.plate_number} -{" "}
                          {
                            maintenanceTypes.find(
                              (t) => t.value === record.type,
                            )?.label
                          }
                        </span>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(actualStatus)}`}
                        >
                          {overdue
                            ? "Overdue"
                            : record.status.replace("_", " ").toUpperCase()}
                        </span>
                      </div>

                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {record.description}
                      </p>

                      <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                        <span>
                          Scheduled:{" "}
                          {new Date(record.scheduled_date).toLocaleDateString()}
                        </span>
                        {record.cost && <span>Cost: ${record.cost}</span>}
                        {record.next_maintenance_date && (
                          <span>
                            Next:{" "}
                            {new Date(
                              record.next_maintenance_date,
                            ).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      {record.status === "scheduled" && (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              updateMaintenanceStatus(record.id, "in_progress")
                            }
                            className="text-blue-600 border-blue-300 hover:bg-blue-50"
                          >
                            Start
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              updateMaintenanceStatus(record.id, "completed")
                            }
                            className="text-green-600 border-green-300 hover:bg-green-50"
                          >
                            Complete
                          </Button>
                        </>
                      )}
                      {record.status === "in_progress" && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() =>
                            updateMaintenanceStatus(record.id, "completed")
                          }
                          className="text-green-600 border-green-300 hover:bg-green-50"
                        >
                          Complete
                        </Button>
                      )}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => deleteMaintenanceRecord(record.id)}
                        className="text-red-600 border-red-300 hover:bg-red-50"
                      >
                        <Trash2 size={14} />
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default MaintenanceScheduler;
