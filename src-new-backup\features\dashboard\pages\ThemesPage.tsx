/**
 * Themes Management Page
 * Phase 3: UI/UX Enhancement - Theme Management
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { ThemeCustomizer } from '../../components/theme/ThemeCustomizer';
import { ResponsiveLayout } from '../../components/layout/ResponsiveLayout';

export const ThemesPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();

  if (!user) {
    return null;
  }

  return (
    <ResponsiveLayout>
      <div className="container mx-auto px-4 py-6">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {user.role === 'admin' ? t('themes.manageThemes') : t('themes.schoolTheme')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {user.role === 'admin'
              ? t('themes.manageThemes') + ' - إدارة وتخصيص ثيمات جميع المدارس في النظام'
              : t('themes.schoolTheme') + ' - خصص مظهر مدرستك وألوانها حسب هويتك البصرية'
            }
          </p>
        </div>

        {/* Theme Customizer */}
        <ThemeCustomizer type={user.role === 'admin' ? 'admin' : 'school'} />
      </div>
    </ResponsiveLayout>
  );
};

export default ThemesPage;
