/**
 * Base Theme System
 * Foundation for all theme variations
 * Phase 3: UI/UX Enhancement
 */

import { baseColors, lightSemanticColors, darkSemanticColors, type SemanticColors } from '../../tokens/colors';
import { typographyTokens, type TypographyTokens } from '../../tokens/typography';
import { spacing, semanticSpacing, type SpacingScale, type SemanticSpacing } from '../../tokens/spacing';

export interface ShadowTokens {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  inner: string;
  none: string;
}

export interface BorderRadiusTokens {
  none: string;
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  full: string;
}

export interface ZIndexTokens {
  hide: number;
  auto: number;
  base: number;
  docked: number;
  dropdown: number;
  sticky: number;
  banner: number;
  overlay: number;
  modal: number;
  popover: number;
  skipLink: number;
  toast: number;
  tooltip: number;
}

export interface TransitionTokens {
  duration: {
    instant: string;
    fast: string;
    normal: string;
    slow: string;
    slower: string;
  };
  easing: {
    linear: string;
    easeIn: string;
    easeOut: string;
    easeInOut: string;
    bounce: string;
    elastic: string;
  };
}

export interface BreakpointTokens {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
}

export interface BaseTheme {
  name: string;
  mode: 'light' | 'dark';
  colors: SemanticColors;
  typography: TypographyTokens;
  spacing: SpacingScale;
  semanticSpacing: SemanticSpacing;
  shadows: ShadowTokens;
  borderRadius: BorderRadiusTokens;
  zIndex: ZIndexTokens;
  transitions: TransitionTokens;
  breakpoints: BreakpointTokens;
}

/**
 * Shadow Tokens
 */
export const shadows: ShadowTokens = {
  xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  none: 'none',
};

/**
 * Border Radius Tokens
 */
export const borderRadius: BorderRadiusTokens = {
  none: '0',
  xs: '0.125rem',   // 2px
  sm: '0.25rem',    // 4px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  '3xl': '1.5rem',  // 24px
  full: '9999px',
};

/**
 * Z-Index Tokens
 */
export const zIndex: ZIndexTokens = {
  hide: -1,
  auto: 0,
  base: 1,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800,
};

/**
 * Transition Tokens
 */
export const transitions: TransitionTokens = {
  duration: {
    instant: '0ms',
    fast: '150ms',
    normal: '200ms',
    slow: '300ms',
    slower: '500ms',
  },
  easing: {
    linear: 'linear',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  },
};

/**
 * Breakpoint Tokens
 */
export const breakpoints: BreakpointTokens = {
  xs: '475px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
};

/**
 * Light Theme
 */
export const lightTheme: BaseTheme = {
  name: 'light',
  mode: 'light',
  colors: lightSemanticColors,
  typography: typographyTokens,
  spacing,
  semanticSpacing,
  shadows,
  borderRadius,
  zIndex,
  transitions,
  breakpoints,
};

/**
 * Dark Theme
 */
export const darkTheme: BaseTheme = {
  name: 'dark',
  mode: 'dark',
  colors: darkSemanticColors,
  typography: typographyTokens,
  spacing,
  semanticSpacing,
  shadows: {
    ...shadows,
    // Adjust shadows for dark mode
    xs: '0 1px 2px 0 rgba(0, 0, 0, 0.3)',
    sm: '0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.2)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.1)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.5)',
  },
  borderRadius,
  zIndex,
  transitions,
  breakpoints,
};

/**
 * Theme utilities
 */
export const themeUtils = {
  /**
   * Get theme by name
   */
  getTheme(name: 'light' | 'dark'): BaseTheme {
    return name === 'dark' ? darkTheme : lightTheme;
  },

  /**
   * Create custom theme based on base theme
   */
  createCustomTheme(
    baseTheme: BaseTheme,
    overrides: Partial<BaseTheme>
  ): BaseTheme {
    return {
      ...baseTheme,
      ...overrides,
      colors: {
        ...baseTheme.colors,
        ...overrides.colors,
      },
      typography: {
        ...baseTheme.typography,
        ...overrides.typography,
      },
      spacing: {
        ...baseTheme.spacing,
        ...overrides.spacing,
      },
      semanticSpacing: {
        ...baseTheme.semanticSpacing,
        ...overrides.semanticSpacing,
      },
      shadows: {
        ...baseTheme.shadows,
        ...overrides.shadows,
      },
      borderRadius: {
        ...baseTheme.borderRadius,
        ...overrides.borderRadius,
      },
      zIndex: {
        ...baseTheme.zIndex,
        ...overrides.zIndex,
      },
      transitions: {
        ...baseTheme.transitions,
        ...overrides.transitions,
      },
      breakpoints: {
        ...baseTheme.breakpoints,
        ...overrides.breakpoints,
      },
    };
  },

  /**
   * Generate CSS custom properties from theme
   */
  generateCSSCustomProperties(theme: BaseTheme): Record<string, string> {
    const properties: Record<string, string> = {};

    // Colors
    Object.entries(theme.colors).forEach(([category, colors]) => {
      if (typeof colors === 'object') {
        Object.entries(colors).forEach(([key, value]) => {
          properties[`--color-${category}-${key}`] = value;
        });
      }
    });

    // Spacing
    Object.entries(theme.spacing).forEach(([key, value]) => {
      properties[`--spacing-${key}`] = value;
    });

    // Shadows
    Object.entries(theme.shadows).forEach(([key, value]) => {
      properties[`--shadow-${key}`] = value;
    });

    // Border Radius
    Object.entries(theme.borderRadius).forEach(([key, value]) => {
      properties[`--radius-${key}`] = value;
    });

    // Z-Index
    Object.entries(theme.zIndex).forEach(([key, value]) => {
      properties[`--z-${key}`] = value.toString();
    });

    // Transitions
    Object.entries(theme.transitions.duration).forEach(([key, value]) => {
      properties[`--duration-${key}`] = value;
    });

    Object.entries(theme.transitions.easing).forEach(([key, value]) => {
      properties[`--easing-${key}`] = value;
    });

    // Breakpoints
    Object.entries(theme.breakpoints).forEach(([key, value]) => {
      properties[`--breakpoint-${key}`] = value;
    });

    return properties;
  },

  /**
   * Apply theme to document
   */
  applyTheme(theme: BaseTheme): void {
    const properties = this.generateCSSCustomProperties(theme);
    const root = document.documentElement;

    Object.entries(properties).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });

    // Set theme mode class
    root.classList.remove('light', 'dark');
    root.classList.add(theme.mode);

    // Set theme name attribute
    root.setAttribute('data-theme', theme.name);
  },

  /**
   * Get current theme from document
   */
  getCurrentTheme(): 'light' | 'dark' {
    const root = document.documentElement;
    return root.classList.contains('dark') ? 'dark' : 'light';
  },

  /**
   * Toggle between light and dark themes
   */
  toggleTheme(): void {
    const current = this.getCurrentTheme();
    const newTheme = current === 'light' ? darkTheme : lightTheme;
    this.applyTheme(newTheme);
  },

  /**
   * Validate theme structure
   */
  validateTheme(theme: any): theme is BaseTheme {
    const requiredKeys = [
      'name', 'mode', 'colors', 'typography', 'spacing',
      'semanticSpacing', 'shadows', 'borderRadius', 'zIndex',
      'transitions', 'breakpoints'
    ];

    return requiredKeys.every(key => key in theme);
  },
};
