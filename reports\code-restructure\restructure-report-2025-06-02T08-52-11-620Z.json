{"restructure_info": {"timestamp": "2025-06-02T08-52-11-620Z", "created_at": "2025-06-02T08:52:11.624Z", "phase": "Phase 2 - Code Restructuring"}, "new_structure": {"core": {"description": "الملفات الأساسية والمشتركة", "subdirs": {"types": "تعريفات الأنواع", "constants": "الثوابت والإعدادات", "utils": "الأدوات المساعدة", "hooks": "React Hooks المخصصة", "contexts": "React Contexts"}}, "features": {"description": "الميزات مقسمة حسب الوظيفة", "subdirs": {"auth": "نظام المصادقة والأمان", "dashboard": "لوحات التحكم", "buses": "إدارة الحافلات", "routes": "إدارة المسارات", "students": "إدارة الطلاب", "drivers": "إدارة السائقين", "attendance": "نظام الحضور", "notifications": "نظام الإشعارات", "reports": "نظام التقارير", "tracking": "نظام التتبع", "maintenance": "نظام الصيانة"}}, "shared": {"description": "المكونات والخدمات المشتركة", "subdirs": {"components": "مكونات UI مشتركة", "services": "الخدمات المشتركة", "layouts": "تخطيطات الصفحات", "guards": "حراس الصفحات والصلاحيات"}}, "assets": {"description": "الملفات الثابتة", "subdirs": {"images": "الصور", "icons": "الأيقونات", "styles": "ملفات CSS", "locales": "ملفات الترجمة"}}}, "benefits": ["تنظيم أفضل للكود حسب الوظيفة", "سهولة العثور على الملفات", "تقليل التبعيات المتداخلة", "تحسين قابلية الصيانة"]}