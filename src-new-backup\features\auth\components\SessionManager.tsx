/**
 * Session Manager Component
 * مكون إدارة الجلسات - المرحلة الأولى
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  MapPin, 
  Clock, 
  LogOut, 
  AlertTriangle,
  RefreshCw,
  Trash2
} from 'lucide-react';
import { Button } from '../ui/Button';
import { EnhancedSecurityService, ActiveSession } from '../../services/security/EnhancedSecurityService';
import { useAuth } from '../../contexts/AuthContext';

interface SessionManagerProps {
  onSessionTerminated?: () => void;
}

export const SessionManager: React.FC<SessionManagerProps> = ({
  onSessionTerminated
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [sessions, setSessions] = useState<ActiveSession[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [terminatingSession, setTerminatingSession] = useState<string | null>(null);

  const securityService = EnhancedSecurityService.getInstance();

  useEffect(() => {
    if (user) {
      loadActiveSessions();
    }
  }, [user]);

  const loadActiveSessions = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError('');
      const activeSessions = await securityService.getUserActiveSessions(user.id);
      setSessions(activeSessions);
    } catch (error) {
      console.error('Error loading sessions:', error);
      setError('فشل في تحميل الجلسات النشطة');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTerminateSession = async (sessionId: string) => {
    if (!user) return;

    try {
      setTerminatingSession(sessionId);
      setError('');
      
      await securityService.terminateSession(sessionId, user.id);
      setSuccess('تم إنهاء الجلسة بنجاح');
      
      // إعادة تحميل الجلسات
      await loadActiveSessions();
      
      onSessionTerminated?.();
      
      // إخفاء رسالة النجاح بعد 3 ثوان
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      console.error('Error terminating session:', error);
      setError('فشل في إنهاء الجلسة');
    } finally {
      setTerminatingSession(null);
    }
  };

  const handleTerminateAllSessions = async () => {
    if (!user) return;

    const confirmed = window.confirm(
      'هل أنت متأكد من إنهاء جميع الجلسات؟ سيتم تسجيل خروجك من جميع الأجهزة.'
    );

    if (!confirmed) return;

    try {
      setIsLoading(true);
      setError('');
      
      await securityService.terminateAllUserSessions(user.id);
      setSuccess('تم إنهاء جميع الجلسات بنجاح');
      
      // إعادة تحميل الجلسات
      await loadActiveSessions();
      
      onSessionTerminated?.();
      
      // إخفاء رسالة النجاح بعد 3 ثوان
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      console.error('Error terminating all sessions:', error);
      setError('فشل في إنهاء جميع الجلسات');
    } finally {
      setIsLoading(false);
    }
  };

  const getDeviceIcon = (userAgent?: string) => {
    if (!userAgent) return <Monitor className="w-5 h-5" />;
    
    const ua = userAgent.toLowerCase();
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      return <Smartphone className="w-5 h-5" />;
    } else if (ua.includes('tablet') || ua.includes('ipad')) {
      return <Tablet className="w-5 h-5" />;
    }
    return <Monitor className="w-5 h-5" />;
  };

  const getDeviceInfo = (session: ActiveSession) => {
    const deviceInfo = session.device_info || {};
    const userAgent = session.user_agent || '';
    
    // استخراج معلومات المتصفح والنظام
    let browser = 'متصفح غير معروف';
    let os = 'نظام غير معروف';
    
    if (userAgent) {
      // استخراج المتصفح
      if (userAgent.includes('Chrome')) browser = 'Chrome';
      else if (userAgent.includes('Firefox')) browser = 'Firefox';
      else if (userAgent.includes('Safari')) browser = 'Safari';
      else if (userAgent.includes('Edge')) browser = 'Edge';
      
      // استخراج نظام التشغيل
      if (userAgent.includes('Windows')) os = 'Windows';
      else if (userAgent.includes('Mac')) os = 'macOS';
      else if (userAgent.includes('Linux')) os = 'Linux';
      else if (userAgent.includes('Android')) os = 'Android';
      else if (userAgent.includes('iOS')) os = 'iOS';
    }
    
    return { browser, os, ...deviceInfo };
  };

  const formatLastActivity = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'الآن';
    if (diffMins < 60) return `منذ ${diffMins} دقيقة`;
    if (diffHours < 24) return `منذ ${diffHours} ساعة`;
    return `منذ ${diffDays} يوم`;
  };

  const isCurrentSession = (session: ActiveSession) => {
    // يمكن تحسين هذا بمقارنة session token مع الجلسة الحالية
    return session.last_activity === sessions[0]?.last_activity;
  };

  if (isLoading && sessions.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin text-blue-500 mr-2" />
        <span className="text-gray-600 dark:text-gray-400">جاري تحميل الجلسات...</span>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          إدارة الجلسات النشطة
        </h2>
        <Button
          onClick={loadActiveSessions}
          variant="outline"
          size="sm"
          disabled={isLoading}
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          تحديث
        </Button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
        </div>
      )}

      {success && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <p className="text-sm text-green-800 dark:text-green-200">{success}</p>
        </div>
      )}

      {sessions.length === 0 ? (
        <div className="text-center py-8">
          <Monitor className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">لا توجد جلسات نشطة</p>
        </div>
      ) : (
        <>
          <div className="space-y-4 mb-6">
            {sessions.map((session) => {
              const deviceInfo = getDeviceInfo(session);
              const isCurrent = isCurrentSession(session);
              
              return (
                <div
                  key={session.id}
                  className={`border rounded-lg p-4 ${
                    isCurrent 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                      : 'border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 space-x-reverse flex-1">
                      <div className="text-gray-500 dark:text-gray-400 mt-1">
                        {getDeviceIcon(session.user_agent)}
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 space-x-reverse mb-2">
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {deviceInfo.browser} على {deviceInfo.os}
                          </h3>
                          {isCurrent && (
                            <span className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">
                              الجلسة الحالية
                            </span>
                          )}
                        </div>
                        
                        <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                          {session.ip_address && (
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <MapPin className="w-4 h-4" />
                              <span>عنوان IP: {session.ip_address}</span>
                            </div>
                          )}
                          
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <Clock className="w-4 h-4" />
                            <span>آخر نشاط: {formatLastActivity(session.last_activity)}</span>
                          </div>
                          
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <Clock className="w-4 h-4" />
                            <span>
                              تنتهي في: {new Date(session.expires_at).toLocaleString('ar-SA')}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <Button
                      onClick={() => handleTerminateSession(session.id)}
                      variant="outline"
                      size="sm"
                      disabled={terminatingSession === session.id}
                      className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
                    >
                      {terminatingSession === session.id ? (
                        <RefreshCw className="w-4 h-4 animate-spin" />
                      ) : (
                        <>
                          <LogOut className="w-4 h-4 mr-1" />
                          إنهاء
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>

          {sessions.length > 1 && (
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                  <AlertTriangle className="w-4 h-4" />
                  <span>لديك {sessions.length} جلسة نشطة</span>
                </div>
                
                <Button
                  onClick={handleTerminateAllSessions}
                  variant="outline"
                  disabled={isLoading}
                  className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
                >
                  {isLoading ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Trash2 className="w-4 h-4 mr-2" />
                  )}
                  إنهاء جميع الجلسات
                </Button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};
