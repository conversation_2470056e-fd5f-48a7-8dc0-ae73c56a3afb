import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  MapPin,
  AlertTriangle,
  Shield,
  Settings,
  Bell,
  Eye,
  CheckCircle,
  Trash2,
  Check,
  X,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";
import * as api from "../../lib/api";

interface GeofenceAlert {
  id: string;
  bus_id: string;
  alert_type: string;
  location: any;
  stop_id?: string;
  timestamp: string;
  metadata?: any;
  bus?: {
    id: string;
    plate_number: string;
  };
  stop?: {
    id: string;
    name: string;
  };
}

export const GeofenceAlerts: React.FC = () => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const { buses } = useDatabase();
  const [alerts, setAlerts] = useState<GeofenceAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [monitoringEnabled, setMonitoringEnabled] = useState(true);
  const [alertRadius, setAlertRadius] = useState(500); // meters
  const [alertTypes, setAlertTypes] = useState("all");
  const [checkInterval, setCheckInterval] = useState(30); // seconds
  const [selectedAlerts, setSelectedAlerts] = useState<string[]>([]);
  const [bulkActionLoading, setBulkActionLoading] = useState(false);

  const fetchAlerts = async () => {
    if (!tenant?.id) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("geofence_alerts")
        .select(
          `
          *,
          bus:buses!bus_id(
            id,
            plate_number
          ),
          stop:route_stops!stop_id(
            id,
            name
          )
        `,
        )
        .eq("tenant_id", tenant.id)
        .order("timestamp", { ascending: false })
        .limit(50);

      if (error) throw error;
      setAlerts(data || []);
    } catch (error) {
      console.error("Error fetching geofence alerts:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAlerts();
  }, [tenant?.id]);

  // Simulate real-time monitoring
  useEffect(() => {
    if (!monitoringEnabled || !tenant?.id) return;

    const interval = setInterval(async () => {
      // In a real implementation, this would check current bus locations
      // against their assigned routes and generate alerts
      try {
        for (const bus of buses) {
          if (!bus.last_location) continue;

          // Parse location from PostGIS format
          const locationMatch = bus.last_location
            .toString()
            .match(/POINT\(([^)]+)\)/);
          if (!locationMatch) continue;

          const [lng, lat] = locationMatch[1].split(" ").map(Number);

          // Check for geofence violations
          const violation = await api.checkGeofenceViolations(
            bus.id,
            { lat, lng },
            tenant.id,
          );

          if (violation.violation) {
            // Create immediate notification for violation
            try {
              await supabase.from("notifications").insert({
                tenant_id: tenant.id,
                title: "Geofence Violation",
                message: violation.message,
                type: "geofence_violation",
                priority: "high",
                metadata: {
                  type: "geofence_violation",
                  busId: bus.id,
                  plateNumber: bus.plate_number,
                  location: { lat, lng },
                  distance: violation.distance,
                  nearestStop: violation.nearestStop,
                },
              });
            } catch (notificationError) {
              console.warn(
                "Failed to create geofence violation notification:",
                notificationError,
              );
            }

            // Refresh alerts to show new violation
            fetchAlerts();
          }
        }
      } catch (error) {
        console.error("Error checking geofence violations:", error);
      }
    }, checkInterval * 1000); // Check based on configured interval

    return () => clearInterval(interval);
  }, [monitoringEnabled, tenant?.id, buses]);

  const handleSelectAlert = (alertId: string) => {
    setSelectedAlerts((prev) =>
      prev.includes(alertId)
        ? prev.filter((id) => id !== alertId)
        : [...prev, alertId],
    );
  };

  const handleSelectAll = () => {
    if (selectedAlerts.length === alerts.length) {
      setSelectedAlerts([]);
    } else {
      setSelectedAlerts(alerts.map((alert) => alert.id));
    }
  };

  const handleBulkAcknowledge = async () => {
    if (selectedAlerts.length === 0) return;

    setBulkActionLoading(true);
    try {
      // Update alerts as acknowledged in database
      const { error } = await supabase
        .from("geofence_alerts")
        .update({
          acknowledged: true,
          acknowledged_at: new Date().toISOString(),
          acknowledged_by: user?.id,
        })
        .in("id", selectedAlerts);

      if (error) throw error;

      // Refresh alerts and clear selection
      await fetchAlerts();
      setSelectedAlerts([]);
    } catch (error) {
      console.error("Error acknowledging alerts:", error);
      alert("Failed to acknowledge alerts. Please try again.");
    } finally {
      setBulkActionLoading(false);
    }
  };

  const handleBulkDismiss = async () => {
    if (selectedAlerts.length === 0) return;

    if (
      !confirm(
        `Are you sure you want to dismiss ${selectedAlerts.length} alert(s)?`,
      )
    ) {
      return;
    }

    setBulkActionLoading(true);
    try {
      // Delete selected alerts from database
      const { error } = await supabase
        .from("geofence_alerts")
        .delete()
        .in("id", selectedAlerts);

      if (error) throw error;

      // Refresh alerts and clear selection
      await fetchAlerts();
      setSelectedAlerts([]);
    } catch (error) {
      console.error("Error dismissing alerts:", error);
      alert("Failed to dismiss alerts. Please try again.");
    } finally {
      setBulkActionLoading(false);
    }
  };

  const getAlertIcon = (alertType: string) => {
    switch (alertType) {
      case "route_deviation":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case "unauthorized_stop":
        return <MapPin className="h-4 w-4 text-orange-500" />;
      case "zone_entry":
        return <Shield className="h-4 w-4 text-blue-500" />;
      case "zone_exit":
        return <Shield className="h-4 w-4 text-green-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getAlertColor = (alertType: string) => {
    switch (alertType) {
      case "route_deviation":
        return "bg-red-100 text-red-800 border-red-200";
      case "unauthorized_stop":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "zone_entry":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "zone_exit":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const formatAlertMessage = (alert: GeofenceAlert) => {
    switch (alert.alert_type) {
      case "route_deviation":
        return `Bus ${alert.bus?.plate_number} has deviated from its assigned route${alert.metadata?.distance_from_nearest_stop ? ` (${alert.metadata.distance_from_nearest_stop}m from nearest stop)` : ""}`;
      case "unauthorized_stop":
        return `Bus ${alert.bus?.plate_number} has stopped in an unauthorized area`;
      case "zone_entry":
        return `Bus ${alert.bus?.plate_number} has entered ${alert.stop?.name || "a monitored zone"}`;
      case "zone_exit":
        return `Bus ${alert.bus?.plate_number} has left ${alert.stop?.name || "a monitored zone"}`;
      default:
        return `Geofence alert for bus ${alert.bus?.plate_number}`;
    }
  };

  const recentAlerts = alerts.filter(
    (alert) =>
      new Date(alert.timestamp) > new Date(Date.now() - 24 * 60 * 60 * 1000),
  );

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Shield className="mr-2 h-5 w-5 text-primary-500" />
              Geofence Monitoring
            </h3>
            <div className="flex items-center gap-4 mt-2">
              <div className="flex items-center gap-2">
                <div
                  className={`w-2 h-2 rounded-full ${monitoringEnabled ? "bg-green-500" : "bg-gray-400"}`}
                ></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {monitoringEnabled ? "Active" : "Inactive"}
                </span>
              </div>
              {recentAlerts.length > 0 && (
                <div className="flex items-center gap-2">
                  <Bell className="h-4 w-4 text-orange-500" />
                  <span className="text-sm text-orange-600">
                    {recentAlerts.length} alert
                    {recentAlerts.length !== 1 ? "s" : ""} in last 24h
                  </span>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center gap-3">
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={monitoringEnabled}
                onChange={(e) => setMonitoringEnabled(e.target.checked)}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="text-gray-700 dark:text-gray-300">
                Enable Monitoring
              </span>
            </label>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Settings size={16} />
              Settings
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Monitoring Settings */}
        <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
            Monitoring Configuration
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Alert Radius (meters)
              </label>
              <input
                type="number"
                value={alertRadius}
                onChange={(e) =>
                  setAlertRadius(parseInt(e.target.value) || 500)
                }
                min="100"
                max="2000"
                step="50"
                className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Check Interval
              </label>
              <select
                value={checkInterval}
                onChange={(e) => setCheckInterval(parseInt(e.target.value))}
                className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-600 dark:text-white"
              >
                <option value={15}>15 seconds</option>
                <option value={30}>30 seconds</option>
                <option value={60}>1 minute</option>
                <option value={120}>2 minutes</option>
                <option value={300}>5 minutes</option>
              </select>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Alert Types
              </label>
              <select
                value={alertTypes}
                onChange={(e) => setAlertTypes(e.target.value)}
                className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-600 dark:text-white"
              >
                <option value="all">All alerts</option>
                <option value="violations">Violations only</option>
                <option value="critical">Critical only</option>
              </select>
            </div>
          </div>
        </div>

        {/* Alerts List */}
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
        ) : alerts.length === 0 ? (
          <div className="text-center py-8">
            <Shield size={48} className="mx-auto mb-4 text-green-400" />
            <p className="text-gray-500 dark:text-gray-400">
              {monitoringEnabled
                ? "No geofence alerts detected. All buses are within authorized areas."
                : "Geofence monitoring is disabled."}
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="flex items-center justify-between mb-4">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                Recent Alerts ({alerts.length})
                {selectedAlerts.length > 0 && (
                  <span className="ml-2 text-primary-600">
                    ({selectedAlerts.length} selected)
                  </span>
                )}
              </div>

              {alerts.length > 0 && (
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleSelectAll}
                    className="text-xs"
                  >
                    {selectedAlerts.length === alerts.length
                      ? "Deselect All"
                      : "Select All"}
                  </Button>

                  {selectedAlerts.length > 0 && (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleBulkAcknowledge}
                        disabled={bulkActionLoading}
                        className="text-xs flex items-center gap-1"
                      >
                        <Check size={12} />
                        Acknowledge ({selectedAlerts.length})
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleBulkDismiss}
                        disabled={bulkActionLoading}
                        className="text-xs flex items-center gap-1 text-red-600 border-red-300 hover:bg-red-50"
                      >
                        <Trash2 size={12} />
                        Dismiss ({selectedAlerts.length})
                      </Button>
                    </>
                  )}
                </div>
              )}
            </div>

            {alerts.map((alert) => {
              const isRecent =
                new Date(alert.timestamp) >
                new Date(Date.now() - 60 * 60 * 1000); // Last hour

              return (
                <div
                  key={alert.id}
                  className={`p-4 border rounded-lg ${getAlertColor(alert.alert_type)} ${
                    isRecent ? "ring-2 ring-primary-200" : ""
                  } ${selectedAlerts.includes(alert.id) ? "ring-2 ring-blue-300 bg-blue-50" : ""}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <input
                        type="checkbox"
                        checked={selectedAlerts.includes(alert.id)}
                        onChange={() => handleSelectAlert(alert.id)}
                        className="mt-1 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      {getAlertIcon(alert.alert_type)}
                      <div className="flex-1">
                        <div className="text-sm font-medium mb-1">
                          {formatAlertMessage(alert)}
                        </div>
                        <div className="text-xs opacity-75">
                          {new Date(alert.timestamp).toLocaleString()}
                          {alert.metadata?.nearest_stop_name && (
                            <span className="ml-2">
                              • Nearest stop: {alert.metadata.nearest_stop_name}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {isRecent && (
                        <span className="px-2 py-1 bg-primary-100 text-primary-800 text-xs rounded-full">
                          New
                        </span>
                      )}
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-xs"
                        onClick={async () => {
                          try {
                            await supabase
                              .from("geofence_alerts")
                              .update({
                                acknowledged: true,
                                acknowledged_at: new Date().toISOString(),
                                acknowledged_by: user?.id,
                              })
                              .eq("id", alert.id);
                            fetchAlerts();
                          } catch (error) {
                            console.error("Error acknowledging alert:", error);
                          }
                        }}
                      >
                        <CheckCircle size={12} className="mr-1" />
                        Ack
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-xs text-red-600 border-red-300 hover:bg-red-50"
                        onClick={async () => {
                          if (
                            confirm(
                              "Are you sure you want to dismiss this alert?",
                            )
                          ) {
                            try {
                              await supabase
                                .from("geofence_alerts")
                                .delete()
                                .eq("id", alert.id);
                              fetchAlerts();
                            } catch (error) {
                              console.error("Error dismissing alert:", error);
                            }
                          }
                        }}
                      >
                        <X size={12} />
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default GeofenceAlerts;
