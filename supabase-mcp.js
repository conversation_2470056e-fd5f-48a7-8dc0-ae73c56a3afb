// استيراد المكتبات اللازمة
import { spawn } from 'child_process';
import { createInterface } from 'readline';

// تكوين Supabase
const supabaseUrl = 'https://pcavtwqvgnkgybzfqeuz.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBjYXZ0d3F2Z25rZ3liemZxZXV6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyNTE5MzEsImV4cCI6MjA2MjgyNzkzMX0.vK0rK-IW5lDgXBTeFXpef4PqC6H83FF62bI7LoOs5HA';
const schema = 'public';

// Use Node.js executable to run npx
const mcpServer = spawn('node', [
  process.execPath.replace('node.exe', 'npx.cmd'),  // For Windows
  '-y',
  '@supabase/mcp-server-postgrest@latest',
  '--apiUrl',
  `${supabaseUrl}/rest/v1`,
  '--api<PERSON>ey',
  supabaseKey,
  '--schema',
  schema
]);

// معالجة مخرجات الخادم
const rl = createInterface({
  input: mcpServer.stdout,
  terminal: false
});

rl.on('line', (line) => {
  try {
    const data = JSON.parse(line);
    console.log('MCP Server Response:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.log('MCP Server Output:', line);
  }
});

// معالجة أخطاء الخادم
mcpServer.stderr.on('data', (data) => {
  console.error('MCP Server Error:', data.toString());
});

// إرسال رسالة تهيئة إلى الخادم
const initializeMessage = {
  jsonrpc: '2.0',
  id: 1,
  method: 'initialize',
  params: {
    client: {
      name: 'Supabase MCP Client',
      version: '1.0.0'
    },
    protocol: {
      version: '0.1.0'
    }
  }
};

mcpServer.stdin.write(JSON.stringify(initializeMessage) + '\n');

// إرسال طلب لعرض الأدوات المتاحة
setTimeout(() => {
  const listToolsMessage = {
    jsonrpc: '2.0',
    id: 2,
    method: 'listTools'
  };
  mcpServer.stdin.write(JSON.stringify(listToolsMessage) + '\n');
}, 1000);

// مثال على استخدام أداة postgrestRequest
setTimeout(() => {
  const callToolMessage = {
    jsonrpc: '2.0',
    id: 3,
    method: 'callTool',
    params: {
      name: 'postgrestRequest',
      arguments: {
        method: 'GET',
        path: '/your_table_name'
      }
    }
  };
  mcpServer.stdin.write(JSON.stringify(callToolMessage) + '\n');
}, 2000);

// تنظيف الموارد بعد 10 ثوانٍ
setTimeout(() => {
  console.log('Cleaning up...');
  mcpServer.kill();
  process.exit(0);
}, 10000);