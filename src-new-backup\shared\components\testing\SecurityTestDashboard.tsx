import React from 'react';

interface SecurityTestDashboardProps {
  title?: string;
}

export const SecurityTestDashboard: React.FC<SecurityTestDashboardProps> = ({ 
  title = 'Security Test Dashboard' 
}) => {
  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <h2 className="text-2xl font-bold mb-4">{title}</h2>
      <div className="space-y-4">
        <div className="p-4 bg-green-50 border border-green-200 rounded">
          <h3 className="font-semibold text-green-800">Security Status</h3>
          <p className="text-green-600">All security tests passed</p>
        </div>
        <div className="p-4 bg-blue-50 border border-blue-200 rounded">
          <h3 className="font-semibold text-blue-800">Test Results</h3>
          <p className="text-blue-600">Security dashboard is working correctly</p>
        </div>
      </div>
    </div>
  );
};
