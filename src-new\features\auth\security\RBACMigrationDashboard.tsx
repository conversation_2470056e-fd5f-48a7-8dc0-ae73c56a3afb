/**
 * RBAC Migration Dashboard
 * Tool for monitoring and managing the migration from hardcoded role checks to centralized RBAC
 */

import React, { useState, useEffect } from "react";
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  Users,
  Lock,
  Activity,
  TrendingUp,
  Search,
} from "lucide-react";
import { useAuth } from "../../contexts/AuthContext";
import { usePermissions } from "../../hooks/usePermissions";
import { useRBACEnhancedSecurity } from "../../hooks/useRBACEnhancedSecurity";
import { Permission, UserRole } from "../../lib/rbac";
import {
  MIGRATION_CHECKLIST,
  scanForLegacyPatterns,
} from "../../lib/rbacMigrationHelper";
import { Button } from "../ui/Button";
import { PermissionGuard } from "../auth/PermissionGuard";

interface MigrationStatus {
  phase: string;
  completed: number;
  total: number;
  issues: string[];
}

interface SecurityMetrics {
  totalPermissions: number;
  activeUsers: number;
  securityEvents: number;
  complianceScore: number;
}

export const RBACMigrationDashboard: React.FC = () => {
  const { user } = useAuth();
  const { hasPermission } = usePermissions();
  const { logSecurityEvent, calculateRiskScore } = useRBACEnhancedSecurity();

  const [migrationStatus, setMigrationStatus] = useState<MigrationStatus[]>([]);
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetrics>({
    totalPermissions: 0,
    activeUsers: 0,
    securityEvents: 0,
    complianceScore: 0,
  });
  const [legacyPatterns, setLegacyPatterns] = useState<any[]>([]);
  const [isScanning, setIsScanning] = useState(false);

  useEffect(() => {
    loadMigrationStatus();
    loadSecurityMetrics();
  }, []);

  const loadMigrationStatus = () => {
    // Simulate migration status - in real app, this would come from API
    const status: MigrationStatus[] = [
      {
        phase: "Phase 1: Remove Hardcoded Checks",
        completed: 6,
        total: 8,
        issues: [
          "App.tsx: 2 hardcoded role checks remaining",
          "Sidebar.tsx: 1 direct role comparison",
        ],
      },
      {
        phase: "Phase 2: Implement Enhanced Guards",
        completed: 8,
        total: 10,
        issues: [
          "Missing component keys for 2 modals",
          "Rate limiting not configured for reports",
        ],
      },
      {
        phase: "Phase 3: Testing & Validation",
        completed: 5,
        total: 8,
        issues: [
          "Performance tests pending",
          "Audit log validation incomplete",
          "Cross-tenant access tests needed",
        ],
      },
    ];
    setMigrationStatus(status);
  };

  const loadSecurityMetrics = () => {
    // Simulate security metrics - in real app, this would come from API
    setSecurityMetrics({
      totalPermissions: 85,
      activeUsers: 1247,
      securityEvents: 23,
      complianceScore: 78,
    });
  };

  const scanForLegacyCode = async () => {
    setIsScanning(true);

    try {
      // In a real implementation, this would scan actual source files
      // For demo purposes, we'll simulate findings
      const mockFindings = [
        {
          file: "src/App.tsx",
          pattern: 'user?.role === "admin"',
          line: 218,
          suggestion: "Replace with Permission-based check",
        },
        {
          file: "src/App.tsx",
          pattern: "user.role === UserRole.DRIVER",
          line: 344,
          suggestion: "Use role-based routing helper",
        },
        {
          file: "src/pages/dashboard/SchoolsPage.tsx",
          pattern: 'user?.role === "admin"',
          line: 392,
          suggestion: "Replace with hasPermission check",
        },
      ];

      setLegacyPatterns(mockFindings);

      // Log the scan event
      logSecurityEvent("legacy_code_scan", {
        patternsFound: mockFindings.length,
        scanTimestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Error scanning for legacy patterns:", error);
    } finally {
      setIsScanning(false);
    }
  };

  const getPhaseProgress = (completed: number, total: number) => {
    return Math.round((completed / total) * 100);
  };

  const getComplianceColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <PermissionGuard permission={Permission.SYSTEM_ADMIN}>
      <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            RBAC Migration Dashboard
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor the migration from hardcoded role checks to centralized RBAC
            system
          </p>
        </div>

        {/* Security Metrics Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  Total Permissions
                </p>
                <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                  {securityMetrics.totalPermissions}
                </p>
              </div>
              <Shield className="h-8 w-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-600 dark:text-green-400">
                  Active Users
                </p>
                <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                  {securityMetrics.activeUsers}
                </p>
              </div>
              <Users className="h-8 w-8 text-green-500" />
            </div>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-yellow-600 dark:text-yellow-400">
                  Security Events
                </p>
                <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                  {securityMetrics.securityEvents}
                </p>
              </div>
              <Activity className="h-8 w-8 text-yellow-500" />
            </div>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-600 dark:text-purple-400">
                  Compliance Score
                </p>
                <p
                  className={`text-2xl font-bold ${getComplianceColor(securityMetrics.complianceScore)}`}
                >
                  {securityMetrics.complianceScore}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </div>
        </div>

        {/* Migration Progress */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Migration Progress
          </h3>
          <div className="space-y-4">
            {migrationStatus.map((phase, index) => {
              const progress = getPhaseProgress(phase.completed, phase.total);
              return (
                <div
                  key={index}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {phase.phase}
                    </h4>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {phase.completed}/{phase.total} ({progress}%)
                    </span>
                  </div>

                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-3">
                    <div
                      className={`h-2 rounded-full ${
                        progress === 100
                          ? "bg-green-500"
                          : progress >= 75
                            ? "bg-blue-500"
                            : progress >= 50
                              ? "bg-yellow-500"
                              : "bg-red-500"
                      }`}
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>

                  {phase.issues.length > 0 && (
                    <div className="space-y-1">
                      {phase.issues.map((issue, issueIndex) => (
                        <div
                          key={issueIndex}
                          className="flex items-center text-sm text-amber-600 dark:text-amber-400"
                        >
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          {issue}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Legacy Code Scanner */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Legacy Code Scanner
            </h3>
            <Button
              onClick={scanForLegacyCode}
              disabled={isScanning}
              className="flex items-center gap-2"
            >
              <Search className="h-4 w-4" />
              {isScanning ? "Scanning..." : "Scan for Legacy Patterns"}
            </Button>
          </div>

          {legacyPatterns.length > 0 && (
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <h4 className="font-medium text-gray-900 dark:text-white">
                  Found {legacyPatterns.length} legacy patterns
                </h4>
              </div>
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {legacyPatterns.map((pattern, index) => (
                  <div key={index} className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="font-medium text-gray-900 dark:text-white">
                          {pattern.file}:{pattern.line}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400 font-mono bg-gray-100 dark:bg-gray-700 p-2 rounded mt-1">
                          {pattern.pattern}
                        </p>
                        <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                          💡 {pattern.suggestion}
                        </p>
                      </div>
                      <AlertTriangle className="h-5 w-5 text-amber-500 ml-4" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Migration Checklist */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Migration Checklist
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Object.entries(MIGRATION_CHECKLIST).map(([phaseKey, phase]) => (
              <div
                key={phaseKey}
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
              >
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                  {phase.title}
                </h4>
                <div className="space-y-2">
                  {phase.tasks.map((task, index) => (
                    <div key={index} className="flex items-start text-sm">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-600 dark:text-gray-400">
                        {task}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </PermissionGuard>
  );
};

export default RBACMigrationDashboard;
