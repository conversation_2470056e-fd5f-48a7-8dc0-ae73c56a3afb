/**
 * خدمة حماية من هجمات Brute-force
 * Brute-force Protection Service
 * 
 * المرحلة 1: تأمين الدخول والسياسات الأمنية الأساسية
 */

import { supabase } from '../../lib/supabase';

export interface LoginAttempt {
  id?: string;
  email: string;
  ip_address: string;
  user_agent: string;
  success: boolean;
  attempted_at: string;
  blocked_until?: string;
}

export interface BruteForceConfig {
  maxAttempts: number;
  timeWindowMinutes: number;
  lockoutDurationMinutes: number;
  progressiveLockout: boolean;
  ipBasedBlocking: boolean;
}

export class BruteForceProtectionService {
  private static instance: BruteForceProtectionService;
  
  private readonly DEFAULT_CONFIG: BruteForceConfig = {
    maxAttempts: 5,
    timeWindowMinutes: 15,
    lockoutDurationMinutes: 30,
    progressiveLockout: true,
    ipBasedBlocking: true
  };

  private constructor() {}

  static getInstance(): BruteForceProtectionService {
    if (!BruteForceProtectionService.instance) {
      BruteForceProtectionService.instance = new BruteForceProtectionService();
    }
    return BruteForceProtectionService.instance;
  }

  /**
   * فحص ما إذا كان البريد الإلكتروني أو IP محظور
   */
  async isBlocked(email: string, ipAddress: string): Promise<{
    isBlocked: boolean;
    reason?: string;
    blockedUntil?: Date;
    attemptsRemaining?: number;
  }> {
    try {
      // فحص الحظر بناءً على البريد الإلكتروني
      const emailBlock = await this.checkEmailBlock(email);
      if (emailBlock.isBlocked) {
        return emailBlock;
      }

      // فحص الحظر بناءً على IP
      if (this.DEFAULT_CONFIG.ipBasedBlocking) {
        const ipBlock = await this.checkIPBlock(ipAddress);
        if (ipBlock.isBlocked) {
          return ipBlock;
        }
      }

      // فحص عدد المحاولات الحالية
      const attempts = await this.getRecentAttempts(email, ipAddress);
      const attemptsRemaining = this.DEFAULT_CONFIG.maxAttempts - attempts.length;

      return {
        isBlocked: false,
        attemptsRemaining: Math.max(0, attemptsRemaining)
      };

    } catch (error) {
      console.error('Error checking brute force protection:', error);
      // في حالة الخطأ، نسمح بالمحاولة لتجنب منع المستخدمين الشرعيين
      return { isBlocked: false };
    }
  }

  /**
   * تسجيل محاولة تسجيل دخول
   */
  async recordAttempt(
    email: string, 
    ipAddress: string, 
    userAgent: string, 
    success: boolean
  ): Promise<void> {
    try {
      const attempt: LoginAttempt = {
        email: email.toLowerCase(),
        ip_address: ipAddress,
        user_agent: userAgent,
        success,
        attempted_at: new Date().toISOString()
      };

      // حفظ المحاولة في قاعدة البيانات
      const { error } = await supabase
        .from('login_attempts')
        .insert([attempt]);

      if (error) {
        console.error('Error recording login attempt:', error);
      }

      // إذا فشلت المحاولة، فحص ما إذا كان يجب حظر المستخدم
      if (!success) {
        await this.checkAndApplyBlock(email, ipAddress);
      }

    } catch (error) {
      console.error('Error in recordAttempt:', error);
    }
  }

  /**
   * فحص الحظر بناءً على البريد الإلكتروني
   */
  private async checkEmailBlock(email: string): Promise<{
    isBlocked: boolean;
    reason?: string;
    blockedUntil?: Date;
    attemptsRemaining?: number;
  }> {
    try {
      // فحص إذا كان هناك حظر نشط
      const { data: activeBlock } = await supabase
        .from('user_blocks')
        .select('*')
        .eq('email', email.toLowerCase())
        .eq('is_active', true)
        .gt('blocked_until', new Date().toISOString())
        .single();

      if (activeBlock) {
        return {
          isBlocked: true,
          reason: 'تم حظر هذا الحساب مؤقتاً بسبب محاولات دخول متكررة فاشلة',
          blockedUntil: new Date(activeBlock.blocked_until)
        };
      }

      return { isBlocked: false };

    } catch (error) {
      console.error('Error checking email block:', error);
      return { isBlocked: false };
    }
  }

  /**
   * فحص الحظر بناءً على IP
   */
  private async checkIPBlock(ipAddress: string): Promise<{
    isBlocked: boolean;
    reason?: string;
    blockedUntil?: Date;
  }> {
    try {
      const { data: activeBlock } = await supabase
        .from('ip_blocks')
        .select('*')
        .eq('ip_address', ipAddress)
        .eq('is_active', true)
        .gt('blocked_until', new Date().toISOString())
        .single();

      if (activeBlock) {
        return {
          isBlocked: true,
          reason: 'تم حظر هذا العنوان مؤقتاً بسبب نشاط مشبوه',
          blockedUntil: new Date(activeBlock.blocked_until)
        };
      }

      return { isBlocked: false };

    } catch (error) {
      console.error('Error checking IP block:', error);
      return { isBlocked: false };
    }
  }

  /**
   * الحصول على المحاولات الأخيرة
   */
  private async getRecentAttempts(email: string, ipAddress: string): Promise<LoginAttempt[]> {
    try {
      const timeWindow = new Date();
      timeWindow.setMinutes(timeWindow.getMinutes() - this.DEFAULT_CONFIG.timeWindowMinutes);

      const { data: attempts } = await supabase
        .from('login_attempts')
        .select('*')
        .or(`email.eq.${email.toLowerCase()},ip_address.eq.${ipAddress}`)
        .eq('success', false)
        .gte('attempted_at', timeWindow.toISOString())
        .order('attempted_at', { ascending: false });

      return attempts || [];

    } catch (error) {
      console.error('Error getting recent attempts:', error);
      return [];
    }
  }

  /**
   * فحص وتطبيق الحظر إذا لزم الأمر
   */
  private async checkAndApplyBlock(email: string, ipAddress: string): Promise<void> {
    try {
      const attempts = await this.getRecentAttempts(email, ipAddress);
      
      if (attempts.length >= this.DEFAULT_CONFIG.maxAttempts) {
        // حساب مدة الحظر
        const lockoutDuration = this.calculateLockoutDuration(email);
        const blockedUntil = new Date();
        blockedUntil.setMinutes(blockedUntil.getMinutes() + lockoutDuration);

        // حظر البريد الإلكتروني
        await this.blockEmail(email, blockedUntil, attempts.length);

        // حظر IP إذا كان مفعلاً
        if (this.DEFAULT_CONFIG.ipBasedBlocking) {
          await this.blockIP(ipAddress, blockedUntil, attempts.length);
        }
      }

    } catch (error) {
      console.error('Error in checkAndApplyBlock:', error);
    }
  }

  /**
   * حساب مدة الحظر (مع الحظر التدريجي)
   */
  private async calculateLockoutDuration(email: string): Promise<number> {
    if (!this.DEFAULT_CONFIG.progressiveLockout) {
      return this.DEFAULT_CONFIG.lockoutDurationMinutes;
    }

    try {
      // عد المحاولات السابقة للحظر
      const { data: previousBlocks } = await supabase
        .from('user_blocks')
        .select('id')
        .eq('email', email.toLowerCase())
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()); // آخر 24 ساعة

      const blockCount = previousBlocks?.length || 0;
      
      // زيادة مدة الحظر تدريجياً
      const multiplier = Math.min(Math.pow(2, blockCount), 8); // حد أقصى 8x
      return this.DEFAULT_CONFIG.lockoutDurationMinutes * multiplier;

    } catch (error) {
      console.error('Error calculating lockout duration:', error);
      return this.DEFAULT_CONFIG.lockoutDurationMinutes;
    }
  }

  /**
   * حظر البريد الإلكتروني
   */
  private async blockEmail(email: string, blockedUntil: Date, attemptCount: number): Promise<void> {
    try {
      await supabase
        .from('user_blocks')
        .insert([{
          email: email.toLowerCase(),
          blocked_until: blockedUntil.toISOString(),
          reason: `محاولات دخول فاشلة متكررة (${attemptCount} محاولات)`,
          is_active: true,
          created_at: new Date().toISOString()
        }]);

    } catch (error) {
      console.error('Error blocking email:', error);
    }
  }

  /**
   * حظر IP
   */
  private async blockIP(ipAddress: string, blockedUntil: Date, attemptCount: number): Promise<void> {
    try {
      await supabase
        .from('ip_blocks')
        .insert([{
          ip_address: ipAddress,
          blocked_until: blockedUntil.toISOString(),
          reason: `محاولات دخول فاشلة متكررة من هذا العنوان (${attemptCount} محاولات)`,
          is_active: true,
          created_at: new Date().toISOString()
        }]);

    } catch (error) {
      console.error('Error blocking IP:', error);
    }
  }

  /**
   * إلغاء حظر البريد الإلكتروني يدوياً (للأدمن)
   */
  async unblockEmail(email: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_blocks')
        .update({ is_active: false })
        .eq('email', email.toLowerCase())
        .eq('is_active', true);

      return !error;

    } catch (error) {
      console.error('Error unblocking email:', error);
      return false;
    }
  }

  /**
   * إلغاء حظر IP يدوياً (للأدمن)
   */
  async unblockIP(ipAddress: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('ip_blocks')
        .update({ is_active: false })
        .eq('ip_address', ipAddress)
        .eq('is_active', true);

      return !error;

    } catch (error) {
      console.error('Error unblocking IP:', error);
      return false;
    }
  }

  /**
   * الحصول على إحصائيات الحماية
   */
  async getProtectionStats(): Promise<{
    totalAttempts: number;
    failedAttempts: number;
    blockedEmails: number;
    blockedIPs: number;
    successRate: number;
  }> {
    try {
      const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

      const [totalAttempts, failedAttempts, blockedEmails, blockedIPs] = await Promise.all([
        supabase.from('login_attempts').select('id', { count: 'exact' }).gte('attempted_at', last24Hours),
        supabase.from('login_attempts').select('id', { count: 'exact' }).eq('success', false).gte('attempted_at', last24Hours),
        supabase.from('user_blocks').select('id', { count: 'exact' }).eq('is_active', true),
        supabase.from('ip_blocks').select('id', { count: 'exact' }).eq('is_active', true)
      ]);

      const total = totalAttempts.count || 0;
      const failed = failedAttempts.count || 0;
      const successRate = total > 0 ? ((total - failed) / total) * 100 : 100;

      return {
        totalAttempts: total,
        failedAttempts: failed,
        blockedEmails: blockedEmails.count || 0,
        blockedIPs: blockedIPs.count || 0,
        successRate: Math.round(successRate * 100) / 100
      };

    } catch (error) {
      console.error('Error getting protection stats:', error);
      return {
        totalAttempts: 0,
        failedAttempts: 0,
        blockedEmails: 0,
        blockedIPs: 0,
        successRate: 100
      };
    }
  }
}
