/**
 * Tenant Service
 * Handles tenant/school data management
 * Phase 3: UI/UX Enhancement - School Information Management
 */

import { supabase } from '../lib/supabase';

export interface TenantData {
  id: string;
  name: string;
  logo_url?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export class TenantService {
  /**
   * Update tenant/school information
   */
  static async updateTenant(
    tenantId: string,
    updates: {
      name?: string;
      logo_url?: string;
      address?: string;
      phone?: string;
      email?: string;
      website?: string;
      description?: string;
    }
  ): Promise<TenantData> {
    try {
      console.log('🏫 Updating tenant:', tenantId, updates);

      const updateData = {
        ...updates,
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('tenants')
        .update(updateData)
        .eq('id', tenantId)
        .select()
        .single();

      if (error) {
        console.error('Error updating tenant:', error);
        throw new Error(`Failed to update school information: ${error.message}`);
      }

      console.log('✅ Tenant updated successfully:', data);
      return data;
    } catch (error) {
      console.error('Error in updateTenant:', error);
      throw error;
    }
  }

  /**
   * Get tenant information
   */
  static async getTenant(tenantId: string): Promise<TenantData | null> {
    try {
      const { data, error } = await supabase
        .from('tenants')
        .select('*')
        .eq('id', tenantId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error getting tenant:', error);
        throw new Error(`Failed to get school information: ${error.message}`);
      }

      return data || null;
    } catch (error) {
      console.error('Error in getTenant:', error);
      return null;
    }
  }

  /**
   * Update school name specifically
   */
  static async updateSchoolName(tenantId: string, newName: string): Promise<TenantData> {
    try {
      console.log('🏫 Updating school name:', tenantId, newName);

      if (!newName || newName.trim().length === 0) {
        throw new Error('اسم المدرسة لا يمكن أن يكون فارغاً');
      }

      // First check if the name is actually different
      const currentTenant = await this.getTenant(tenantId);
      if (currentTenant && currentTenant.name === newName.trim()) {
        console.log('School name is already the same, no update needed');
        return currentTenant;
      }

      const { data, error } = await supabase
        .from('tenants')
        .update({
          name: newName.trim(),
          updated_at: new Date().toISOString()
        })
        .eq('id', tenantId)
        .select()
        .single();

      if (error) {
        console.error('Error updating school name:', error);
        console.error('Error details:', {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });

        // Provide more user-friendly error messages
        let errorMessage = 'فشل في تحديث اسم المدرسة';
        if (error.code === '23505') {
          errorMessage = 'اسم المدرسة موجود بالفعل';
        } else if (error.code === '23514') {
          errorMessage = 'اسم المدرسة غير صالح';
        } else if (error.message) {
          errorMessage = `فشل في تحديث اسم المدرسة: ${error.message}`;
        }

        throw new Error(errorMessage);
      }

      console.log('✅ School name updated successfully in tenants table:', data);

      // Also update the school name in any existing themes for this tenant
      try {
        // Get all themes for this tenant
        const { data: themes, error: getThemesError } = await supabase
          .from('themes')
          .select('id, branding')
          .eq('tenant_id', tenantId);

        if (getThemesError) {
          console.warn('Warning: Could not get themes for update:', getThemesError);
        } else if (themes && themes.length > 0) {
          // Update each theme's branding.schoolName
          for (const theme of themes) {
            const updatedBranding = {
              ...theme.branding,
              schoolName: newName.trim()
            };

            const { error: updateError } = await supabase
              .from('themes')
              .update({
                branding: updatedBranding,
                updated_at: new Date().toISOString()
              })
              .eq('id', theme.id);

            if (updateError) {
              console.warn(`Warning: Could not update theme ${theme.id}:`, updateError);
            }
          }
          console.log('✅ School name also updated in all themes');
        }
      } catch (themeError) {
        console.warn('Warning: Error updating themes:', themeError);
        // Don't throw error here as the main update succeeded
      }

      return data;
    } catch (error) {
      console.error('Error in updateSchoolName:', error);
      throw error;
    }
  }

  /**
   * Check if user can update tenant
   */
  static async canUserUpdateTenant(userId: string, tenantId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('role, tenant_id')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error checking user permissions:', error);
        return false;
      }

      // Admin can update any tenant, school_manager can update their own tenant
      return data.role === 'admin' || 
             (data.role === 'school_manager' && data.tenant_id === tenantId);
    } catch (error) {
      console.error('Error in canUserUpdateTenant:', error);
      return false;
    }
  }
}
