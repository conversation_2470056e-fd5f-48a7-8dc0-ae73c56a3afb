/**
 * Typography Design Tokens
 * Centralized typography system for consistent text styling
 * Phase 3: UI/UX Enhancement
 */

export interface FontWeight {
  thin: number;
  extraLight: number;
  light: number;
  normal: number;
  medium: number;
  semiBold: number;
  bold: number;
  extraBold: number;
  black: number;
}

export interface FontSize {
  xs: string;
  sm: string;
  base: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  '4xl': string;
  '5xl': string;
  '6xl': string;
  '7xl': string;
  '8xl': string;
  '9xl': string;
}

export interface LineHeight {
  none: string;
  tight: string;
  snug: string;
  normal: string;
  relaxed: string;
  loose: string;
}

export interface LetterSpacing {
  tighter: string;
  tight: string;
  normal: string;
  wide: string;
  wider: string;
  widest: string;
}

export interface FontFamily {
  sans: string[];
  serif: string[];
  mono: string[];
  arabic: string[];
  display: string[];
}

export interface TypographyScale {
  fontSize: string;
  lineHeight: string;
  letterSpacing?: string;
  fontWeight?: number;
}

export interface TypographyTokens {
  fontFamily: FontFamily;
  fontSize: FontSize;
  fontWeight: FontWeight;
  lineHeight: LineHeight;
  letterSpacing: LetterSpacing;
  scale: {
    display: {
      '2xl': TypographyScale;
      xl: TypographyScale;
      lg: TypographyScale;
      md: TypographyScale;
      sm: TypographyScale;
    };
    heading: {
      '6xl': TypographyScale;
      '5xl': TypographyScale;
      '4xl': TypographyScale;
      '3xl': TypographyScale;
      '2xl': TypographyScale;
      xl: TypographyScale;
      lg: TypographyScale;
      md: TypographyScale;
      sm: TypographyScale;
      xs: TypographyScale;
    };
    body: {
      '2xl': TypographyScale;
      xl: TypographyScale;
      lg: TypographyScale;
      md: TypographyScale;
      sm: TypographyScale;
      xs: TypographyScale;
    };
    label: {
      lg: TypographyScale;
      md: TypographyScale;
      sm: TypographyScale;
      xs: TypographyScale;
    };
    caption: {
      lg: TypographyScale;
      md: TypographyScale;
      sm: TypographyScale;
    };
  };
}

/**
 * Font Families
 */
export const fontFamily: FontFamily = {
  sans: [
    'Inter',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'sans-serif',
  ],
  serif: [
    'Georgia',
    'Cambria',
    'Times New Roman',
    'Times',
    'serif',
  ],
  mono: [
    'JetBrains Mono',
    'Fira Code',
    'Monaco',
    'Consolas',
    'Liberation Mono',
    'Courier New',
    'monospace',
  ],
  arabic: [
    'Noto Sans Arabic',
    'Cairo',
    'Amiri',
    'Scheherazade New',
    'Arial',
    'sans-serif',
  ],
  display: [
    'Cal Sans',
    'Inter',
    '-apple-system',
    'BlinkMacSystemFont',
    'sans-serif',
  ],
};

/**
 * Font Weights
 */
export const fontWeight: FontWeight = {
  thin: 100,
  extraLight: 200,
  light: 300,
  normal: 400,
  medium: 500,
  semiBold: 600,
  bold: 700,
  extraBold: 800,
  black: 900,
};

/**
 * Font Sizes
 */
export const fontSize: FontSize = {
  xs: '0.75rem',    // 12px
  sm: '0.875rem',   // 14px
  base: '1rem',     // 16px
  lg: '1.125rem',   // 18px
  xl: '1.25rem',    // 20px
  '2xl': '1.5rem',  // 24px
  '3xl': '1.875rem', // 30px
  '4xl': '2.25rem', // 36px
  '5xl': '3rem',    // 48px
  '6xl': '3.75rem', // 60px
  '7xl': '4.5rem',  // 72px
  '8xl': '6rem',    // 96px
  '9xl': '8rem',    // 128px
};

/**
 * Line Heights
 */
export const lineHeight: LineHeight = {
  none: '1',
  tight: '1.25',
  snug: '1.375',
  normal: '1.5',
  relaxed: '1.625',
  loose: '2',
};

/**
 * Letter Spacing
 */
export const letterSpacing: LetterSpacing = {
  tighter: '-0.05em',
  tight: '-0.025em',
  normal: '0em',
  wide: '0.025em',
  wider: '0.05em',
  widest: '0.1em',
};

/**
 * Typography Scale
 */
export const typographyTokens: TypographyTokens = {
  fontFamily,
  fontSize,
  fontWeight,
  lineHeight,
  letterSpacing,
  scale: {
    display: {
      '2xl': {
        fontSize: fontSize['9xl'],
        lineHeight: lineHeight.none,
        letterSpacing: letterSpacing.tighter,
        fontWeight: fontWeight.bold,
      },
      xl: {
        fontSize: fontSize['8xl'],
        lineHeight: lineHeight.none,
        letterSpacing: letterSpacing.tighter,
        fontWeight: fontWeight.bold,
      },
      lg: {
        fontSize: fontSize['7xl'],
        lineHeight: lineHeight.none,
        letterSpacing: letterSpacing.tighter,
        fontWeight: fontWeight.bold,
      },
      md: {
        fontSize: fontSize['6xl'],
        lineHeight: lineHeight.none,
        letterSpacing: letterSpacing.tight,
        fontWeight: fontWeight.bold,
      },
      sm: {
        fontSize: fontSize['5xl'],
        lineHeight: lineHeight.tight,
        letterSpacing: letterSpacing.tight,
        fontWeight: fontWeight.bold,
      },
    },
    heading: {
      '6xl': {
        fontSize: fontSize['6xl'],
        lineHeight: lineHeight.tight,
        letterSpacing: letterSpacing.tight,
        fontWeight: fontWeight.bold,
      },
      '5xl': {
        fontSize: fontSize['5xl'],
        lineHeight: lineHeight.tight,
        letterSpacing: letterSpacing.tight,
        fontWeight: fontWeight.bold,
      },
      '4xl': {
        fontSize: fontSize['4xl'],
        lineHeight: lineHeight.tight,
        letterSpacing: letterSpacing.tight,
        fontWeight: fontWeight.bold,
      },
      '3xl': {
        fontSize: fontSize['3xl'],
        lineHeight: lineHeight.tight,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.bold,
      },
      '2xl': {
        fontSize: fontSize['2xl'],
        lineHeight: lineHeight.tight,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.bold,
      },
      xl: {
        fontSize: fontSize.xl,
        lineHeight: lineHeight.tight,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.semiBold,
      },
      lg: {
        fontSize: fontSize.lg,
        lineHeight: lineHeight.tight,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.semiBold,
      },
      md: {
        fontSize: fontSize.base,
        lineHeight: lineHeight.snug,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.semiBold,
      },
      sm: {
        fontSize: fontSize.sm,
        lineHeight: lineHeight.snug,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.semiBold,
      },
      xs: {
        fontSize: fontSize.xs,
        lineHeight: lineHeight.snug,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.semiBold,
      },
    },
    body: {
      '2xl': {
        fontSize: fontSize['2xl'],
        lineHeight: lineHeight.relaxed,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.normal,
      },
      xl: {
        fontSize: fontSize.xl,
        lineHeight: lineHeight.relaxed,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.normal,
      },
      lg: {
        fontSize: fontSize.lg,
        lineHeight: lineHeight.relaxed,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.normal,
      },
      md: {
        fontSize: fontSize.base,
        lineHeight: lineHeight.normal,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.normal,
      },
      sm: {
        fontSize: fontSize.sm,
        lineHeight: lineHeight.normal,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.normal,
      },
      xs: {
        fontSize: fontSize.xs,
        lineHeight: lineHeight.normal,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.normal,
      },
    },
    label: {
      lg: {
        fontSize: fontSize.base,
        lineHeight: lineHeight.snug,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.medium,
      },
      md: {
        fontSize: fontSize.sm,
        lineHeight: lineHeight.snug,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.medium,
      },
      sm: {
        fontSize: fontSize.xs,
        lineHeight: lineHeight.snug,
        letterSpacing: letterSpacing.wide,
        fontWeight: fontWeight.medium,
      },
      xs: {
        fontSize: fontSize.xs,
        lineHeight: lineHeight.snug,
        letterSpacing: letterSpacing.wider,
        fontWeight: fontWeight.semiBold,
      },
    },
    caption: {
      lg: {
        fontSize: fontSize.sm,
        lineHeight: lineHeight.normal,
        letterSpacing: letterSpacing.normal,
        fontWeight: fontWeight.normal,
      },
      md: {
        fontSize: fontSize.xs,
        lineHeight: lineHeight.normal,
        letterSpacing: letterSpacing.wide,
        fontWeight: fontWeight.normal,
      },
      sm: {
        fontSize: fontSize.xs,
        lineHeight: lineHeight.snug,
        letterSpacing: letterSpacing.wider,
        fontWeight: fontWeight.normal,
      },
    },
  },
};

/**
 * Typography utilities
 */
export const typographyUtils = {
  /**
   * Get font stack for specific language
   */
  getFontStack(language: 'en' | 'ar' | 'he' = 'en'): string[] {
    switch (language) {
      case 'ar':
      case 'he':
        return fontFamily.arabic;
      default:
        return fontFamily.sans;
    }
  },

  /**
   * Get optimal line height for font size
   */
  getOptimalLineHeight(fontSizeValue: string): string {
    const size = parseFloat(fontSizeValue);
    if (size <= 14) return lineHeight.snug;
    if (size <= 18) return lineHeight.normal;
    if (size <= 24) return lineHeight.relaxed;
    return lineHeight.tight;
  },

  /**
   * Calculate reading time
   */
  calculateReadingTime(text: string, wordsPerMinute: number = 200): number {
    const words = text.trim().split(/\s+/).length;
    return Math.ceil(words / wordsPerMinute);
  },

  /**
   * Truncate text with ellipsis
   */
  truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength - 3) + '...';
  },

  /**
   * Convert rem to px
   */
  remToPx(rem: string, baseFontSize: number = 16): number {
    return parseFloat(rem) * baseFontSize;
  },

  /**
   * Convert px to rem
   */
  pxToRem(px: number, baseFontSize: number = 16): string {
    return `${px / baseFontSize}rem`;
  },
};
