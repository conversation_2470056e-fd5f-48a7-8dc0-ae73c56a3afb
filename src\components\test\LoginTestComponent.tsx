/**
 * مكون اختبار لصفحة تسجيل الدخول المحدثة
 * Test component for updated login page
 */

import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useUpdatedPermissions } from '../../hooks/useUpdatedPermissions';
import { CentralizedPermissionGuard } from '../auth/CentralizedPermissionGuard';
import { ResourceType, Action } from '../../services/CentralizedPermissionService';
import { UserRole } from '../../types';

export const LoginTestComponent: React.FC = () => {
  const { user, isLoading, login, logout } = useAuth();
  const {
    isAdmin,
    isSchoolManager,
    canCreateUser,
    canEditUser,
    canDeleteUser,
    logSecurityEvent
  } = useUpdatedPermissions();

  const [testEmail, setTestEmail] = useState('');
  const [testPassword, setTestPassword] = useState('');
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const handleTestLogin = async () => {
    if (!testEmail || !testPassword) {
      addTestResult('❌ يرجى إدخال البريد الإلكتروني وكلمة المرور');
      return;
    }

    addTestResult('🔄 بدء اختبار تسجيل الدخول...');
    
    try {
      const result = await login(testEmail, testPassword);
      
      if (result.success) {
        addTestResult('✅ تم تسجيل الدخول بنجاح');
        await logSecurityEvent(
          'LOGIN_TEST_SUCCESS',
          'INFO',
          'Login test completed successfully'
        );
      } else {
        addTestResult(`❌ فشل تسجيل الدخول: ${result.error}`);
        await logSecurityEvent(
          'LOGIN_TEST_FAILED',
          'WARNING',
          'Login test failed',
          { error: result.error }
        );
      }
    } catch (error) {
      addTestResult(`🚨 خطأ في الاختبار: ${error}`);
    }
  };

  const handleTestPermissions = async () => {
    if (!user) {
      addTestResult('❌ يجب تسجيل الدخول أولاً لاختبار الصلاحيات');
      return;
    }

    addTestResult('🔄 بدء اختبار الصلاحيات...');

    try {
      // اختبار الصلاحيات الأساسية
      addTestResult(`👤 المستخدم: ${user.email} (${user.role})`);
      addTestResult(`🔑 أدمن: ${isAdmin ? 'نعم' : 'لا'}`);
      addTestResult(`🏫 مدير مدرسة: ${isSchoolManager ? 'نعم' : 'لا'}`);

      // اختبار صلاحيات المستخدمين
      const canCreate = await canCreateUser(UserRole.DRIVER);
      const canEdit = await canEditUser('test-user-id');
      const canDelete = await canDeleteUser('test-user-id');

      addTestResult(`➕ يمكن إنشاء مستخدمين: ${canCreate ? 'نعم' : 'لا'}`);
      addTestResult(`✏️ يمكن تعديل مستخدمين: ${canEdit ? 'نعم' : 'لا'}`);
      addTestResult(`🗑️ يمكن حذف مستخدمين: ${canDelete ? 'نعم' : 'لا'}`);

      addTestResult('✅ اكتمل اختبار الصلاحيات بنجاح');
    } catch (error) {
      addTestResult(`🚨 خطأ في اختبار الصلاحيات: ${error}`);
    }
  };

  const handleTestLogout = async () => {
    addTestResult('🔄 بدء اختبار تسجيل الخروج...');
    
    try {
      await logout();
      addTestResult('✅ تم تسجيل الخروج بنجاح');
    } catch (error) {
      addTestResult(`🚨 خطأ في تسجيل الخروج: ${error}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  if (isLoading) {
    return (
      <div className="p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <div className="flex items-center space-x-2 space-x-reverse">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="text-blue-600 dark:text-blue-400">جاري التحميل...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          🧪 اختبار النظام الأمني المحدث
        </h2>

        {/* معلومات المستخدم الحالي */}
        <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">معلومات المستخدم الحالي:</h3>
          {user ? (
            <div className="space-y-1 text-sm">
              <p><strong>البريد:</strong> {user.email}</p>
              <p><strong>الاسم:</strong> {user.name}</p>
              <p><strong>الدور:</strong> {user.role}</p>
              <p><strong>المدرسة:</strong> {user.tenant_id || 'غير محدد'}</p>
            </div>
          ) : (
            <p className="text-gray-500">لا يوجد مستخدم مسجل دخول</p>
          )}
        </div>

        {/* اختبار تسجيل الدخول */}
        {!user && (
          <div className="mb-6 p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">اختبار تسجيل الدخول:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <input
                type="email"
                placeholder="البريد الإلكتروني"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
              <input
                type="password"
                placeholder="كلمة المرور"
                value={testPassword}
                onChange={(e) => setTestPassword(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <button
              onClick={handleTestLogin}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              اختبار تسجيل الدخول
            </button>
          </div>
        )}

        {/* اختبار الصلاحيات */}
        {user && (
          <div className="mb-6 p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">اختبار الصلاحيات:</h3>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={handleTestPermissions}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
              >
                اختبار الصلاحيات
              </button>
              <button
                onClick={handleTestLogout}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                اختبار تسجيل الخروج
              </button>
            </div>
          </div>
        )}

        {/* اختبار مكون الحماية */}
        <div className="mb-6 p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">اختبار مكون الحماية:</h3>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">حماية إنشاء المستخدمين (أدمن فقط):</h4>
              <CentralizedPermissionGuard
                resourceType={ResourceType.USER}
                action={Action.CREATE}
                fallback={
                  <div className="p-3 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-md">
                    ❌ ليس لديك صلاحية لإنشاء المستخدمين
                  </div>
                }
              >
                <div className="p-3 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-md">
                  ✅ يمكنك إنشاء المستخدمين
                </div>
              </CentralizedPermissionGuard>
            </div>

            <div>
              <h4 className="font-medium mb-2">حماية إدارة الحافلات:</h4>
              <CentralizedPermissionGuard
                resourceType={ResourceType.BUS}
                action={Action.UPDATE}
                fallback={
                  <div className="p-3 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-md">
                    ❌ ليس لديك صلاحية لإدارة الحافلات
                  </div>
                }
              >
                <div className="p-3 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-md">
                  ✅ يمكنك إدارة الحافلات
                </div>
              </CentralizedPermissionGuard>
            </div>
          </div>
        </div>

        {/* نتائج الاختبار */}
        <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">نتائج الاختبار:</h3>
            <button
              onClick={clearResults}
              className="px-3 py-1 text-sm bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
            >
              مسح النتائج
            </button>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700 rounded-md p-3 max-h-64 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500 text-sm">لا توجد نتائج اختبار بعد</p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
