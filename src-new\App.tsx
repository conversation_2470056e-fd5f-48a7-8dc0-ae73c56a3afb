import React, { useEffect, useState } from "react";
import { Routes, Route, Navigate, useRoutes } from "react-router-dom";
import { useAuth } from "./core/contexts/AuthContext";
import { LoginPage } from "./features/auth/pages/login/LoginPage";
import { ResetPasswordPage } from "./features/auth/pages/ResetPasswordPage";
import { DashboardPage } from "./features/dashboard/pages/DashboardPage";
import { SchoolsPage } from "./features/dashboard/pages/SchoolsPage";
import { BusesPage } from "./features/dashboard/pages/BusesPage";
import { BusDetailsPage } from "./features/dashboard/pages/BusDetailsPage";
import { RoutesPage } from "./features/dashboard/pages/RoutesPage";
import { StudentsPage } from "./features/dashboard/pages/StudentsPage";
import { AttendancePage } from "./features/dashboard/pages/AttendancePage";
import { ReportsPage } from "./features/dashboard/pages/ReportsPage";
import { TrackingPage } from "./features/dashboard/pages/TrackingPage";
import { MyRoutePage } from "./features/dashboard/pages/MyRoutePage";
import { ParentDashboard } from "./features/dashboard/pages/ParentDashboard";
import { SchoolDashboard } from "./features/dashboard/pages/SchoolDashboard";
import { UsersPage } from "./features/dashboard/pages/UsersPage";
import { ProfilePage } from "./features/dashboard/pages/ProfilePage";
import { SettingsPage } from "./features/dashboard/pages/SettingsPage";
import { DriverAttendancePage } from "./features/dashboard/pages/DriverAttendancePage";
import { EvaluationPage } from "./features/dashboard/pages/EvaluationPage";
import { NotificationsPage } from "./features/dashboard/pages/NotificationsPage";
import { SecurityTestPage } from "./features/testing/SecurityTestPage";
import { SecurityDashboard as ThemeManagementPage } from "./features/dashboard/admin/SecurityDashboard";
import { SettingsPage as ThemeSettingsPage } from "./features/dashboard/pages/SettingsPage";
import ThemesPage from "./features/dashboard/pages/ThemesPage";
import { MaintenancePage } from "./features/dashboard/pages/MaintenancePage";
import { AdvancedAttendancePage } from "./features/dashboard/pages/AdvancedAttendancePage";
import SchoolsManagementPage from "./features/dashboard/pages/SchoolsManagementPage";
import { ThemeProtectedRoute } from "./features/auth/components/ThemeProtectedRoute";
import { TestPage } from "./features/testing/TestPage";
import { UserRole, TenantBranding } from "./core/types";
import { pushNotificationService } from "./shared/services/lib/pushNotifications";
import { BusLocationUpdater } from "./features/buses/components/BusLocationUpdater";
import { EnhancedPermissionGuard } from "./features/auth/components/EnhancedPermissionGuard";
import { usePermissions } from "./core/hooks/usePermissions";
import { ENHANCED_ROUTE_PERMISSIONS } from "./shared/services/lib/rbacCentralizedConfigEnhanced";
import { useThemePermissions } from "./core/hooks/useThemePermissions";

function App() {
  const { user, tenant, isLoading, logout } = useAuth();
  const { isAdmin, isSchoolManager } = usePermissions();
  const [tenantBranding, setTenantBranding] = useState<TenantBranding | null>(
    null,
  );
  const [tempoRoutes, setTempoRoutes] = useState<any[]>([]);

  // Load tempo routes dynamically
  useEffect(() => {
    const loadTempoRoutes = async () => {
      if (import.meta.env.VITE_TEMPO) {
        try {
          const routes = await import("tempo-routes");
          setTempoRoutes(routes.default || []);
        } catch (error) {
          console.log("Tempo routes not available");
        }
      }
    };
    loadTempoRoutes();
  }, []);

  // Apply tenant branding
  useEffect(() => {
    if (tenant?.settings?.branding) {
      const branding = tenant.settings.branding;
      setTenantBranding({
        schoolName: branding.schoolName || tenant.name,
        logo: branding.logo || tenant.logo_url,
        primaryColor: branding.colors?.primary || "#3b82f6",
        secondaryColor: branding.colors?.secondary || "#10b981",
        favicon: tenant.settings.theme?.favicon,
        tagline: branding.tagline,
      });

      // Apply custom CSS variables for tenant theming
      if (branding.colors?.primary) {
        document.documentElement.style.setProperty(
          "--tenant-primary",
          branding.colors.primary,
        );
      }
      if (branding.colors?.secondary) {
        document.documentElement.style.setProperty(
          "--tenant-secondary",
          branding.colors.secondary,
        );
      }
      if (branding.colors?.accent) {
        document.documentElement.style.setProperty(
          "--tenant-accent",
          branding.colors.accent,
        );
      }

      // Update page title
      document.title = `${branding.schoolName || tenant.name} - School Transport Management`;

      // Update favicon if provided
      if (tenant.settings.theme?.favicon) {
        const favicon = document.querySelector(
          'link[rel="icon"]',
        ) as HTMLLinkElement;
        if (favicon) {
          favicon.href = tenant.settings.theme.favicon;
        }
      }
    } else if (tenant) {
      // Fallback branding
      setTenantBranding({
        schoolName: tenant.name,
        logo: tenant.logo_url,
        primaryColor: "#3b82f6",
        secondaryColor: "#10b981",
      });
      document.title = `${tenant.name} - School Transport Management`;
    }
  }, [tenant]);

  // Initialize service worker and push notifications
  useEffect(() => {
    const initializeServiceWorker = async () => {
      if ("serviceWorker" in navigator) {
        try {
          // Register service worker
          const registration = await navigator.serviceWorker.register(
            "/sw.js",
            {
              scope: "/",
              updateViaCache: "none",
            },
          );

          console.log("Service Worker registered successfully:", registration);

          // Initialize push notification service
          await pushNotificationService.initialize();

          // Handle service worker updates
          registration.addEventListener("updatefound", () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener("statechange", () => {
                if (
                  newWorker.state === "installed" &&
                  navigator.serviceWorker.controller
                ) {
                  console.log("New service worker available, reloading...");
                  window.location.reload();
                }
              });
            }
          });

          // Listen for messages from service worker
          navigator.serviceWorker.addEventListener("message", (event) => {
            console.log("Message from service worker:", event.data);

            if (event.data.type === "NOTIFICATION_CLICKED") {
              // Handle notification click events
              const { url } = event.data;
              if (url) {
                window.location.href = url;
              }
            }
          });
        } catch (error) {
          console.error("Service Worker registration failed:", error);
        }
      } else {
        console.warn("Service Workers are not supported in this browser");
      }
    };

    initializeServiceWorker();
  }, []);

  // Debug logging
  console.log('🔍 App render - isLoading:', isLoading, 'user:', !!user, 'tenant:', !!tenant);

  if (isLoading) {
    console.log('⏳ App showing loading screen');
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <div className="text-lg font-medium text-gray-700 dark:text-gray-300">
            Loading...
          </div>
          {tenantBranding && (
            <div className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              {tenantBranding.schoolName}
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Bus Location Updater - Only render when user is logged in and has tenant */}
      {user && user.tenant_id && <BusLocationUpdater />}

      {/* Tenant-specific styling */}
      {tenantBranding && (
        <style>
          {`
            :root {
              --tenant-primary: ${tenantBranding.primaryColor};
              --tenant-secondary: ${tenantBranding.secondaryColor || "#10b981"};
            }
            .tenant-primary { color: var(--tenant-primary) !important; }
            .tenant-bg-primary { background-color: var(--tenant-primary) !important; }
            .tenant-border-primary { border-color: var(--tenant-primary) !important; }
            .tenant-secondary { color: var(--tenant-secondary) !important; }
            .tenant-bg-secondary { background-color: var(--tenant-secondary) !important; }
          `}
        </style>
      )}

      {/* Tempo routes */}
      {import.meta.env.VITE_TEMPO &&
        tempoRoutes.length > 0 &&
        useRoutes(tempoRoutes)}

      <Routes>
        {/* Public routes */}
        <Route path="/login" element={<LoginPage />} />
        <Route path="/reset-password" element={<ResetPasswordPage />} />

        {/* Protected routes */}
        {user ? (
          <>
            {/* Enhanced access control using centralized RBAC - Admin always has access */}
            {(user.role === "admin" && user.is_active) || (user.tenant_id && user.is_active) ? (
              <>
                {/* Dashboard routes - accessible to all authenticated users */}
                <Route path="/dashboard" element={<DashboardPage />} />

                {/* Admin routes - using enhanced permission guard */}
                <Route
                  path="/dashboard/schools"
                  element={
                    <ProtectedRoute route="/dashboard/schools">
                      <SchoolsPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard/schools-management"
                  element={
                    <ProtectedRoute route="/dashboard/schools-management">
                      <SchoolsManagementPage />
                    </ProtectedRoute>
                  }
                />

                {/* Tenant-specific routes with access control */}
                <Route
                  path="/dashboard/users"
                  element={
                    <ProtectedRoute route="/dashboard/users">
                      <UsersPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard/buses"
                  element={
                    <ProtectedRoute route="/dashboard/buses">
                      <BusesPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard/buses/:id"
                  element={<BusDetailsPage />}
                />
                <Route
                  path="/dashboard/routes"
                  element={
                    <ProtectedRoute route="/dashboard/routes">
                      <RoutesPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard/students"
                  element={
                    <ProtectedRoute route="/dashboard/students">
                      <StudentsPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard/attendance"
                  element={
                    <ProtectedRoute route="/dashboard/attendance">
                      <AttendancePage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard/reports"
                  element={
                    <ProtectedRoute route="/dashboard/reports">
                      <ReportsPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard/tracking"
                  element={
                    <ProtectedRoute route="/dashboard/tracking">
                      <TrackingPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard/my-route"
                  element={
                    <ProtectedRoute route="/dashboard/my-route">
                      <MyRoutePage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard/driver-attendance"
                  element={
                    <ProtectedRoute route="/dashboard/driver-attendance">
                      <DriverAttendancePage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard/children"
                  element={
                    <ProtectedRoute route="/dashboard/children">
                      <StudentsPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard/notifications"
                  element={
                    <ProtectedRoute route="/dashboard/notifications">
                      <NotificationsPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard/evaluation"
                  element={
                    <ProtectedRoute route="/dashboard/evaluation">
                      <EvaluationPage />
                    </ProtectedRoute>
                  }
                />

                {/* Common routes - accessible to all authenticated users */}
                <Route path="/dashboard/profile" element={<ProfilePage />} />
                <Route path="/dashboard/settings" element={<SettingsPage />} />
                <Route path="/dashboard/themes" element={<ThemesPage />} />

                {/* Phase 4: Advanced Features */}
                <Route
                  path="/dashboard/maintenance"
                  element={
                    <ProtectedRoute route="/dashboard/maintenance">
                      <MaintenancePage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard/advanced-attendance"
                  element={
                    <ProtectedRoute route="/dashboard/advanced-attendance">
                      <AdvancedAttendancePage />
                    </ProtectedRoute>
                  }
                />

                {/* Theme Management Routes */}
                <Route
                  path="/admin/themes"
                  element={
                    <ThemeProtectedRoute requiredRole="admin">
                      <ThemeManagementPage />
                    </ThemeProtectedRoute>
                  }
                />
                <Route
                  path="/school/theme"
                  element={
                    <ThemeProtectedRoute requiredRole="school_manager">
                      <ThemeSettingsPage />
                    </ThemeProtectedRoute>
                  }
                />

                {/* Security Test Page - Available for testing */}
                <Route
                  path="/dashboard/security-test"
                  element={<SecurityTestPage />}
                />

                {/* Test Page - Available for debugging */}
                <Route
                  path="/test"
                  element={<TestPage />}
                />

                {/* Enhanced role-based redirect using centralized config */}
                <Route path="/" element={<RoleBasedRedirect />} />
              </>
            ) : (
              // User doesn't have tenant access - show tenant selection or error
              <Route
                path="*"
                element={
                  <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
                    <div className="text-center p-8">
                      <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                        No School Access
                      </h1>
                      <p className="text-gray-600 dark:text-gray-400 mb-6">
                        You don't have access to any school. Please contact your
                        administrator.
                      </p>
                      <button
                        onClick={logout}
                        className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
                      >
                        Sign Out
                      </button>
                    </div>
                  </div>
                }
              />
            )}
          </>
        ) : (
          <>
            {console.log('🔄 No user, redirecting to login')}
            <Route path="/login" element={<LoginPage />} />
            <Route path="*" element={<Navigate to="/login" replace />} />
          </>
        )}

        {/* Allow Tempo routes before catch-all */}
        {import.meta.env.VITE_TEMPO && <Route path="/tempobook/*" />}

        {/* Catch all route */}
        <Route
          path="*"
          element={
            user ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />
      </Routes>
    </>
  );
}

// Enhanced role-based redirect component
const RoleBasedRedirect: React.FC = () => {
  const { user } = useAuth();

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Enhanced role-based routing using centralized configuration
  const defaultRoutes = {
    [UserRole.ADMIN]: "/dashboard",
    [UserRole.SCHOOL_MANAGER]: "/dashboard",
    [UserRole.SUPERVISOR]: "/dashboard",
    [UserRole.DRIVER]: "/dashboard/my-route",
    [UserRole.PARENT]: "/dashboard/children",
    [UserRole.STUDENT]: "/dashboard/profile",
  };

  const targetRoute = defaultRoutes[user.role as UserRole] || "/dashboard";
  return <Navigate to={targetRoute} replace />;
};

// Enhanced protected route component
const ProtectedRoute: React.FC<{
  route: string;
  children: React.ReactNode;
}> = ({ route, children }) => {
  const { user } = useAuth();
  const { isAdmin, isSchoolManager } = usePermissions();

  // Special handling for theme routes
  if (route === "/admin/themes") {
    if (!isAdmin) {
      return <Navigate to="/dashboard" replace />;
    }
    return <>{children}</>;
  }

  if (route === "/school/theme") {
    if (!isSchoolManager) {
      return <Navigate to="/dashboard" replace />;
    }
    return <>{children}</>;
  }

  // Basic access control - if user exists and has tenant or is admin, allow access
  if (!user || (!user.tenant_id && !isAdmin)) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

export default App;
