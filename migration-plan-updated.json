{"phase1": {"name": "نقل الملفات الأساسية", "priority": "high", "files": [{"from": "src/types", "to": "src-new/core/types", "type": "directory"}, {"from": "src/utils", "to": "src-new/core/utils", "type": "directory"}, {"from": "src/hooks", "to": "src-new/core/hooks", "type": "directory"}, {"from": "src/contexts", "to": "src-new/core/contexts", "type": "directory"}, {"from": "src/config", "to": "src-new/core/constants", "type": "directory"}]}, "phase2": {"name": "نقل الخدمات والمكتبات", "priority": "high", "files": [{"from": "src/services", "to": "src-new/shared/services", "type": "directory"}, {"from": "src/lib", "to": "src-new/shared/services/lib", "type": "directory"}, {"from": "src/middleware", "to": "src-new/shared/services/middleware", "type": "directory"}, {"from": "src/api", "to": "src-new/shared/services/api", "type": "directory"}]}, "phase3": {"name": "نقل المكونات المشتركة", "priority": "medium", "files": [{"from": "src/components/ui", "to": "src-new/shared/components/ui", "type": "directory"}, {"from": "src/components/layout", "to": "src-new/shared/layouts", "type": "directory"}, {"from": "src/components/common", "to": "src-new/shared/components/common", "type": "directory"}, {"from": "src/design-system", "to": "src-new/shared/components/design-system", "type": "directory"}]}, "phase4": {"name": "نقل ميزات المصادقة والأمان", "priority": "high", "files": [{"from": "src/components/auth", "to": "src-new/features/auth/components", "type": "directory"}, {"from": "src/pages/auth", "to": "src-new/features/auth/pages", "type": "directory"}, {"from": "src/pages/login", "to": "src-new/features/auth/pages/login", "type": "directory"}, {"from": "src/components/security", "to": "src-new/features/auth/security", "type": "directory"}]}, "phase5": {"name": "نقل ميزات لوحة التحكم", "priority": "medium", "files": [{"from": "src/components/dashboard", "to": "src-new/features/dashboard/components", "type": "directory"}, {"from": "src/components/dashboards", "to": "src-new/features/dashboard/variants", "type": "directory"}, {"from": "src/pages/dashboard", "to": "src-new/features/dashboard/pages", "type": "directory"}, {"from": "src/components/admin", "to": "src-new/features/dashboard/admin", "type": "directory"}]}, "phase6": {"name": "نقل ميزات إدارة الحافلات", "priority": "medium", "files": [{"from": "src/components/buses", "to": "src-new/features/buses/components", "type": "directory"}, {"from": "src/components/routes", "to": "src-new/features/routes/components", "type": "directory"}, {"from": "src/components/drivers", "to": "src-new/features/drivers/components", "type": "directory"}, {"from": "src/components/tracking", "to": "src-new/features/tracking/components", "type": "directory"}]}, "phase7": {"name": "نقل ميزات إدارة الطلاب والمدارس", "priority": "medium", "files": [{"from": "src/components/students", "to": "src-new/features/students/components", "type": "directory"}, {"from": "src/components/schools", "to": "src-new/features/schools/components", "type": "directory"}, {"from": "src/pages/school", "to": "src-new/features/schools/pages", "type": "directory"}]}, "phase8": {"name": "نقل الميزات المتقدمة", "priority": "low", "files": [{"from": "src/components/notifications", "to": "src-new/features/notifications/components", "type": "directory"}, {"from": "src/components/reports", "to": "src-new/features/reports/components", "type": "directory"}, {"from": "src/components/maintenance", "to": "src-new/features/maintenance/components", "type": "directory"}, {"from": "src/components/maps", "to": "src-new/features/tracking/maps", "type": "directory"}, {"from": "src/components/map", "to": "src-new/features/tracking/map", "type": "directory"}]}, "phase9": {"name": "نقل الملفات الثابتة والموضوعات", "priority": "low", "files": [{"from": "src/themes", "to": "src-new/assets/themes", "type": "directory"}, {"from": "src/i18n", "to": "src-new/assets/locales", "type": "directory"}]}, "phase10": {"name": "نقل الملفات الجذرية والاختبارات", "priority": "low", "files": [{"from": "src/App.tsx", "to": "src-new/App.tsx", "type": "file"}, {"from": "src/main.tsx", "to": "src-new/main.tsx", "type": "file"}, {"from": "src/index.css", "to": "src-new/assets/styles/index.css", "type": "file"}, {"from": "src/tests", "to": "src-new/tests", "type": "directory"}, {"from": "src/pages/testing", "to": "src-new/features/testing", "type": "directory"}]}}