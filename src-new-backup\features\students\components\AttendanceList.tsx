import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Calendar, Clock, MapPin, User, Search, Filter } from "lucide-react";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/api";

interface AttendanceListProps {
  studentId?: string;
  date?: Date;
  showDateFilter?: boolean;
  busId?: string;
  routeId?: string;
  className?: string;
}

export const AttendanceList: React.FC<AttendanceListProps> = ({
  studentId,
  date = new Date(),
  showDateFilter = true,
  busId,
  routeId,
  className = "",
}) => {
  const { t } = useTranslation();
  const { buses, users } = useDatabase();
  const [records, setRecords] = useState<Tables<"attendance">[]>([]);
  const [loading, setLoading] = useState(true);
  const [filterType, setFilterType] = useState<"all" | "pickup" | "dropoff">(
    "all",
  );
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDate, setSelectedDate] = useState(date);

  useEffect(() => {
    const fetchAttendance = async () => {
      try {
        setLoading(true);
        const startOfDay = new Date(selectedDate);
        startOfDay.setHours(0, 0, 0, 0);

        const endOfDay = new Date(selectedDate);
        endOfDay.setHours(23, 59, 59, 999);

        let query = supabase
          .from("attendance")
          .select("*")
          .gte("recorded_at", startOfDay.toISOString())
          .lte("recorded_at", endOfDay.toISOString());

        if (studentId) {
          query = query.eq("student_id", studentId);
        }

        if (busId) {
          query = query.eq("bus_id", busId);
        }

        const { data, error } = await query;

        if (error) throw error;
        setRecords(data || []);
      } catch (error) {
        console.error("Error fetching attendance:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchAttendance();
  }, [studentId, selectedDate]);

  // Filter records
  const filteredRecords = records
    .filter((record) => {
      if (filterType === "all") return true;
      return record.type === filterType;
    })
    .filter((record) => {
      if (!searchQuery) return true;
      const bus = buses.find((b) => b.id === record.bus_id);
      return bus?.plate_number
        .toLowerCase()
        .includes(searchQuery.toLowerCase());
    });

  // Get bus plate number
  const getBusPlateNumber = (busId: string) => {
    const bus = buses.find((b) => b.id === busId);
    return bus?.plate_number || "-";
  };

  // Get recorder name
  const getRecorderName = (userId: string) => {
    const user = users.find((u) => u.id === userId);
    return user?.name || "-";
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex flex-col md:flex-row gap-4 flex-1">
          <div className="relative max-w-xs w-full">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              placeholder={t("buses.plateNumber")}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {showDateFilter && (
            <div className="relative max-w-xs w-full">
              <input
                type="date"
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                value={selectedDate.toISOString().split("T")[0]}
                onChange={(e) => setSelectedDate(new Date(e.target.value))}
              />
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Filter size={18} className="text-gray-400" />
            </div>
            <select
              className="block pl-10 pr-8 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
              value={filterType}
              onChange={(e) =>
                setFilterType(e.target.value as "all" | "pickup" | "dropoff")
              }
            >
              <option value="all">{t("common.all")}</option>
              <option value="pickup">{t("students.pickup")}</option>
              <option value="dropoff">{t("students.dropoff")}</option>
            </select>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {filteredRecords.length > 0 ? (
            filteredRecords.map((record) => (
              <div key={record.id} className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div
                      className={`p-2 rounded-full ${
                        record.type === "pickup"
                          ? "bg-accent-100 text-accent-700 dark:bg-accent-900/20 dark:text-accent-400"
                          : "bg-secondary-100 text-secondary-700 dark:bg-secondary-900/20 dark:text-secondary-400"
                      }`}
                    >
                      {record.type === "pickup" ? (
                        <User size={20} />
                      ) : (
                        <User size={20} />
                      )}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {record.type === "pickup"
                          ? t("students.pickup")
                          : t("students.dropoff")}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {getBusPlateNumber(record.bus_id)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                      <Clock size={16} />
                      <span>
                        {new Date(record.recorded_at).toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-xs text-gray-400 dark:text-gray-500 mt-1">
                      <User size={14} />
                      <span>{getRecorderName(record.recorded_by)}</span>
                    </div>
                  </div>
                </div>
                <div className="mt-2 flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                  <MapPin size={14} />
                  <span>
                    {record.location && (record.location as any).coordinates
                      ? `Lat: ${(record.location as any).coordinates[1].toFixed(6)}, Lng: ${(record.location as any).coordinates[0].toFixed(6)}`
                      : "Location not available"}
                  </span>
                </div>
              </div>
            ))
          ) : (
            <div className="p-4 text-center text-gray-500 dark:text-gray-400">
              {t("common.noData")}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
