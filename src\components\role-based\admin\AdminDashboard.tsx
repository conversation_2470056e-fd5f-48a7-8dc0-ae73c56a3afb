/**
 * Admin Dashboard Component
 * Comprehensive dashboard for system administrators
 * Phase 2: Application Structure Reorganization
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { userService } from '../../../services/data/UserService';
import { schoolService } from '../../../services/data/SchoolService';
import { busService } from '../../../services/data/BusService';
import { studentService } from '../../../services/data/StudentService';

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '../../common/ui/Card';
import { Button } from '../../common/ui/Button';
import { Badge } from '../../common/ui/Badge';
import { Alert, AlertDescription } from '../../common/ui/Alert';

// Icons
import { 
  Users, 
  Building2, 
  Bus, 
  GraduationCap, 
  TrendingUp, 
  Al<PERSON><PERSON>riangle,
  CheckCircle,
  Clock,
  BarChart3
} from 'lucide-react';

interface DashboardStats {
  users: {
    total: number;
    active: number;
    by_role: Record<string, number>;
    recent_registrations: number;
  };
  schools: {
    total: number;
    active: number;
  };
  buses: {
    total: number;
    active: number;
    in_route: number;
    maintenance: number;
  };
  students: {
    total: number;
    active: number;
    attendance_rate: number;
  };
}

interface SystemHealth {
  overall_status: 'healthy' | 'warning' | 'critical';
  services: Array<{
    name: string;
    status: 'online' | 'offline' | 'degraded';
    response_time: number;
    last_check: string;
  }>;
  alerts: Array<{
    id: string;
    type: 'info' | 'warning' | 'error';
    message: string;
    timestamp: string;
  }>;
}

/**
 * Admin Dashboard Component
 * Provides comprehensive system overview for administrators
 */
export const AdminDashboard: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [health, setHealth] = useState<SystemHealth | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Load dashboard data
   */
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load all statistics in parallel
        const [userStats, schoolStats, busStats, studentStats] = await Promise.all([
          userService.getUserStats(),
          schoolService.getSchoolStats(),
          busService.getBusStats(),
          studentService.getStudentStats(),
        ]);

        // Check if all requests were successful
        if (userStats.success && schoolStats.success && busStats.success && studentStats.success) {
          setStats({
            users: userStats.data!,
            schools: schoolStats.data!,
            buses: busStats.data!,
            students: studentStats.data!,
          });
        } else {
          setError('Failed to load some dashboard data');
        }

        // Load system health (mock data for now)
        setHealth({
          overall_status: 'healthy',
          services: [
            { name: 'API Server', status: 'online', response_time: 45, last_check: new Date().toISOString() },
            { name: 'Database', status: 'online', response_time: 12, last_check: new Date().toISOString() },
            { name: 'GPS Tracking', status: 'online', response_time: 89, last_check: new Date().toISOString() },
            { name: 'Notifications', status: 'degraded', response_time: 156, last_check: new Date().toISOString() },
          ],
          alerts: [
            {
              id: '1',
              type: 'warning',
              message: 'High memory usage detected on server 2',
              timestamp: new Date().toISOString(),
            },
            {
              id: '2',
              type: 'info',
              message: 'Scheduled maintenance completed successfully',
              timestamp: new Date(Date.now() - 3600000).toISOString(),
            },
          ],
        });

      } catch (err) {
        setError('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  /**
   * Get status icon
   */
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
      case 'healthy':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'degraded':
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'offline':
      case 'critical':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold">Welcome back, {user?.name}!</h1>
        <p className="mt-2 opacity-90">
          System Administrator Dashboard - Monitor and manage your school bus system
        </p>
      </div>

      {/* System Health Overview */}
      {health && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="w-5 h-5" />
              <span>System Health</span>
              <Badge variant={health.overall_status === 'healthy' ? 'default' : 'destructive'}>
                {health.overall_status.toUpperCase()}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {health.services.map((service) => (
                <div key={service.name} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(service.status)}
                    <span className="font-medium">{service.name}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-500">{service.response_time}ms</div>
                    <div className="text-xs text-gray-400">
                      {new Date(service.last_check).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Users Stats */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.users.total}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+{stats.users.recent_registrations}</span> new this week
              </p>
              <div className="mt-2 space-y-1">
                {Object.entries(stats.users.by_role).map(([role, count]) => (
                  <div key={role} className="flex justify-between text-xs">
                    <span className="capitalize">{role.replace('_', ' ')}</span>
                    <span>{count}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Schools Stats */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Schools</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.schools.total}</div>
              <p className="text-xs text-muted-foreground">
                {stats.schools.active} active schools
              </p>
              <div className="mt-2">
                <div className="flex justify-between text-xs">
                  <span>Active Rate</span>
                  <span>{((stats.schools.active / stats.schools.total) * 100).toFixed(1)}%</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Buses Stats */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Fleet Status</CardTitle>
              <Bus className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.buses.total}</div>
              <p className="text-xs text-muted-foreground">
                {stats.buses.in_route} currently in route
              </p>
              <div className="mt-2 space-y-1">
                <div className="flex justify-between text-xs">
                  <span>Active</span>
                  <span>{stats.buses.active}</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span>Maintenance</span>
                  <span>{stats.buses.maintenance}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Students Stats */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Students</CardTitle>
              <GraduationCap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.students.total}</div>
              <p className="text-xs text-muted-foreground">
                {stats.students.attendance_rate}% attendance rate
              </p>
              <div className="mt-2">
                <div className="flex justify-between text-xs">
                  <span>Active Students</span>
                  <span>{stats.students.active}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Recent Alerts */}
      {health && health.alerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5" />
              <span>Recent Alerts</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {health.alerts.map((alert) => (
                <div key={alert.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                  {getStatusIcon(alert.type)}
                  <div className="flex-1">
                    <p className="text-sm">{alert.message}</p>
                    <p className="text-xs text-gray-500">
                      {new Date(alert.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button className="h-20 flex flex-col space-y-2">
              <Users className="w-6 h-6" />
              <span>Manage Users</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <Building2 className="w-6 h-6" />
              <span>Manage Schools</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <BarChart3 className="w-6 h-6" />
              <span>View Reports</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
