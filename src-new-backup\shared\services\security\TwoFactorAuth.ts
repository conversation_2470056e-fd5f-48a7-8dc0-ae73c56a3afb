/**
 * خدمة التحقق الثنائي (2FA)
 * Two-Factor Authentication Service
 * 
 * المرحلة 1: تأمين الدخول والسياسات الأمنية الأساسية
 */

import { supabase } from '../../lib/supabase';
import { User } from '../../types';

export interface TwoFactorSetup {
  secret: string;
  qrCode: string;
  backupCodes: string[];
}

export interface TwoFactorConfig {
  user_id: string;
  secret_key: string;
  is_enabled: boolean;
  backup_codes: string[];
  last_used_at?: string;
  created_at: string;
}

export interface EmailVerificationCode {
  code: string;
  expires_at: string;
  attempts: number;
}

export class TwoFactorAuthService {
  private static instance: TwoFactorAuthService;

  private constructor() {}

  static getInstance(): TwoFactorAuthService {
    if (!TwoFactorAuthService.instance) {
      TwoFactorAuthService.instance = new TwoFactorAuthService();
    }
    return TwoFactorAuthService.instance;
  }

  /**
   * إعداد التحقق الثنائي للمستخدم
   */
  async setupTwoFactor(userId: string): Promise<TwoFactorSetup> {
    try {
      // إنشاء مفتاح سري جديد
      const secret = this.generateSecret();
      
      // إنشاء رمز QR
      const qrCode = await this.generateQRCode(userId, secret);
      
      // إنشاء رموز النسخ الاحتياطي
      const backupCodes = this.generateBackupCodes();

      // حفظ الإعدادات في قاعدة البيانات (غير مفعل بعد)
      await this.saveTwoFactorConfig(userId, secret, backupCodes, false);

      return {
        secret,
        qrCode,
        backupCodes
      };

    } catch (error) {
      console.error('Error setting up 2FA:', error);
      throw new Error('فشل في إعداد التحقق الثنائي');
    }
  }

  /**
   * تفعيل التحقق الثنائي بعد التحقق من الرمز
   */
  async enableTwoFactor(userId: string, verificationCode: string): Promise<boolean> {
    try {
      // الحصول على إعدادات المستخدم
      const config = await this.getTwoFactorConfig(userId);
      if (!config) {
        throw new Error('لم يتم العثور على إعدادات التحقق الثنائي');
      }

      // التحقق من الرمز
      const isValid = this.verifyTOTP(config.secret_key, verificationCode);
      if (!isValid) {
        throw new Error('رمز التحقق غير صحيح');
      }

      // تفعيل التحقق الثنائي
      const { error } = await supabase
        .from('user_2fa_config')
        .update({ 
          is_enabled: true,
          last_used_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (error) throw error;

      // تسجيل حدث أمني
      await this.logSecurityEvent(userId, '2FA_ENABLED', 'Two-factor authentication enabled');

      return true;

    } catch (error) {
      console.error('Error enabling 2FA:', error);
      throw error;
    }
  }

  /**
   * إلغاء تفعيل التحقق الثنائي
   */
  async disableTwoFactor(userId: string, password: string): Promise<boolean> {
    try {
      // التحقق من كلمة المرور أولاً (يجب تطبيقه)
      // const isPasswordValid = await this.verifyPassword(userId, password);
      // if (!isPasswordValid) {
      //   throw new Error('كلمة المرور غير صحيحة');
      // }

      // إلغاء تفعيل التحقق الثنائي
      const { error } = await supabase
        .from('user_2fa_config')
        .update({ is_enabled: false })
        .eq('user_id', userId);

      if (error) throw error;

      // تسجيل حدث أمني
      await this.logSecurityEvent(userId, '2FA_DISABLED', 'Two-factor authentication disabled');

      return true;

    } catch (error) {
      console.error('Error disabling 2FA:', error);
      throw error;
    }
  }

  /**
   * التحقق من رمز TOTP
   */
  verifyTOTP(secret: string, token: string): boolean {
    try {
      // محاكاة التحقق من TOTP (يجب استخدام مكتبة مثل otplib)
      const currentTime = Math.floor(Date.now() / 1000);
      const timeStep = 30; // 30 ثانية
      const window = 1; // نافذة تسامح

      for (let i = -window; i <= window; i++) {
        const time = currentTime + (i * timeStep);
        const expectedToken = this.generateTOTP(secret, time);
        
        if (expectedToken === token) {
          return true;
        }
      }

      return false;

    } catch (error) {
      console.error('Error verifying TOTP:', error);
      return false;
    }
  }

  /**
   * إرسال رمز التحقق عبر البريد الإلكتروني
   */
  async sendEmailVerificationCode(userId: string, email: string): Promise<boolean> {
    try {
      // إنشاء رمز التحقق
      const code = this.generateEmailCode();
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 10); // ينتهي خلال 10 دقائق

      // حفظ الرمز في قاعدة البيانات
      const { error } = await supabase
        .from('email_verification_codes')
        .insert([{
          user_id: userId,
          code,
          expires_at: expiresAt.toISOString(),
          attempts: 0,
          created_at: new Date().toISOString()
        }]);

      if (error) throw error;

      // إرسال البريد الإلكتروني (محاكاة - يجب تطبيق خدمة إرسال حقيقية)
      console.log(`📧 إرسال رمز التحقق ${code} إلى ${email}`);
      
      // في التطبيق الحقيقي، استخدم خدمة مثل SendGrid أو AWS SES
      // await this.sendEmail(email, 'رمز التحقق', `رمز التحقق الخاص بك: ${code}`);

      return true;

    } catch (error) {
      console.error('Error sending email verification code:', error);
      throw new Error('فشل في إرسال رمز التحقق');
    }
  }

  /**
   * التحقق من رمز البريد الإلكتروني
   */
  async verifyEmailCode(userId: string, code: string): Promise<boolean> {
    try {
      // البحث عن الرمز
      const { data: verificationData, error: fetchError } = await supabase
        .from('email_verification_codes')
        .select('*')
        .eq('user_id', userId)
        .eq('code', code)
        .gt('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (fetchError || !verificationData) {
        throw new Error('رمز التحقق غير صحيح أو منتهي الصلاحية');
      }

      // التحقق من عدد المحاولات
      if (verificationData.attempts >= 3) {
        throw new Error('تم تجاوز عدد المحاولات المسموح');
      }

      // تحديث عدد المحاولات
      await supabase
        .from('email_verification_codes')
        .update({ attempts: verificationData.attempts + 1 })
        .eq('id', verificationData.id);

      // حذف الرمز بعد الاستخدام
      await supabase
        .from('email_verification_codes')
        .delete()
        .eq('id', verificationData.id);

      return true;

    } catch (error) {
      console.error('Error verifying email code:', error);
      throw error;
    }
  }

  /**
   * فحص ما إذا كان التحقق الثنائي مطلوب للمستخدم
   */
  async isTwoFactorRequired(user: User): Promise<boolean> {
    try {
      // التحقق من إعدادات المستخدم
      const config = await this.getTwoFactorConfig(user.id);
      if (config?.is_enabled) {
        return true;
      }

      // التحقق من الأدوار التي تتطلب 2FA إلزامياً
      const requiredRoles = ['admin', 'school_manager'];
      return requiredRoles.includes(user.role);

    } catch (error) {
      console.error('Error checking 2FA requirement:', error);
      return false;
    }
  }

  /**
   * الحصول على إعدادات التحقق الثنائي للمستخدم
   */
  private async getTwoFactorConfig(userId: string): Promise<TwoFactorConfig | null> {
    try {
      const { data, error } = await supabase
        .from('user_2fa_config')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) return null;
      return data as TwoFactorConfig;

    } catch (error) {
      console.error('Error getting 2FA config:', error);
      return null;
    }
  }

  /**
   * حفظ إعدادات التحقق الثنائي
   */
  private async saveTwoFactorConfig(
    userId: string, 
    secret: string, 
    backupCodes: string[], 
    isEnabled: boolean
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('user_2fa_config')
        .upsert([{
          user_id: userId,
          secret_key: secret,
          backup_codes: backupCodes,
          is_enabled: isEnabled,
          created_at: new Date().toISOString()
        }]);

      if (error) throw error;

    } catch (error) {
      console.error('Error saving 2FA config:', error);
      throw error;
    }
  }

  /**
   * إنشاء مفتاح سري
   */
  private generateSecret(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    let secret = '';
    for (let i = 0; i < 32; i++) {
      secret += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return secret;
  }

  /**
   * إنشاء رمز QR
   */
  private async generateQRCode(userId: string, secret: string): Promise<string> {
    try {
      // في التطبيق الحقيقي، استخدم مكتبة مثل qrcode
      const issuer = 'School Bus Management';
      const accountName = `user-${userId}`;
      const otpAuthUrl = `otpauth://totp/${encodeURIComponent(issuer)}:${encodeURIComponent(accountName)}?secret=${secret}&issuer=${encodeURIComponent(issuer)}`;
      
      // محاكاة إنشاء QR code
      return `data:image/svg+xml;base64,${btoa(`<svg>QR Code for: ${otpAuthUrl}</svg>`)}`;

    } catch (error) {
      console.error('Error generating QR code:', error);
      throw error;
    }
  }

  /**
   * إنشاء رموز النسخ الاحتياطي
   */
  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();
      codes.push(code);
    }
    return codes;
  }

  /**
   * إنشاء رمز TOTP
   */
  private generateTOTP(secret: string, time: number): string {
    // محاكاة إنشاء TOTP (يجب استخدام مكتبة مثل otplib)
    const hash = this.simpleHash(secret + time.toString());
    return (hash % 1000000).toString().padStart(6, '0');
  }

  /**
   * إنشاء رمز البريد الإلكتروني
   */
  private generateEmailCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * دالة hash بسيطة (للمحاكاة فقط)
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * تسجيل حدث أمني
   */
  private async logSecurityEvent(userId: string, eventType: string, description: string): Promise<void> {
    try {
      await supabase.rpc('log_security_event', {
        event_type: eventType,
        severity: 'INFO',
        description,
        user_id: userId,
        tenant_id: null,
        metadata: { timestamp: new Date().toISOString() }
      });
    } catch (error) {
      console.error('Error logging security event:', error);
    }
  }
}
