import React from "react";
import { useTranslation } from "react-i18next";
import { MapPin, Clock, CheckCircle, Circle, Navigation } from "lucide-react";
import type { Tables } from "../../lib/api";

interface RouteProgressProps {
  route: Tables<"routes"> & {
    stops?: Tables<"route_stops">[];
  };
  currentStopId?: string;
  completedStopIds?: string[];
  estimatedArrival?: { [stopId: string]: string };
  className?: string;
}

export const RouteProgress: React.FC<RouteProgressProps> = ({
  route,
  currentStopId,
  completedStopIds = [],
  estimatedArrival = {},
  className = "",
}) => {
  const { t } = useTranslation();

  if (!route.stops || route.stops.length === 0) {
    return (
      <div
        className={`text-center py-8 text-gray-500 dark:text-gray-400 ${className}`}
      >
        <MapPin size={48} className="mx-auto mb-4 opacity-50" />
        <p>{t("routes.noStops")}</p>
      </div>
    );
  }

  const sortedStops = [...route.stops].sort((a, b) => a.order - b.order);
  const currentStopIndex = currentStopId
    ? sortedStops.findIndex((stop) => stop.id === currentStopId)
    : -1;

  const getStopStatus = (stop: Tables<"route_stops">, index: number) => {
    if (completedStopIds.includes(stop.id)) {
      return "completed";
    }
    if (stop.id === currentStopId) {
      return "current";
    }
    if (currentStopIndex >= 0 && index < currentStopIndex) {
      return "completed";
    }
    return "upcoming";
  };

  const getStopIcon = (status: string) => {
    switch (status) {
      case "completed":
        return (
          <CheckCircle
            size={20}
            className="text-accent-600 dark:text-accent-400"
          />
        );
      case "current":
        return (
          <Navigation
            size={20}
            className="text-primary-600 dark:text-primary-400"
          />
        );
      default:
        return <Circle size={20} className="text-gray-400" />;
    }
  };

  const getStopStyles = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-accent-50 dark:bg-accent-900/20 border-accent-200 dark:border-accent-800";
      case "current":
        return "bg-primary-50 dark:bg-primary-900/20 border-primary-200 dark:border-primary-800 ring-2 ring-primary-500/20";
      default:
        return "bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700";
    }
  };

  const getConnectorStyles = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-accent-500";
      case "current":
        return "bg-gradient-to-b from-accent-500 to-gray-300";
      default:
        return "bg-gray-300 dark:bg-gray-600";
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {t("routes.routeProgress")}
        </h3>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {completedStopIds.length} / {sortedStops.length}{" "}
          {t("routes.stopsCompleted")}
        </div>
      </div>

      <div className="relative">
        {sortedStops.map((stop, index) => {
          const status = getStopStatus(stop, index);
          const isLast = index === sortedStops.length - 1;

          return (
            <div key={stop.id} className="relative">
              {/* Connector Line */}
              {!isLast && (
                <div className="absolute left-6 top-12 w-0.5 h-8 z-0">
                  <div
                    className={`w-full h-full ${getConnectorStyles(status)}`}
                  />
                </div>
              )}

              {/* Stop Item */}
              <div
                className={`relative flex items-start space-x-4 p-4 rounded-lg border transition-all duration-200 ${getStopStyles(status)}`}
              >
                <div className="flex-shrink-0 z-10">{getStopIcon(status)}</div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4
                        className={`text-sm font-medium ${
                          status === "completed"
                            ? "text-accent-900 dark:text-accent-100"
                            : status === "current"
                              ? "text-primary-900 dark:text-primary-100"
                              : "text-gray-900 dark:text-white"
                        }`}
                      >
                        {stop.name}
                      </h4>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {t("routes.stop")} {stop.order}
                      </p>
                    </div>

                    <div className="text-right">
                      {stop.arrival_time && (
                        <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                          <Clock size={12} />
                          <span>{stop.arrival_time}</span>
                        </div>
                      )}
                      {estimatedArrival[stop.id] && (
                        <div className="text-xs text-primary-600 dark:text-primary-400 mt-1">
                          {t("tracking.eta")}: {estimatedArrival[stop.id]}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Status Badge */}
                  <div className="mt-2">
                    <span
                      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        status === "completed"
                          ? "bg-accent-100 text-accent-800 dark:bg-accent-800 dark:text-accent-100"
                          : status === "current"
                            ? "bg-primary-100 text-primary-800 dark:bg-primary-800 dark:text-primary-100"
                            : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                      }`}
                    >
                      {status === "completed" && t("routes.completed")}
                      {status === "current" && t("routes.current")}
                      {status === "upcoming" && t("routes.upcoming")}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Progress Bar */}
      <div className="mt-6">
        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
          <span>{t("routes.progress")}</span>
          <span>
            {Math.round((completedStopIds.length / sortedStops.length) * 100)}%
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="bg-accent-500 h-2 rounded-full transition-all duration-500 ease-out"
            style={{
              width: `${(completedStopIds.length / sortedStops.length) * 100}%`,
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default RouteProgress;
