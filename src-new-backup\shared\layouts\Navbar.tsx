import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";
import {
  Menu,
  X,
  User,
  Settings,
  LogOut,
  Sun,
  Moon,
  ChevronDown,
} from "lucide-react";
import { Button } from "../ui/Button";
import { NotificationBadge } from "../notifications/NotificationBadge";
import { NotificationCenter } from "../notifications/NotificationCenter";
import { useTheme } from "../../contexts/ThemeContext";
import { useAuth } from "../../contexts/AuthContext";
import { useNotifications } from "../../contexts/NotificationsContext";

export const Navbar: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { theme, toggleTheme, direction, setDirection } = useTheme();
  const { user, logout } = useAuth();
  const { notifications } = useNotifications();
  const location = useLocation();

  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);

  const toggleMobileMenu = () => setIsMobileMenuOpen(!isMobileMenuOpen);
  const toggleProfileDropdown = () => {
    setIsProfileDropdownOpen(!isProfileDropdownOpen);
    if (isNotificationsOpen) setIsNotificationsOpen(false);
  };
  const toggleNotifications = () => {
    setIsNotificationsOpen(!isNotificationsOpen);
    if (isProfileDropdownOpen) setIsProfileDropdownOpen(false);
  };

  const toggleLanguage = () => {
    const newLang = i18n.language === "en" ? "ar" : "en";
    const newDir = newLang === "ar" ? "rtl" : "ltr";
    i18n.changeLanguage(newLang);
    setDirection(newDir);
  };

  const unreadNotifications = notifications.filter((n) => !n.read);

  return (
    <header className="bg-white dark:bg-gray-800 shadow-sm z-50 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link to="/" className="flex items-center gap-2">
              <div className="h-8 w-8 bg-primary-500 text-white rounded flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M8 6v6"></path>
                  <path d="M16 6v6"></path>
                  <path d="M2 12h20"></path>
                  <path d="M18 18h2a2 2 0 0 0 2-2v-6a8 8 0 0 0-16 0v6a2 2 0 0 0 2 2h2"></path>
                  <path d="M9 19h6"></path>
                  <path d="M9 21v-2"></path>
                  <path d="M15 21v-2"></path>
                </svg>
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white tracking-tight">
                {t("app.name")}
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-4">
            <Link
              to="/dashboard"
              className={`px-3 py-2 text-sm font-medium rounded-md ${
                location.pathname === "/dashboard"
                  ? "text-primary-700 dark:text-primary-400 bg-primary-50 dark:bg-gray-700"
                  : "text-gray-700 dark:text-gray-200 hover:text-primary-600 hover:bg-gray-100 dark:hover:bg-gray-700"
              }`}
            >
              {t("nav.dashboard")}
            </Link>
            <Link
              to="/dashboard/schools"
              className={`px-3 py-2 text-sm font-medium rounded-md ${
                location.pathname.startsWith("/dashboard/schools")
                  ? "text-primary-700 dark:text-primary-400 bg-primary-50 dark:bg-gray-700"
                  : "text-gray-700 dark:text-gray-200 hover:text-primary-600 hover:bg-gray-100 dark:hover:bg-gray-700"
              }`}
            >
              {t("nav.schools")}
            </Link>
            <Link
              to="/dashboard/buses"
              className={`px-3 py-2 text-sm font-medium rounded-md ${
                location.pathname.startsWith("/dashboard/buses")
                  ? "text-primary-700 dark:text-primary-400 bg-primary-50 dark:bg-gray-700"
                  : "text-gray-700 dark:text-gray-200 hover:text-primary-600 hover:bg-gray-100 dark:hover:bg-gray-700"
              }`}
            >
              {t("nav.buses")}
            </Link>
            <Link
              to="/dashboard/routes"
              className={`px-3 py-2 text-sm font-medium rounded-md ${
                location.pathname.startsWith("/dashboard/routes")
                  ? "text-primary-700 dark:text-primary-400 bg-primary-50 dark:bg-gray-700"
                  : "text-gray-700 dark:text-gray-200 hover:text-primary-600 hover:bg-gray-100 dark:hover:bg-gray-700"
              }`}
            >
              {t("nav.routes")}
            </Link>
            <Link
              to="/dashboard/students"
              className={`px-3 py-2 text-sm font-medium rounded-md ${
                location.pathname.startsWith("/dashboard/students")
                  ? "text-primary-700 dark:text-primary-400 bg-primary-50 dark:bg-gray-700"
                  : "text-gray-700 dark:text-gray-200 hover:text-primary-600 hover:bg-gray-100 dark:hover:bg-gray-700"
              }`}
            >
              {t("nav.students")}
            </Link>
          </nav>

          <div className="flex items-center">
            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              aria-label={
                theme === "dark"
                  ? "Switch to light mode"
                  : "Switch to dark mode"
              }
            >
              {theme === "dark" ? <Sun size={20} /> : <Moon size={20} />}
            </button>

            {/* Language Toggle */}
            <button
              onClick={toggleLanguage}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 ml-2"
            >
              <span className="text-sm font-medium">
                {i18n.language === "en" ? "عربي" : "EN"}
              </span>
            </button>

            {/* Notifications */}
            <div className="relative ml-2">
              <NotificationBadge
                onClick={toggleNotifications}
                className=""
                size="md"
                showCount={true}
              />

              <NotificationCenter
                isOpen={isNotificationsOpen}
                onClose={() => setIsNotificationsOpen(false)}
              />
            </div>

            {/* Profile Dropdown */}
            <div className="relative ml-3">
              <button
                onClick={toggleProfileDropdown}
                className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white focus:outline-none"
              >
                <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                  {user?.avatar ? (
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <User size={20} className="text-gray-500" />
                  )}
                </div>
                <span className="hidden md:block">{user?.name}</span>
                <ChevronDown
                  size={16}
                  className={`transform transition-transform ${isProfileDropdownOpen ? "rotate-180" : ""}`}
                />
              </button>

              {isProfileDropdownOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50 border border-gray-200 dark:border-gray-700 animate-fade-in">
                  <Link
                    to="/dashboard/profile"
                    className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
                  >
                    <User size={16} className="mr-2" />
                    {t("nav.profile")}
                  </Link>
                  <Link
                    to="/dashboard/settings"
                    className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
                  >
                    <Settings size={16} className="mr-2" />
                    {t("nav.settings")}
                  </Link>
                  <button
                    onClick={logout}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
                  >
                    <LogOut size={16} className="mr-2" />
                    {t("auth.signOut")}
                  </button>
                </div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <div className="flex md:hidden ml-2">
              <button
                onClick={toggleMobileMenu}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:text-gray-300 dark:hover:bg-gray-700"
              >
                <span className="sr-only">Open main menu</span>
                {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 animate-slide-in">
          <div className="px-2 pt-2 pb-3 space-y-1">
            <Link
              to="/dashboard"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                location.pathname === "/dashboard"
                  ? "text-primary-700 dark:text-primary-400 bg-primary-50 dark:bg-gray-700"
                  : "text-gray-700 dark:text-gray-200 hover:text-primary-600 hover:bg-gray-100 dark:hover:bg-gray-700"
              }`}
              onClick={toggleMobileMenu}
            >
              {t("nav.dashboard")}
            </Link>
            <Link
              to="/dashboard/schools"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                location.pathname.startsWith("/dashboard/schools")
                  ? "text-primary-700 dark:text-primary-400 bg-primary-50 dark:bg-gray-700"
                  : "text-gray-700 dark:text-gray-200 hover:text-primary-600 hover:bg-gray-100 dark:hover:bg-gray-700"
              }`}
              onClick={toggleMobileMenu}
            >
              {t("nav.schools")}
            </Link>
            <Link
              to="/dashboard/buses"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                location.pathname.startsWith("/dashboard/buses")
                  ? "text-primary-700 dark:text-primary-400 bg-primary-50 dark:bg-gray-700"
                  : "text-gray-700 dark:text-gray-200 hover:text-primary-600 hover:bg-gray-100 dark:hover:bg-gray-700"
              }`}
              onClick={toggleMobileMenu}
            >
              {t("nav.buses")}
            </Link>
            <Link
              to="/dashboard/routes"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                location.pathname.startsWith("/dashboard/routes")
                  ? "text-primary-700 dark:text-primary-400 bg-primary-50 dark:bg-gray-700"
                  : "text-gray-700 dark:text-gray-200 hover:text-primary-600 hover:bg-gray-100 dark:hover:bg-gray-700"
              }`}
              onClick={toggleMobileMenu}
            >
              {t("nav.routes")}
            </Link>
            <Link
              to="/dashboard/students"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                location.pathname.startsWith("/dashboard/students")
                  ? "text-primary-700 dark:text-primary-400 bg-primary-50 dark:bg-gray-700"
                  : "text-gray-700 dark:text-gray-200 hover:text-primary-600 hover:bg-gray-100 dark:hover:bg-gray-700"
              }`}
              onClick={toggleMobileMenu}
            >
              {t("nav.students")}
            </Link>
          </div>
        </div>
      )}
    </header>
  );
};
