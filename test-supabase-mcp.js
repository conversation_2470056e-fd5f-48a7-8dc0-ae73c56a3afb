import { spawn } from 'child_process';

// Start the MCP server
const server = spawn('cmd', [
  '/c',
  'npx',
  '-y',
  '@supabase/mcp-server-supabase@latest',
  '--access-token',
  '********************************************'
], {
  stdio: ['pipe', 'pipe', 'pipe']
});

let messageId = 1;
const pendingRequests = new Map();

server.stdout.on('data', (data) => {
  const lines = data.toString().split('\n');
  
  for (const line of lines) {
    if (line.trim()) {
      try {
        const response = JSON.parse(line.trim());
        console.log(`\n📨 Server Response (ID: ${response.id}):`);
        console.log(JSON.stringify(response, null, 2));
        
        if (pendingRequests.has(response.id)) {
          const handler = pendingRequests.get(response.id);
          handler(response);
          pendingRequests.delete(response.id);
        }
      } catch (e) {
        // Not valid JSON, might be partial data
      }
    }
  }
});

server.stderr.on('data', (data) => {
  console.error('❌ Server error:', data.toString());
});

function sendMessage(message, handler) {
  message.id = messageId++;
  if (handler) {
    pendingRequests.set(message.id, handler);
  }
  
  console.log(`\n📤 Sending message (ID: ${message.id}): ${message.method}`);
  server.stdin.write(JSON.stringify(message) + '\n');
  return message.id;
}

// Step 1: Initialize the server
console.log('🚀 Starting Supabase MCP Server test...');

sendMessage({
  jsonrpc: "2.0",
  method: "initialize",
  params: {
    protocolVersion: "2024-11-05",
    capabilities: { tools: {} },
    clientInfo: { name: "supabase-test-client", version: "1.0.0" }
  }
}, (response) => {
  if (response.result) {
    console.log('✅ Server initialized successfully!');
    console.log(`   Server: ${response.result.serverInfo.name} v${response.result.serverInfo.version}`);
    
    // Step 2: List available tools
    setTimeout(() => {
      sendMessage({
        jsonrpc: "2.0",
        method: "tools/list"
      }, (toolsResponse) => {
        if (toolsResponse.result && toolsResponse.result.tools) {
          console.log(`\n🛠️  Available tools (${toolsResponse.result.tools.length}):`);
          toolsResponse.result.tools.forEach((tool, index) => {
            console.log(`   ${index + 1}. ${tool.name} - ${tool.description}`);
          });
          
          // Step 3: Try to call list_projects tool
          setTimeout(() => {
            sendMessage({
              jsonrpc: "2.0",
              method: "tools/call",
              params: {
                name: "list_projects",
                arguments: {}
              }
            }, (projectsResponse) => {
              if (projectsResponse.result) {
                console.log('\n🏗️  Supabase Projects:');
                if (projectsResponse.result.content && projectsResponse.result.content.length > 0) {
                  const projects = JSON.parse(projectsResponse.result.content[0].text);
                  projects.forEach((project, index) => {
                    console.log(`   ${index + 1}. ${project.name} (${project.id})`);
                    console.log(`      Status: ${project.status}`);
                    console.log(`      Region: ${project.region}`);
                  });
                } else {
                  console.log('   No projects found or empty response');
                }
              } else if (projectsResponse.error) {
                console.log('❌ Error calling list_projects:', projectsResponse.error.message);
              }
              
              // Clean up
              setTimeout(() => {
                console.log('\n🏁 Test completed. Terminating server...');
                server.kill();
                process.exit(0);
              }, 1000);
            });
          }, 1000);
        } else {
          console.log('❌ Failed to get tools list');
        }
      });
    }, 1000);
  } else {
    console.log('❌ Server initialization failed:', response.error);
  }
});

// Cleanup after 15 seconds if something goes wrong
setTimeout(() => {
  console.log('\n⏰ Timeout reached. Terminating server...');
  server.kill();
  process.exit(1);
}, 15000);