/**
 * سكريبت اختبار ميزات المرحلة الأولى
 * Phase 1 Features Testing Script
 */

import { createClient } from '@supabase/supabase-js';
import { PasswordStrengthValidator } from '../src/utils/passwordStrength';
import { BruteForceProtectionService } from '../src/services/security/BruteForceProtection';
import { TwoFactorAuthService } from '../src/services/security/TwoFactorAuth';
import { AnomalyDetectionService } from '../src/services/security/AnomalyDetection';
import { SessionManagementService } from '../src/services/security/SessionManagement';
import * as dotenv from 'dotenv';

// تحميل متغيرات البيئة
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface TestResult {
  testName: string;
  passed: boolean;
  details: string;
  duration: number;
}

class Phase1Tester {
  private results: TestResult[] = [];

  /**
   * تشغيل اختبار واحد
   */
  private async runTest(testName: string, testFunction: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    console.log(`🧪 اختبار: ${testName}`);
    
    try {
      await testFunction();
      const duration = Date.now() - startTime;
      this.results.push({
        testName,
        passed: true,
        details: 'نجح الاختبار',
        duration
      });
      console.log(`✅ نجح: ${testName} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        testName,
        passed: false,
        details: error instanceof Error ? error.message : 'خطأ غير معروف',
        duration
      });
      console.log(`❌ فشل: ${testName} - ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * اختبار نظام فحص قوة كلمة المرور
   */
  async testPasswordStrength(): Promise<void> {
    console.log('\n🔒 اختبار نظام فحص قوة كلمة المرور...');

    await this.runTest('كلمة مرور ضعيفة', async () => {
      const result = PasswordStrengthValidator.validatePassword('123');
      if (result.isValid || result.score > 30) {
        throw new Error('كلمة المرور الضعيفة لم يتم رفضها');
      }
    });

    await this.runTest('كلمة مرور قوية', async () => {
      const result = PasswordStrengthValidator.validatePassword('MyStr0ng!P@ssw0rd2024');
      if (!result.isValid || result.score < 80) {
        throw new Error('كلمة المرور القوية لم يتم قبولها');
      }
    });

    await this.runTest('كلمة مرور محظورة', async () => {
      const result = PasswordStrengthValidator.validatePassword('password123');
      if (result.isValid || result.score > 50) {
        throw new Error('كلمة المرور المحظورة لم يتم رفضها');
      }
    });

    await this.runTest('متطلبات كلمة المرور', async () => {
      const result = PasswordStrengthValidator.validatePassword('Test123!@#');
      if (!result.requirements.hasUppercase || !result.requirements.hasLowercase || 
          !result.requirements.hasNumbers || !result.requirements.hasSpecialChars) {
        throw new Error('فحص المتطلبات لا يعمل بشكل صحيح');
      }
    });
  }

  /**
   * اختبار حماية Brute-force
   */
  async testBruteForceProtection(): Promise<void> {
    console.log('\n🛡️ اختبار حماية Brute-force...');

    const bruteForceService = BruteForceProtectionService.getInstance();
    const testEmail = '<EMAIL>';
    const testIP = '*************';
    const testUserAgent = 'Test Browser';

    await this.runTest('تسجيل محاولة ناجحة', async () => {
      await bruteForceService.recordAttempt(testEmail, testIP, testUserAgent, true);
    });

    await this.runTest('تسجيل محاولة فاشلة', async () => {
      await bruteForceService.recordAttempt(testEmail, testIP, testUserAgent, false);
    });

    await this.runTest('فحص الحظر قبل الوصول للحد', async () => {
      const blockCheck = await bruteForceService.isBlocked(testEmail, testIP);
      if (blockCheck.isBlocked) {
        throw new Error('تم حظر المستخدم قبل الوصول للحد الأقصى');
      }
    });

    await this.runTest('محاولات متعددة فاشلة', async () => {
      for (let i = 0; i < 5; i++) {
        await bruteForceService.recordAttempt(`test${i}@example.com`, testIP, testUserAgent, false);
      }
    });

    await this.runTest('الحصول على إحصائيات الحماية', async () => {
      const stats = await bruteForceService.getProtectionStats();
      if (typeof stats.totalAttempts !== 'number' || typeof stats.failedAttempts !== 'number') {
        throw new Error('إحصائيات الحماية غير صحيحة');
      }
    });
  }

  /**
   * اختبار التحقق الثنائي
   */
  async testTwoFactorAuth(): Promise<void> {
    console.log('\n🔐 اختبار التحقق الثنائي...');

    const twoFactorService = TwoFactorAuthService.getInstance();
    const testUserId = 'test-user-id';

    await this.runTest('إعداد التحقق الثنائي', async () => {
      const setup = await twoFactorService.setupTwoFactor(testUserId);
      if (!setup.secret || !setup.qrCode || !setup.backupCodes || setup.backupCodes.length !== 10) {
        throw new Error('إعداد التحقق الثنائي غير مكتمل');
      }
    });

    await this.runTest('التحقق من رمز TOTP', async () => {
      const isValid = twoFactorService.verifyTOTP('TESTSECRET123456789012345678901234', '123456');
      // هذا اختبار للدالة نفسها، النتيجة قد تكون true أو false
      if (typeof isValid !== 'boolean') {
        throw new Error('دالة التحقق من TOTP لا تعمل');
      }
    });

    await this.runTest('إرسال رمز البريد الإلكتروني', async () => {
      try {
        await twoFactorService.sendEmailVerificationCode(testUserId, '<EMAIL>');
      } catch (error) {
        // متوقع أن يفشل في البيئة التجريبية
        if (!error.message.includes('فشل في إرسال رمز التحقق')) {
          throw error;
        }
      }
    });
  }

  /**
   * اختبار مراقبة السلوك الشاذ
   */
  async testAnomalyDetection(): Promise<void> {
    console.log('\n🕵️ اختبار مراقبة السلوك الشاذ...');

    const anomalyService = AnomalyDetectionService.getInstance();
    const testUserId = 'test-user-id';

    await this.runTest('تحليل محاولة دخول عادية', async () => {
      const context = {
        ip_address: '***********',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        device_fingerprint: 'normal-device',
        login_time: new Date()
      };

      const result = await anomalyService.analyzeLoginAttempt(testUserId, context);
      if (typeof result.isAnomalous !== 'boolean' || typeof result.riskScore !== 'number') {
        throw new Error('تحليل السلوك لا يعمل بشكل صحيح');
      }
    });

    await this.runTest('تحليل محاولة دخول مشبوهة', async () => {
      const context = {
        ip_address: '*******', // IP مختلف
        user_agent: 'Suspicious Browser',
        device_fingerprint: 'suspicious-device',
        login_time: new Date(Date.now() - 1000 * 60 * 60 * 3) // 3 صباحاً
      };

      const result = await anomalyService.analyzeLoginAttempt(testUserId, context);
      // قد يكون مشبوه أو لا، المهم أن الدالة تعمل
      if (typeof result.riskScore !== 'number' || result.riskScore < 0 || result.riskScore > 100) {
        throw new Error('نقاط المخاطر غير صحيحة');
      }
    });

    await this.runTest('الحصول على التنبيهات', async () => {
      const alerts = await anomalyService.getUnresolvedAlerts();
      if (!Array.isArray(alerts)) {
        throw new Error('قائمة التنبيهات غير صحيحة');
      }
    });
  }

  /**
   * اختبار إدارة الجلسات
   */
  async testSessionManagement(): Promise<void> {
    console.log('\n💻 اختبار إدارة الجلسات...');

    const sessionService = SessionManagementService.getInstance();
    const testUserId = 'test-user-id';
    const testToken = 'test-session-token-' + Date.now();

    await this.runTest('إنشاء جلسة جديدة', async () => {
      const session = await sessionService.createSession(
        testUserId,
        testToken,
        '***********',
        'Mozilla/5.0 Test Browser'
      );

      if (!session.id || session.user_id !== testUserId) {
        throw new Error('فشل في إنشاء الجلسة');
      }
    });

    await this.runTest('تحديث نشاط الجلسة', async () => {
      const updated = await sessionService.updateSessionActivity(testToken);
      if (!updated) {
        throw new Error('فشل في تحديث نشاط الجلسة');
      }
    });

    await this.runTest('التحقق من صحة الجلسة', async () => {
      const validation = await sessionService.validateSession(testToken);
      if (!validation.isValid || !validation.session) {
        throw new Error('فشل في التحقق من صحة الجلسة');
      }
    });

    await this.runTest('الحصول على الجلسات النشطة', async () => {
      const sessions = await sessionService.getUserActiveSessions(testUserId);
      if (!Array.isArray(sessions)) {
        throw new Error('قائمة الجلسات غير صحيحة');
      }
    });

    await this.runTest('إنهاء الجلسة', async () => {
      const terminated = await sessionService.terminateSession(testToken, 'Test termination');
      if (!terminated) {
        throw new Error('فشل في إنهاء الجلسة');
      }
    });

    await this.runTest('الحصول على إحصائيات الجلسات', async () => {
      const stats = await sessionService.getSessionStats();
      if (typeof stats.totalActiveSessions !== 'number' || 
          typeof stats.uniqueActiveUsers !== 'number') {
        throw new Error('إحصائيات الجلسات غير صحيحة');
      }
    });
  }

  /**
   * اختبار قاعدة البيانات
   */
  async testDatabase(): Promise<void> {
    console.log('\n🗄️ اختبار قاعدة البيانات...');

    await this.runTest('اختبار جدول login_attempts', async () => {
      const { data, error } = await supabase
        .from('login_attempts')
        .select('count')
        .limit(1);
      
      if (error) throw new Error(`خطأ في جدول login_attempts: ${error.message}`);
    });

    await this.runTest('اختبار جدول user_2fa_config', async () => {
      const { data, error } = await supabase
        .from('user_2fa_config')
        .select('count')
        .limit(1);
      
      if (error) throw new Error(`خطأ في جدول user_2fa_config: ${error.message}`);
    });

    await this.runTest('اختبار جدول anomaly_alerts', async () => {
      const { data, error } = await supabase
        .from('anomaly_alerts')
        .select('count')
        .limit(1);
      
      if (error) throw new Error(`خطأ في جدول anomaly_alerts: ${error.message}`);
    });

    await this.runTest('اختبار دالة log_security_event', async () => {
      const { data, error } = await supabase.rpc('log_security_event', {
        event_type: 'TEST_EVENT',
        severity: 'INFO',
        description: 'Test security event from automated testing',
        user_id: null,
        tenant_id: null,
        metadata: { test: true }
      });
      
      if (error) throw new Error(`خطأ في دالة log_security_event: ${error.message}`);
    });
  }

  /**
   * تشغيل جميع الاختبارات
   */
  async runAllTests(): Promise<void> {
    console.log('🚀 بدء اختبار جميع ميزات المرحلة الأولى...\n');

    const startTime = Date.now();

    // تشغيل جميع الاختبارات
    await this.testPasswordStrength();
    await this.testBruteForceProtection();
    await this.testTwoFactorAuth();
    await this.testAnomalyDetection();
    await this.testSessionManagement();
    await this.testDatabase();

    const totalTime = Date.now() - startTime;

    // تقرير النتائج
    this.generateReport(totalTime);
  }

  /**
   * إنشاء تقرير النتائج
   */
  private generateReport(totalTime: number): void {
    console.log('\n📊 تقرير نتائج الاختبار:');
    console.log('=' .repeat(50));

    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => r.passed === false).length;
    const successRate = (passed / this.results.length) * 100;

    console.log(`✅ نجح: ${passed}`);
    console.log(`❌ فشل: ${failed}`);
    console.log(`📈 معدل النجاح: ${successRate.toFixed(1)}%`);
    console.log(`⏱️ الوقت الإجمالي: ${totalTime}ms`);

    if (failed > 0) {
      console.log('\n❌ الاختبارات الفاشلة:');
      this.results
        .filter(r => !r.passed)
        .forEach(result => {
          console.log(`  • ${result.testName}: ${result.details}`);
        });
    }

    console.log('\n📋 تفاصيل جميع الاختبارات:');
    this.results.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${result.testName} (${result.duration}ms)`);
    });

    if (successRate >= 90) {
      console.log('\n🎉 ممتاز! جميع الميزات تعمل بشكل صحيح');
    } else if (successRate >= 70) {
      console.log('\n⚠️ جيد، لكن هناك بعض المشاكل التي تحتاج إصلاح');
    } else {
      console.log('\n🚨 تحذير: هناك مشاكل كبيرة تحتاج إصلاح فوري');
    }
  }
}

/**
 * الدالة الرئيسية
 */
async function main(): Promise<void> {
  const tester = new Phase1Tester();
  await tester.runAllTests();
}

// تشغيل الاختبارات
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { Phase1Tester };
