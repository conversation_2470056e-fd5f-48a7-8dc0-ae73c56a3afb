{"name": "school-bus-management-saas", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:watch": "vitest --watch", "test:permissions": "vitest src/tests/permissionService.test.ts", "setup:phase1": "tsx scripts/setup-phase1.ts", "security:audit": "tsx scripts/security-audit.ts", "permissions:validate": "tsx scripts/validate-permissions.ts", "fix:final": "tsx scripts/apply-final-fix.ts", "phase1:security": "tsx scripts/apply-phase1-security.ts", "test:phase1": "tsx scripts/test-phase1-features.ts", "phase2:backup": "tsx scripts/phase2-backup-system.ts", "phase2:cleanup": "tsx scripts/phase2-database-cleanup.ts", "phase2:restructure": "tsx scripts/phase2-code-restructure.ts", "phase2:master": "tsx scripts/phase2-master.ts", "migrate": "node scripts/migration-system.cjs", "switch:new": "node scripts/switch-structure.cjs new", "switch:old": "node scripts/switch-structure.cjs old", "switch:status": "node scripts/switch-structure.cjs status", "switch:cleanup": "node scripts/switch-structure.cjs cleanup", "test:comprehensive": "node scripts/comprehensive-testing.cjs", "test:interactive": "node scripts/interactive-testing.cjs", "test:app": "npm run test:comprehensive && npm run test:interactive", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "optimize": "node scripts/performance-optimizer.cjs", "enhance": "node scripts/feature-enhancer.cjs", "optimize:build": "npm run optimize && npm run build", "enhance:all": "npm run enhance && npm run optimize", "security:analyze": "node scripts/security-analyzer.cjs", "security:rls": "node scripts/rls-builder.cjs", "security:zero-trust": "node scripts/zero-trust-builder.cjs", "security:test": "node scripts/security-testing.cjs", "security:full": "npm run security:analyze && npm run security:rls && npm run security:zero-trust && npm run security:test", "db:migrate": "node scripts/apply-migrations.cjs", "db:validate": "node scripts/migration-validator.cjs", "db:deploy": "node scripts/quick-deploy.cjs", "db:setup": "npm run security:rls && npm run db:validate && npm run db:deploy", "analyze": "npm run build && npx vite-bundle-analyzer dist"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-toast": "^1.1.5", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.49.5", "@types/mapbox-gl": "^3.4.1", "child_process": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "dotenv": "^16.5.0", "html2canvas": "^1.4.1", "i18next": "^23.10.0", "i18next-browser-languagedetector": "^7.2.0", "jspdf": "^3.0.1", "lucide-react": "^0.344.0", "mapbox-gl": "^3.12.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^14.0.5", "react-map-gl": "^7.1.9", "react-router-dom": "^6.22.2", "readline": "^1.3.0", "recharts": "^2.15.3", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/ui": "^1.2.0", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^26.1.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "tempo-devtools": "^2.0.106", "tsx": "^4.7.0", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.19", "vitest": "^1.6.1"}, "description": "", "main": "demo-supabase-mcp.js", "directories": {"doc": "docs"}, "keywords": [], "author": "", "license": "ISC", "sideEffects": false}