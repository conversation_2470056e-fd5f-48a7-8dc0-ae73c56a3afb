/**
 * School Logo Component
 * Displays school logo and branding
 * Phase 3: UI/UX Enhancement - Logo Display
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useCustomTheme } from '../../contexts/CustomThemeContext';
import { ThemeService } from '../../services/ThemeService';
import { Building2, Image as ImageIcon } from 'lucide-react';

interface SchoolLogoProps {
  size?: 'small' | 'medium' | 'large';
  showName?: boolean;
  showTagline?: boolean;
  className?: string;
}

export const SchoolLogo: React.FC<SchoolLogoProps> = ({
  size = 'medium',
  showName = true,
  showTagline = false,
  className = '',
}) => {
  const { user, tenant } = useAuth();
  const { theme } = useCustomTheme();
  const [logoUrl, setLogoUrl] = useState<string | null>(null);
  const [schoolName, setSchoolName] = useState<string>('');
  const [tagline, setTagline] = useState<string>('');
  const [imageError, setImageError] = useState(false);

  // Update data when theme or tenant changes
  useEffect(() => {
    if (theme?.branding) {
      // Use theme data first
      setLogoUrl(theme.branding.logo || null);
      setSchoolName(theme.branding.schoolName || tenant?.name || 'مدرستي');
      setTagline(theme.branding.tagline || 'التعليم هو المستقبل');
    } else if (tenant) {
      // Fallback to tenant data
      setSchoolName(tenant.name || 'مدرستي');
      setTagline('التعليم هو المستقبل');
      setLogoUrl(tenant.logo_url || null);
    } else {
      // Default values
      setSchoolName('مدرستي');
      setTagline('التعليم هو المستقبل');
      setLogoUrl(null);
    }
  }, [theme, tenant]);

  // Size configurations
  const sizeConfig = {
    small: {
      logo: 'w-8 h-8',
      name: 'text-sm font-semibold',
      tagline: 'text-xs',
      container: 'gap-2',
    },
    medium: {
      logo: 'w-12 h-12',
      name: 'text-lg font-bold',
      tagline: 'text-sm',
      container: 'gap-3',
    },
    large: {
      logo: 'w-16 h-16',
      name: 'text-2xl font-bold',
      tagline: 'text-base',
      container: 'gap-4',
    },
  };

  const config = sizeConfig[size];

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div className={`flex items-center ${config.container} ${className}`}>
      {/* Logo */}
      <div className={`${config.logo} flex-shrink-0`}>
        {logoUrl && !imageError ? (
          <img
            src={logoUrl}
            alt={`شعار ${schoolName}`}
            className={`${config.logo} object-contain rounded-lg`}
            onError={handleImageError}
            onLoad={() => setImageError(false)}
          />
        ) : (
          <div className={`${config.logo} bg-blue-600 text-white rounded-lg flex items-center justify-center`}>
            <Building2 size={size === 'small' ? 16 : size === 'medium' ? 20 : 24} />
          </div>
        )}
      </div>

      {/* School Name and Tagline */}
      {(showName || showTagline) && (
        <div className="flex flex-col">
          {showName && (
            <span className={`${config.name} text-gray-900 leading-tight`}>
              {schoolName}
            </span>
          )}
          {showTagline && tagline && (
            <span className={`${config.tagline} text-gray-600 leading-tight`}>
              {tagline}
            </span>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * School Logo for Login Page
 */
export const LoginLogo: React.FC = () => {
  return (
    <div className="text-center mb-8">
      <SchoolLogo 
        size="large" 
        showName={true} 
        showTagline={true}
        className="justify-center"
      />
    </div>
  );
};

/**
 * School Logo for Sidebar
 */
export const SidebarLogo: React.FC = () => {
  return (
    <SchoolLogo 
      size="medium" 
      showName={true} 
      showTagline={false}
      className="justify-start"
    />
  );
};

/**
 * School Logo for Header
 */
export const HeaderLogo: React.FC = () => {
  return (
    <SchoolLogo 
      size="small" 
      showName={true} 
      showTagline={false}
      className="justify-start"
    />
  );
};
