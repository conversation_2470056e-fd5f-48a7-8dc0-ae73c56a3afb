import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Calendar,
  TrendingUp,
  TrendingDown,
  Users,
  AlertTriangle,
  Filter,
  Download,
  Search,
  BarChart3,
} from "lucide-react";
import { Button } from "../ui/Button";
import { AttendanceStats } from "./AttendanceStats";
import { StudentAttendanceHistory } from "./StudentAttendanceHistory";
import { supabase } from "../../lib/supabase";
import { useDatabase } from "../../contexts/DatabaseContext";
import type { Tables } from "../../lib/api";

interface AttendanceTrend {
  date: string;
  present: number;
  absent: number;
  total: number;
  rate: number;
}

interface StudentAttendanceData {
  student: Tables<"students">;
  attendanceRate: number;
  totalDays: number;
  presentDays: number;
  absentDays: number;
  lastAttendance?: string;
  isFrequentAbsent: boolean;
}

export const AttendanceDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { students, routes } = useDatabase();
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    end: new Date().toISOString().split("T")[0],
  });
  const [filterRoute, setFilterRoute] = useState<string>("all");
  const [filterGrade, setFilterGrade] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStudent, setSelectedStudent] = useState<string | null>(null);

  const [attendanceTrends, setAttendanceTrends] = useState<AttendanceTrend[]>(
    [],
  );
  const [studentData, setStudentData] = useState<StudentAttendanceData[]>([]);
  const [overallStats, setOverallStats] = useState({
    totalStudents: 0,
    averageAttendanceRate: 0,
    frequentAbsentees: 0,
    perfectAttendance: 0,
  });

  useEffect(() => {
    fetchAttendanceData();
  }, [dateRange, students]);

  const fetchAttendanceData = async () => {
    try {
      setLoading(true);

      const startDate = new Date(dateRange.start);
      startDate.setHours(0, 0, 0, 0);

      const endDate = new Date(dateRange.end);
      endDate.setHours(23, 59, 59, 999);

      // Fetch all attendance records in the date range
      const { data: attendanceRecords, error } = await supabase
        .from("attendance")
        .select("*")
        .gte("recorded_at", startDate.toISOString())
        .lte("recorded_at", endDate.toISOString());

      if (error) throw error;

      // Calculate trends by day
      const trends = calculateDailyTrends(
        attendanceRecords || [],
        startDate,
        endDate,
      );
      setAttendanceTrends(trends);

      // Calculate student-specific data
      const studentAttendanceData = await calculateStudentData(
        attendanceRecords || [],
        startDate,
        endDate,
      );
      setStudentData(studentAttendanceData);

      // Calculate overall statistics
      calculateOverallStats(studentAttendanceData);
    } catch (error) {
      console.error("Error fetching attendance data:", error);
    } finally {
      setLoading(false);
    }
  };

  const calculateDailyTrends = (
    records: Tables<"attendance">[],
    startDate: Date,
    endDate: Date,
  ): AttendanceTrend[] => {
    const trends: AttendanceTrend[] = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const dayStart = new Date(currentDate);
      dayStart.setHours(0, 0, 0, 0);

      const dayEnd = new Date(currentDate);
      dayEnd.setHours(23, 59, 59, 999);

      const dayRecords = records.filter((record) => {
        const recordDate = new Date(record.recorded_at);
        return recordDate >= dayStart && recordDate <= dayEnd;
      });

      const uniqueStudents = new Set(dayRecords.map((r) => r.student_id));
      const present = uniqueStudents.size;
      const total = students.length;
      const absent = total - present;
      const rate = total > 0 ? (present / total) * 100 : 0;

      // Only include weekdays (assuming school days)
      if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) {
        trends.push({
          date: currentDate.toISOString().split("T")[0],
          present,
          absent,
          total,
          rate,
        });
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    return trends;
  };

  const calculateStudentData = async (
    records: Tables<"attendance">[],
    startDate: Date,
    endDate: Date,
  ): Promise<StudentAttendanceData[]> => {
    const studentAttendanceData: StudentAttendanceData[] = [];

    // Calculate school days in the range
    let schoolDays = 0;
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) {
        schoolDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    for (const student of students) {
      const studentRecords = records.filter((r) => r.student_id === student.id);

      // Count unique days with attendance
      const attendanceDays = new Set(
        studentRecords.map((r) => new Date(r.recorded_at).toDateString()),
      ).size;

      const attendanceRate =
        schoolDays > 0 ? (attendanceDays / schoolDays) * 100 : 0;
      const absentDays = schoolDays - attendanceDays;

      // Check if student is frequently absent (less than 80% attendance)
      const isFrequentAbsent = attendanceRate < 80;

      // Get last attendance date
      const lastRecord = studentRecords.sort(
        (a, b) =>
          new Date(b.recorded_at).getTime() - new Date(a.recorded_at).getTime(),
      )[0];

      studentAttendanceData.push({
        student,
        attendanceRate,
        totalDays: schoolDays,
        presentDays: attendanceDays,
        absentDays,
        lastAttendance: lastRecord?.recorded_at,
        isFrequentAbsent,
      });
    }

    return studentAttendanceData.sort(
      (a, b) => a.attendanceRate - b.attendanceRate,
    );
  };

  const calculateOverallStats = (data: StudentAttendanceData[]) => {
    const totalStudents = data.length;
    const averageAttendanceRate =
      totalStudents > 0
        ? data.reduce((sum, student) => sum + student.attendanceRate, 0) /
          totalStudents
        : 0;
    const frequentAbsentees = data.filter(
      (student) => student.isFrequentAbsent,
    ).length;
    const perfectAttendance = data.filter(
      (student) => student.attendanceRate === 100,
    ).length;

    setOverallStats({
      totalStudents,
      averageAttendanceRate,
      frequentAbsentees,
      perfectAttendance,
    });
  };

  const filteredStudentData = studentData.filter((data) => {
    const student = data.student;

    // Filter by route
    if (filterRoute !== "all") {
      const studentRoute = routes.find((route) => {
        if (!route.stops || !Array.isArray(route.stops)) {
          return false;
        }
        return route.stops.some((stop) => stop.id === student.route_stop_id);
      });
      if (!studentRoute || studentRoute.id !== filterRoute) return false;
    }

    // Filter by grade
    if (filterGrade !== "all" && student.grade !== filterGrade) return false;

    // Filter by search query
    if (
      searchQuery &&
      !student.name.toLowerCase().includes(searchQuery.toLowerCase())
    ) {
      return false;
    }

    return true;
  });

  const exportData = () => {
    const headers = [
      "Student Name",
      "Grade",
      "Route",
      "Attendance Rate",
      "Present Days",
      "Absent Days",
      "Total Days",
      "Last Attendance",
      "Status",
    ];

    const csvContent = [
      headers.join(","),
      ...filteredStudentData.map((data) => {
        const route = routes.find((r) => {
          if (!r.stops || !Array.isArray(r.stops)) {
            return false;
          }
          return r.stops.some((s) => s.id === data.student.route_stop_id);
        });

        return [
          data.student.name,
          data.student.grade,
          route?.name || "N/A",
          `${data.attendanceRate.toFixed(1)}%`,
          data.presentDays,
          data.absentDays,
          data.totalDays,
          data.lastAttendance
            ? new Date(data.lastAttendance).toLocaleDateString()
            : "N/A",
          data.isFrequentAbsent ? "Frequent Absent" : "Good",
        ].join(",");
      }),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `attendance_dashboard_${dateRange.start}_to_${dateRange.end}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t("students.attendanceDashboard")}
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t("students.attendanceDashboardDescription")}
          </p>
        </div>
        <Button
          onClick={exportData}
          variant="outline"
          leftIcon={<Download size={16} />}
          disabled={filteredStudentData.length === 0}
        >
          {t("common.export")}
        </Button>
      </div>

      {/* Date Range Filter */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("common.startDate")}
            </label>
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) =>
                setDateRange((prev) => ({ ...prev, start: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("common.endDate")}
            </label>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) =>
                setDateRange((prev) => ({ ...prev, end: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>
        </div>
      </div>

      {/* Overall Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-primary-100 dark:bg-primary-800/20 rounded-lg">
              <Users className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("students.totalStudents")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {overallStats.totalStudents}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-accent-100 dark:bg-accent-800/20 rounded-lg">
              <TrendingUp className="h-6 w-6 text-accent-600 dark:text-accent-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("students.averageAttendance")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {overallStats.averageAttendanceRate.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-error-100 dark:bg-error-800/20 rounded-lg">
              <AlertTriangle className="h-6 w-6 text-error-600 dark:text-error-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("students.frequentAbsentees")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {overallStats.frequentAbsentees}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-secondary-100 dark:bg-secondary-800/20 rounded-lg">
              <BarChart3 className="h-6 w-6 text-secondary-600 dark:text-secondary-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("students.perfectAttendance")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {overallStats.perfectAttendance}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Attendance Trends Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {t("students.attendanceTrends")}
        </h3>
        <div className="h-64 flex items-end space-x-1">
          {attendanceTrends.map((trend, index) => (
            <div key={trend.date} className="flex-1 flex flex-col items-center">
              <div
                className="w-full bg-gray-200 dark:bg-gray-700 rounded-t"
                style={{ height: "200px" }}
              >
                <div
                  className="bg-accent-500 rounded-t transition-all duration-300"
                  style={{
                    height: `${(trend.rate / 100) * 200}px`,
                    width: "100%",
                  }}
                  title={`${trend.date}: ${trend.rate.toFixed(1)}% (${trend.present}/${trend.total})`}
                />
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 transform -rotate-45 origin-top-left">
                {new Date(trend.date).toLocaleDateString(undefined, {
                  month: "short",
                  day: "numeric",
                })}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={16} className="text-gray-400" />
              </div>
              <input
                type="text"
                placeholder={t("students.searchStudents")}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={filterRoute}
              onChange={(e) => setFilterRoute(e.target.value)}
              className="block px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="all">{t("common.all")} Routes</option>
              {routes.map((route) => (
                <option key={route.id} value={route.id}>
                  {route.name}
                </option>
              ))}
            </select>
            <select
              value={filterGrade}
              onChange={(e) => setFilterGrade(e.target.value)}
              className="block px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="all">{t("common.all")} Grades</option>
              {[1, 2, 3, 4, 5, 6].map((grade) => (
                <option key={grade} value={grade.toString()}>
                  Grade {grade}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Student Attendance List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t("students.studentAttendance")} ({filteredStudentData.length})
          </h3>
        </div>

        <div className="divide-y divide-gray-200 dark:divide-gray-700 max-h-96 overflow-y-auto">
          {filteredStudentData.map((data) => {
            const route = routes.find((r) => {
              if (!r.stops || !Array.isArray(r.stops)) {
                return false;
              }
              return r.stops.some((s) => s.id === data.student.route_stop_id);
            });

            return (
              <div
                key={data.student.id}
                className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                onClick={() =>
                  setSelectedStudent(
                    selectedStudent === data.student.id
                      ? null
                      : data.student.id,
                  )
                }
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0 h-10 w-10">
                      {data.student.photo_url ? (
                        <img
                          src={data.student.photo_url}
                          alt={data.student.name}
                          className="h-10 w-10 rounded-full object-cover"
                          onError={(e) => {
                            e.currentTarget.style.display = "none";
                            e.currentTarget.nextElementSibling?.classList.remove(
                              "hidden",
                            );
                          }}
                        />
                      ) : null}
                      <div
                        className={`h-10 w-10 bg-primary-100 dark:bg-primary-800/20 rounded-full flex items-center justify-center text-primary-600 dark:text-primary-400 ${data.student.photo_url ? "hidden" : ""}`}
                      >
                        <Users size={20} />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {data.student.name}
                        </p>
                        {data.isFrequentAbsent && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-error-100 text-error-800 dark:bg-error-900/20 dark:text-error-400">
                            <AlertTriangle size={12} className="mr-1" />
                            {t("students.frequentAbsent")}
                          </span>
                        )}
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Grade {data.student.grade} • {route?.name || "No Route"}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div
                      className={`text-lg font-semibold ${
                        data.attendanceRate >= 90
                          ? "text-green-600 dark:text-green-400"
                          : data.attendanceRate >= 80
                            ? "text-yellow-600 dark:text-yellow-400"
                            : "text-red-600 dark:text-red-400"
                      }`}
                    >
                      {data.attendanceRate.toFixed(1)}%
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {data.presentDays}/{data.totalDays} days
                    </div>
                  </div>
                </div>

                {selectedStudent === data.student.id && (
                  <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <StudentAttendanceHistory
                      studentId={data.student.id}
                      studentName={data.student.name}
                    />
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default AttendanceDashboard;
