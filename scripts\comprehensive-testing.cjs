/**
 * نظام الاختبار الشامل للتطبيق
 * Comprehensive Application Testing System
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🧪 بدء نظام الاختبار الشامل للتطبيق...\n');

class ComprehensiveTestingSystem {
  constructor() {
    this.testResults = [];
    this.errors = [];
    this.warnings = [];
    this.stats = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0,
      warningTests: 0
    };
  }

  /**
   * تنفيذ الاختبار الشامل
   */
  async executeComprehensiveTesting() {
    console.log('🚀 بدء الاختبار الشامل...\n');

    try {
      // اختبار البنية الأساسية
      await this.testBasicStructure();
      
      // اختبار ملفات التكوين
      await this.testConfigurationFiles();
      
      // اختبار مسارات الاستيراد
      await this.testImportPaths();
      
      // اختبار المكونات الأساسية
      await this.testCoreComponents();
      
      // اختبار الصفحات الرئيسية
      await this.testMainPages();
      
      // اختبار الخدمات
      await this.testServices();
      
      // اختبار قاعدة البيانات
      await this.testDatabaseConnections();
      
      // اختبار الأمان
      await this.testSecurityFeatures();
      
      // إنشاء تقرير الاختبار
      await this.generateTestReport();
      
      console.log('\n✅ تم إكمال الاختبار الشامل بنجاح!');
      this.showTestSummary();
      
    } catch (error) {
      console.error('❌ خطأ في نظام الاختبار:', error);
      this.stats.failedTests++;
    }
  }

  /**
   * اختبار البنية الأساسية
   */
  async testBasicStructure() {
    console.log('📁 اختبار البنية الأساسية...');

    const requiredDirectories = [
      'src/components',
      'src/pages',
      'src/contexts',
      'src/services',
      'src/lib',
      'src/hooks',
      'src/types',
      'src/utils',
      'src/config',
      'src/i18n'
    ];

    for (const dir of requiredDirectories) {
      this.stats.totalTests++;
      if (fs.existsSync(dir)) {
        this.testResults.push({
          test: `البنية الأساسية - ${dir}`,
          status: 'passed',
          message: 'المجلد موجود'
        });
        this.stats.passedTests++;
        console.log(`  ✅ ${dir} - موجود`);
      } else {
        this.testResults.push({
          test: `البنية الأساسية - ${dir}`,
          status: 'failed',
          message: 'المجلد مفقود'
        });
        this.stats.failedTests++;
        console.log(`  ❌ ${dir} - مفقود`);
      }
    }
  }

  /**
   * اختبار ملفات التكوين
   */
  async testConfigurationFiles() {
    console.log('⚙️ اختبار ملفات التكوين...');

    const configFiles = [
      { file: 'package.json', required: true },
      { file: 'vite.config.ts', required: true },
      { file: 'tsconfig.json', required: true },
      { file: 'tailwind.config.js', required: true },
      { file: '.env.example', required: false },
      { file: 'src/main.tsx', required: true },
      { file: 'src/App.tsx', required: true },
      { file: 'src/index.css', required: true }
    ];

    for (const { file, required } of configFiles) {
      this.stats.totalTests++;
      if (fs.existsSync(file)) {
        this.testResults.push({
          test: `ملفات التكوين - ${file}`,
          status: 'passed',
          message: 'الملف موجود'
        });
        this.stats.passedTests++;
        console.log(`  ✅ ${file} - موجود`);
      } else {
        const status = required ? 'failed' : 'warning';
        this.testResults.push({
          test: `ملفات التكوين - ${file}`,
          status: status,
          message: required ? 'ملف مطلوب مفقود' : 'ملف اختياري مفقود'
        });
        
        if (required) {
          this.stats.failedTests++;
          console.log(`  ❌ ${file} - مفقود (مطلوب)`);
        } else {
          this.stats.warningTests++;
          console.log(`  ⚠️ ${file} - مفقود (اختياري)`);
        }
      }
    }
  }

  /**
   * اختبار مسارات الاستيراد
   */
  async testImportPaths() {
    console.log('🔗 اختبار مسارات الاستيراد...');

    const filesToCheck = [
      'src/main.tsx',
      'src/App.tsx'
    ];

    for (const file of filesToCheck) {
      this.stats.totalTests++;
      if (fs.existsSync(file)) {
        try {
          const content = fs.readFileSync(file, 'utf8');
          const imports = this.extractImports(content);
          let validImports = 0;
          let invalidImports = 0;

          for (const importPath of imports) {
            if (this.validateImportPath(importPath, file)) {
              validImports++;
            } else {
              invalidImports++;
            }
          }

          if (invalidImports === 0) {
            this.testResults.push({
              test: `مسارات الاستيراد - ${file}`,
              status: 'passed',
              message: `جميع المسارات صحيحة (${validImports})`
            });
            this.stats.passedTests++;
            console.log(`  ✅ ${file} - جميع المسارات صحيحة (${validImports})`);
          } else {
            this.testResults.push({
              test: `مسارات الاستيراد - ${file}`,
              status: 'failed',
              message: `${invalidImports} مسار غير صحيح من أصل ${imports.length}`
            });
            this.stats.failedTests++;
            console.log(`  ❌ ${file} - ${invalidImports} مسار غير صحيح`);
          }
        } catch (error) {
          this.testResults.push({
            test: `مسارات الاستيراد - ${file}`,
            status: 'failed',
            message: `خطأ في قراءة الملف: ${error.message}`
          });
          this.stats.failedTests++;
          console.log(`  ❌ ${file} - خطأ في القراءة`);
        }
      } else {
        this.testResults.push({
          test: `مسارات الاستيراد - ${file}`,
          status: 'failed',
          message: 'الملف غير موجود'
        });
        this.stats.failedTests++;
        console.log(`  ❌ ${file} - غير موجود`);
      }
    }
  }

  /**
   * اختبار المكونات الأساسية
   */
  async testCoreComponents() {
    console.log('🔧 اختبار المكونات الأساسية...');

    const coreComponents = [
      'src/contexts/AuthContext.tsx',
      'src/contexts/ThemeContext.tsx',
      'src/contexts/DatabaseContext.tsx',
      'src/contexts/NotificationsContext.tsx',
      'src/contexts/CustomThemeContext.tsx'
    ];

    for (const component of coreComponents) {
      this.stats.totalTests++;
      if (fs.existsSync(component)) {
        try {
          const content = fs.readFileSync(component, 'utf8');
          
          // فحص أساسي للمحتوى
          if (content.includes('createContext') || content.includes('Provider')) {
            this.testResults.push({
              test: `المكونات الأساسية - ${component}`,
              status: 'passed',
              message: 'المكون يحتوي على Context صحيح'
            });
            this.stats.passedTests++;
            console.log(`  ✅ ${path.basename(component)} - صحيح`);
          } else {
            this.testResults.push({
              test: `المكونات الأساسية - ${component}`,
              status: 'warning',
              message: 'المكون قد لا يحتوي على Context صحيح'
            });
            this.stats.warningTests++;
            console.log(`  ⚠️ ${path.basename(component)} - تحذير`);
          }
        } catch (error) {
          this.testResults.push({
            test: `المكونات الأساسية - ${component}`,
            status: 'failed',
            message: `خطأ في قراءة الملف: ${error.message}`
          });
          this.stats.failedTests++;
          console.log(`  ❌ ${path.basename(component)} - خطأ`);
        }
      } else {
        this.testResults.push({
          test: `المكونات الأساسية - ${component}`,
          status: 'failed',
          message: 'الملف غير موجود'
        });
        this.stats.failedTests++;
        console.log(`  ❌ ${path.basename(component)} - مفقود`);
      }
    }
  }

  /**
   * اختبار الصفحات الرئيسية
   */
  async testMainPages() {
    console.log('📄 اختبار الصفحات الرئيسية...');

    const mainPages = [
      'src/pages/login/LoginPage.tsx',
      'src/pages/dashboard/DashboardPage.tsx',
      'src/pages/dashboard/BusesPage.tsx',
      'src/pages/dashboard/StudentsPage.tsx',
      'src/pages/dashboard/RoutesPage.tsx'
    ];

    for (const page of mainPages) {
      this.stats.totalTests++;
      if (fs.existsSync(page)) {
        try {
          const content = fs.readFileSync(page, 'utf8');
          
          // فحص أساسي للمحتوى
          if (content.includes('export') && (content.includes('function') || content.includes('const'))) {
            this.testResults.push({
              test: `الصفحات الرئيسية - ${page}`,
              status: 'passed',
              message: 'الصفحة تحتوي على مكون صحيح'
            });
            this.stats.passedTests++;
            console.log(`  ✅ ${path.basename(page)} - صحيحة`);
          } else {
            this.testResults.push({
              test: `الصفحات الرئيسية - ${page}`,
              status: 'warning',
              message: 'الصفحة قد لا تحتوي على مكون صحيح'
            });
            this.stats.warningTests++;
            console.log(`  ⚠️ ${path.basename(page)} - تحذير`);
          }
        } catch (error) {
          this.testResults.push({
            test: `الصفحات الرئيسية - ${page}`,
            status: 'failed',
            message: `خطأ في قراءة الملف: ${error.message}`
          });
          this.stats.failedTests++;
          console.log(`  ❌ ${path.basename(page)} - خطأ`);
        }
      } else {
        this.testResults.push({
          test: `الصفحات الرئيسية - ${page}`,
          status: 'failed',
          message: 'الملف غير موجود'
        });
        this.stats.failedTests++;
        console.log(`  ❌ ${path.basename(page)} - مفقود`);
      }
    }
  }

  /**
   * اختبار الخدمات
   */
  async testServices() {
    console.log('⚙️ اختبار الخدمات...');

    const services = [
      'src/lib/supabase.ts',
      'src/services/CentralizedPermissionService.ts',
      'src/services/DatabaseService.ts',
      'src/services/TenantService.ts'
    ];

    for (const service of services) {
      this.stats.totalTests++;
      if (fs.existsSync(service)) {
        this.testResults.push({
          test: `الخدمات - ${service}`,
          status: 'passed',
          message: 'الخدمة موجودة'
        });
        this.stats.passedTests++;
        console.log(`  ✅ ${path.basename(service)} - موجودة`);
      } else {
        this.testResults.push({
          test: `الخدمات - ${service}`,
          status: 'failed',
          message: 'الخدمة مفقودة'
        });
        this.stats.failedTests++;
        console.log(`  ❌ ${path.basename(service)} - مفقودة`);
      }
    }
  }

  /**
   * اختبار قاعدة البيانات
   */
  async testDatabaseConnections() {
    console.log('🗄️ اختبار اتصالات قاعدة البيانات...');

    this.stats.totalTests++;
    
    // فحص ملف supabase
    if (fs.existsSync('src/lib/supabase.ts')) {
      try {
        const content = fs.readFileSync('src/lib/supabase.ts', 'utf8');
        
        if (content.includes('createClient') && content.includes('supabase')) {
          this.testResults.push({
            test: 'اتصال قاعدة البيانات - Supabase',
            status: 'passed',
            message: 'إعداد Supabase صحيح'
          });
          this.stats.passedTests++;
          console.log(`  ✅ Supabase - إعداد صحيح`);
        } else {
          this.testResults.push({
            test: 'اتصال قاعدة البيانات - Supabase',
            status: 'warning',
            message: 'إعداد Supabase قد يكون غير مكتمل'
          });
          this.stats.warningTests++;
          console.log(`  ⚠️ Supabase - تحذير في الإعداد`);
        }
      } catch (error) {
        this.testResults.push({
          test: 'اتصال قاعدة البيانات - Supabase',
          status: 'failed',
          message: `خطأ في قراءة ملف Supabase: ${error.message}`
        });
        this.stats.failedTests++;
        console.log(`  ❌ Supabase - خطأ في القراءة`);
      }
    } else {
      this.testResults.push({
        test: 'اتصال قاعدة البيانات - Supabase',
        status: 'failed',
        message: 'ملف Supabase غير موجود'
      });
      this.stats.failedTests++;
      console.log(`  ❌ Supabase - ملف غير موجود`);
    }
  }

  /**
   * اختبار الأمان
   */
  async testSecurityFeatures() {
    console.log('🔒 اختبار ميزات الأمان...');

    const securityFiles = [
      'src/services/security',
      'src/components/auth',
      'src/middleware/authMiddleware.ts'
    ];

    for (const securityItem of securityFiles) {
      this.stats.totalTests++;
      if (fs.existsSync(securityItem)) {
        this.testResults.push({
          test: `ميزات الأمان - ${securityItem}`,
          status: 'passed',
          message: 'مكون الأمان موجود'
        });
        this.stats.passedTests++;
        console.log(`  ✅ ${path.basename(securityItem)} - موجود`);
      } else {
        this.testResults.push({
          test: `ميزات الأمان - ${securityItem}`,
          status: 'warning',
          message: 'مكون الأمان مفقود'
        });
        this.stats.warningTests++;
        console.log(`  ⚠️ ${path.basename(securityItem)} - مفقود`);
      }
    }
  }

  /**
   * استخراج مسارات الاستيراد من الملف
   */
  extractImports(content) {
    const importRegex = /import.*from\s+['"`]([^'"`]+)['"`]/g;
    const imports = [];
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      imports.push(match[1]);
    }
    
    return imports;
  }

  /**
   * التحقق من صحة مسار الاستيراد
   */
  validateImportPath(importPath, fromFile) {
    // تجاهل المسارات الخارجية
    if (!importPath.startsWith('.')) {
      return true;
    }

    const fromDir = path.dirname(fromFile);
    const resolvedPath = path.resolve(fromDir, importPath);
    
    // فحص الملف مع امتدادات مختلفة
    const extensions = ['', '.ts', '.tsx', '.js', '.jsx'];
    
    for (const ext of extensions) {
      if (fs.existsSync(resolvedPath + ext)) {
        return true;
      }
    }
    
    // فحص إذا كان مجلد مع index
    const indexExtensions = ['/index.ts', '/index.tsx', '/index.js', '/index.jsx'];
    for (const indexExt of indexExtensions) {
      if (fs.existsSync(resolvedPath + indexExt)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * إنشاء تقرير الاختبار
   */
  async generateTestReport() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportDir = path.join(process.cwd(), 'reports', 'testing');
    
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const report = {
      test_info: {
        timestamp: timestamp,
        total_tests: this.stats.totalTests,
        passed_tests: this.stats.passedTests,
        failed_tests: this.stats.failedTests,
        warning_tests: this.stats.warningTests,
        skipped_tests: this.stats.skippedTests,
        success_rate: ((this.stats.passedTests / this.stats.totalTests) * 100).toFixed(1)
      },
      detailed_results: this.testResults,
      errors: this.errors,
      warnings: this.warnings,
      recommendations: this.generateRecommendations()
    };

    const reportPath = path.join(reportDir, `comprehensive-test-report-${timestamp}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log(`📊 تم إنشاء تقرير الاختبار: ${reportPath}`);
  }

  /**
   * إنشاء التوصيات
   */
  generateRecommendations() {
    const recommendations = [];

    if (this.stats.failedTests > 0) {
      recommendations.push('إصلاح الاختبارات الفاشلة أولاً');
    }

    if (this.stats.warningTests > 0) {
      recommendations.push('مراجعة التحذيرات وإصلاحها إن أمكن');
    }

    if (this.stats.passedTests === this.stats.totalTests) {
      recommendations.push('جميع الاختبارات نجحت - يمكن المتابعة للمرحلة التالية');
    }

    return recommendations;
  }

  /**
   * عرض ملخص الاختبار
   */
  showTestSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 ملخص الاختبار الشامل');
    console.log('='.repeat(60));
    console.log(`🧪 إجمالي الاختبارات: ${this.stats.totalTests}`);
    console.log(`✅ نجح: ${this.stats.passedTests}`);
    console.log(`❌ فشل: ${this.stats.failedTests}`);
    console.log(`⚠️ تحذيرات: ${this.stats.warningTests}`);
    console.log(`⏭️ تم تخطيه: ${this.stats.skippedTests}`);
    console.log(`📈 معدل النجاح: ${((this.stats.passedTests / this.stats.totalTests) * 100).toFixed(1)}%`);
    
    if (this.stats.failedTests === 0 && this.stats.warningTests === 0) {
      console.log('\n🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام.');
    } else if (this.stats.failedTests === 0) {
      console.log('\n✅ جميع الاختبارات الأساسية نجحت مع بعض التحذيرات.');
    } else {
      console.log('\n⚠️ هناك اختبارات فاشلة تحتاج إصلاح.');
    }
    
    console.log('\n🎯 الخطوات التالية:');
    if (this.stats.failedTests > 0) {
      console.log('1. إصلاح الاختبارات الفاشلة');
      console.log('2. إعادة تشغيل الاختبار');
    } else {
      console.log('1. مراجعة التحذيرات (إن وجدت)');
      console.log('2. اختبار التطبيق يدوياً');
      console.log('3. المتابعة للمرحلة التالية');
    }
    console.log('='.repeat(60));
  }
}

/**
 * الدالة الرئيسية
 */
async function main() {
  try {
    const testingSystem = new ComprehensiveTestingSystem();
    await testingSystem.executeComprehensiveTesting();
  } catch (error) {
    console.error('💥 خطأ في نظام الاختبار الشامل:', error);
    process.exit(1);
  }
}

// تشغيل النظام
main();
