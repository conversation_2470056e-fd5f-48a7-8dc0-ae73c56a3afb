/**
 * خدمة مراقبة السلوك الشاذ
 * Anomaly Detection Service
 * 
 * المرحلة 1: تأمين الدخول والسياسات الأمنية الأساسية
 */

import { supabase } from '../../lib/supabase';

export interface UserBehaviorPattern {
  user_id: string;
  typical_login_hours: number[];
  typical_locations: string[];
  typical_devices: string[];
  average_session_duration: number;
  last_updated: string;
}

export interface AnomalyAlert {
  id: string;
  user_id: string;
  anomaly_type: 'unusual_location' | 'unusual_time' | 'unusual_device' | 'suspicious_activity';
  severity: 'low' | 'medium' | 'high';
  description: string;
  metadata: any;
  is_resolved: boolean;
  created_at: string;
}

export interface LoginContext {
  ip_address: string;
  user_agent: string;
  location?: {
    country: string;
    city: string;
    timezone: string;
  };
  device_fingerprint: string;
  login_time: Date;
}

export class AnomalyDetectionService {
  private static instance: AnomalyDetectionService;

  private constructor() {}

  static getInstance(): AnomalyDetectionService {
    if (!AnomalyDetectionService.instance) {
      AnomalyDetectionService.instance = new AnomalyDetectionService();
    }
    return AnomalyDetectionService.instance;
  }

  /**
   * تحليل محاولة تسجيل الدخول للكشف عن السلوك الشاذ
   */
  async analyzeLoginAttempt(userId: string, context: LoginContext): Promise<{
    isAnomalous: boolean;
    alerts: AnomalyAlert[];
    riskScore: number;
  }> {
    try {
      const alerts: AnomalyAlert[] = [];
      let riskScore = 0;

      // الحصول على نمط سلوك المستخدم
      const behaviorPattern = await this.getUserBehaviorPattern(userId);
      
      if (!behaviorPattern) {
        // مستخدم جديد - إنشاء نمط أولي
        await this.createInitialBehaviorPattern(userId, context);
        return { isAnomalous: false, alerts: [], riskScore: 0 };
      }

      // فحص الموقع الجغرافي
      const locationAnomaly = await this.checkLocationAnomaly(behaviorPattern, context);
      if (locationAnomaly) {
        alerts.push(locationAnomaly);
        riskScore += 30;
      }

      // فحص توقيت تسجيل الدخول
      const timeAnomaly = await this.checkTimeAnomaly(behaviorPattern, context);
      if (timeAnomaly) {
        alerts.push(timeAnomaly);
        riskScore += 20;
      }

      // فحص الجهاز
      const deviceAnomaly = await this.checkDeviceAnomaly(behaviorPattern, context);
      if (deviceAnomaly) {
        alerts.push(deviceAnomaly);
        riskScore += 25;
      }

      // فحص النشاط المشبوه
      const activityAnomaly = await this.checkSuspiciousActivity(userId, context);
      if (activityAnomaly) {
        alerts.push(activityAnomaly);
        riskScore += 40;
      }

      // حفظ التنبيهات في قاعدة البيانات
      if (alerts.length > 0) {
        await this.saveAnomalyAlerts(alerts);
      }

      // تحديث نمط السلوك
      await this.updateBehaviorPattern(userId, context);

      return {
        isAnomalous: riskScore >= 50,
        alerts,
        riskScore: Math.min(100, riskScore)
      };

    } catch (error) {
      console.error('Error analyzing login attempt:', error);
      return { isAnomalous: false, alerts: [], riskScore: 0 };
    }
  }

  /**
   * فحص شذوذ الموقع الجغرافي
   */
  private async checkLocationAnomaly(
    pattern: UserBehaviorPattern, 
    context: LoginContext
  ): Promise<AnomalyAlert | null> {
    try {
      if (!context.location) return null;

      const currentLocation = `${context.location.country}-${context.location.city}`;
      
      // إذا كان الموقع غير مألوف
      if (!pattern.typical_locations.includes(currentLocation)) {
        // فحص إذا كان الموقع بعيد جداً عن المواقع المعتادة
        const isDistantLocation = await this.isLocationDistant(
          pattern.typical_locations, 
          context.location
        );

        if (isDistantLocation) {
          return {
            id: this.generateId(),
            user_id: pattern.user_id,
            anomaly_type: 'unusual_location',
            severity: 'high',
            description: `تسجيل دخول من موقع غير مألوف: ${currentLocation}`,
            metadata: {
              current_location: context.location,
              typical_locations: pattern.typical_locations,
              ip_address: context.ip_address
            },
            is_resolved: false,
            created_at: new Date().toISOString()
          };
        }
      }

      return null;

    } catch (error) {
      console.error('Error checking location anomaly:', error);
      return null;
    }
  }

  /**
   * فحص شذوذ التوقيت
   */
  private async checkTimeAnomaly(
    pattern: UserBehaviorPattern, 
    context: LoginContext
  ): Promise<AnomalyAlert | null> {
    try {
      const loginHour = context.login_time.getHours();
      
      // إذا كان التوقيت خارج الساعات المعتادة
      if (!pattern.typical_login_hours.includes(loginHour)) {
        // فحص إذا كان التوقيت غريب جداً (مثلاً 2-5 صباحاً)
        const isUnusualHour = loginHour >= 2 && loginHour <= 5;
        
        if (isUnusualHour || pattern.typical_login_hours.length > 0) {
          return {
            id: this.generateId(),
            user_id: pattern.user_id,
            anomaly_type: 'unusual_time',
            severity: isUnusualHour ? 'medium' : 'low',
            description: `تسجيل دخول في توقيت غير مألوف: ${loginHour}:00`,
            metadata: {
              login_hour: loginHour,
              typical_hours: pattern.typical_login_hours,
              is_unusual_hour: isUnusualHour
            },
            is_resolved: false,
            created_at: new Date().toISOString()
          };
        }
      }

      return null;

    } catch (error) {
      console.error('Error checking time anomaly:', error);
      return null;
    }
  }

  /**
   * فحص شذوذ الجهاز
   */
  private async checkDeviceAnomaly(
    pattern: UserBehaviorPattern, 
    context: LoginContext
  ): Promise<AnomalyAlert | null> {
    try {
      const deviceSignature = this.createDeviceSignature(context.user_agent, context.device_fingerprint);
      
      // إذا كان الجهاز غير مألوف
      if (!pattern.typical_devices.includes(deviceSignature)) {
        return {
          id: this.generateId(),
          user_id: pattern.user_id,
          anomaly_type: 'unusual_device',
          severity: 'medium',
          description: 'تسجيل دخول من جهاز غير مألوف',
          metadata: {
            device_signature: deviceSignature,
            user_agent: context.user_agent,
            device_fingerprint: context.device_fingerprint,
            typical_devices: pattern.typical_devices
          },
          is_resolved: false,
          created_at: new Date().toISOString()
        };
      }

      return null;

    } catch (error) {
      console.error('Error checking device anomaly:', error);
      return null;
    }
  }

  /**
   * فحص النشاط المشبوه
   */
  private async checkSuspiciousActivity(
    userId: string, 
    context: LoginContext
  ): Promise<AnomalyAlert | null> {
    try {
      // فحص محاولات متعددة من نفس IP
      const recentAttempts = await this.getRecentLoginAttempts(context.ip_address, 10);
      
      if (recentAttempts.length >= 5) {
        return {
          id: this.generateId(),
          user_id: userId,
          anomaly_type: 'suspicious_activity',
          severity: 'high',
          description: `محاولات دخول متعددة من نفس العنوان: ${context.ip_address}`,
          metadata: {
            ip_address: context.ip_address,
            attempt_count: recentAttempts.length,
            time_window: '10 minutes'
          },
          is_resolved: false,
          created_at: new Date().toISOString()
        };
      }

      // فحص تسجيل دخول متزامن من مواقع مختلفة
      const simultaneousLogins = await this.checkSimultaneousLogins(userId, context);
      if (simultaneousLogins) {
        return simultaneousLogins;
      }

      return null;

    } catch (error) {
      console.error('Error checking suspicious activity:', error);
      return null;
    }
  }

  /**
   * الحصول على نمط سلوك المستخدم
   */
  private async getUserBehaviorPattern(userId: string): Promise<UserBehaviorPattern | null> {
    try {
      const { data, error } = await supabase
        .from('user_behavior_patterns')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) return null;
      return data as UserBehaviorPattern;

    } catch (error) {
      console.error('Error getting user behavior pattern:', error);
      return null;
    }
  }

  /**
   * إنشاء نمط سلوك أولي للمستخدم الجديد
   */
  private async createInitialBehaviorPattern(userId: string, context: LoginContext): Promise<void> {
    try {
      const pattern: Partial<UserBehaviorPattern> = {
        user_id: userId,
        typical_login_hours: [context.login_time.getHours()],
        typical_locations: context.location ? [`${context.location.country}-${context.location.city}`] : [],
        typical_devices: [this.createDeviceSignature(context.user_agent, context.device_fingerprint)],
        average_session_duration: 3600, // ساعة واحدة افتراضياً
        last_updated: new Date().toISOString()
      };

      await supabase
        .from('user_behavior_patterns')
        .insert([pattern]);

    } catch (error) {
      console.error('Error creating initial behavior pattern:', error);
    }
  }

  /**
   * تحديث نمط سلوك المستخدم
   */
  private async updateBehaviorPattern(userId: string, context: LoginContext): Promise<void> {
    try {
      const pattern = await this.getUserBehaviorPattern(userId);
      if (!pattern) return;

      // تحديث الساعات المعتادة
      const loginHour = context.login_time.getHours();
      if (!pattern.typical_login_hours.includes(loginHour)) {
        pattern.typical_login_hours.push(loginHour);
        // الاحتفاظ بآخر 10 ساعات فقط
        if (pattern.typical_login_hours.length > 10) {
          pattern.typical_login_hours = pattern.typical_login_hours.slice(-10);
        }
      }

      // تحديث المواقع المعتادة
      if (context.location) {
        const currentLocation = `${context.location.country}-${context.location.city}`;
        if (!pattern.typical_locations.includes(currentLocation)) {
          pattern.typical_locations.push(currentLocation);
          // الاحتفاظ بآخر 5 مواقع فقط
          if (pattern.typical_locations.length > 5) {
            pattern.typical_locations = pattern.typical_locations.slice(-5);
          }
        }
      }

      // تحديث الأجهزة المعتادة
      const deviceSignature = this.createDeviceSignature(context.user_agent, context.device_fingerprint);
      if (!pattern.typical_devices.includes(deviceSignature)) {
        pattern.typical_devices.push(deviceSignature);
        // الاحتفاظ بآخر 5 أجهزة فقط
        if (pattern.typical_devices.length > 5) {
          pattern.typical_devices = pattern.typical_devices.slice(-5);
        }
      }

      pattern.last_updated = new Date().toISOString();

      await supabase
        .from('user_behavior_patterns')
        .update(pattern)
        .eq('user_id', userId);

    } catch (error) {
      console.error('Error updating behavior pattern:', error);
    }
  }

  /**
   * حفظ تنبيهات الشذوذ
   */
  private async saveAnomalyAlerts(alerts: AnomalyAlert[]): Promise<void> {
    try {
      await supabase
        .from('anomaly_alerts')
        .insert(alerts);

    } catch (error) {
      console.error('Error saving anomaly alerts:', error);
    }
  }

  /**
   * فحص إذا كان الموقع بعيد
   */
  private async isLocationDistant(typicalLocations: string[], currentLocation: any): Promise<boolean> {
    // محاكاة فحص المسافة الجغرافية
    // في التطبيق الحقيقي، استخدم API للمسافات الجغرافية
    return typicalLocations.length > 0 && !typicalLocations.some(loc => 
      loc.includes(currentLocation.country)
    );
  }

  /**
   * إنشاء توقيع الجهاز
   */
  private createDeviceSignature(userAgent: string, fingerprint: string): string {
    // إنشاء توقيع مبسط للجهاز
    const browser = this.extractBrowser(userAgent);
    const os = this.extractOS(userAgent);
    return `${browser}-${os}-${fingerprint.substring(0, 8)}`;
  }

  /**
   * استخراج نوع المتصفح
   */
  private extractBrowser(userAgent: string): string {
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  /**
   * استخراج نظام التشغيل
   */
  private extractOS(userAgent: string): string {
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'Mac';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  }

  /**
   * الحصول على محاولات تسجيل الدخول الأخيرة
   */
  private async getRecentLoginAttempts(ipAddress: string, minutes: number): Promise<any[]> {
    try {
      const timeThreshold = new Date();
      timeThreshold.setMinutes(timeThreshold.getMinutes() - minutes);

      const { data, error } = await supabase
        .from('login_attempts')
        .select('*')
        .eq('ip_address', ipAddress)
        .gte('attempted_at', timeThreshold.toISOString());

      return data || [];

    } catch (error) {
      console.error('Error getting recent login attempts:', error);
      return [];
    }
  }

  /**
   * فحص تسجيل الدخول المتزامن
   */
  private async checkSimultaneousLogins(userId: string, context: LoginContext): Promise<AnomalyAlert | null> {
    try {
      // فحص الجلسات النشطة للمستخدم
      const { data: activeSessions } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .gte('last_activity', new Date(Date.now() - 5 * 60 * 1000).toISOString()); // آخر 5 دقائق

      if (activeSessions && activeSessions.length > 0) {
        // فحص إذا كانت الجلسات من مواقع مختلفة
        const differentLocations = activeSessions.some(session => 
          session.ip_address !== context.ip_address
        );

        if (differentLocations) {
          return {
            id: this.generateId(),
            user_id: userId,
            anomaly_type: 'suspicious_activity',
            severity: 'high',
            description: 'تسجيل دخول متزامن من مواقع مختلفة',
            metadata: {
              current_ip: context.ip_address,
              active_sessions: activeSessions.map(s => ({ ip: s.ip_address, last_activity: s.last_activity }))
            },
            is_resolved: false,
            created_at: new Date().toISOString()
          };
        }
      }

      return null;

    } catch (error) {
      console.error('Error checking simultaneous logins:', error);
      return null;
    }
  }

  /**
   * إنشاء معرف فريد
   */
  private generateId(): string {
    return 'anomaly_' + Date.now().toString(36) + Math.random().toString(36).substring(2, 15);
  }

  /**
   * الحصول على التنبيهات غير المحلولة
   */
  async getUnresolvedAlerts(userId?: string): Promise<AnomalyAlert[]> {
    try {
      let query = supabase
        .from('anomaly_alerts')
        .select('*')
        .eq('is_resolved', false)
        .order('created_at', { ascending: false });

      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query;
      
      if (error) throw error;
      return data as AnomalyAlert[];

    } catch (error) {
      console.error('Error getting unresolved alerts:', error);
      return [];
    }
  }

  /**
   * حل تنبيه الشذوذ
   */
  async resolveAlert(alertId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('anomaly_alerts')
        .update({ is_resolved: true })
        .eq('id', alertId);

      return !error;

    } catch (error) {
      console.error('Error resolving alert:', error);
      return false;
    }
  }
}
