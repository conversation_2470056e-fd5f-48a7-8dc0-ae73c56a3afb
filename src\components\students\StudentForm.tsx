import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { X, Upload } from "lucide-react";
import { Button } from "../ui/Button";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/api";

interface StudentFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: Partial<Tables<"students">>) => Promise<void>;
  student?: Tables<"students">;
}

export const StudentForm: React.FC<StudentFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  student,
}) => {
  const { t } = useTranslation();
  const { tenant, users, routes } = useDatabase();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [formData, setFormData] = useState<Partial<Tables<"students">>>({
    name: "",
    grade: "1",
    parent_id: "",
    route_stop_id: "",
    is_active: true,
    tenant_id: tenant?.id,
  });

  useEffect(() => {
    if (student) {
      setFormData({
        name: student.name,
        grade: student.grade,
        parent_id: student.parent_id || "",
        route_stop_id: student.route_stop_id || "",
        is_active: student.is_active,
        tenant_id: student.tenant_id,
      });
    } else {
      setFormData({
        name: "",
        grade: "1",
        parent_id: "",
        route_stop_id: "",
        is_active: true,
        tenant_id: tenant?.id,
      });
    }
  }, [student, tenant]);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const uploadFile = async (file: File): Promise<string> => {
    const fileExt = file.name.split(".").pop();
    const fileName = `${Math.random()}.${fileExt}`;
    const filePath = `students/${fileName}`;

    const { data, error } = await supabase.storage
      .from("student-photos")
      .upload(filePath, file);

    if (error) {
      throw error;
    }

    const {
      data: { publicUrl },
    } = supabase.storage.from("student-photos").getPublicUrl(filePath);

    return publicUrl;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      let photoUrl = formData.photo_url;

      if (selectedFile) {
        photoUrl = await uploadFile(selectedFile);
      }

      await onSubmit({
        ...formData,
        photo_url: photoUrl,
      });
      onClose();
    } catch (error) {
      console.error("Error submitting student:", error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  // Get parents list
  const parents = users.filter((u) => u.role === "parent");

  // Get all route stops
  const stops = routes.flatMap(
    (route) =>
      route.stops?.map((stop) => ({
        id: stop.id,
        name: `${route.name} - ${stop.name}`,
        arrival_time: stop.arrival_time,
      })) || [],
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {student ? t("students.editStudent") : t("students.addStudent")}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4">
          <div className="space-y-4">
            <div>
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                {t("students.name")}
              </label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                required
              />
            </div>

            <div>
              <label
                htmlFor="grade"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                {t("students.grade")}
              </label>
              <select
                id="grade"
                value={formData.grade}
                onChange={(e) =>
                  setFormData({ ...formData, grade: e.target.value })
                }
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                required
              >
                {[1, 2, 3, 4, 5, 6].map((grade) => (
                  <option key={grade} value={grade.toString()}>
                    {t("students.grade")} {grade}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label
                htmlFor="parent"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                {t("students.parent")}
              </label>
              <select
                id="parent"
                value={formData.parent_id}
                onChange={(e) =>
                  setFormData({ ...formData, parent_id: e.target.value })
                }
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
              >
                <option value="">{t("common.select")}</option>
                {parents.map((parent) => (
                  <option key={parent.id} value={parent.id}>
                    {parent.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label
                htmlFor="stop"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                {t("routes.stop")}
              </label>
              <select
                id="stop"
                value={formData.route_stop_id}
                onChange={(e) =>
                  setFormData({ ...formData, route_stop_id: e.target.value })
                }
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
              >
                <option value="">{t("common.select")}</option>
                {stops.map((stop) => (
                  <option key={stop.id} value={stop.id}>
                    {stop.name} ({stop.arrival_time})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label
                htmlFor="photo"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                {t("students.photo")}
              </label>
              <div className="mt-1 flex items-center space-x-3">
                <input
                  type="file"
                  id="photo"
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                />
                <button
                  type="button"
                  onClick={() => document.getElementById("photo")?.click()}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                >
                  <Upload size={16} className="mr-2" />
                  {t("common.upload")}
                </button>
                {selectedFile && (
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {selectedFile.name}
                  </span>
                )}
              </div>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_active"
                checked={formData.is_active}
                onChange={(e) =>
                  setFormData({ ...formData, is_active: e.target.checked })
                }
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label
                htmlFor="is_active"
                className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
              >
                {t("common.active")}
              </label>
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              {t("common.cancel")}
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? t("common.saving") : t("common.save")}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
