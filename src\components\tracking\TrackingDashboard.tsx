/**
 * Professional Real-Time Bus Tracking Dashboard
 * لوحة تتبع الحافلات الاحترافية في الوقت الفعلي
 */

import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  MapPin,
  Navigation,
  Users,
  Clock,
  AlertTriangle,
  Settings,
  Maximize2,
  Minimize2,
  RefreshCw,
  Filter,
  Search,
  Zap,
  Activity,
  Route as RouteIcon,
  Bell,
  Eye,
  EyeOff,
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { DatabaseService } from '../../services/DatabaseService';
import { RealTimeTrackingService } from '../../services/RealTimeTrackingService';
import { supabase } from '../../lib/supabase';
import { Button } from '../ui/Button';
import { StatsOverview } from './StatsOverview';
import { BusListSidebar } from './BusListSidebar';
import { InteractiveMap } from '../maps/InteractiveMap';
import { FilterBar } from './FilterBar';
import { DetailsPanel } from './DetailsPanel';

export interface BusTrackingData {
  id: string;
  plateNumber: string;
  driverName: string;
  currentLocation: {
    lat: number;
    lng: number;
    speed: number;
    heading: number;
    timestamp: string;
    accuracy: number;
  } | null;
  route: {
    id: string;
    name: string;
    color: string;
    stops: Array<{
      id: string;
      name: string;
      location: [number, number];
      studentsCount: number;
      estimatedArrival: string;
      status: 'pending' | 'arrived' | 'departed';
    }>;
  };
  status: 'active' | 'stopped' | 'maintenance' | 'emergency';
  studentsCount: number;
  capacity: number;
  nextStop?: {
    name: string;
    eta: string;
  };
  lastUpdate: string;
  alerts: Array<{
    type: 'warning' | 'error' | 'info';
    message: string;
    timestamp: string;
  }>;
}

export interface TrackingStats {
  totalBuses: number;
  activeBuses: number;
  stoppedBuses: number;
  maintenanceBuses: number;
  emergencyBuses: number;
  averageSpeed: number;
  totalStudents: number;
  onTimePercentage: number;
  activeAlerts: number;
}

export interface FilterOptions {
  status: string[];
  routes: string[];
  drivers: string[];
  searchQuery: string;
  showTrails: boolean;
  showStops: boolean;
  showTraffic: boolean;
  autoRefresh: boolean;
  refreshInterval: number;
}

export const TrackingDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  
  // State Management
  const [busData, setBusData] = useState<Map<string, BusTrackingData>>(new Map());
  const [stats, setStats] = useState<TrackingStats>({
    totalBuses: 0,
    activeBuses: 0,
    stoppedBuses: 0,
    maintenanceBuses: 0,
    emergencyBuses: 0,
    averageSpeed: 0,
    totalStudents: 0,
    onTimePercentage: 0,
    activeAlerts: 0,
  });
  const [selectedBusId, setSelectedBusId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [filters, setFilters] = useState<FilterOptions>({
    status: [],
    routes: [],
    drivers: [],
    searchQuery: '',
    showTrails: true,
    showStops: true,
    showTraffic: false,
    autoRefresh: true,
    refreshInterval: 5000,
  });

  // Services
  const databaseService = useRef(DatabaseService.getInstance());
  const trackingService = useRef(RealTimeTrackingService.getInstance());
  const refreshInterval = useRef<NodeJS.Timeout | null>(null);

  // Effects
  useEffect(() => {
    console.log('🚀 TrackingDashboard mounted, tenant:', tenant);
    initializeTracking();
    return () => cleanup();
  }, [tenant?.id]);

  useEffect(() => {
    if (filters.autoRefresh) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }
    return () => stopAutoRefresh();
  }, [filters.autoRefresh, filters.refreshInterval]);

  // Initialize tracking system
  const initializeTracking = async () => {
    try {
      setIsLoading(true);
      console.log('🔄 TrackingDashboard: Initializing tracking for user:', user?.role, 'tenant:', tenant?.id);
      await loadBusData();
      await subscribeToRealTimeUpdates();
    } catch (error) {
      console.error('❌ TrackingDashboard: Error initializing tracking:', error);
    } finally {
      setIsLoading(false);
      console.log('✅ TrackingDashboard: Initialization completed');
    }
  };

  // Load bus data from database
  const loadBusData = async () => {
    try {
      console.log('🔄 TrackingDashboard: Loading bus data for user:', user?.role);

      let busesWithLocations;

      // For admin users, load all buses across all tenants
      if (user?.role === 'admin') {
        console.log('🔧 TrackingDashboard: Loading all buses for admin');

        try {
          // Try to get all buses directly from Supabase
          const { data: allBuses, error } = await supabase
            .from('buses')
            .select(`
              *,
              routes (
                id,
                name
              )
            `)
            .order('created_at', { ascending: false });

          if (error) {
            console.error('❌ TrackingDashboard: Error fetching admin buses:', error);
            busesWithLocations = [];
          } else {
            console.log('✅ TrackingDashboard: Fetched admin buses:', allBuses?.length || 0);
            busesWithLocations = allBuses || [];
          }
        } catch (adminError) {
          console.error('❌ TrackingDashboard: Admin bus fetch failed:', adminError);
          busesWithLocations = [];
        }
      } else {
        // For tenant users, load tenant-specific buses
        if (!tenant?.id) {
          console.log('🚫 TrackingDashboard: No tenant ID available for non-admin user');
          return;
        }

        console.log('🔍 TrackingDashboard: Loading bus data for tenant:', tenant.id);
        busesWithLocations = await databaseService.current.getBusesWithLocations(tenant.id);
      }

      console.log('📊 Raw bus data from database:', busesWithLocations);
      console.log('📊 Number of buses found:', busesWithLocations?.length || 0);

      // إضافة بيانات تجريبية إذا لم تكن موجودة
      if (!busesWithLocations || busesWithLocations.length === 0) {
        console.log('⚠️ TrackingDashboard: No buses found in database, adding mock data');

        // فحص إضافي: محاولة جلب الحافلات مباشرة من Supabase
        let directBuses = null;
        let error = null;

        if (user?.role === 'admin') {
          // For admin, get all buses
          const result = await supabase
            .from('buses')
            .select('*')
            .order('created_at', { ascending: false });
          directBuses = result.data;
          error = result.error;
        } else if (tenant?.id) {
          // For tenant users, get tenant-specific buses
          const result = await supabase
            .from('buses')
            .select('*')
            .eq('tenant_id', tenant.id);
          directBuses = result.data;
          error = result.error;
        }

        console.log('🔍 TrackingDashboard: Direct buses query result:', { data: directBuses, error });

        if (directBuses && directBuses.length > 0) {
          console.log('✅ Found buses in direct query:', directBuses);
          // تحويل البيانات المباشرة
          const transformedData = new Map<string, BusTrackingData>();

          directBuses.forEach(bus => {
            console.log('🚌 Processing bus:', bus);
            const busTrackingData: BusTrackingData = {
              id: bus.id,
              plateNumber: bus.plate_number,
              driverName: bus.driver_name || 'Unknown Driver',
              currentLocation: {
                lat: bus.latitude || 24.7136,
                lng: bus.longitude || 46.6753,
                speed: bus.speed || 0,
                heading: bus.heading || 0,
                timestamp: bus.location_timestamp || new Date().toISOString(),
                accuracy: bus.accuracy || 0,
              },
              route: {
                id: bus.route_id || 'no-route',
                name: bus.route_name || 'No Route Assigned',
                color: getRouteColor(bus.route_id),
                stops: [],
              },
              status: determineStatus(bus),
              studentsCount: bus.current_students || 0,
              capacity: bus.capacity || 0,
              nextStop: undefined,
              lastUpdate: bus.location_timestamp || bus.last_updated || new Date().toISOString(),
              alerts: [],
            };

            transformedData.set(bus.id, busTrackingData);
          });

          console.log('✅ Transformed bus data:', transformedData);
          setBusData(transformedData);
          updateStats(transformedData);
          return;
        }
        // إنشاء بيانات تجريبية محسنة
        const mockBusData = new Map<string, BusTrackingData>();

        // بيانات تجريبية للأدمن (أكثر تنوعاً)
        const mockBuses = user?.role === 'admin' ? [
          {
            id: 'admin-bus-1',
            plateNumber: 'ABC-123',
            driverName: 'أحمد محمد',
            lat: 24.7136,
            lng: 46.6753,
            speed: 45,
            routeName: 'المسار الشمالي - مدرسة الأمل',
            status: 'active' as const,
            studentsCount: 25,
            capacity: 30,
            nextStop: 'محطة المدرسة',
            eta: '5 دقائق'
          },
          {
            id: 'admin-bus-2',
            plateNumber: 'XYZ-456',
            driverName: 'محمد علي',
            lat: 24.7200,
            lng: 46.6800,
            speed: 0,
            routeName: 'المسار الجنوبي - مدرسة النور',
            status: 'stopped' as const,
            studentsCount: 18,
            capacity: 25,
            nextStop: 'حي النرجس',
            eta: '2 دقائق'
          },
          {
            id: 'admin-bus-3',
            plateNumber: 'DEF-789',
            driverName: 'سالم أحمد',
            lat: 24.7300,
            lng: 46.6900,
            speed: 35,
            routeName: 'المسار الشرقي - مدرسة المستقبل',
            status: 'active' as const,
            studentsCount: 22,
            capacity: 28,
            nextStop: 'حي الياسمين',
            eta: '8 دقائق'
          },
          {
            id: 'admin-bus-4',
            plateNumber: 'GHI-012',
            driverName: 'عبدالله سعد',
            lat: 24.7050,
            lng: 46.6650,
            speed: 0,
            routeName: 'المسار الغربي - مدرسة الرياض',
            status: 'maintenance' as const,
            studentsCount: 0,
            capacity: 32,
            nextStop: 'ورشة الصيانة',
            eta: 'غير محدد'
          }
        ] : [
          {
            id: 'demo-bus-1',
            plateNumber: 'ABC-123',
            driverName: 'أحمد محمد',
            lat: 24.7136,
            lng: 46.6753,
            speed: 45,
            routeName: 'المسار الشمالي',
            status: 'active' as const,
            studentsCount: 25,
            capacity: 30,
            nextStop: 'محطة المدرسة',
            eta: '5 دقائق'
          },
          {
            id: 'demo-bus-2',
            plateNumber: 'XYZ-456',
            driverName: 'محمد علي',
            lat: 24.7200,
            lng: 46.6800,
            speed: 0,
            routeName: 'المسار الجنوبي',
            status: 'stopped' as const,
            studentsCount: 18,
            capacity: 25,
            nextStop: 'حي النرجس',
            eta: '2 دقائق'
          }
        ];

        mockBuses.forEach((bus, index) => {
          mockBusData.set(bus.id, {
            id: bus.id,
            plateNumber: bus.plateNumber,
            driverName: bus.driverName,
            currentLocation: {
              lat: bus.lat,
              lng: bus.lng,
              speed: bus.speed,
              heading: 90 + (index * 45),
              timestamp: new Date().toISOString(),
              accuracy: 5,
            },
            route: {
              id: `route-${index + 1}`,
              name: bus.routeName,
              color: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'][index % 4],
              stops: [],
            },
            status: bus.status,
            studentsCount: bus.studentsCount,
            capacity: bus.capacity,
            nextStop: {
              name: bus.nextStop,
              eta: bus.eta,
            },
            lastUpdate: new Date().toISOString(),
            alerts: bus.status === 'maintenance' ? [{
              type: 'warning' as const,
              message: 'الحافلة في الصيانة الدورية',
              timestamp: new Date().toISOString()
            }] : [],
          });
        });

        console.log(`✅ TrackingDashboard: Created ${mockBusData.size} mock buses for ${user?.role || 'unknown'} user`);
        setBusData(mockBusData);
        updateStats(mockBusData);
        return;
      }

      const transformedData = new Map<string, BusTrackingData>();

      busesWithLocations.forEach(bus => {
        const busTrackingData: BusTrackingData = {
          id: bus.id,
          plateNumber: bus.plate_number,
          driverName: bus.driver_name || 'Unknown Driver',
          currentLocation: bus.latitude && bus.longitude ? {
            lat: bus.latitude,
            lng: bus.longitude,
            speed: bus.speed || 0,
            heading: bus.heading || 0,
            timestamp: bus.location_timestamp || new Date().toISOString(),
            accuracy: bus.accuracy || 0,
          } : null,
          route: {
            id: bus.route_id || 'no-route',
            name: bus.route_name || 'No Route Assigned',
            color: getRouteColor(bus.route_id),
            stops: [], // Will be loaded separately
          },
          status: determineStatus(bus),
          studentsCount: bus.current_students || 0,
          capacity: bus.capacity || 0,
          nextStop: undefined, // Will be calculated
          lastUpdate: bus.location_timestamp || bus.last_updated || new Date().toISOString(),
          alerts: [], // Will be loaded separately
        };

        transformedData.set(bus.id, busTrackingData);
      });

      console.log(`✅ TrackingDashboard: Processed ${transformedData.size} real buses from database`);
      setBusData(transformedData);
      updateStats(transformedData);
    } catch (error) {
      console.error('❌ TrackingDashboard: Error loading bus data:', error);

      // Fallback to mock data on error
      console.log('🔄 TrackingDashboard: Falling back to mock data due to error');
      const mockBusData = new Map<string, BusTrackingData>();
      mockBusData.set('fallback-bus-1', {
        id: 'fallback-bus-1',
        plateNumber: 'ERR-001',
        driverName: 'بيانات تجريبية',
        currentLocation: {
          lat: 24.7136,
          lng: 46.6753,
          speed: 0,
          heading: 0,
          timestamp: new Date().toISOString(),
          accuracy: 0,
        },
        route: {
          id: 'fallback-route',
          name: 'مسار تجريبي',
          color: '#6B7280',
          stops: [],
        },
        status: 'stopped',
        studentsCount: 0,
        capacity: 30,
        lastUpdate: new Date().toISOString(),
        alerts: [{
          type: 'warning',
          message: 'خطأ في تحميل البيانات الحقيقية',
          timestamp: new Date().toISOString()
        }],
      });

      setBusData(mockBusData);
      updateStats(mockBusData);
    }
  };

  // Subscribe to real-time updates
  const subscribeToRealTimeUpdates = async () => {
    try {
      const busIds = Array.from(busData.keys());
      
      for (const busId of busIds) {
        await trackingService.current.subscribeToBusLocation(
          busId,
          (location) => {
            updateBusLocation(busId, location);
          },
          (error) => {
            console.error(`Error tracking bus ${busId}:`, error);
          }
        );
      }
    } catch (error) {
      console.error('Error subscribing to real-time updates:', error);
    }
  };

  // Update bus location
  const updateBusLocation = (busId: string, location: any) => {
    setBusData(prev => {
      const newData = new Map(prev);
      const busData = newData.get(busId);
      
      if (busData) {
        const updatedBus: BusTrackingData = {
          ...busData,
          currentLocation: {
            lat: location.latitude,
            lng: location.longitude,
            speed: location.speed || 0,
            heading: location.heading || 0,
            timestamp: location.timestamp,
            accuracy: location.accuracy || 0,
          },
          status: location.speed > 5 ? 'active' : 'stopped',
          lastUpdate: location.timestamp,
        };
        
        newData.set(busId, updatedBus);
      }
      
      updateStats(newData);
      return newData;
    });
  };

  // Update statistics
  const updateStats = (data: Map<string, BusTrackingData>) => {
    const buses = Array.from(data.values());
    
    const newStats: TrackingStats = {
      totalBuses: buses.length,
      activeBuses: buses.filter(b => b.status === 'active').length,
      stoppedBuses: buses.filter(b => b.status === 'stopped').length,
      maintenanceBuses: buses.filter(b => b.status === 'maintenance').length,
      emergencyBuses: buses.filter(b => b.status === 'emergency').length,
      averageSpeed: buses.reduce((sum, b) => sum + (b.currentLocation?.speed || 0), 0) / buses.length || 0,
      totalStudents: buses.reduce((sum, b) => sum + b.studentsCount, 0),
      onTimePercentage: 95, // This would be calculated based on schedule data
      activeAlerts: buses.reduce((sum, b) => sum + b.alerts.length, 0),
    };
    
    setStats(newStats);
  };

  // Auto refresh
  const startAutoRefresh = () => {
    stopAutoRefresh();
    refreshInterval.current = setInterval(() => {
      loadBusData();
    }, filters.refreshInterval);
  };

  const stopAutoRefresh = () => {
    if (refreshInterval.current) {
      clearInterval(refreshInterval.current);
      refreshInterval.current = null;
    }
  };

  // Cleanup
  const cleanup = async () => {
    stopAutoRefresh();
    await trackingService.current.cleanup();
  };

  // Helper functions
  const getRouteColor = (routeId?: string): string => {
    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];
    return colors[Math.abs((routeId || '').length) % colors.length];
  };

  const determineStatus = (bus: any): 'active' | 'stopped' | 'maintenance' | 'emergency' => {
    if (bus.speed > 5) return 'active';
    if (bus.speed === 0) return 'stopped';
    return 'stopped'; // Default
  };

  // Filter buses based on current filters
  const getFilteredBuses = (): BusTrackingData[] => {
    return Array.from(busData.values()).filter(bus => {
      // Status filter
      if (filters.status.length > 0 && !filters.status.includes(bus.status)) {
        return false;
      }
      
      // Route filter
      if (filters.routes.length > 0 && !filters.routes.includes(bus.route.id)) {
        return false;
      }
      
      // Driver filter
      if (filters.drivers.length > 0 && !filters.drivers.includes(bus.driverName)) {
        return false;
      }
      
      // Search query
      if (filters.searchQuery) {
        const query = filters.searchQuery.toLowerCase();
        return (
          bus.plateNumber.toLowerCase().includes(query) ||
          bus.driverName.toLowerCase().includes(query) ||
          bus.route.name.toLowerCase().includes(query)
        );
      }
      
      return true;
    });
  };

  // Event handlers
  const handleBusSelect = (busId: string) => {
    setSelectedBusId(busId);
  };

  const handleFilterChange = (newFilters: Partial<FilterOptions>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg font-medium text-gray-700">{t('tracking.loading')}</p>
        </div>
      </div>
    );
  }

  const filteredBuses = getFilteredBuses();
  const selectedBus = selectedBusId ? busData.get(selectedBusId) : null;

  // تسجيل البيانات للتشخيص
  console.log('🔍 Total buses in busData:', busData.size);
  console.log('🔍 Filtered buses count:', filteredBuses.length);
  console.log('🔍 Filtered buses data:', filteredBuses);
  console.log('🔍 Current filters:', filters);

  return (
    <div className={`h-full bg-gradient-to-br from-blue-50 to-indigo-100 ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* Header */}
      <div className="bg-white shadow-lg border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <MapPin className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {t('tracking.realTimeTracking')}
                  </h1>
                  <p className="text-sm text-gray-500">
                    {t('tracking.monitorAllBuses')}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={toggleSidebar}
                className="lg:hidden"
              >
                {sidebarCollapsed ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleFilterChange({ autoRefresh: !filters.autoRefresh })}
              >
                <RefreshCw className={`w-4 h-4 ${filters.autoRefresh ? 'animate-spin' : ''}`} />
                {filters.autoRefresh ? t('tracking.autoRefreshOn') : t('tracking.autoRefreshOff')}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={toggleFullscreen}
              >
                {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <StatsOverview stats={stats} />

        {/* Filter Bar */}
        <FilterBar
          filters={filters}
          onFilterChange={handleFilterChange}
          availableRoutes={Array.from(new Set(Array.from(busData.values()).map(b => b.route.name)))}
          availableDrivers={Array.from(new Set(Array.from(busData.values()).map(b => b.driverName)))}
        />
      </div>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-280px)]">
        {/* Sidebar */}
        <BusListSidebar
          buses={filteredBuses}
          selectedBusId={selectedBusId}
          onBusSelect={handleBusSelect}
          collapsed={sidebarCollapsed}
          onToggleCollapse={toggleSidebar}
        />

        {/* Map Area */}
        <div className="flex-1 relative">
          {(() => {
            const mapBuses = filteredBuses.map(bus => ({
              id: bus.id,
              plateNumber: bus.plateNumber,
              latitude: bus.currentLocation?.lat || 24.7136,
              longitude: bus.currentLocation?.lng || 46.6753,
              speed: bus.currentLocation?.speed || 0,
              heading: bus.currentLocation?.heading || 0,
              status: bus.status as 'moving' | 'stopped' | 'maintenance' | 'emergency',
              driver: bus.driverName,
              route: bus.route.name,
              studentsCount: bus.studentsCount,
              lastUpdate: bus.lastUpdate,
              nextStop: bus.nextStop?.name,
              eta: bus.nextStop?.eta,
            }));

            console.log('🗺️ Buses being sent to map:', mapBuses);
            console.log('🗺️ Map props:', {
              busesCount: mapBuses.length,
              selectedBus: selectedBusId,
              showRoutes: filters.showTrails,
              showStops: filters.showStops,
              showTrails: filters.showTrails,
              autoRefresh: filters.autoRefresh
            });

            return (
              <InteractiveMap
                buses={mapBuses}
                selectedBus={selectedBusId}
                onBusSelect={handleBusSelect}
                showRoutes={filters.showTrails}
                showStops={filters.showStops}
                showTrails={filters.showTrails}
                autoRefresh={filters.autoRefresh}
                className="h-full rounded-tl-2xl"
              />
            );
          })()}
        </div>
      </div>

      {/* Details Panel */}
      {selectedBus && (
        <DetailsPanel
          bus={selectedBus}
          onClose={() => setSelectedBusId(null)}
        />
      )}
    </div>
  );
};

export default TrackingDashboard;
