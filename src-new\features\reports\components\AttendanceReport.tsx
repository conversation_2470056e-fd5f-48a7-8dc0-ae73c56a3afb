import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Calendar,
  Download,
  Filter,
  FileText,
  Users,
  TrendingUp,
} from "lucide-react";
import { Button } from "../ui/Button";
import { supabase } from "../../lib/supabase";
import { useDatabase } from "../../contexts/DatabaseContext";
import type { Tables } from "../../lib/api";

interface AttendanceReportData {
  date: string;
  studentId: string;
  studentName: string;
  grade: string;
  routeName: string;
  busPlateNumber: string;
  pickupTime?: string;
  dropoffTime?: string;
  status: "present" | "absent" | "partial";
}

interface AttendanceReportProps {
  className?: string;
}

export const AttendanceReport: React.FC<AttendanceReportProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { students, buses, routes } = useDatabase();
  const [reportData, setReportData] = useState<AttendanceReportData[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
    routeId: "",
    grade: "",
    status: "all" as "all" | "present" | "absent" | "partial",
  });
  const [stats, setStats] = useState({
    totalStudents: 0,
    presentCount: 0,
    absentCount: 0,
    attendanceRate: 0,
  });

  useEffect(() => {
    generateReport();
  }, [filters, students, buses, routes]);

  const generateReport = async () => {
    if (students.length === 0) return;

    setLoading(true);
    try {
      const startDate = new Date(filters.startDate);
      startDate.setHours(0, 0, 0, 0);

      const endDate = new Date(filters.endDate);
      endDate.setHours(23, 59, 59, 999);

      // Fetch attendance records for the date range
      const { data: attendanceRecords, error } = await supabase
        .from("attendance")
        .select("*")
        .gte("recorded_at", startDate.toISOString())
        .lte("recorded_at", endDate.toISOString());

      if (error) throw error;

      // Process the data
      const reportMap = new Map<string, AttendanceReportData>();

      // Initialize report data for all students
      students.forEach((student) => {
        if (filters.grade && student.grade !== filters.grade) return;

        const route = routes.find((r) =>
          r.stops?.some((s) => s.id === student.route_stop_id),
        );
        if (filters.routeId && route?.id !== filters.routeId) return;

        const bus = buses.find((b) => b.id === route?.bus_id);

        // Skip weekends for school days calculation
        const isWeekend = (date: Date) => {
          const day = date.getDay();
          return day === 0 || day === 6; // Sunday or Saturday
        };

        // Create entries for each school day in the range (skip weekends)
        for (
          let d = new Date(startDate);
          d <= endDate;
          d.setDate(d.getDate() + 1)
        ) {
          // Skip weekends
          if (!isWeekend(d)) {
            const dateKey = `${student.id}-${d.toISOString().split("T")[0]}`;
            reportMap.set(dateKey, {
              date: d.toISOString().split("T")[0],
              studentId: student.id,
              studentName: student.name,
              grade: student.grade,
              routeName: route?.name || "No Route",
              busPlateNumber: bus?.plate_number || "No Bus",
              status: "absent",
            });
          }
        }
      });

      // Update with actual attendance data
      attendanceRecords?.forEach((record) => {
        const date = new Date(record.recorded_at).toISOString().split("T")[0];
        const dateKey = `${record.student_id}-${date}`;
        const existing = reportMap.get(dateKey);

        if (existing) {
          const time = new Date(record.recorded_at).toLocaleTimeString();
          if (record.type === "pickup") {
            existing.pickupTime = time;
          } else {
            existing.dropoffTime = time;
          }

          // Update status based on attendance
          if (existing.pickupTime && existing.dropoffTime) {
            existing.status = "present";
          } else if (existing.pickupTime || existing.dropoffTime) {
            existing.status = "partial";
          }
        }
      });

      let filteredData = Array.from(reportMap.values());

      // Apply status filter
      if (filters.status !== "all") {
        filteredData = filteredData.filter(
          (item) => item.status === filters.status,
        );
      }

      setReportData(filteredData);

      // Calculate stats
      const totalStudents = new Set(filteredData.map((item) => item.studentId))
        .size;
      const presentCount = filteredData.filter(
        (item) => item.status === "present",
      ).length;
      const absentCount = filteredData.filter(
        (item) => item.status === "absent",
      ).length;
      const partialCount = filteredData.filter(
        (item) => item.status === "partial",
      ).length;
      const attendanceRate =
        filteredData.length > 0
          ? (presentCount / filteredData.length) * 100
          : 0;

      setStats({
        totalStudents,
        presentCount,
        absentCount,
        attendanceRate,
      });
    } catch (error) {
      console.error("Error generating report:", error);
    } finally {
      setLoading(false);
    }
  };

  const exportToCSV = () => {
    const headers = [
      "Date",
      "Student Name",
      "Grade",
      "Route",
      "Bus",
      "Pickup Time",
      "Dropoff Time",
      "Status",
    ];

    const csvContent = [
      headers.join(","),
      ...reportData.map((row) =>
        [
          row.date,
          `"${row.studentName}"`,
          row.grade,
          `"${row.routeName}"`,
          `"${row.busPlateNumber}"`,
          row.pickupTime || "",
          row.dropoffTime || "",
          row.status,
        ].join(","),
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `attendance_report_${filters.startDate}_to_${filters.endDate}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const exportToPDF = () => {
    // Create PDF content
    const printContent = `
      <html>
        <head>
          <title>Attendance Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            .header { text-align: center; margin-bottom: 20px; }
            .stats { display: flex; justify-content: space-around; margin: 20px 0; }
            .stat-card { text-align: center; padding: 10px; border: 1px solid #ddd; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${t("reports.attendanceReport")}</h1>
            <p>Period: ${filters.startDate} to ${filters.endDate}</p>
          </div>
          
          <div class="stats">
            <div class="stat-card">
              <h3>${stats.totalStudents}</h3>
              <p>${t("reports.totalStudents")}</p>
            </div>
            <div class="stat-card">
              <h3>${stats.attendanceRate.toFixed(1)}%</h3>
              <p>${t("students.attendanceRate")}</p>
            </div>
            <div class="stat-card">
              <h3>${stats.presentCount}</h3>
              <p>${t("students.present")}</p>
            </div>
            <div class="stat-card">
              <h3>${stats.absentCount}</h3>
              <p>${t("students.absent")}</p>
            </div>
          </div>
          
          <table>
            <thead>
              <tr>
                <th>${t("common.date")}</th>
                <th>${t("students.name")}</th>
                <th>${t("students.grade")}</th>
                <th>${t("nav.routes")}</th>
                <th>${t("students.pickup")}</th>
                <th>${t("students.dropoff")}</th>
                <th>${t("students.status")}</th>
              </tr>
            </thead>
            <tbody>
              ${reportData
                .map(
                  (row) => `
                <tr>
                  <td>${new Date(row.date).toLocaleDateString()}</td>
                  <td>${row.studentName}</td>
                  <td>${row.grade}</td>
                  <td>${row.routeName}</td>
                  <td>${row.pickupTime || "-"}</td>
                  <td>${row.dropoffTime || "-"}</td>
                  <td>${row.status}</td>
                </tr>
              `,
                )
                .join("")}
            </tbody>
          </table>
        </body>
      </html>
    `;

    // Open print dialog
    const printWindow = window.open("", "_blank");
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t("reports.attendanceReport")}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t("reports.generateAttendanceReports")}
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={exportToCSV}
            variant="outline"
            size="sm"
            leftIcon={<Download size={16} />}
            disabled={reportData.length === 0}
          >
            {t("common.exportCSV")}
          </Button>
          <Button
            onClick={exportToPDF}
            variant="outline"
            size="sm"
            leftIcon={<FileText size={16} />}
            disabled={reportData.length === 0}
          >
            {t("common.exportPDF")}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("common.startDate")}
            </label>
            <input
              type="date"
              value={filters.startDate}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, startDate: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("common.endDate")}
            </label>
            <input
              type="date"
              value={filters.endDate}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, endDate: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("nav.routes")}
            </label>
            <select
              value={filters.routeId}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, routeId: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="">{t("common.all")}</option>
              {routes.map((route) => (
                <option key={route.id} value={route.id}>
                  {route.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("students.grade")}
            </label>
            <select
              value={filters.grade}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, grade: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="">{t("common.all")}</option>
              {[1, 2, 3, 4, 5, 6].map((grade) => (
                <option key={grade} value={grade.toString()}>
                  {t("students.grade")} {grade}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("students.status")}
            </label>
            <select
              value={filters.status}
              onChange={(e) =>
                setFilters((prev) => ({
                  ...prev,
                  status: e.target.value as any,
                }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="all">{t("common.all")}</option>
              <option value="present">{t("students.present")}</option>
              <option value="absent">{t("students.absent")}</option>
              <option value="partial">{t("students.partial")}</option>
            </select>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-primary-100 dark:bg-primary-800/20 rounded-lg">
              <Users className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("reports.totalStudents")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {stats.totalStudents}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-accent-100 dark:bg-accent-800/20 rounded-lg">
              <TrendingUp className="h-6 w-6 text-accent-600 dark:text-accent-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("students.attendanceRate")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {stats.attendanceRate.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-800/20 rounded-lg">
              <Calendar className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("students.present")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {stats.presentCount}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 dark:bg-red-800/20 rounded-lg">
              <Calendar className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("students.absent")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {stats.absentCount}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Report Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t("reports.attendanceData")} ({reportData.length}{" "}
            {t("common.records")})
          </h3>
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
          </div>
        ) : reportData.length > 0 ? (
          <div className="overflow-x-auto max-h-96">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700 sticky top-0">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("common.date")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("students.name")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("students.grade")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("nav.routes")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("students.pickup")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("students.dropoff")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("students.status")}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {reportData.map((row, index) => (
                  <tr
                    key={`${row.studentId}-${row.date}`}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {new Date(row.date).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {row.studentName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {row.grade}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {row.routeName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {row.pickupTime || "-"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {row.dropoffTime || "-"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          row.status === "present"
                            ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                            : row.status === "partial"
                              ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100"
                              : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                        }`}
                      >
                        {row.status === "present" && t("students.present")}
                        {row.status === "partial" && t("students.partial")}
                        {row.status === "absent" && t("students.absent")}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <FileText size={48} className="mx-auto mb-4 opacity-50" />
            <p>{t("reports.noDataFound")}</p>
            <p className="text-sm mt-2">{t("reports.adjustFilters")}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AttendanceReport;
