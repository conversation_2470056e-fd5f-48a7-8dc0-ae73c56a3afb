import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '../utils/test-utils'
import { Button } from '@components/ui/Button'

describe('Button Component', () => {
  it('should render button with text', () => {
    render(<Button>Click me</Button>)
    
    const button = screen.getByRole('button', { name: /click me/i })
    expect(button).toBeInTheDocument()
  })

  it('should handle click events', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    const button = screen.getByRole('button', { name: /click me/i })
    fireEvent.click(button)
    
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('should be disabled when disabled prop is true', () => {
    render(<Button disabled>Disabled Button</Button>)
    
    const button = screen.getByRole('button', { name: /disabled button/i })
    expect(button).toBeDisabled()
  })

  it('should not call onClick when disabled', () => {
    const handleClick = vi.fn()
    render(<Button disabled onClick={handleClick}>Disabled Button</Button>)
    
    const button = screen.getByRole('button', { name: /disabled button/i })
    fireEvent.click(button)
    
    expect(handleClick).not.toHaveBeenCalled()
  })

  it('should apply variant classes correctly', () => {
    const { rerender } = render(<Button variant="primary">Primary</Button>)
    
    let button = screen.getByRole('button', { name: /primary/i })
    expect(button).toHaveClass('bg-blue-600')
    
    rerender(<Button variant="secondary">Secondary</Button>)
    button = screen.getByRole('button', { name: /secondary/i })
    expect(button).toHaveClass('bg-gray-600')
    
    rerender(<Button variant="danger">Danger</Button>)
    button = screen.getByRole('button', { name: /danger/i })
    expect(button).toHaveClass('bg-red-600')
  })

  it('should apply size classes correctly', () => {
    const { rerender } = render(<Button size="sm">Small</Button>)
    
    let button = screen.getByRole('button', { name: /small/i })
    expect(button).toHaveClass('px-3', 'py-1.5', 'text-sm')
    
    rerender(<Button size="md">Medium</Button>)
    button = screen.getByRole('button', { name: /medium/i })
    expect(button).toHaveClass('px-4', 'py-2', 'text-base')
    
    rerender(<Button size="lg">Large</Button>)
    button = screen.getByRole('button', { name: /large/i })
    expect(button).toHaveClass('px-6', 'py-3', 'text-lg')
  })

  it('should show loading state', () => {
    render(<Button loading>Loading Button</Button>)
    
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(screen.getByText(/loading/i)).toBeInTheDocument()
  })

  it('should render with icon', () => {
    const TestIcon = () => <span data-testid="test-icon">Icon</span>
    render(<Button icon={<TestIcon />}>Button with Icon</Button>)
    
    expect(screen.getByTestId('test-icon')).toBeInTheDocument()
    expect(screen.getByText(/button with icon/i)).toBeInTheDocument()
  })

  it('should apply custom className', () => {
    render(<Button className="custom-class">Custom Button</Button>)
    
    const button = screen.getByRole('button', { name: /custom button/i })
    expect(button).toHaveClass('custom-class')
  })

  it('should forward ref correctly', () => {
    const ref = vi.fn()
    render(<Button ref={ref}>Ref Button</Button>)
    
    expect(ref).toHaveBeenCalled()
  })

  it('should handle keyboard events', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Keyboard Button</Button>)
    
    const button = screen.getByRole('button', { name: /keyboard button/i })
    
    // اختبار مفتاح Enter
    fireEvent.keyDown(button, { key: 'Enter', code: 'Enter' })
    expect(handleClick).toHaveBeenCalledTimes(1)
    
    // اختبار مفتاح Space
    fireEvent.keyDown(button, { key: ' ', code: 'Space' })
    expect(handleClick).toHaveBeenCalledTimes(2)
  })

  it('should support different button types', () => {
    const { rerender } = render(<Button type="button">Button</Button>)
    
    let button = screen.getByRole('button', { name: /button/i })
    expect(button).toHaveAttribute('type', 'button')
    
    rerender(<Button type="submit">Submit</Button>)
    button = screen.getByRole('button', { name: /submit/i })
    expect(button).toHaveAttribute('type', 'submit')
    
    rerender(<Button type="reset">Reset</Button>)
    button = screen.getByRole('button', { name: /reset/i })
    expect(button).toHaveAttribute('type', 'reset')
  })

  it('should handle full width prop', () => {
    render(<Button fullWidth>Full Width Button</Button>)
    
    const button = screen.getByRole('button', { name: /full width button/i })
    expect(button).toHaveClass('w-full')
  })

  it('should apply hover and focus states', () => {
    render(<Button>Hover Button</Button>)
    
    const button = screen.getByRole('button', { name: /hover button/i })
    
    // اختبار حالة التركيز
    fireEvent.focus(button)
    expect(button).toHaveFocus()
    
    // اختبار حالة عدم التركيز
    fireEvent.blur(button)
    expect(button).not.toHaveFocus()
  })

  it('should render as link when href is provided', () => {
    render(<Button href="/test">Link Button</Button>)
    
    const link = screen.getByRole('link', { name: /link button/i })
    expect(link).toBeInTheDocument()
    expect(link).toHaveAttribute('href', '/test')
  })

  it('should handle external links correctly', () => {
    render(<Button href="https://example.com" external>External Link</Button>)
    
    const link = screen.getByRole('link', { name: /external link/i })
    expect(link).toHaveAttribute('href', 'https://example.com')
    expect(link).toHaveAttribute('target', '_blank')
    expect(link).toHaveAttribute('rel', 'noopener noreferrer')
  })

  it('should support aria attributes', () => {
    render(
      <Button 
        aria-label="Custom aria label"
        aria-describedby="description"
      >
        Accessible Button
      </Button>
    )
    
    const button = screen.getByRole('button', { name: /custom aria label/i })
    expect(button).toHaveAttribute('aria-describedby', 'description')
  })

  it('should handle tooltip', () => {
    render(<Button tooltip="This is a tooltip">Tooltip Button</Button>)
    
    const button = screen.getByRole('button', { name: /tooltip button/i })
    expect(button).toHaveAttribute('title', 'This is a tooltip')
  })
})
