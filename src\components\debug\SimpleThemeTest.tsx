/**
 * Simple Theme Test Component
 * Simple test component for theme pages
 * Phase 3: UI/UX Enhancement - Debug
 */

import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole } from '../../types';
import { Palette, Settings, CheckCircle } from 'lucide-react';

interface SimpleThemeTestProps {
  type: 'admin' | 'school';
}

export const SimpleThemeTest: React.FC<SimpleThemeTestProps> = ({ type }) => {
  const { user, tenant } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center gap-3 mb-4">
            <Palette className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {type === 'admin' ? 'إدارة الثيمات - أدمن' : 'ثيم المدرسة - مدير'}
              </h1>
              <p className="text-gray-600">
                {type === 'admin' 
                  ? 'إدارة شاملة لجميع ثيمات المدارس' 
                  : 'تخصيص ثيم مدرستك'
                }
              </p>
            </div>
          </div>
          
          {/* Success Message */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="text-green-800 font-medium">
                🎉 تم تحميل الصفحة بنجاح! المسار يعمل بشكل صحيح.
              </span>
            </div>
          </div>
        </div>

        {/* User Info */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">معلومات المستخدم</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">الاسم</label>
              <p className="mt-1 text-sm text-gray-900">{user?.name || 'غير محدد'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">الدور</label>
              <p className="mt-1 text-sm text-gray-900">{user?.role || 'غير محدد'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">البريد الإلكتروني</label>
              <p className="mt-1 text-sm text-gray-900">{user?.email || 'غير محدد'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">المدرسة</label>
              <p className="mt-1 text-sm text-gray-900">{tenant?.name || 'غير محدد'}</p>
            </div>
          </div>
        </div>

        {/* Route Info */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">معلومات المسار</h2>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700">المسار الحالي</label>
              <p className="mt-1 text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded">
                {window.location.pathname}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">نوع الصفحة</label>
              <p className="mt-1 text-sm text-gray-900">
                {type === 'admin' ? 'صفحة إدارة الثيمات للأدمن' : 'صفحة ثيم المدرسة'}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">الدور المطلوب</label>
              <p className="mt-1 text-sm text-gray-900">
                {type === 'admin' ? UserRole.ADMIN : UserRole.SCHOOL_MANAGER}
              </p>
            </div>
          </div>
        </div>

        {/* Test Actions */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">إجراءات الاختبار</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Settings className="h-4 w-4" />
              العودة للوحة التحكم
            </button>
            
            <button
              onClick={() => window.location.reload()}
              className="flex items-center justify-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <CheckCircle className="h-4 w-4" />
              إعادة تحميل الصفحة
            </button>
          </div>
        </div>

        {/* Debug Info */}
        <div className="mt-6 bg-gray-100 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-700 mb-2">معلومات التشخيص</h3>
          <pre className="text-xs text-gray-600 overflow-auto">
{JSON.stringify({
  currentPath: window.location.pathname,
  userRole: user?.role,
  expectedRole: type === 'admin' ? UserRole.ADMIN : UserRole.SCHOOL_MANAGER,
  hasAccess: type === 'admin' ? user?.role === UserRole.ADMIN : user?.role === UserRole.SCHOOL_MANAGER,
  timestamp: new Date().toISOString()
}, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};
