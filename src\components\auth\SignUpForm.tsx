import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/Button';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole } from '../../types';
import { CentralizedPermissionService } from '../../services/CentralizedPermissionService';

interface SignUpFormProps {
  onBack: () => void;
}

export const SignUpForm: React.FC<SignUpFormProps> = ({ onBack }) => {
  const { t, i18n } = useTranslation();
  const { signUp, isLoading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [role, setRole] = useState<UserRole>(UserRole.PARENT);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const permissionService = CentralizedPermissionService.getInstance();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess(false);

    if (!email || !password || !name) {
      setError(t('auth.errors.allFieldsRequired'));
      return;
    }

    try {
      const result = await signUp(email, password, name, role);

      if (result) {
        // تسجيل حدث أمني للتسجيل الناجح
        await permissionService.logSecurityEvent(
          'USER_SIGNUP_SUCCESS',
          'INFO',
          'New user registered successfully',
          undefined,
          undefined,
          {
            email,
            name,
            role,
            timestamp: new Date().toISOString()
          }
        );

        setSuccess(true);
      } else {
        // تسجيل حدث أمني للتسجيل الفاشل
        await permissionService.logSecurityEvent(
          'USER_SIGNUP_FAILED',
          'WARNING',
          'Failed user registration attempt',
          undefined,
          undefined,
          {
            email,
            name,
            role,
            timestamp: new Date().toISOString()
          }
        );

        setError(t('auth.errors.signUpFailed') || 'فشل في إنشاء الحساب');
      }
    } catch (error) {
      console.error('Sign up error:', error);
      setError(t('auth.errors.networkError') || 'حدث خطأ في الشبكة');
    }
  };

  return (
    <form className="space-y-6" onSubmit={handleSubmit}>
      {error && (
        <div className="bg-error-50 dark:bg-error-900/20 border border-error-200 dark:border-error-800 rounded-md p-3">
          <p className="text-sm text-error-600 dark:text-error-400">{error}</p>
        </div>
      )}
      {success && (
        <div className="bg-success-50 dark:bg-success-900/20 border border-success-200 dark:border-success-800 rounded-md p-3">
          <p className="text-sm text-success-600 dark:text-success-400">{t('auth.resetPasswordEmailSent') || 'تم التسجيل بنجاح. تحقق من بريدك الإلكتروني.'}</p>
        </div>
      )}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          {t('users.name')}
        </label>
        <div className="mt-1">
          <input
            id="name"
            name="name"
            type="text"
            required
            value={name}
            onChange={e => setName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            placeholder={i18n.language === 'ar' ? 'اسمك' : 'Your Name'}
          />
        </div>
      </div>
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          {t('auth.email')}
        </label>
        <div className="mt-1">
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={email}
            onChange={e => setEmail(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            placeholder={i18n.language === 'ar' ? 'البريد الإلكتروني' : '<EMAIL>'}
          />
        </div>
      </div>
      <div>
        <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          {t('auth.password')}
        </label>
        <div className="mt-1">
          <input
            id="password"
            name="password"
            type="password"
            autoComplete="new-password"
            required
            value={password}
            onChange={e => setPassword(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            placeholder={i18n.language === 'ar' ? 'كلمة المرور' : 'Password'}
          />
        </div>
      </div>
      <div>
        <label htmlFor="role" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          {t('users.role')}
        </label>
        <div className="mt-1">
          <select
            id="role"
            name="role"
            value={role}
            onChange={e => setRole(e.target.value as UserRole)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
            <option value={UserRole.PARENT}>{t('users.parent')}</option>
            <option value={UserRole.DRIVER}>{t('users.driver')}</option>
            <option value={UserRole.STUDENT}>{t('users.student')}</option>
          </select>
        </div>
      </div>
      <div className="flex items-center justify-between">
        <Button type="button" variant="outline" onClick={onBack} className="flex items-center gap-2">
          {t('common.back')}
        </Button>
        <Button type="submit" disabled={isLoading} className="flex items-center gap-2">
          {isLoading ? t('common.loading') : t('auth.signUp') || 'Sign Up'}
        </Button>
      </div>
    </form>
  );
};
