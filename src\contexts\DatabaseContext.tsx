import React, { createContext, useContext, useEffect, useState } from "react";
import { RealtimeChannel } from "@supabase/supabase-js";
import { supabase } from "../lib/supabase";
import { useAuth } from "./AuthContext";
import * as api from "../lib/api";
import type { Tables } from "../lib/api";
import { Tenant, User, Bus, Route, Student } from "../types";

interface RouteWithStops extends Tables<"routes"> {
  stops?: Tables<"route_stops">[];
}

interface DatabaseContextType {
  tenant: Tenant | null;
  tenants: Tenant[];
  users: User[];
  buses: Bus[];
  routes: RouteWithStops[];
  students: Student[];
  loading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
  // Tenant-specific data management
  createBus: (busData: Partial<Bus>) => Promise<Bus | null>;
  updateBus: (id: string, updates: Partial<Bus>) => Promise<Bus | null>;
  deleteBus: (id: string) => Promise<boolean>;
  createStudent: (studentData: Partial<Student>) => Promise<Student | null>;
  updateStudent: (
    id: string,
    updates: Partial<Student>,
  ) => Promise<Student | null>;
  deleteStudent: (id: string) => Promise<boolean>;
  createRoute: (routeData: Partial<Route>) => Promise<Route | null>;
  updateRoute: (id: string, updates: Partial<Route>) => Promise<Route | null>;
  deleteRoute: (id: string) => Promise<boolean>;
  // Tenant management for super admin
  createTenant: (tenantData: Partial<Tenant>) => Promise<Tenant | null>;
  updateTenant: (
    id: string,
    updates: Partial<Tenant>,
  ) => Promise<Tenant | null>;
  deleteTenant: (id: string) => Promise<boolean>;
  assignSchoolManager: (tenantId: string, userId: string) => Promise<boolean>;
  assignBusDriver: (busId: string, driverId: string | null) => Promise<boolean>;
}

const DatabaseContext = createContext<DatabaseContextType | undefined>(
  undefined,
);

function DatabaseProvider({ children }: { children: React.ReactNode }) {
  const { user, tenant: authTenant } = useAuth();
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [buses, setBuses] = useState<Bus[]>([]);
  const [routes, setRoutes] = useState<RouteWithStops[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [subscriptions, setSubscriptions] = useState<RealtimeChannel[]>([]);

  const fetchData = async () => {
    if (!user) {
      console.log('🚫 DatabaseContext: No user, setting loading to false');
      setLoading(false);
      return;
    }

    try {
      console.log('🔄 DatabaseContext: Starting data fetch for user:', {
        id: user.id,
        role: user.role,
        tenant_id: user.tenant_id,
      });

      // إضافة تشخيص إضافي
      console.log('🔍 DatabaseContext: Supabase client status:', {
        url: supabase.supabaseUrl,
        key: supabase.supabaseKey ? 'Present' : 'Missing'
      });

      setLoading(true);
      setError(null);

      console.log("Fetching data for user:", {
        id: user.id,
        role: user.role,
        tenant_id: user.tenant_id,
      });

      // اختبار الاتصال بقاعدة البيانات أولاً
      console.log('🔍 DatabaseContext: Testing database connection...');
      try {
        const { data: testData, error: testError } = await supabase
          .from('users')
          .select('count')
          .limit(1);

        if (testError) {
          console.error('❌ DatabaseContext: Database connection test failed:', testError);
        } else {
          console.log('✅ DatabaseContext: Database connection test successful');
        }
      } catch (connError) {
        console.error('❌ DatabaseContext: Database connection error:', connError);
      }

      // Force refresh to ensure we get latest data
      console.log("Starting data fetch...");

      // Set tenant from auth context
      if (authTenant) {
        setTenant(authTenant);
      }

      if (user.role === "admin") {
        // Admin has full access to all data across all tenants
        console.log("🔧 DatabaseContext: Fetching all data for admin user");

        try {
          // Use direct queries since RLS is disabled
          console.log("🔄 DatabaseContext: Fetching admin data with direct queries...");

          const [usersResult, busesResult, routesResult, studentsResult, tenantsResult] = await Promise.allSettled([
            supabase.from("users").select("*").order("created_at", { ascending: false }),
            supabase.from("buses").select("*").order("created_at", { ascending: false }),
            supabase.from("routes").select("*").order("created_at", { ascending: false }),
            supabase.from("students").select("*").order("created_at", { ascending: false }),
            supabase.from("tenants").select("*").order("created_at", { ascending: false })
          ]);

          // Process users
          if (usersResult.status === "fulfilled" && !usersResult.value.error) {
            const userData = usersResult.value.data || [];
            console.log(`✅ DatabaseContext: Fetched ${userData.length} users`);
            setUsers(userData);
          } else {
            console.error("❌ DatabaseContext: Failed to fetch users:",
              usersResult.status === "fulfilled" ? usersResult.value.error : usersResult.reason);
            setUsers([]);
          }

          // Process buses
          if (busesResult.status === "fulfilled" && !busesResult.value.error) {
            const busesData = busesResult.value.data || [];
            console.log(`✅ DatabaseContext: Fetched ${busesData.length} buses`);
            setBuses(busesData);
          } else {
            console.error("❌ DatabaseContext: Failed to fetch buses:",
              busesResult.status === "fulfilled" ? busesResult.value.error : busesResult.reason);
            setBuses([]);
          }

          // Process routes
          if (routesResult.status === "fulfilled" && !routesResult.value.error) {
            const routesData = routesResult.value.data || [];
            console.log(`✅ DatabaseContext: Fetched ${routesData.length} routes`);
            setRoutes(routesData);
          } else {
            console.error("❌ DatabaseContext: Failed to fetch routes:",
              routesResult.status === "fulfilled" ? routesResult.value.error : routesResult.reason);
            setRoutes([]);
          }

          // Process students
          if (studentsResult.status === "fulfilled" && !studentsResult.value.error) {
            const studentsData = studentsResult.value.data || [];
            console.log(`✅ DatabaseContext: Fetched ${studentsData.length} students`);
            setStudents(studentsData);
          } else {
            console.error("❌ DatabaseContext: Failed to fetch students:",
              studentsResult.status === "fulfilled" ? studentsResult.value.error : studentsResult.reason);
            setStudents([]);
          }

          // Process tenants
          if (tenantsResult.status === "fulfilled" && !tenantsResult.value.error) {
            const tenantsData = tenantsResult.value.data || [];
            console.log(`✅ DatabaseContext: Fetched ${tenantsData.length} tenants`);
            setTenants(tenantsData);
          } else {
            console.error("❌ DatabaseContext: Failed to fetch tenants:",
              tenantsResult.status === "fulfilled" ? tenantsResult.value.error : tenantsResult.reason);
            setTenants([]);
          }

          console.log("🎉 DatabaseContext: Admin data fetch completed successfully");

        } catch (adminError) {
          console.error("🚨 DatabaseContext: Error in admin data fetch:", adminError);
          // Set empty arrays as fallback
          setUsers([]);
          setBuses([]);
          setRoutes([]);
          setStudents([]);
          setTenants([]);
        }

        // Set up real-time subscriptions for all data (admin)
        setupRealtimeSubscriptionsForAllData();
      } else if (user.tenant_id) {
        // Tenant-scoped user sees only their tenant's data
        console.log("Fetching tenant users for tenant:", user.tenant_id);
        const [usersData, busesData, routesData, studentsData] =
          await Promise.all([
            api.getUsersByTenant(user.tenant_id),
            api.getBusesByTenant(user.tenant_id),
            api.getRoutesByTenant(user.tenant_id),
            api.getStudentsByTenant(user.tenant_id),
          ]);

        console.log("Tenant users fetched:", usersData?.length || 0, "users");
        console.log("Tenant buses fetched:", busesData?.length || 0, "buses");
        console.log(
          "Tenant routes fetched:",
          routesData?.length || 0,
          "routes",
        );
        console.log(
          "Tenant students fetched:",
          studentsData?.length || 0,
          "students",
        );

        setUsers(usersData || []);
        setBuses(busesData || []);
        setRoutes(routesData || []);
        setStudents(studentsData || []);

        // Set up real-time subscriptions for tenant data
        setupRealtimeSubscriptions(user.tenant_id);
      } else {
        // User without tenant_id and not admin - limited access
        console.log("Fetching limited data for user without tenant access");
        setUsers([]);
        setBuses([]);
        setRoutes([]);
        setStudents([]);
        setTenants([]);
      }

    } catch (err) {
      console.error("🚨 DatabaseContext: Error fetching data:", err);
      setError(err instanceof Error ? err : new Error("An error occurred"));

      // Set empty data on error to prevent infinite loading
      setUsers([]);
      setBuses([]);
      setRoutes([]);
      setStudents([]);
      setTenants([]);
    } finally {
      console.log('✅ DatabaseContext: Setting loading to false');
      setLoading(false);
    }
  };

  const setupRealtimeSubscriptions = (tenantId: string) => {
    // Clean up existing subscriptions
    subscriptions.forEach((sub) => sub.unsubscribe());

    const newSubscriptions: RealtimeChannel[] = [];

    // Subscribe to buses changes
    const busesChannel = supabase
      .channel(`tenant-${tenantId}-buses`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "buses",
          filter: `tenant_id=eq.${tenantId}`,
        },
        (payload) => {
          console.log("Buses change received:", payload);
          // Refresh buses data
          api.getBusesByTenant(tenantId).then((data) => {
            if (data) setBuses(data);
          });
        },
      )
      .subscribe();

    // Subscribe to users changes
    const usersChannel = supabase
      .channel(`tenant-${tenantId}-users`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "users",
          filter: `tenant_id=eq.${tenantId}`,
        },
        (payload) => {
          console.log("Users change received:", payload);
          // Refresh users data
          api.getUsersByTenant(tenantId).then((data) => {
            if (data) {
              console.log(
                "Updated users from subscription:",
                data.length,
                "users",
              );
              setUsers(data);
            }
          });
        },
      )
      .subscribe();

    // Subscribe to students changes
    const studentsChannel = supabase
      .channel(`tenant-${tenantId}-students`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "students",
          filter: `tenant_id=eq.${tenantId}`,
        },
        (payload) => {
          console.log("Students change received:", payload);
          // Refresh students data
          api.getStudentsByTenant(tenantId).then((data) => {
            if (data) setStudents(data);
          });
        },
      )
      .subscribe();

    // Subscribe to routes changes
    const routesChannel = supabase
      .channel(`tenant-${tenantId}-routes`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "routes",
          filter: `tenant_id=eq.${tenantId}`,
        },
        (payload) => {
          console.log("Routes change received:", payload);
          // Refresh routes data
          api.getRoutesByTenant(tenantId).then((data) => {
            if (data) setRoutes(data);
          });
        },
      )
      .subscribe();

    newSubscriptions.push(
      busesChannel,
      usersChannel,
      studentsChannel,
      routesChannel,
    );
    setSubscriptions(newSubscriptions);
  };

  // Setup subscriptions for all data (for admin users)
  const setupRealtimeSubscriptionsForAllData = () => {
    // Clean up existing subscriptions
    subscriptions.forEach((sub) => sub.unsubscribe());

    const newSubscriptions: RealtimeChannel[] = [];

    // Subscribe to all users changes
    const usersChannel = supabase
      .channel(`all-users`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "users",
        },
        (payload) => {
          console.log("All users change received:", payload);
          // Refresh all users data using RPC function
          supabase.rpc("get_all_users").then(({ data, error }) => {
            if (!error && data) {
              console.log(
                "Updated all users from subscription:",
                data.length,
                "users",
              );
              setUsers(data);
            } else if (error) {
              console.error("Error refreshing users from subscription:", error);
            }
          });
        },
      )
      .subscribe();

    // Subscribe to all buses changes
    const busesChannel = supabase
      .channel(`all-buses`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "buses",
        },
        (payload) => {
          console.log("All buses change received:", payload);
          // Refresh all buses data
          api.getAllBuses().then((data) => {
            if (data) {
              console.log(
                "Updated all buses from subscription:",
                data.length,
                "buses",
              );
              setBuses(data);
            }
          });
        },
      )
      .subscribe();

    // Subscribe to all routes changes
    const routesChannel = supabase
      .channel(`all-routes`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "routes",
        },
        (payload) => {
          console.log("All routes change received:", payload);
          // Refresh all routes data
          api.getAllRoutes().then((data) => {
            if (data) {
              console.log(
                "Updated all routes from subscription:",
                data.length,
                "routes",
              );
              setRoutes(data);
            }
          });
        },
      )
      .subscribe();

    // Subscribe to all students changes
    const studentsChannel = supabase
      .channel(`all-students`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "students",
        },
        (payload) => {
          console.log("All students change received:", payload);
          // Refresh all students data
          api.getAllStudents().then((data) => {
            if (data) {
              console.log(
                "Updated all students from subscription:",
                data.length,
                "students",
              );
              setStudents(data);
            }
          });
        },
      )
      .subscribe();

    // Subscribe to all tenants changes
    const tenantsChannel = supabase
      .channel(`all-tenants`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "tenants",
        },
        (payload) => {
          console.log("All tenants change received:", payload);
          // Refresh all data since tenant changes affect multiple entities
          fetchData();
        },
      )
      .subscribe();

    newSubscriptions.push(
      usersChannel,
      busesChannel,
      routesChannel,
      studentsChannel,
      tenantsChannel,
    );
    setSubscriptions(newSubscriptions);
  };

  // Tenant-specific CRUD operations
  const createBus = async (busData: Partial<Bus>): Promise<Bus | null> => {
    if (!user?.tenant_id) return null;

    try {
      const newBus = await api.createBus({
        ...busData,
        tenant_id: user.tenant_id,
        is_active: true,
      } as any);

      if (newBus) {
        setBuses((prev) => [...prev, newBus as Bus]);
      }

      return newBus as Bus;
    } catch (error) {
      console.error("Error creating bus:", error);
      return null;
    }
  };

  const updateBus = async (
    id: string,
    updates: Partial<Bus>,
  ): Promise<Bus | null> => {
    try {
      const updatedBus = await api.updateBus(id, updates as any);

      if (updatedBus) {
        setBuses((prev) =>
          prev.map((bus) => (bus.id === id ? (updatedBus as Bus) : bus)),
        );
      }

      return updatedBus as Bus;
    } catch (error) {
      console.error("Error updating bus:", error);
      return null;
    }
  };

  const deleteBus = async (id: string): Promise<boolean> => {
    try {
      await api.deleteBus(id);
      setBuses((prev) => prev.filter((bus) => bus.id !== id));
      return true;
    } catch (error) {
      console.error("Error deleting bus:", error);
      return false;
    }
  };

  const createStudent = async (
    studentData: Partial<Student>,
  ): Promise<Student | null> => {
    if (!user?.tenant_id) return null;

    try {
      const newStudent = await api.createStudent({
        ...studentData,
        tenant_id: user.tenant_id,
        is_active: true,
      } as any);

      if (newStudent) {
        setStudents((prev) => [...prev, newStudent as Student]);
      }

      return newStudent as Student;
    } catch (error) {
      console.error("Error creating student:", error);
      return null;
    }
  };

  const updateStudent = async (
    id: string,
    updates: Partial<Student>,
  ): Promise<Student | null> => {
    try {
      const updatedStudent = await api.updateStudent(id, updates as any);

      if (updatedStudent) {
        setStudents((prev) =>
          prev.map((student) =>
            student.id === id ? (updatedStudent as Student) : student,
          ),
        );
      }

      return updatedStudent as Student;
    } catch (error) {
      console.error("Error updating student:", error);
      return null;
    }
  };

  const deleteStudent = async (id: string): Promise<boolean> => {
    try {
      await api.deleteStudent(id);
      setStudents((prev) => prev.filter((student) => student.id !== id));
      return true;
    } catch (error) {
      console.error("Error deleting student:", error);
      return false;
    }
  };

  const createRoute = async (
    routeData: Partial<Route>,
  ): Promise<Route | null> => {
    if (!user?.tenant_id) return null;

    try {
      const newRoute = await api.createRoute({
        ...routeData,
        tenant_id: user.tenant_id,
        is_active: true,
      } as any);

      if (newRoute) {
        setRoutes((prev) => [...prev, newRoute as RouteWithStops]);
      }

      return newRoute as Route;
    } catch (error) {
      console.error("Error creating route:", error);
      return null;
    }
  };

  const updateRoute = async (
    id: string,
    updates: Partial<Route>,
  ): Promise<Route | null> => {
    try {
      const updatedRoute = await api.updateRoute(id, updates as any);

      if (updatedRoute) {
        setRoutes((prev) =>
          prev.map((route) =>
            route.id === id ? (updatedRoute as RouteWithStops) : route,
          ),
        );
      }

      return updatedRoute as Route;
    } catch (error) {
      console.error("Error updating route:", error);
      return null;
    }
  };

  const deleteRoute = async (id: string): Promise<boolean> => {
    try {
      await api.deleteRoute(id);
      setRoutes((prev) => prev.filter((route) => route.id !== id));
      return true;
    } catch (error) {
      console.error("Error deleting route:", error);
      return false;
    }
  };

  // Tenant management functions for super admin
  const createTenant = async (
    tenantData: Partial<Tenant>,
  ): Promise<Tenant | null> => {
    try {
      const newTenant = await api.createSchool(tenantData as any);
      if (newTenant) {
        setTenants((prev) => [...prev, newTenant as Tenant]);
      }
      return newTenant as Tenant;
    } catch (error) {
      console.error("Error creating tenant:", error);
      return null;
    }
  };

  const updateTenant = async (
    id: string,
    updates: Partial<Tenant>,
  ): Promise<Tenant | null> => {
    try {
      const updatedTenant = await api.updateSchool(id, updates as any);
      if (updatedTenant) {
        setTenants((prev) =>
          prev.map((tenant) =>
            tenant.id === id ? (updatedTenant as Tenant) : tenant,
          ),
        );
      }
      return updatedTenant as Tenant;
    } catch (error) {
      console.error("Error updating tenant:", error);
      return null;
    }
  };

  const deleteTenant = async (id: string): Promise<boolean> => {
    try {
      await api.deleteSchool(id);
      setTenants((prev) => prev.filter((tenant) => tenant.id !== id));
      return true;
    } catch (error) {
      console.error("Error deleting tenant:", error);
      return false;
    }
  };

  const assignSchoolManager = async (
    tenantId: string,
    userId: string,
  ): Promise<boolean> => {
    try {
      console.log(
        `DatabaseContext: Assigning user ${userId} as manager for tenant ${tenantId}`,
      );

      // Find the user first to ensure they exist
      const userToUpdate = users.find((u) => u.id === userId);
      if (!userToUpdate) {
        throw new Error(
          `User with ID ${userId} not found in current users list`,
        );
      }

      console.log("DatabaseContext: Found user to update:", {
        id: userToUpdate.id,
        name: userToUpdate.name,
        email: userToUpdate.email,
        currentRole: userToUpdate.role,
        currentTenantId: userToUpdate.tenant_id,
      });

      // Use the dedicated school manager assignment function
      const { data: updatedUser, error } = await supabase.rpc(
        "assign_school_manager",
        {
          user_id: userId,
          tenant_id: tenantId,
        },
      );

      if (error) {
        console.error("DatabaseContext: Assignment failed:", error);
        throw error;
      }

      if (updatedUser) {
        console.log("DatabaseContext: User successfully updated:", updatedUser);

        // Update local state immediately
        setUsers((prev) => {
          const newUsers = prev.map((user) =>
            user.id === userId
              ? {
                  ...user,
                  tenant_id: tenantId,
                  role: "school_manager",
                  updated_at: new Date().toISOString(),
                }
              : user,
          );
          console.log(
            `DatabaseContext: Updated users in state, found user: ${newUsers.some((u) => u.id === userId)}`,
          );
          return newUsers;
        });

        // Force refresh data to ensure we have the latest from database
        setTimeout(() => {
          fetchData();
        }, 500);

        return true;
      }
      console.warn(
        "DatabaseContext: No updated user returned from assignment function",
      );
      return false;
    } catch (error) {
      console.error("DatabaseContext: Error assigning school manager:", error);
      throw error; // Propagate the error to handle it in the UI
    }
  };

  const assignBusDriver = async (
    busId: string,
    driverId: string | null,
  ): Promise<boolean> => {
    try {
      console.log(
        `DatabaseContext: Assigning driver ${driverId} to bus ${busId}`,
      );

      // Update the bus with the new driver
      const { data: updatedBus, error } = await supabase
        .from("buses")
        .update({ driver_id: driverId, updated_at: new Date().toISOString() })
        .eq("id", busId)
        .select()
        .maybeSingle();

      if (error) {
        console.error("DatabaseContext: Bus driver assignment failed:", error);
        throw error;
      }

      if (updatedBus) {
        console.log(
          "DatabaseContext: Bus driver successfully updated:",
          updatedBus,
        );

        // Update local state immediately
        setBuses((prev) => {
          const newBuses = prev.map((bus) =>
            bus.id === busId
              ? {
                  ...bus,
                  driver_id: driverId,
                  updated_at: new Date().toISOString(),
                }
              : bus,
          );
          return newBuses;
        });

        // Create notification for driver assignment
        if (driverId && user?.tenant_id) {
          try {
            await supabase.from("notifications").insert({
              user_id: driverId,
              tenant_id: user.tenant_id,
              title: "Bus Assignment",
              message: `You have been assigned to bus ${updatedBus.plate_number}`,
              type: "assignment",
              priority: "normal",
              metadata: {
                type: "bus_assignment",
                busId: busId,
                plateNumber: updatedBus.plate_number,
              },
            });
          } catch (notificationError) {
            console.warn(
              "Failed to create assignment notification:",
              notificationError,
            );
          }
        }

        // Force refresh data to ensure we have the latest from database
        setTimeout(() => {
          fetchData();
        }, 500);

        return true;
      }
      console.warn(
        "DatabaseContext: No updated bus returned from assignment function",
      );
      return false;
    } catch (error) {
      console.error("DatabaseContext: Error assigning bus driver:", error);
      throw error;
    }
  };

  useEffect(() => {
    console.log('🔄 DatabaseContext useEffect triggered:', {
      userId: user?.id,
      userRole: user?.role,
      userTenantId: user?.tenant_id,
      authTenant: !!authTenant,
      loading
    });
    fetchData();
  }, [user?.id, user?.role, user?.tenant_id, authTenant]);

  // Cleanup subscriptions on unmount
  useEffect(() => {
    return () => {
      subscriptions.forEach((sub) => sub.unsubscribe());
    };
  }, [subscriptions]);

  return (
    <DatabaseContext.Provider
      value={{
        tenant,
        tenants,
        users,
        buses,
        routes,
        students,
        loading,
        error,
        refreshData: fetchData,
        createBus,
        updateBus,
        deleteBus,
        createStudent,
        updateStudent,
        deleteStudent,
        createRoute,
        updateRoute,
        deleteRoute,
        createTenant,
        updateTenant,
        deleteTenant,
        assignSchoolManager,
        assignBusDriver,
      }}
    >
      {children}
    </DatabaseContext.Provider>
  );
}

// Custom hook for accessing database context
const useDatabase = () => {
  const context = useContext(DatabaseContext);
  if (!context) {
    throw new Error("useDatabase must be used within a DatabaseProvider");
  }
  return context;
};

// Named exports for Fast Refresh compatibility
export { DatabaseProvider, useDatabase };

// Default export for compatibility
export default DatabaseProvider;
