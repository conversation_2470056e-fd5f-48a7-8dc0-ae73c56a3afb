/**
 * School List Component
 * Displays paginated list of schools with management actions
 * Phase 2: Application Structure Reorganization
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { School } from '../../../api/types';
import { schoolService, SchoolListParams } from '../../../services/data/SchoolService';
import { usePermissionService } from '../../../hooks/usePermissionService';
import { ResourceType, Action } from '../../../lib/rbac';

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '../../common/ui/Card';
import { Button } from '../../common/ui/Button';
import { Badge } from '../../common/ui/Badge';
import { Alert, AlertDescription } from '../../common/ui/Alert';
import { DataTable } from '../../common/data-display/DataTable';
import { SearchInput } from '../../common/forms/SearchInput';
import { FilterSelect } from '../../common/forms/FilterSelect';
import { Pagination } from '../../common/data-display/Pagination';

// Icons
import { Plus, Edit, Trash2, Eye, Building2, Users, Settings } from 'lucide-react';

interface SchoolListProps {
  onSchoolSelect?: (school: School) => void;
  onSchoolCreate?: () => void;
  onSchoolEdit?: (school: School) => void;
  onSchoolDelete?: (school: School) => void;
  onSchoolSettings?: (school: School) => void;
  className?: string;
}

interface SchoolListState {
  schools: School[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filters: {
    search: string;
    isActive: boolean | '';
    managerId: string;
  };
}

/**
 * School List Component
 * Implements comprehensive school management with RBAC integration
 */
export const SchoolList: React.FC<SchoolListProps> = ({
  onSchoolSelect,
  onSchoolCreate,
  onSchoolEdit,
  onSchoolDelete,
  onSchoolSettings,
  className,
}) => {
  const { t } = useTranslation();

  // Permissions
  const { canRead, canCreate, canUpdate, canDelete } = usePermissionService();

  // State
  const [state, setState] = useState<SchoolListState>({
    schools: [],
    loading: false,
    error: null,
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
    },
    filters: {
      search: '',
      isActive: '',
      managerId: '',
    },
  });

  // Check permissions
  const canViewSchools = canRead(ResourceType.SCHOOL);
  const canCreateSchools = canCreate(ResourceType.SCHOOL);
  const canUpdateSchools = canUpdate(ResourceType.SCHOOL);
  const canDeleteSchools = canDelete(ResourceType.SCHOOL);

  /**
   * Load schools with current filters and pagination
   */
  const loadSchools = useCallback(async () => {
    if (!canViewSchools) {
      setState(prev => ({
        ...prev,
        error: 'You do not have permission to view schools',
      }));
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const params: SchoolListParams = {
        page: state.pagination.page,
        limit: state.pagination.limit,
        ...(state.filters.search && { search: state.filters.search }),
        ...(state.filters.isActive !== '' && { is_active: state.filters.isActive }),
        ...(state.filters.managerId && { manager_id: state.filters.managerId }),
      };

      const response = await schoolService.getSchools(params);

      if (response.success && response.data) {
        setState(prev => ({
          ...prev,
          schools: response.data!.data,
          pagination: {
            ...prev.pagination,
            total: response.data!.pagination.total,
            totalPages: response.data!.pagination.totalPages,
          },
          loading: false,
        }));
      } else {
        setState(prev => ({
          ...prev,
          error: response.error?.message || 'Failed to load schools',
          loading: false,
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'An unexpected error occurred',
        loading: false,
      }));
    }
  }, [state.pagination.page, state.pagination.limit, state.filters, canViewSchools]);

  // Load schools on mount and when dependencies change
  useEffect(() => {
    loadSchools();
  }, [loadSchools]);

  /**
   * Handle search input change
   */
  const handleSearchChange = useCallback((search: string) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, search },
      pagination: { ...prev.pagination, page: 1 },
    }));
  }, []);

  /**
   * Handle active status filter change
   */
  const handleActiveFilterChange = useCallback((isActive: boolean | '') => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, isActive },
      pagination: { ...prev.pagination, page: 1 },
    }));
  }, []);

  /**
   * Handle page change
   */
  const handlePageChange = useCallback((page: number) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page },
    }));
  }, []);

  /**
   * Handle page size change
   */
  const handlePageSizeChange = useCallback((limit: number) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, limit, page: 1 },
    }));
  }, []);

  /**
   * Handle school status toggle
   */
  const handleToggleSchoolStatus = useCallback(async (school: School) => {
    if (!canUpdateSchools) return;

    try {
      const response = await schoolService.toggleSchoolStatus(school.id, !school.is_active);
      if (response.success) {
        loadSchools();
      } else {
        setState(prev => ({
          ...prev,
          error: response.error?.message || 'Failed to update school status',
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'Failed to update school status',
      }));
    }
  }, [canUpdateSchools, loadSchools]);

  /**
   * Table columns configuration
   */
  const columns = useMemo(() => [
    {
      key: 'name',
      title: 'School',
      render: (school: School) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <Building2 className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <div className="font-medium">{school.name}</div>
            <div className="text-sm text-gray-500">Code: {school.code}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'contact',
      title: 'Contact',
      render: (school: School) => (
        <div>
          <div className="text-sm">{school.contact.email}</div>
          <div className="text-sm text-gray-500">{school.contact.phone}</div>
        </div>
      ),
    },
    {
      key: 'address',
      title: 'Location',
      render: (school: School) => (
        <div className="text-sm">
          <div>{school.address.city}, {school.address.state}</div>
          <div className="text-gray-500">{school.address.country}</div>
        </div>
      ),
    },
    {
      key: 'manager',
      title: 'Manager',
      render: (school: School) => (
        <div className="flex items-center space-x-2">
          <Users className="w-4 h-4 text-gray-400" />
          <span className="text-sm">
            {school.manager?.name || 'Not assigned'}
          </span>
        </div>
      ),
    },
    {
      key: 'status',
      title: 'Status',
      render: (school: School) => (
        <Badge variant={school.is_active ? 'default' : 'destructive'}>
          {school.is_active ? 'Active' : 'Inactive'}
        </Badge>
      ),
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (school: School) => (
        <div className="flex items-center space-x-2">
          {onSchoolSelect && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onSchoolSelect(school)}
              title="View Details"
            >
              <Eye className="w-4 h-4" />
            </Button>
          )}
          
          {canUpdateSchools && onSchoolEdit && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onSchoolEdit(school)}
              title="Edit School"
            >
              <Edit className="w-4 h-4" />
            </Button>
          )}
          
          {canUpdateSchools && onSchoolSettings && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onSchoolSettings(school)}
              title="School Settings"
            >
              <Settings className="w-4 h-4" />
            </Button>
          )}
          
          {canUpdateSchools && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleToggleSchoolStatus(school)}
              title={school.is_active ? 'Deactivate' : 'Activate'}
            >
              <Badge variant={school.is_active ? 'destructive' : 'default'}>
                {school.is_active ? 'Deactivate' : 'Activate'}
              </Badge>
            </Button>
          )}
          
          {canDeleteSchools && onSchoolDelete && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onSchoolDelete(school)}
              title="Delete School"
            >
              <Trash2 className="w-4 h-4 text-red-500" />
            </Button>
          )}
        </div>
      ),
    },
  ], [canUpdateSchools, canDeleteSchools, onSchoolSelect, onSchoolEdit, onSchoolSettings, onSchoolDelete, handleToggleSchoolStatus]);

  // Status options for filter
  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: true, label: 'Active' },
    { value: false, label: 'Inactive' },
  ];

  if (!canViewSchools) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          You do not have permission to view schools.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Building2 className="w-5 h-5" />
            <span>Schools</span>
          </CardTitle>
          {/* زر إضافة مدرسة - مؤقت بدون فحص الصلاحيات */}
          {onSchoolCreate && (
            <Button onClick={onSchoolCreate}>
              <Plus className="w-4 h-4 mr-2" />
              {t("schools.addSchool")}
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Filters */}
        <div className="flex items-center space-x-4 mb-6">
          <SearchInput
            placeholder="Search schools..."
            value={state.filters.search}
            onChange={handleSearchChange}
            className="flex-1"
          />
          
          <FilterSelect
            options={statusOptions}
            value={state.filters.isActive}
            onChange={handleActiveFilterChange}
            placeholder="Filter by status"
          />
        </div>

        {/* Error Display */}
        {state.error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{state.error}</AlertDescription>
          </Alert>
        )}

        {/* Data Table */}
        <DataTable
          data={state.schools}
          columns={columns}
          loading={state.loading}
          emptyMessage="No schools found"
        />

        {/* Pagination */}
        <div className="mt-6">
          <Pagination
            currentPage={state.pagination.page}
            totalPages={state.pagination.totalPages}
            pageSize={state.pagination.limit}
            totalItems={state.pagination.total}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        </div>
      </CardContent>
    </Card>
  );
};
