/**
 * نظام إعادة هيكلة الكود - المرحلة الثانية
 * Code Restructuring System - Phase 2
 */

import { readFileSync, writeFileSync, mkdirSync, existsSync, readdirSync, statSync } from 'fs';
import { join, dirname, extname, basename } from 'path';

export class CodeRestructuringService {
  private restructureLog: any[] = [];
  private srcPath: string;
  private newStructure: any;

  constructor() {
    this.srcPath = join(process.cwd(), 'src');
    this.newStructure = this.defineNewStructure();
  }

  /**
   * تعريف البنية الجديدة للمشروع
   */
  private defineNewStructure(): any {
    return {
      // البنية الجديدة المنظمة
      'core/': {
        description: 'الملفات الأساسية والمشتركة',
        subdirs: {
          'types/': 'تعريفات الأنواع',
          'constants/': 'الثوابت والإعدادات',
          'utils/': 'الأدوات المساعدة',
          'hooks/': 'React Hooks المخصصة',
          'contexts/': 'React Contexts'
        }
      },
      'features/': {
        description: 'الميزات مقسمة حسب الوظيفة',
        subdirs: {
          'auth/': 'نظام المصادقة والأمان',
          'dashboard/': 'لوحات التحكم',
          'buses/': 'إدارة الحافلات',
          'routes/': 'إدارة المسارات',
          'students/': 'إدارة الطلاب',
          'drivers/': 'إدارة السائقين',
          'attendance/': 'نظام الحضور',
          'notifications/': 'نظام الإشعارات',
          'reports/': 'نظام التقارير',
          'tracking/': 'نظام التتبع',
          'maintenance/': 'نظام الصيانة'
        }
      },
      'shared/': {
        description: 'المكونات والخدمات المشتركة',
        subdirs: {
          'components/': 'مكونات UI مشتركة',
          'services/': 'الخدمات المشتركة',
          'layouts/': 'تخطيطات الصفحات',
          'guards/': 'حراس الصفحات والصلاحيات'
        }
      },
      'assets/': {
        description: 'الملفات الثابتة',
        subdirs: {
          'images/': 'الصور',
          'icons/': 'الأيقونات',
          'styles/': 'ملفات CSS',
          'locales/': 'ملفات الترجمة'
        }
      }
    };
  }

  /**
   * تحليل البنية الحالية
   */
  async analyzeCurrentStructure(): Promise<any> {
    console.log('🔍 تحليل البنية الحالية للكود...');

    const analysis = {
      totalFiles: 0,
      filesByType: new Map(),
      filesByDirectory: new Map(),
      duplicateFiles: [],
      largeFiles: [],
      issues: []
    };

    try {
      await this.scanDirectory(this.srcPath, analysis);
      
      console.log(`  📊 إجمالي الملفات: ${analysis.totalFiles}`);
      console.log(`  📁 عدد المجلدات: ${analysis.filesByDirectory.size}`);
      
      // عرض أنواع الملفات
      console.log('  📋 أنواع الملفات:');
      for (const [type, count] of analysis.filesByType) {
        console.log(`    ${type}: ${count} ملف`);
      }

      // عرض الملفات الكبيرة
      if (analysis.largeFiles.length > 0) {
        console.log('  ⚠️ ملفات كبيرة (>500 سطر):');
        analysis.largeFiles.forEach(file => {
          console.log(`    ${file.path}: ${file.lines} سطر`);
        });
      }

      this.restructureLog.push({
        task: 'تحليل البنية الحالية',
        status: 'success',
        details: analysis
      });

      return analysis;

    } catch (error) {
      console.error('❌ خطأ في تحليل البنية:', error);
      this.restructureLog.push({
        task: 'تحليل البنية الحالية',
        status: 'error',
        message: error instanceof Error ? error.message : 'خطأ غير معروف'
      });
      throw error;
    }
  }

  /**
   * مسح مجلد بشكل تكراري
   */
  private async scanDirectory(dirPath: string, analysis: any): Promise<void> {
    const items = readdirSync(dirPath);

    for (const item of items) {
      const fullPath = join(dirPath, item);
      const stat = statSync(fullPath);

      if (stat.isDirectory()) {
        // تجاهل مجلدات معينة
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          analysis.filesByDirectory.set(fullPath, 0);
          await this.scanDirectory(fullPath, analysis);
        }
      } else if (stat.isFile()) {
        analysis.totalFiles++;
        
        const ext = extname(item);
        const currentCount = analysis.filesByType.get(ext) || 0;
        analysis.filesByType.set(ext, currentCount + 1);

        // فحص حجم الملف
        if (['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
          const content = readFileSync(fullPath, 'utf8');
          const lines = content.split('\n').length;
          
          if (lines > 500) {
            analysis.largeFiles.push({
              path: fullPath.replace(process.cwd(), ''),
              lines: lines
            });
          }
        }

        // تحديث عداد المجلد
        const dirCount = analysis.filesByDirectory.get(dirPath) || 0;
        analysis.filesByDirectory.set(dirPath, dirCount + 1);
      }
    }
  }

  /**
   * إنشاء البنية الجديدة
   */
  async createNewStructure(): Promise<void> {
    console.log('🏗️ إنشاء البنية الجديدة...');

    const newSrcPath = join(process.cwd(), 'src-new');

    try {
      // إنشاء المجلد الجديد
      if (!existsSync(newSrcPath)) {
        mkdirSync(newSrcPath, { recursive: true });
      }

      // إنشاء المجلدات الرئيسية
      for (const [mainDir, config] of Object.entries(this.newStructure)) {
        const mainDirPath = join(newSrcPath, mainDir);
        mkdirSync(mainDirPath, { recursive: true });
        
        console.log(`  📁 تم إنشاء: ${mainDir}`);

        // إنشاء المجلدات الفرعية
        if (config.subdirs) {
          for (const [subDir, description] of Object.entries(config.subdirs)) {
            const subDirPath = join(mainDirPath, subDir);
            mkdirSync(subDirPath, { recursive: true });
            
            // إنشاء ملف README لكل مجلد
            const readmeContent = `# ${subDir}\n\n${description}\n\nهذا المجلد جزء من البنية الجديدة للمشروع - المرحلة الثانية.\n`;
            writeFileSync(join(subDirPath, 'README.md'), readmeContent);
            
            console.log(`    📂 تم إنشاء: ${mainDir}${subDir}`);
          }
        }
      }

      // إنشاء ملف index.ts رئيسي
      const mainIndexContent = `/**
 * الملف الرئيسي للمشروع - البنية الجديدة
 * Main Project Entry Point - New Structure
 * 
 * المرحلة الثانية: التنظيف وإعادة التنظيم
 */

// تصدير الأنواع الأساسية
export * from './core/types';

// تصدير الأدوات المساعدة
export * from './core/utils';

// تصدير الخدمات المشتركة
export * from './shared/services';

// تصدير المكونات المشتركة
export * from './shared/components';

// تصدير الميزات
export * from './features';

console.log('🚀 تم تحميل البنية الجديدة للمشروع');
`;

      writeFileSync(join(newSrcPath, 'index.ts'), mainIndexContent);

      this.restructureLog.push({
        task: 'إنشاء البنية الجديدة',
        status: 'success',
        message: 'تم إنشاء البنية الجديدة بنجاح'
      });

      console.log('  ✅ تم إنشاء البنية الجديدة بنجاح');

    } catch (error) {
      console.error('❌ خطأ في إنشاء البنية الجديدة:', error);
      this.restructureLog.push({
        task: 'إنشاء البنية الجديدة',
        status: 'error',
        message: error instanceof Error ? error.message : 'خطأ غير معروف'
      });
      throw error;
    }
  }

  /**
   * تحليل التبعيات والاستيرادات
   */
  async analyzeDependencies(): Promise<any> {
    console.log('🔗 تحليل التبعيات والاستيرادات...');

    const dependencies = {
      imports: new Map(),
      exports: new Map(),
      circularDeps: [],
      unusedFiles: []
    };

    try {
      await this.scanForDependencies(this.srcPath, dependencies);

      console.log(`  📊 عدد الاستيرادات: ${dependencies.imports.size}`);
      console.log(`  📊 عدد التصديرات: ${dependencies.exports.size}`);

      this.restructureLog.push({
        task: 'تحليل التبعيات',
        status: 'success',
        details: {
          importsCount: dependencies.imports.size,
          exportsCount: dependencies.exports.size
        }
      });

      return dependencies;

    } catch (error) {
      console.error('❌ خطأ في تحليل التبعيات:', error);
      this.restructureLog.push({
        task: 'تحليل التبعيات',
        status: 'error',
        message: error instanceof Error ? error.message : 'خطأ غير معروف'
      });
      throw error;
    }
  }

  /**
   * مسح الملفات للبحث عن التبعيات
   */
  private async scanForDependencies(dirPath: string, dependencies: any): Promise<void> {
    const items = readdirSync(dirPath);

    for (const item of items) {
      const fullPath = join(dirPath, item);
      const stat = statSync(fullPath);

      if (stat.isDirectory()) {
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          await this.scanForDependencies(fullPath, dependencies);
        }
      } else if (stat.isFile() && ['.ts', '.tsx', '.js', '.jsx'].includes(extname(item))) {
        try {
          const content = readFileSync(fullPath, 'utf8');
          
          // البحث عن الاستيرادات
          const importMatches = content.match(/import\s+.*?\s+from\s+['"](.+?)['"]/g);
          if (importMatches) {
            importMatches.forEach(match => {
              const path = match.match(/from\s+['"](.+?)['"]/)?.[1];
              if (path) {
                const currentImports = dependencies.imports.get(fullPath) || [];
                currentImports.push(path);
                dependencies.imports.set(fullPath, currentImports);
              }
            });
          }

          // البحث عن التصديرات
          const exportMatches = content.match(/export\s+.*?/g);
          if (exportMatches) {
            dependencies.exports.set(fullPath, exportMatches.length);
          }

        } catch (error) {
          console.warn(`⚠️ تحذير: لا يمكن قراءة الملف ${fullPath}`);
        }
      }
    }
  }

  /**
   * إنشاء خطة الترحيل
   */
  async createMigrationPlan(): Promise<any> {
    console.log('📋 إنشاء خطة الترحيل...');

    const migrationPlan = {
      phase1: {
        name: 'نقل الملفات الأساسية',
        files: [
          { from: 'src/types', to: 'src-new/core/types' },
          { from: 'src/utils', to: 'src-new/core/utils' },
          { from: 'src/hooks', to: 'src-new/core/hooks' },
          { from: 'src/contexts', to: 'src-new/core/contexts' }
        ]
      },
      phase2: {
        name: 'نقل الخدمات',
        files: [
          { from: 'src/services', to: 'src-new/shared/services' },
          { from: 'src/lib', to: 'src-new/shared/services' }
        ]
      },
      phase3: {
        name: 'نقل المكونات',
        files: [
          { from: 'src/components/ui', to: 'src-new/shared/components/ui' },
          { from: 'src/components/layout', to: 'src-new/shared/layouts' }
        ]
      },
      phase4: {
        name: 'نقل الميزات',
        files: [
          { from: 'src/components/auth', to: 'src-new/features/auth/components' },
          { from: 'src/pages/dashboard', to: 'src-new/features/dashboard' },
          { from: 'src/components/buses', to: 'src-new/features/buses/components' }
        ]
      }
    };

    // حفظ خطة الترحيل
    const planPath = join(process.cwd(), 'migration-plan.json');
    writeFileSync(planPath, JSON.stringify(migrationPlan, null, 2));

    console.log(`  ✅ تم إنشاء خطة الترحيل: ${planPath}`);

    this.restructureLog.push({
      task: 'إنشاء خطة الترحيل',
      status: 'success',
      message: 'تم إنشاء خطة الترحيل بنجاح'
    });

    return migrationPlan;
  }

  /**
   * إنشاء تقرير إعادة الهيكلة
   */
  async generateRestructureReport(): Promise<void> {
    console.log('📊 إنشاء تقرير إعادة الهيكلة...');

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportDir = join(process.cwd(), 'reports', 'code-restructure');
    
    if (!existsSync(reportDir)) {
      mkdirSync(reportDir, { recursive: true });
    }

    const report = {
      restructure_info: {
        timestamp: timestamp,
        created_at: new Date().toISOString(),
        phase: 'Phase 2 - Code Restructuring'
      },
      new_structure: this.newStructure,
      summary: {
        total_tasks: this.restructureLog.length,
        successful_tasks: this.restructureLog.filter(log => log.status === 'success').length,
        error_tasks: this.restructureLog.filter(log => log.status === 'error').length
      },
      detailed_log: this.restructureLog,
      benefits: [
        'تنظيم أفضل للكود حسب الوظيفة',
        'سهولة العثور على الملفات',
        'تقليل التبعيات المتداخلة',
        'تحسين قابلية الصيانة',
        'دعم أفضل للفرق الكبيرة'
      ],
      next_steps: [
        'تنفيذ خطة الترحيل',
        'تحديث مسارات الاستيراد',
        'اختبار البنية الجديدة',
        'تحديث الوثائق'
      ]
    };

    const reportPath = join(reportDir, `restructure-report-${timestamp}.json`);
    writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log(`  ✅ تم إنشاء تقرير إعادة الهيكلة: ${reportPath}`);
  }

  /**
   * تنفيذ إعادة الهيكلة الشاملة
   */
  async performComprehensiveRestructure(): Promise<void> {
    console.log('🔄 بدء إعادة الهيكلة الشاملة للكود...\n');

    try {
      // 1. تحليل البنية الحالية
      await this.analyzeCurrentStructure();
      
      // 2. تحليل التبعيات
      await this.analyzeDependencies();
      
      // 3. إنشاء البنية الجديدة
      await this.createNewStructure();
      
      // 4. إنشاء خطة الترحيل
      await this.createMigrationPlan();
      
      // 5. إنشاء تقرير إعادة الهيكلة
      await this.generateRestructureReport();
      
      console.log('\n✅ تم إكمال إعادة الهيكلة الشاملة للكود بنجاح!');
      
    } catch (error) {
      console.error('❌ خطأ في إعادة هيكلة الكود:', error);
      throw error;
    }
  }
}

/**
 * الدالة الرئيسية
 */
async function main(): Promise<void> {
  console.log('🚀 بدء نظام إعادة هيكلة الكود - المرحلة الثانية\n');

  try {
    const restructureService = new CodeRestructuringService();
    
    // تنفيذ إعادة الهيكلة الشاملة
    await restructureService.performComprehensiveRestructure();
    
    console.log('\n🎉 تم إكمال إعادة هيكلة الكود بنجاح!');
    console.log('\n📋 الخطوات التالية:');
    console.log('1. مراجعة البنية الجديدة في مجلد src-new');
    console.log('2. تنفيذ خطة الترحيل');
    console.log('3. تحديث مسارات الاستيراد');
    console.log('4. اختبار البنية الجديدة');

  } catch (error) {
    console.error('💥 خطأ عام في إعادة هيكلة الكود:', error);
    process.exit(1);
  }
}

// تشغيل السكريبت
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { CodeRestructuringService };
