import React, { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { supabase } from "../../lib/supabase";
import { Button } from "../ui/Button";
import { RefreshCw, Database, AlertTriangle } from "lucide-react";

interface BusDebugInfo {
  totalBuses: number;
  busesByTenant: { [key: string]: number };
  userRole: string;
  tenantId: string;
  rawBusData: any[];
}

export const BusDebugPanel: React.FC = () => {
  const { user, tenant } = useAuth();
  const [debugInfo, setDebugInfo] = useState<BusDebugInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDebugInfo = async () => {
    if (!user || !tenant) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch all buses with tenant info
      const { data: allBuses, error: busError } = await supabase.from("buses")
        .select(`
          *,
          tenant:tenants(id, name)
        `);

      if (busError) throw busError;

      // Count buses by tenant
      const busesByTenant: { [key: string]: number } = {};
      allBuses?.forEach((bus) => {
        const tenantName = bus.tenant?.name || "Unknown";
        busesByTenant[tenantName] = (busesByTenant[tenantName] || 0) + 1;
      });

      setDebugInfo({
        totalBuses: allBuses?.length || 0,
        busesByTenant,
        userRole: user.role || "unknown",
        tenantId: tenant.id,
        rawBusData: allBuses || [],
      });
    } catch (error) {
      console.error("Debug fetch error:", error);
      setError(error instanceof Error ? error.message : "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDebugInfo();
  }, [user, tenant]);

  if (!user || user.role !== "admin") {
    return null;
  }

  return (
    <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-200 flex items-center">
          <Database className="mr-2 h-5 w-5" />
          Bus Debug Panel (Admin Only)
        </h3>
        <Button
          onClick={fetchDebugInfo}
          disabled={loading}
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          <RefreshCw size={16} className={loading ? "animate-spin" : ""} />
          Refresh
        </Button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-300 rounded-md">
          <div className="flex items-center">
            <AlertTriangle className="h-4 w-4 text-red-500 mr-2" />
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        </div>
      )}

      {debugInfo && (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white dark:bg-gray-800 p-3 rounded border">
              <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Buses
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {debugInfo.totalBuses}
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 p-3 rounded border">
              <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
                User Role
              </div>
              <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                {debugInfo.userRole}
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 p-3 rounded border">
              <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Current Tenant
              </div>
              <div className="text-sm text-gray-900 dark:text-white truncate">
                {debugInfo.tenantId}
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-4 rounded border">
            <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
              Buses by School
            </h4>
            <div className="space-y-1">
              {Object.entries(debugInfo.busesByTenant).map(
                ([tenantName, count]) => (
                  <div
                    key={tenantName}
                    className="flex justify-between text-sm"
                  >
                    <span className="text-gray-700 dark:text-gray-300">
                      {tenantName}
                    </span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {count}
                    </span>
                  </div>
                ),
              )}
            </div>
          </div>

          <details className="bg-white dark:bg-gray-800 p-4 rounded border">
            <summary className="text-sm font-medium text-gray-600 dark:text-gray-400 cursor-pointer">
              Raw Bus Data ({debugInfo.rawBusData.length} records)
            </summary>
            <pre className="mt-2 text-xs bg-gray-100 dark:bg-gray-900 p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(debugInfo.rawBusData, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  );
};

export default BusDebugPanel;
