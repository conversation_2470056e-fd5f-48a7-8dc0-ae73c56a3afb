/**
 * أداة إدارة قاعدة البيانات الشاملة
 * Comprehensive Database Administration Tool
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import readline from 'readline';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

class DatabaseAdmin {
  constructor() {
    this.supabase = supabase;
  }

  // فحص حالة قاعدة البيانات
  async checkStatus() {
    console.log('🏥 فحص حالة قاعدة البيانات...\n');
    
    const tables = ['users', 'schools', 'buses', 'routes', 'students', 'notifications'];
    
    for (const table of tables) {
      try {
        const { count, error } = await this.supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        if (error) {
          console.log(`❌ ${table}: ${error.message}`);
        } else {
          console.log(`✅ ${table}: ${count || 0} سجل`);
        }
      } catch (err) {
        console.log(`❌ ${table}: غير متاح`);
      }
    }
  }

  // إصلاح جدول المستخدمين
  async fixUsersTable() {
    console.log('🔧 إصلاح جدول المستخدمين...');
    
    try {
      // التحقق من الأعمدة الموجودة
      const { data: columns, error } = await this.supabase
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_name', 'users')
        .eq('table_schema', 'public');
      
      if (error) {
        console.log('❌ فشل فحص أعمدة الجدول:', error.message);
        return false;
      }
      
      const columnNames = columns.map(col => col.column_name);
      console.log('📋 الأعمدة الموجودة:', columnNames);
      
      // إضافة العمود المفقود إذا لم يكن موجوداً
      if (!columnNames.includes('full_name')) {
        console.log('➕ إضافة عمود full_name...');
        
        const { error: alterError } = await this.supabase.rpc('exec', {
          sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS full_name TEXT;'
        });
        
        if (alterError) {
          console.log('❌ فشل إضافة العمود:', alterError.message);
          return false;
        }
        
        console.log('✅ تم إضافة عمود full_name');
      }
      
      return true;
    } catch (err) {
      console.error('❌ خطأ في إصلاح جدول المستخدمين:', err);
      return false;
    }
  }

  // تفعيل RLS
  async enableRLS(tableName) {
    try {
      console.log(`🛡️ تفعيل RLS لجدول ${tableName}...`);
      
      const { error } = await this.supabase.rpc('exec', {
        sql: `ALTER TABLE ${tableName} ENABLE ROW LEVEL SECURITY;`
      });
      
      if (error) {
        console.log(`❌ فشل تفعيل RLS:`, error.message);
        return false;
      }
      
      console.log(`✅ تم تفعيل RLS لجدول ${tableName}`);
      return true;
    } catch (err) {
      console.log(`❌ خطأ في تفعيل RLS:`, err.message);
      return false;
    }
  }

  // إلغاء تفعيل RLS
  async disableRLS(tableName) {
    try {
      console.log(`🔓 إلغاء تفعيل RLS لجدول ${tableName}...`);
      
      const { error } = await this.supabase.rpc('exec', {
        sql: `ALTER TABLE ${tableName} DISABLE ROW LEVEL SECURITY;`
      });
      
      if (error) {
        console.log(`❌ فشل إلغاء تفعيل RLS:`, error.message);
        return false;
      }
      
      console.log(`✅ تم إلغاء تفعيل RLS لجدول ${tableName}`);
      return true;
    } catch (err) {
      console.log(`❌ خطأ في إلغاء تفعيل RLS:`, err.message);
      return false;
    }
  }

  // إنشاء سياسة بسيطة للمدراء
  async createAdminPolicy(tableName) {
    try {
      console.log(`📝 إنشاء سياسة المدراء لجدول ${tableName}...`);
      
      const policyName = `admin_all_access_${tableName}`;
      
      const { error } = await this.supabase.rpc('exec', {
        sql: `
          CREATE POLICY "${policyName}" ON ${tableName}
          FOR ALL
          USING (
            EXISTS (
              SELECT 1 FROM users 
              WHERE users.id = auth.uid() 
              AND users.role = 'admin'
            )
          );
        `
      });
      
      if (error) {
        console.log(`❌ فشل إنشاء السياسة:`, error.message);
        return false;
      }
      
      console.log(`✅ تم إنشاء سياسة المدراء لجدول ${tableName}`);
      return true;
    } catch (err) {
      console.log(`❌ خطأ في إنشاء السياسة:`, err.message);
      return false;
    }
  }

  // حذف جميع السياسات
  async dropAllPolicies(tableName) {
    try {
      console.log(`🗑️ حذف جميع سياسات ${tableName}...`);
      
      // الحصول على أسماء السياسات
      const { data: policies, error } = await this.supabase
        .from('pg_policies')
        .select('policyname')
        .eq('tablename', tableName);
      
      if (error) {
        console.log('❌ فشل الحصول على السياسات:', error.message);
        return false;
      }
      
      // حذف كل سياسة
      for (const policy of policies) {
        const { error: dropError } = await this.supabase.rpc('exec', {
          sql: `DROP POLICY IF EXISTS "${policy.policyname}" ON ${tableName};`
        });
        
        if (dropError) {
          console.log(`❌ فشل حذف السياسة ${policy.policyname}:`, dropError.message);
        } else {
          console.log(`✅ تم حذف السياسة ${policy.policyname}`);
        }
      }
      
      return true;
    } catch (err) {
      console.log(`❌ خطأ في حذف السياسات:`, err.message);
      return false;
    }
  }

  // إنشاء مستخدم إداري
  async createAdmin(email, password, fullName) {
    try {
      console.log(`👤 إنشاء مستخدم إداري: ${email}...`);
      
      const { data, error } = await this.supabase.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
        user_metadata: {
          full_name: fullName,
          role: 'admin'
        }
      });
      
      if (error) {
        console.log(`❌ فشل إنشاء المستخدم:`, error.message);
        return false;
      }
      
      // إضافة إلى جدول users
      const { error: insertError } = await this.supabase
        .from('users')
        .insert({
          id: data.user.id,
          email: email,
          full_name: fullName,
          role: 'admin',
          is_active: true
        });
      
      if (insertError) {
        console.log(`❌ فشل إضافة المستخدم إلى الجدول:`, insertError.message);
        return false;
      }
      
      console.log(`✅ تم إنشاء المستخدم الإداري: ${data.user.id}`);
      return true;
    } catch (err) {
      console.log(`❌ خطأ في إنشاء المستخدم:`, err.message);
      return false;
    }
  }

  // تنفيذ SQL مخصص
  async executeCustomSQL(query) {
    try {
      console.log('⚡ تنفيذ استعلام SQL مخصص...');
      
      const { data, error } = await this.supabase.rpc('exec', {
        sql: query
      });
      
      if (error) {
        console.log('❌ فشل تنفيذ الاستعلام:', error.message);
        return false;
      }
      
      console.log('✅ تم تنفيذ الاستعلام بنجاح');
      if (data) {
        console.log('📊 النتيجة:', data);
      }
      return true;
    } catch (err) {
      console.log('❌ خطأ في تنفيذ الاستعلام:', err.message);
      return false;
    }
  }

  // إعداد قاعدة البيانات الأساسية
  async setupBasicDatabase() {
    console.log('🚀 إعداد قاعدة البيانات الأساسية...\n');
    
    // إصلاح جدول المستخدمين
    await this.fixUsersTable();
    
    // إلغاء تفعيل RLS لجميع الجداول (للاختبار)
    const tables = ['users', 'schools', 'buses', 'routes', 'students', 'notifications'];
    
    for (const table of tables) {
      await this.disableRLS(table);
      await this.dropAllPolicies(table);
    }
    
    console.log('\n✅ تم إعداد قاعدة البيانات الأساسية');
  }

  // إعداد الأمان المتقدم
  async setupAdvancedSecurity() {
    console.log('🛡️ إعداد الأمان المتقدم...\n');
    
    const tables = ['users', 'schools', 'buses', 'routes', 'students', 'notifications'];
    
    for (const table of tables) {
      await this.enableRLS(table);
      await this.createAdminPolicy(table);
    }
    
    console.log('\n✅ تم إعداد الأمان المتقدم');
  }
}

// واجهة سطر الأوامر
function showMenu() {
  console.log('\n📋 أداة إدارة قاعدة البيانات');
  console.log('1. فحص حالة قاعدة البيانات');
  console.log('2. إعداد قاعدة البيانات الأساسية');
  console.log('3. إعداد الأمان المتقدم');
  console.log('4. إنشاء مستخدم إداري');
  console.log('5. تنفيذ SQL مخصص');
  console.log('6. إصلاح جدول المستخدمين');
  console.log('0. خروج');
  console.log('');
}

async function handleUserInput(admin) {
  return new Promise((resolve) => {
    rl.question('اختر رقم العملية: ', async (choice) => {
      switch (choice) {
        case '1':
          await admin.checkStatus();
          break;
          
        case '2':
          await admin.setupBasicDatabase();
          break;
          
        case '3':
          await admin.setupAdvancedSecurity();
          break;
          
        case '4':
          rl.question('البريد الإلكتروني: ', (email) => {
            rl.question('كلمة المرور: ', (password) => {
              rl.question('الاسم الكامل: ', async (fullName) => {
                await admin.createAdmin(email, password, fullName);
                resolve();
              });
            });
          });
          return;
          
        case '5':
          rl.question('أدخل استعلام SQL: ', async (query) => {
            await admin.executeCustomSQL(query);
            resolve();
          });
          return;
          
        case '6':
          await admin.fixUsersTable();
          break;
          
        case '0':
          console.log('👋 وداعاً!');
          rl.close();
          process.exit(0);
          
        default:
          console.log('❌ اختيار غير صحيح');
      }
      resolve();
    });
  });
}

async function main() {
  const admin = new DatabaseAdmin();
  
  console.log('🚀 مرحباً بك في أداة إدارة قاعدة البيانات');
  
  while (true) {
    showMenu();
    await handleUserInput(admin);
  }
}

// تشغيل البرنامج
if (process.argv[2]) {
  // تشغيل أمر مباشر
  const admin = new DatabaseAdmin();
  const command = process.argv[2];
  
  switch (command) {
    case 'status':
      admin.checkStatus().then(() => process.exit(0));
      break;
    case 'setup':
      admin.setupBasicDatabase().then(() => process.exit(0));
      break;
    case 'security':
      admin.setupAdvancedSecurity().then(() => process.exit(0));
      break;
    default:
      console.log('الأوامر المتاحة: status, setup, security');
      process.exit(1);
  }
} else {
  // تشغيل الواجهة التفاعلية
  main().catch(console.error);
}
