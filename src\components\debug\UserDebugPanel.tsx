import React, { useState } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";
import { Button } from "../ui/Button";
import { EyeOff, RefreshCw } from "lucide-react";

export const UserDebugPanel: React.FC = () => {
  const { user } = useAuth();
  const { users, refreshData } = useDatabase();
  const [isVisible, setIsVisible] = useState(false);
  const [dbUsers, setDbUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchDirectFromDB = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("users")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching users directly:", error);
      } else {
        console.log("Users from direct DB query:", data);
        setDbUsers(data || []);
      }
    } catch (err) {
      console.error("Error in direct DB fetch:", err);
    } finally {
      setLoading(false);
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-4 max-w-md max-h-96 overflow-y-auto">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          User Debug Panel
        </h3>
        <Button
          onClick={() => setIsVisible(false)}
          className="p-1 text-gray-500 hover:text-gray-700"
        >
          <EyeOff size={16} />
        </Button>
      </div>

      <div className="space-y-4">
        <div>
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">
            Current User Info:
          </h4>
          <div className="text-sm text-gray-600 dark:text-gray-300">
            <p>ID: {user?.id}</p>
            <p>Role: {user?.role}</p>
            <p>Tenant ID: {user?.tenant_id || "null"}</p>
            <p>Email: {user?.email}</p>
          </div>
        </div>

        <div>
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium text-gray-900 dark:text-white">
              Context Users ({users.length}):
            </h4>
            <Button
              onClick={refreshData}
              className="p-1 text-blue-600 hover:text-blue-800"
              title="Refresh Context Data"
            >
              <RefreshCw size={14} />
            </Button>
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-300 max-h-32 overflow-y-auto">
            {users.length === 0 ? (
              <p>No users in context</p>
            ) : (
              users.map((u) => (
                <div
                  key={u.id}
                  className="border-b border-gray-200 dark:border-gray-600 py-1"
                >
                  <p className="font-medium">{u.name}</p>
                  <p className="text-xs">
                    {u.email} - {u.role}
                  </p>
                  <p className="text-xs">Tenant: {u.tenant_id || "null"}</p>
                </div>
              ))
            )}
          </div>
        </div>

        <div>
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium text-gray-900 dark:text-white">
              Direct DB Users ({dbUsers.length}):
            </h4>
            <Button
              onClick={fetchDirectFromDB}
              disabled={loading}
              className="p-1 text-green-600 hover:text-green-800"
              title="Fetch Direct from DB"
            >
              <RefreshCw size={14} className={loading ? "animate-spin" : ""} />
            </Button>
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-300 max-h-32 overflow-y-auto">
            {dbUsers.length === 0 ? (
              <p>No users fetched from DB</p>
            ) : (
              dbUsers.map((u) => (
                <div
                  key={u.id}
                  className="border-b border-gray-200 dark:border-gray-600 py-1"
                >
                  <p className="font-medium">{u.name}</p>
                  <p className="text-xs">
                    {u.email} - {u.role}
                  </p>
                  <p className="text-xs">Tenant: {u.tenant_id || "null"}</p>
                  <p className="text-xs">
                    Created: {new Date(u.created_at).toLocaleString()}
                  </p>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
