/**
 * Advanced Resource Management Service
 * Phase 4: Core System Functionality Development
 */

import { supabase } from '../lib/supabase';
import { PermissionService } from '../lib/permissionService';
import { ResourceType, Action } from '../lib/rbac';

export interface ResourceFilter {
  search?: string;
  status?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  category?: string;
  tenantId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface ResourceStats {
  total: number;
  active: number;
  inactive: number;
  pending: number;
  categories: Record<string, number>;
  trends: {
    daily: number[];
    weekly: number[];
    monthly: number[];
  };
}

export interface BulkOperation {
  action: 'activate' | 'deactivate' | 'delete' | 'update';
  resourceIds: string[];
  data?: any;
}

export class ResourceManagementService {
  private static instance: ResourceManagementService;
  private permissionService = PermissionService.getInstance();

  private constructor() {}

  static getInstance(): ResourceManagementService {
    if (!ResourceManagementService.instance) {
      ResourceManagementService.instance = new ResourceManagementService();
    }
    return ResourceManagementService.instance;
  }

  /**
   * Advanced Student Management
   */
  async getStudentsAdvanced(filter: ResourceFilter, userId: string): Promise<{
    data: any[];
    stats: ResourceStats;
    pagination: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }> {
    try {
      // Build query with filters
      let query = supabase
        .from('students')
        .select(`
          *,
          parent:users!parent_id(id, name, email, phone),
          route_stop:route_stops!route_stop_id(
            id, name,
            route:routes!route_id(id, name)
          ),
          attendance_stats:attendance(
            id,
            type,
            recorded_at
          )
        `, { count: 'exact' });

      // Apply filters
      if (filter.tenantId) {
        query = query.eq('tenant_id', filter.tenantId);
      }

      if (filter.search) {
        query = query.or(`name.ilike.%${filter.search}%,grade.ilike.%${filter.search}%`);
      }

      if (filter.status) {
        query = query.eq('is_active', filter.status === 'active');
      }

      if (filter.category) {
        query = query.eq('grade', filter.category);
      }

      if (filter.dateRange) {
        query = query
          .gte('created_at', filter.dateRange.start)
          .lte('created_at', filter.dateRange.end);
      }

      // Apply sorting
      const sortBy = filter.sortBy || 'created_at';
      const sortOrder = filter.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      const limit = filter.limit || 20;
      const offset = filter.offset || 0;
      query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) throw error;

      // Calculate statistics
      const stats = await this.calculateStudentStats(filter.tenantId);

      return {
        data: data || [],
        stats,
        pagination: {
          total: count || 0,
          page: Math.floor(offset / limit) + 1,
          limit,
          totalPages: Math.ceil((count || 0) / limit),
        },
      };
    } catch (error) {
      console.error('Error getting students:', error);
      throw error;
    }
  }

  /**
   * Advanced Bus Management
   */
  async getBusesAdvanced(filter: ResourceFilter, userId: string): Promise<{
    data: any[];
    stats: ResourceStats;
    pagination: any;
  }> {
    try {
      let query = supabase
        .from('buses')
        .select(`
          *,
          driver:users!driver_id(id, name, email, phone),
          route:routes!bus_id(id, name, is_active),
          maintenance_records:bus_maintenance(
            id, type, status, scheduled_date, cost
          ),
          current_location:bus_locations(
            latitude, longitude, timestamp
          )
        `, { count: 'exact' });

      // Apply filters similar to students
      if (filter.tenantId) {
        query = query.eq('tenant_id', filter.tenantId);
      }

      if (filter.search) {
        query = query.or(`plate_number.ilike.%${filter.search}%`);
      }

      if (filter.status) {
        query = query.eq('is_active', filter.status === 'active');
      }

      // Apply sorting and pagination
      const sortBy = filter.sortBy || 'created_at';
      const sortOrder = filter.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      const limit = filter.limit || 20;
      const offset = filter.offset || 0;
      query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) throw error;

      const stats = await this.calculateBusStats(filter.tenantId);

      return {
        data: data || [],
        stats,
        pagination: {
          total: count || 0,
          page: Math.floor(offset / limit) + 1,
          limit,
          totalPages: Math.ceil((count || 0) / limit),
        },
      };
    } catch (error) {
      console.error('Error getting buses:', error);
      throw error;
    }
  }

  /**
   * Advanced Route Management
   */
  async getRoutesAdvanced(filter: ResourceFilter, userId: string): Promise<{
    data: any[];
    stats: ResourceStats;
    pagination: any;
  }> {
    try {
      let query = supabase
        .from('routes')
        .select(`
          *,
          bus:buses!bus_id(id, plate_number, capacity, driver_id),
          route_stops(
            id, name, location, arrival_time, "order"
          ),
          students:students!route_stop_id(
            id, name, grade
          )
        `, { count: 'exact' });

      // Apply filters
      if (filter.tenantId) {
        query = query.eq('tenant_id', filter.tenantId);
      }

      if (filter.search) {
        query = query.ilike('name', `%${filter.search}%`);
      }

      if (filter.status) {
        query = query.eq('is_active', filter.status === 'active');
      }

      // Apply sorting and pagination
      const sortBy = filter.sortBy || 'created_at';
      const sortOrder = filter.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      const limit = filter.limit || 20;
      const offset = filter.offset || 0;
      query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) throw error;

      const stats = await this.calculateRouteStats(filter.tenantId);

      return {
        data: data || [],
        stats,
        pagination: {
          total: count || 0,
          page: Math.floor(offset / limit) + 1,
          limit,
          totalPages: Math.ceil((count || 0) / limit),
        },
      };
    } catch (error) {
      console.error('Error getting routes:', error);
      throw error;
    }
  }

  /**
   * Bulk Operations
   */
  async performBulkOperation(
    resourceType: 'students' | 'buses' | 'routes',
    operation: BulkOperation,
    userId: string
  ): Promise<{ success: boolean; affected: number; errors: string[] }> {
    try {
      const errors: string[] = [];
      let affected = 0;

      for (const resourceId of operation.resourceIds) {
        try {
          switch (operation.action) {
            case 'activate':
              await supabase
                .from(resourceType)
                .update({ is_active: true })
                .eq('id', resourceId);
              break;

            case 'deactivate':
              await supabase
                .from(resourceType)
                .update({ is_active: false })
                .eq('id', resourceId);
              break;

            case 'delete':
              await supabase
                .from(resourceType)
                .delete()
                .eq('id', resourceId);
              break;

            case 'update':
              if (operation.data) {
                await supabase
                  .from(resourceType)
                  .update(operation.data)
                  .eq('id', resourceId);
              }
              break;
          }
          affected++;
        } catch (error) {
          errors.push(`Failed to ${operation.action} ${resourceId}: ${error}`);
        }
      }

      return {
        success: errors.length === 0,
        affected,
        errors,
      };
    } catch (error) {
      console.error('Error performing bulk operation:', error);
      throw error;
    }
  }

  /**
   * Calculate Statistics
   */
  private async calculateStudentStats(tenantId?: string): Promise<ResourceStats> {
    try {
      let query = supabase.from('students').select('*', { count: 'exact' });
      
      if (tenantId) {
        query = query.eq('tenant_id', tenantId);
      }

      const { data, count } = await query;

      const active = data?.filter(s => s.is_active).length || 0;
      const inactive = (count || 0) - active;

      // Calculate grade distribution
      const categories: Record<string, number> = {};
      data?.forEach(student => {
        categories[student.grade] = (categories[student.grade] || 0) + 1;
      });

      return {
        total: count || 0,
        active,
        inactive,
        pending: 0,
        categories,
        trends: {
          daily: [], // TODO: Implement trend calculation
          weekly: [],
          monthly: [],
        },
      };
    } catch (error) {
      console.error('Error calculating student stats:', error);
      throw error;
    }
  }

  private async calculateBusStats(tenantId?: string): Promise<ResourceStats> {
    // Similar implementation for buses
    return {
      total: 0,
      active: 0,
      inactive: 0,
      pending: 0,
      categories: {},
      trends: { daily: [], weekly: [], monthly: [] },
    };
  }

  private async calculateRouteStats(tenantId?: string): Promise<ResourceStats> {
    // Similar implementation for routes
    return {
      total: 0,
      active: 0,
      inactive: 0,
      pending: 0,
      categories: {},
      trends: { daily: [], weekly: [], monthly: [] },
    };
  }
}

export default ResourceManagementService;
