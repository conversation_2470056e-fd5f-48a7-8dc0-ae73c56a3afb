import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { ThemeProvider } from '@contexts/ThemeContext'
import { AuthProvider } from '@contexts/AuthContext'
import { CustomThemeProvider } from '@contexts/CustomThemeContext'

// إعداد المزودين للاختبار
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <BrowserRouter>
      <ThemeProvider>
        <CustomThemeProvider>
          <AuthProvider>
            {children}
          </AuthProvider>
        </CustomThemeProvider>
      </ThemeProvider>
    </BrowserRouter>
  )
}

// دالة render مخصصة مع المزودين
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => render(ui, { wrapper: AllTheProviders, ...options })

// إعادة تصدير كل شيء
export * from '@testing-library/react'
export { customRender as render }

// مساعدات اختبار مخصصة
export const createMockUser = (overrides = {}) => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  role: 'admin',
  tenant_id: 'test-tenant',
  ...overrides
})

export const createMockTenant = (overrides = {}) => ({
  id: 'test-tenant',
  name: 'Test School',
  domain: 'test-school.com',
  settings: {},
  ...overrides
})

// Mock للـ Supabase
export const mockSupabase = {
  auth: {
    getUser: vi.fn(),
    signInWithPassword: vi.fn(),
    signOut: vi.fn(),
    onAuthStateChange: vi.fn(),
  },
  from: vi.fn(() => ({
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    single: vi.fn(),
  })),
}

// Mock للـ React Router
export const mockNavigate = vi.fn()
export const mockLocation = {
  pathname: '/',
  search: '',
  hash: '',
  state: null,
  key: 'default',
}

// دالة لإنشاء mock للـ context
export const createMockContext = <T>(defaultValue: T) => {
  const MockContext = React.createContext<T>(defaultValue)
  
  const MockProvider = ({ 
    children, 
    value = defaultValue 
  }: { 
    children: React.ReactNode
    value?: T 
  }) => (
    <MockContext.Provider value={value}>
      {children}
    </MockContext.Provider>
  )
  
  return { MockContext, MockProvider }
}

// دالة لانتظار التحديثات غير المتزامنة
export const waitForNextUpdate = () => 
  new Promise(resolve => setTimeout(resolve, 0))

// دالة لمحاكاة الأحداث
export const createMockEvent = (type: string, properties = {}) => ({
  type,
  preventDefault: vi.fn(),
  stopPropagation: vi.fn(),
  target: { value: '' },
  ...properties,
})

// دالة لمحاكاة الاستجابات
export const createMockResponse = <T>(data: T, success = true) => ({
  data: success ? data : null,
  error: success ? null : { message: 'Test error' },
  status: success ? 200 : 400,
})

// دالة لمحاكاة التأخير
export const delay = (ms: number) => 
  new Promise(resolve => setTimeout(resolve, ms))

// دالة لمحاكاة الأخطاء
export const createMockError = (message = 'Test error') => 
  new Error(message)

// دالة لتنظيف المحاكيات
export const clearAllMocks = () => {
  vi.clearAllMocks()
  localStorage.clear()
  sessionStorage.clear()
}
