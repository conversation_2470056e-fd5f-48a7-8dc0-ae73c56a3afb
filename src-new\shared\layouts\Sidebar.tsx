import React, { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";
import {
  LayoutDashboard,
  School,
  Bus,
  MapPin,
  Users,
  FileBarChart,
  Settings,
  User,
  LogOut,
  Menu,
  X,
  Bell,
  Star,
  ClipboardCheck,
  Route,
  Palette,
  Navigation,
  Wrench,
  MessageSquare,
  Activity,
  Calendar,
} from "lucide-react";
import { useAuth } from "../../contexts/AuthContext";
import { useTheme } from "../../contexts/ThemeContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { ResourceType } from "../../lib/rbac";
import { UserRole } from "../../types";
import { Button } from "../ui/Button";
import { usePermissions } from "../../hooks/usePermissions";
import { Permission } from "../../lib/rbac";
import { useCanAccessThemes } from "../../hooks/useThemePermissions";
import { SidebarLogo } from "../theme/SchoolLogo";

interface SidebarProps {
  isOpen?: boolean;
  onToggle?: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({
  isOpen = false,
  onToggle,
}) => {
  const [isMobileOpen, setIsMobileOpen] = useState(isOpen);

  // Sync with external control if provided
  useEffect(() => {
    if (onToggle) {
      setIsMobileOpen(isOpen);
    }
  }, [isOpen, onToggle]);

  const handleToggle = () => {
    const newState = !isMobileOpen;
    setIsMobileOpen(newState);
    if (onToggle) onToggle();
  };
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const { user, tenant, logout } = useAuth();
  const { direction } = useTheme();
  const { users, tenants, buses, students, routes } = useDatabase();
  const {
    hasPermission,
    isAdmin,
    isSchoolManager,
    isSupervisor,
    isDriver,
    isParent,
    isStudent
  } = usePermissions();

  // دالة تصفية البيانات حسب الدور
  const filterDataByRole = (data: any[], resourceType: string, ownerField?: string) => {
    if (!user) return [];

    // الأدمن يرى كل شيء
    if (user.role === UserRole.ADMIN) {
      return data;
    }

    // مدير المدرسة يرى بيانات مدرسته فقط
    if (user.role === UserRole.SCHOOL_MANAGER) {
      return data.filter(item => item.tenant_id === user.tenant_id);
    }

    // المستخدمون الآخرون يرون بياناتهم فقط
    if (ownerField) {
      return data.filter(item => item[ownerField] === user.id);
    }

    // إذا كان هناك tenant_id، تحقق منه
    return data.filter(item => item.tenant_id === user.tenant_id);
  };

  // Check theme access permissions
  const canAccessThemes = user?.role === UserRole.ADMIN || user?.role === UserRole.SCHOOL_MANAGER;

  // Update direction when language changes
  useEffect(() => {
    if (i18n.language === "ar") {
      document.documentElement.setAttribute("dir", "rtl");
    } else {
      document.documentElement.setAttribute("dir", "ltr");
    }
  }, [i18n.language]);

  // استخدام النظام الجديد للتنقل مع الصلاحيات
  const getNavItems = () => {
    if (!user) return [];

    // الحصول على البيانات المفلترة للشارات
    const filteredBuses = filterDataByRole(buses, "bus", "driver_id");
    const filteredStudents = filterDataByRole(students, "student", "parent_id");
    const filteredRoutes = filterDataByRole(routes, "route");

    const navigationItems = [
      {
        key: "dashboard",
        to: "/dashboard",
        icon: <LayoutDashboard size={20} />,
        label: t("nav.dashboard"),
        permission: null, // الداشبورد متاح للجميع
      },
      {
        key: "schools",
        to: "/dashboard/schools",
        icon: <School size={20} />,
        label: t("nav.schools"),
        permission: Permission.TENANTS_VIEW,
        badge: isAdmin && tenants ? tenants.length : undefined,
        adminOnly: true,
      },
      {
        key: "users",
        to: "/dashboard/users",
        icon: <Users size={20} />,
        label: isAdmin ? t("nav.allUsers") : t("nav.users"),
        permission: Permission.USERS_VIEW,
        badge: filterDataByRole(users, "user").length,
      },
      {
        key: "buses",
        to: "/dashboard/buses",
        icon: <Bus size={20} />,
        label: isAdmin ? t("nav.allBuses") : t("nav.buses"),
        permission: Permission.BUSES_VIEW,
        badge: filteredBuses.filter((b) => b.is_active).length,
      },
      {
        key: "routes",
        to: isDriver ? "/dashboard/my-route" : "/dashboard/routes",
        icon: <Route size={20} />,
        label: isDriver ? t("routes.manageRoutes") : isAdmin ? t("nav.allRoutes") : t("nav.routes"),
        permission: Permission.ROUTES_VIEW,
        badge: !isDriver ? filteredRoutes.filter((r) => r.is_active).length : undefined,
      },
      {
        key: "students",
        to: isParent ? "/dashboard/children" : "/dashboard/students",
        icon: <Users size={20} />,
        label: isParent ? t("students.manageStudents") : isAdmin ? t("nav.allStudents") : t("nav.students"),
        permission: Permission.STUDENTS_VIEW,
        badge: !isParent ? filteredStudents.filter((s) => s.is_active).length : undefined,
      },
      {
        key: "attendance",
        to: isDriver ? "/dashboard/driver-attendance" : "/dashboard/attendance",
        icon: <ClipboardCheck size={20} />,
        label: t("students.attendance"),
        permission: Permission.ATTENDANCE_VIEW,
      },
      {
        key: "tracking",
        to: "/dashboard/tracking",
        icon: <Navigation size={20} />,
        label: t("tracking.realTimeTracking"),
        permission: Permission.BUSES_VIEW,
        featureFlag: tenant?.settings?.features?.gpsTracking !== false,
      },
      {
        key: "maintenance",
        to: "/dashboard/maintenance",
        icon: <Wrench size={20} />,
        label: t("maintenance.title"),
        permission: Permission.BUSES_EDIT,
      },
      {
        key: "advanced-attendance",
        to: "/dashboard/advanced-attendance",
        icon: <Activity size={20} />,
        label: t("attendance.advancedAttendance"),
        permission: Permission.ATTENDANCE_VIEW,
      },
      {
        key: "reports",
        to: "/dashboard/reports",
        icon: <FileBarChart size={20} />,
        label: isAdmin ? t("nav.systemReports") : t("nav.reports"),
        permission: Permission.REPORTS_VIEW,
      },
      {
        key: "notifications",
        to: "/dashboard/notifications",
        icon: <MessageSquare size={20} />,
        label: isAdmin ? t("nav.smartNotifications") : t("nav.smartNotifications"),
        permission: Permission.NOTIFICATIONS_VIEW,
        featureFlag: tenant?.settings?.features?.notifications !== false,
      },
      {
        key: "evaluation",
        to: "/dashboard/evaluation",
        icon: <Star size={20} />,
        label: t("evaluation.title"),
        permission: Permission.REPORTS_VIEW,
        excludeRoles: ["student", "driver"],
      },
      // إضافة الثيمات للأدمن ومدير المدرسة فقط
      ...(isAdmin || isSchoolManager ? [{
        key: "themes",
        to: "/dashboard/themes",
        icon: <Palette size={20} />,
        label: isAdmin ? t("themes.manageThemes") : t("themes.schoolTheme"),
        permission: null, // فحص مخصص في المكون
      }] : []),
      {
        key: "settings",
        to: "/dashboard/settings",
        icon: <Settings size={20} />,
        label: t("nav.settings"),
        permission: Permission.SYSTEM_SETTINGS,
        adminOnly: true,
      },
      {
        key: "profile",
        to: "/dashboard/profile",
        icon: <User size={20} />,
        label: t("nav.profile"),
        permission: null, // الملف الشخصي متاح للجميع
      },
    ];

    return navigationItems.filter((item) => {
      // فحص الميزات المفعلة
      if (item.featureFlag === false) return false;

      // فحص الأدوار المستبعدة
      if (item.excludeRoles && user?.role && item.excludeRoles.includes(user.role)) {
        return false;
      }

      // فحص الأدوار المحددة للأدمن فقط
      if (item.adminOnly && !isAdmin) {
        return false;
      }

      // إذا لم تكن هناك صلاحية محددة، اعرض العنصر
      if (!item.permission) {
        return true;
      }

      // فحص الصلاحية باستخدام النظام الجديد
      return hasPermission(item.permission);
    });
  };

  const navItems = getNavItems();
  const isRTL = i18n.language === "ar" || direction === "rtl";

  const tenantLogo = tenant?.settings?.branding?.logo || tenant?.logo_url;
  const tenantName = tenant?.settings?.branding?.schoolName || tenant?.name || "School";
  const tenantTagline = tenant?.settings?.branding?.tagline;

  return (
    <>
      {/* Mobile toggle button - position based on language direction */}
      <Button
        onClick={handleToggle}
        className={`md:hidden fixed top-4 z-50 shadow-lg ${
          isRTL ? "right-4" : "left-4"
        }`}
        variant="default"
        size="sm"
        aria-label="Toggle sidebar"
      >
        {isMobileOpen ? <X size={20} /> : <Menu size={20} />}
      </Button>

      {/* Overlay for mobile - only visible when sidebar is open */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
          onClick={handleToggle}
          aria-hidden="true"
        />
      )}

      {/* Sidebar - position based on language direction */}
      <aside
        className={`sidebar w-64 bg-white dark:bg-gray-800 h-screen fixed top-0 z-40 overflow-y-auto transition-transform duration-300 ease-in-out ${
          isRTL
            ? `right-0 border-l border-gray-200 dark:border-gray-700 ${
                isMobileOpen
                  ? "translate-x-0"
                  : "translate-x-full md:translate-x-0"
              }`
            : `left-0 border-r border-gray-200 dark:border-gray-700 ${
                isMobileOpen
                  ? "translate-x-0"
                  : "-translate-x-full md:translate-x-0"
              }`
        }`}
        data-sidebar="true"
        data-theme-target="sidebar"
      >
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <Link to="/" className="flex items-center gap-2 flex-1">
            <SidebarLogo />
          </Link>
          <Button
            onClick={handleToggle}
            className="md:hidden"
            variant="ghost"
            size="sm"
            aria-label="Close sidebar"
          >
            <X size={20} />
          </Button>
        </div>

        <nav className="mt-4 px-2 space-y-1">
          {navItems.map((item) => (
            <Link
              key={item.to}
              to={item.to}
              onClick={() => {
                if (window.innerWidth < 768) {
                  handleToggle();
                }
              }}
              className={`sidebar-item flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                location.pathname === item.to ||
                location.pathname.startsWith(`${item.to}/`)
                  ? "active text-primary-700 dark:text-primary-400 bg-primary-50 dark:bg-gray-700"
                  : "text-gray-700 dark:text-gray-300 hover:text-primary-600 hover:bg-gray-100 dark:hover:bg-gray-700"
              }`}
              aria-current={
                location.pathname === item.to ||
                location.pathname.startsWith(`${item.to}/`)
                  ? "page"
                  : undefined
              }
            >
              <div className="flex items-center">
                <span className="mr-3">{item.icon}</span>
                {item.label}
              </div>
              {item.badge !== undefined && (
                <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-primary-600 rounded-full">
                  {item.badge}
                </span>
              )}
            </Link>
          ))}
        </nav>

        <div className="px-2 mt-auto pb-4 absolute bottom-0 w-full border-t border-gray-200 dark:border-gray-700 pt-4">
          <Button
            onClick={logout}
            variant="ghost"
            size="sm"
            className="w-full justify-start"
            leftIcon={<LogOut size={20} />}
          >
            {t("auth.signOut")}
          </Button>
        </div>
      </aside>

      {/* Content spacer for desktop view */}
      <div className="hidden md:block w-64"></div>
    </>
  );
};
