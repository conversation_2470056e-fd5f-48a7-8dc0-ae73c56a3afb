/**
 * Smart Notification Service
 * Phase 4: Advanced notification system with intelligent routing
 */

import { supabase } from '../lib/supabase';
import { notificationService } from '../lib/notificationService';

export interface NotificationRule {
  id: string;
  name: string;
  trigger: NotificationTrigger;
  conditions: NotificationCondition[];
  actions: NotificationAction[];
  priority: 'low' | 'normal' | 'high' | 'critical';
  isActive: boolean;
  tenantId: string;
}

export interface NotificationTrigger {
  type: 'location' | 'time' | 'event' | 'condition';
  event?: string;
  schedule?: {
    type: 'once' | 'daily' | 'weekly' | 'monthly';
    time?: string;
    days?: string[];
    date?: string;
  };
  location?: {
    type: 'geofence' | 'route_stop' | 'school';
    radius?: number;
    coordinates?: [number, number];
  };
}

export interface NotificationCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains';
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

export interface NotificationAction {
  type: 'push' | 'email' | 'sms' | 'in_app';
  template: string;
  recipients: NotificationRecipient[];
  delay?: number; // milliseconds
  retryPolicy?: {
    maxRetries: number;
    retryDelay: number;
  };
}

export interface NotificationRecipient {
  type: 'user' | 'role' | 'group' | 'parent' | 'driver';
  identifier: string;
  preferences?: {
    channels: string[];
    quietHours?: {
      start: string;
      end: string;
    };
  };
}

export interface NotificationTemplate {
  id: string;
  name: string;
  type: 'push' | 'email' | 'sms';
  subject?: string;
  body: string;
  variables: string[];
  tenantId: string;
}

export interface NotificationAnalytics {
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
  failed: number;
  deliveryRate: number;
  openRate: number;
  clickRate: number;
}

export class SmartNotificationService {
  private static instance: SmartNotificationService;
  private baseNotificationService = notificationService;
  private activeRules = new Map<string, NotificationRule>();
  private scheduledNotifications = new Map<string, NodeJS.Timeout>();

  private constructor() {
    this.loadActiveRules();
  }

  static getInstance(): SmartNotificationService {
    if (!SmartNotificationService.instance) {
      SmartNotificationService.instance = new SmartNotificationService();
    }
    return SmartNotificationService.instance;
  }

  /**
   * Create notification rule
   */
  async createNotificationRule(rule: Omit<NotificationRule, 'id'>): Promise<string> {
    try {
      const { data, error } = await supabase
        .from('notification_rules')
        .insert([{
          name: rule.name,
          trigger: rule.trigger,
          conditions: rule.conditions,
          actions: rule.actions,
          priority: rule.priority,
          is_active: rule.isActive,
          tenant_id: rule.tenantId,
        }])
        .select()
        .single();

      if (error) throw error;

      const ruleId = data.id;
      this.activeRules.set(ruleId, { ...rule, id: ruleId });

      // Schedule if it's a time-based trigger
      if (rule.trigger.type === 'time' && rule.trigger.schedule) {
        this.scheduleNotification(ruleId, rule);
      }

      return ruleId;
    } catch (error) {
      console.error('Error creating notification rule:', error);
      throw error;
    }
  }

  /**
   * Process event-based notifications
   */
  async processEvent(
    eventType: string,
    eventData: any,
    tenantId: string
  ): Promise<void> {
    try {
      const relevantRules = Array.from(this.activeRules.values()).filter(
        rule => 
          rule.tenantId === tenantId &&
          rule.isActive &&
          rule.trigger.type === 'event' &&
          rule.trigger.event === eventType
      );

      for (const rule of relevantRules) {
        if (await this.evaluateConditions(rule.conditions, eventData)) {
          await this.executeActions(rule.actions, eventData, tenantId);
        }
      }
    } catch (error) {
      console.error('Error processing event:', error);
    }
  }

  /**
   * Process location-based notifications
   */
  async processLocationEvent(
    busId: string,
    location: { latitude: number; longitude: number },
    tenantId: string
  ): Promise<void> {
    try {
      const relevantRules = Array.from(this.activeRules.values()).filter(
        rule => 
          rule.tenantId === tenantId &&
          rule.isActive &&
          rule.trigger.type === 'location'
      );

      for (const rule of relevantRules) {
        if (await this.evaluateLocationTrigger(rule.trigger, location, busId)) {
          const eventData = { busId, location, timestamp: new Date().toISOString() };
          if (await this.evaluateConditions(rule.conditions, eventData)) {
            await this.executeActions(rule.actions, eventData, tenantId);
          }
        }
      }
    } catch (error) {
      console.error('Error processing location event:', error);
    }
  }

  /**
   * Send smart notification with intelligent routing
   */
  async sendSmartNotification(
    templateId: string,
    recipients: NotificationRecipient[],
    variables: Record<string, any>,
    tenantId: string,
    priority: 'low' | 'normal' | 'high' | 'critical' = 'normal'
  ): Promise<void> {
    try {
      // Get template
      const template = await this.getNotificationTemplate(templateId);
      if (!template) {
        throw new Error(`Template ${templateId} not found`);
      }

      // Process each recipient
      for (const recipient of recipients) {
        const users = await this.resolveRecipients(recipient, tenantId);
        
        for (const user of users) {
          // Check user preferences and quiet hours
          if (await this.shouldSendNotification(user, priority)) {
            const personalizedContent = this.personalizeContent(
              template,
              { ...variables, userName: user.name }
            );

            // Choose best delivery channel
            const channels = await this.selectOptimalChannels(user, priority);
            
            for (const channel of channels) {
              await this.sendViaChannel(
                channel,
                user,
                personalizedContent,
                tenantId
              );
            }
          }
        }
      }
    } catch (error) {
      console.error('Error sending smart notification:', error);
      throw error;
    }
  }

  /**
   * Get notification analytics
   */
  async getNotificationAnalytics(
    tenantId: string,
    dateRange: { start: string; end: string }
  ): Promise<NotificationAnalytics> {
    try {
      const { data, error } = await supabase
        .from('notification_delivery_log')
        .select('status, opened_at, clicked_at')
        .eq('tenant_id', tenantId)
        .gte('created_at', dateRange.start)
        .lte('created_at', dateRange.end);

      if (error) throw error;

      const sent = data.length;
      const delivered = data.filter(n => n.status === 'delivered').length;
      const opened = data.filter(n => n.opened_at).length;
      const clicked = data.filter(n => n.clicked_at).length;
      const failed = data.filter(n => n.status === 'failed').length;

      return {
        sent,
        delivered,
        opened,
        clicked,
        failed,
        deliveryRate: sent > 0 ? (delivered / sent) * 100 : 0,
        openRate: delivered > 0 ? (opened / delivered) * 100 : 0,
        clickRate: opened > 0 ? (clicked / opened) * 100 : 0,
      };
    } catch (error) {
      console.error('Error getting notification analytics:', error);
      throw error;
    }
  }

  /**
   * Private helper methods
   */
  private async loadActiveRules(): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('notification_rules')
        .select('*')
        .eq('is_active', true);

      if (error) throw error;

      for (const rule of data || []) {
        this.activeRules.set(rule.id, {
          id: rule.id,
          name: rule.name,
          trigger: rule.trigger,
          conditions: rule.conditions,
          actions: rule.actions,
          priority: rule.priority,
          isActive: rule.is_active,
          tenantId: rule.tenant_id,
        });

        // Schedule time-based rules
        if (rule.trigger.type === 'time' && rule.trigger.schedule) {
          this.scheduleNotification(rule.id, rule);
        }
      }
    } catch (error) {
      console.error('Error loading active rules:', error);
    }
  }

  private scheduleNotification(ruleId: string, rule: NotificationRule): void {
    if (!rule.trigger.schedule) return;

    const schedule = rule.trigger.schedule;
    let delay = 0;

    switch (schedule.type) {
      case 'once':
        if (schedule.date && schedule.time) {
          const targetDate = new Date(`${schedule.date}T${schedule.time}`);
          delay = targetDate.getTime() - Date.now();
        }
        break;
      case 'daily':
        // Calculate next occurrence
        delay = this.calculateDailyDelay(schedule.time || '09:00');
        break;
      // Add other schedule types
    }

    if (delay > 0) {
      const timeout = setTimeout(() => {
        this.executeScheduledRule(rule);
      }, delay);

      this.scheduledNotifications.set(ruleId, timeout);
    }
  }

  private calculateDailyDelay(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    const now = new Date();
    const target = new Date();
    target.setHours(hours, minutes, 0, 0);

    if (target <= now) {
      target.setDate(target.getDate() + 1);
    }

    return target.getTime() - now.getTime();
  }

  private async executeScheduledRule(rule: NotificationRule): Promise<void> {
    try {
      const eventData = { 
        type: 'scheduled',
        ruleId: rule.id,
        timestamp: new Date().toISOString()
      };

      if (await this.evaluateConditions(rule.conditions, eventData)) {
        await this.executeActions(rule.actions, eventData, rule.tenantId);
      }

      // Reschedule if recurring
      if (rule.trigger.schedule?.type !== 'once') {
        this.scheduleNotification(rule.id, rule);
      }
    } catch (error) {
      console.error('Error executing scheduled rule:', error);
    }
  }

  private async evaluateConditions(
    conditions: NotificationCondition[],
    data: any
  ): Promise<boolean> {
    if (conditions.length === 0) return true;

    // Simple condition evaluation - can be enhanced
    for (const condition of conditions) {
      const fieldValue = this.getNestedValue(data, condition.field);
      const result = this.evaluateCondition(fieldValue, condition.operator, condition.value);
      
      if (!result) return false; // AND logic for now
    }

    return true;
  }

  private evaluateCondition(fieldValue: any, operator: string, value: any): boolean {
    switch (operator) {
      case 'equals':
        return fieldValue === value;
      case 'not_equals':
        return fieldValue !== value;
      case 'greater_than':
        return fieldValue > value;
      case 'less_than':
        return fieldValue < value;
      case 'contains':
        return String(fieldValue).includes(String(value));
      default:
        return false;
    }
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private async executeActions(
    actions: NotificationAction[],
    eventData: any,
    tenantId: string
  ): Promise<void> {
    for (const action of actions) {
      try {
        if (action.delay) {
          setTimeout(() => {
            this.executeAction(action, eventData, tenantId);
          }, action.delay);
        } else {
          await this.executeAction(action, eventData, tenantId);
        }
      } catch (error) {
        console.error('Error executing action:', error);
      }
    }
  }

  private async executeAction(
    action: NotificationAction,
    eventData: any,
    tenantId: string
  ): Promise<void> {
    // Implementation for executing specific actions
    // This would integrate with the base notification service
  }

  private async getNotificationTemplate(templateId: string): Promise<NotificationTemplate | null> {
    try {
      const { data, error } = await supabase
        .from('notification_templates')
        .select('*')
        .eq('id', templateId)
        .single();

      if (error) return null;
      return data;
    } catch (error) {
      return null;
    }
  }

  private async resolveRecipients(
    recipient: NotificationRecipient,
    tenantId: string
  ): Promise<any[]> {
    // Implementation to resolve recipients based on type
    return [];
  }

  private async shouldSendNotification(user: any, priority: string): Promise<boolean> {
    // Check user preferences, quiet hours, etc.
    return true;
  }

  private personalizeContent(template: NotificationTemplate, variables: Record<string, any>): any {
    // Replace variables in template
    let content = template.body;
    for (const [key, value] of Object.entries(variables)) {
      content = content.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
    }
    return { ...template, body: content };
  }

  private async selectOptimalChannels(user: any, priority: string): Promise<string[]> {
    // Select best channels based on user preferences and priority
    return ['push']; // Default
  }

  private async sendViaChannel(
    channel: string,
    user: any,
    content: any,
    tenantId: string
  ): Promise<void> {
    // Send via specific channel
  }

  private async evaluateLocationTrigger(
    trigger: NotificationTrigger,
    location: { latitude: number; longitude: number },
    busId: string
  ): Promise<boolean> {
    // Evaluate location-based triggers
    return false;
  }
}

export default SmartNotificationService;
