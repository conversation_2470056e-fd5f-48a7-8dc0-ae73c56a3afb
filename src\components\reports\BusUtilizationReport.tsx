import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Calendar,
  Download,
  Filter,
  FileText,
  Bus,
  TrendingUp,
  Gauge,
  Users,
  Fuel,
} from "lucide-react";
import { Button } from "../ui/Button";
import { supabase } from "../../lib/supabase";
import { useDatabase } from "../../contexts/DatabaseContext";
import { getBusUtilization } from "../../lib/api";
import { useAuth } from "../../contexts/AuthContext";
import type { Tables } from "../../lib/api";

interface BusUtilizationData {
  id: string;
  bus_id: string;
  date: string;
  total_trips: number;
  total_distance: number;
  total_students: number;
  utilization_percentage: number;
  fuel_consumption: number;
  bus?: {
    id: string;
    plate_number: string;
    capacity: number;
  };
}

interface BusUtilizationReportProps {
  className?: string;
}

export const BusUtilizationReport: React.FC<BusUtilizationReportProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { buses } = useDatabase();
  const [reportData, setReportData] = useState<BusUtilizationData[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
    busId: "",
  });
  const [stats, setStats] = useState({
    avgUtilization: 0,
    totalTrips: 0,
    totalDistance: 0,
    totalFuelConsumption: 0,
  });

  useEffect(() => {
    generateReport();
  }, [filters]);

  const generateReport = async () => {
    setLoading(true);
    try {
      // Fetch real data from the API
      const data = await getBusUtilization(
        filters.busId || null,
        user?.tenant_id || "",
        filters.startDate,
        filters.endDate,
      );

      if (data && data.length > 0) {
        setReportData(data);
        // Calculate stats from real data
        const avgUtilization =
          data.reduce((sum, item) => sum + item.utilization_percentage, 0) /
          data.length;
        const totalTrips = data.reduce(
          (sum, item) => sum + item.total_trips,
          0,
        );
        const totalDistance = data.reduce(
          (sum, item) => sum + item.total_distance,
          0,
        );
        const totalFuelConsumption = data.reduce(
          (sum, item) => sum + item.fuel_consumption,
          0,
        );

        setStats({
          avgUtilization,
          totalTrips,
          totalDistance,
          totalFuelConsumption,
        });
        return;
      }

      // Fallback to sample data if no real data available
      const sampleData: BusUtilizationData[] = [];

      // Get dates between start and end
      const startDate = new Date(filters.startDate);
      const endDate = new Date(filters.endDate);
      const dateArray = [];
      let currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        dateArray.push(new Date(currentDate));
        currentDate.setDate(currentDate.getDate() + 1);
      }

      // Filter buses based on selection
      const relevantBuses = filters.busId
        ? buses.filter((b) => b.id === filters.busId)
        : buses;

      // Generate data for each bus and date
      relevantBuses.forEach((bus) => {
        dateArray.forEach((date) => {
          // Skip weekends
          if (date.getDay() === 0 || date.getDay() === 6) return;

          const utilization = Math.floor(Math.random() * 40) + 60; // 60-100%

          sampleData.push({
            id: `${bus.id}-${date.toISOString().split("T")[0]}`,
            bus_id: bus.id,
            date: date.toISOString().split("T")[0],
            total_trips: Math.floor(Math.random() * 6) + 2, // 2-8 trips
            total_distance: Math.floor(Math.random() * 100) + 50, // 50-150 km
            total_students: Math.floor((bus.capacity * utilization) / 100),
            utilization_percentage: utilization,
            fuel_consumption: Math.floor(Math.random() * 30) + 20, // 20-50 liters
            bus: {
              id: bus.id,
              plate_number: bus.plate_number,
              capacity: bus.capacity,
            },
          });
        });
      });

      setReportData(sampleData);

      // Calculate stats
      if (sampleData.length > 0) {
        const avgUtilization =
          sampleData.reduce(
            (sum, item) => sum + item.utilization_percentage,
            0,
          ) / sampleData.length;
        const totalTrips = sampleData.reduce(
          (sum, item) => sum + item.total_trips,
          0,
        );
        const totalDistance = sampleData.reduce(
          (sum, item) => sum + item.total_distance,
          0,
        );
        const totalFuelConsumption = sampleData.reduce(
          (sum, item) => sum + item.fuel_consumption,
          0,
        );

        setStats({
          avgUtilization,
          totalTrips,
          totalDistance,
          totalFuelConsumption,
        });
      }
    } catch (error) {
      console.error("Error generating bus utilization report:", error);
    } finally {
      setLoading(false);
    }
  };

  const exportToCSV = () => {
    const headers = [
      "Date",
      "Bus",
      "Capacity",
      "Trips",
      "Distance (km)",
      "Students",
      "Utilization %",
      "Fuel (L)",
    ];

    const csvContent = [
      headers.join(","),
      ...reportData.map((row) =>
        [
          row.date,
          `"${row.bus?.plate_number || "Unknown"}"`,
          row.bus?.capacity || 0,
          row.total_trips,
          row.total_distance,
          row.total_students,
          row.utilization_percentage,
          row.fuel_consumption,
        ].join(","),
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `bus_utilization_${filters.startDate}_to_${filters.endDate}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const exportToPDF = () => {
    const printContent = `
      <html>
        <head>
          <title>Bus Utilization Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            .header { text-align: center; margin-bottom: 20px; }
            .stats { display: flex; justify-content: space-around; margin: 20px 0; }
            .stat-card { text-align: center; padding: 10px; border: 1px solid #ddd; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${t("reports.busUtilizationReport")}</h1>
            <p>Period: ${filters.startDate} to ${filters.endDate}</p>
          </div>
          
          <div class="stats">
            <div class="stat-card">
              <h3>${stats.avgUtilization.toFixed(1)}%</h3>
              <p>${t("reports.avgUtilization")}</p>
            </div>
            <div class="stat-card">
              <h3>${stats.totalTrips}</h3>
              <p>${t("reports.totalTrips")}</p>
            </div>
            <div class="stat-card">
              <h3>${stats.totalDistance.toFixed(1)} km</h3>
              <p>${t("reports.totalDistance")}</p>
            </div>
            <div class="stat-card">
              <h3>${stats.totalFuelConsumption.toFixed(1)} L</h3>
              <p>${t("reports.fuelConsumption")}</p>
            </div>
          </div>
          
          <table>
            <thead>
              <tr>
                <th>${t("common.date")}</th>
                <th>${t("reports.bus")}</th>
                <th>${t("reports.capacity")}</th>
                <th>${t("reports.trips")}</th>
                <th>${t("reports.distance")} (km)</th>
                <th>${t("reports.students")}</th>
                <th>${t("reports.utilization")}</th>
                <th>${t("reports.fuel")} (L)</th>
              </tr>
            </thead>
            <tbody>
              ${reportData
                .map(
                  (row) => `
                <tr>
                  <td>${new Date(row.date).toLocaleDateString()}</td>
                  <td>${row.bus?.plate_number || "Unknown"}</td>
                  <td>${row.bus?.capacity || 0}</td>
                  <td>${row.total_trips}</td>
                  <td>${row.total_distance.toFixed(1)}</td>
                  <td>${row.total_students}</td>
                  <td>${row.utilization_percentage.toFixed(1)}%</td>
                  <td>${row.fuel_consumption.toFixed(1)}</td>
                </tr>
              `,
                )
                .join("")}
            </tbody>
          </table>
        </body>
      </html>
    `;

    const printWindow = window.open("", "_blank");
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t("reports.busUtilizationReport")}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t("reports.analyzeBusUtilization")}
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={exportToCSV}
            variant="outline"
            size="sm"
            leftIcon={<Download size={16} />}
            disabled={reportData.length === 0}
          >
            {t("common.exportCSV")}
          </Button>
          <Button
            onClick={exportToPDF}
            variant="outline"
            size="sm"
            leftIcon={<FileText size={16} />}
            disabled={reportData.length === 0}
          >
            {t("common.exportPDF")}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("common.startDate")}
            </label>
            <input
              type="date"
              value={filters.startDate}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, startDate: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("common.endDate")}
            </label>
            <input
              type="date"
              value={filters.endDate}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, endDate: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("reports.bus")}
            </label>
            <select
              value={filters.busId}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, busId: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="">{t("common.all")}</option>
              {buses.map((bus) => (
                <option key={bus.id} value={bus.id}>
                  {bus.plate_number}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-primary-100 dark:bg-primary-800/20 rounded-lg">
              <Gauge className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("reports.avgUtilization")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {stats.avgUtilization.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-accent-100 dark:bg-accent-800/20 rounded-lg">
              <Bus className="h-6 w-6 text-accent-600 dark:text-accent-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("reports.totalTrips")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {stats.totalTrips}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-800/20 rounded-lg">
              <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("reports.totalDistance")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {stats.totalDistance.toFixed(1)} km
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 dark:bg-blue-800/20 rounded-lg">
              <Fuel className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("reports.fuelConsumption")}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {stats.totalFuelConsumption.toFixed(1)} L
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Report Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t("reports.busUtilizationData")} ({reportData.length}{" "}
            {t("common.records")})
          </h3>
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
          </div>
        ) : reportData.length > 0 ? (
          <div className="overflow-x-auto max-h-96">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700 sticky top-0">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("common.date")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("reports.bus")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("reports.capacity")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("reports.trips")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("reports.distance")} (km)
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("reports.students")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("reports.utilization")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t("reports.fuel")} (L)
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {reportData.map((row) => (
                  <tr
                    key={row.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {new Date(row.date).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {row.bus?.plate_number || "Unknown"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {row.bus?.capacity || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {row.total_trips}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {row.total_distance.toFixed(1)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {row.total_students}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-16 bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-2">
                          <div
                            className={`h-2.5 rounded-full ${row.utilization_percentage > 80 ? "bg-green-500" : row.utilization_percentage > 60 ? "bg-yellow-500" : "bg-red-500"}`}
                            style={{ width: `${row.utilization_percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-900 dark:text-white">
                          {row.utilization_percentage.toFixed(1)}%
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {row.fuel_consumption.toFixed(1)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <FileText size={48} className="mx-auto mb-4 opacity-50" />
            <p>{t("reports.noDataFound")}</p>
            <p className="text-sm mt-2">{t("reports.adjustFilters")}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default BusUtilizationReport;
