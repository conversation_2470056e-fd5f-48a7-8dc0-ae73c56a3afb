import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  X,
  Plus,
  Trash2,
  Edit,
  Map,
  Clock,
  Navigation,
  Users,
  AlertTriangle,
} from "lucide-react";
import { supabase } from "../../lib/supabase";
import { Route, Bus, RouteStop } from "../../types";
import { Button } from "../ui/Button";
import { MapboxMap } from "../map/MapboxMap";
import { Marker, Source, Layer } from "react-map-gl";
import RouteStopModal from "./RouteStopModal";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";

interface RouteModalProps {
  isOpen: boolean;
  onClose: (refreshData?: boolean) => void;
  route: Route | null;
}

const RouteModal: React.FC<RouteModalProps> = ({ isOpen, onClose, route }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { buses: dbBuses, users } = useDatabase();
  const [name, setName] = useState("");
  const [busId, setBusId] = useState<string | undefined>(undefined);
  const [driverId, setDriverId] = useState<string | undefined>(undefined);
  const [isActive, setIsActive] = useState(true);
  const [schedule, setSchedule] = useState({
    startTime: "",
    endTime: "",
    days: [] as string[],
  });
  const [buses, setBuses] = useState<Bus[]>([]);
  const [drivers, setDrivers] = useState<any[]>([]);
  const [stops, setStops] = useState<RouteStop[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isStopModalOpen, setIsStopModalOpen] = useState(false);
  const [currentStop, setCurrentStop] = useState<RouteStop | null>(null);
  const [showMap, setShowMap] = useState(false);
  const [routeGeometry, setRouteGeometry] = useState<any>(null);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [routeDistance, setRouteDistance] = useState<number>(0);
  const [estimatedDuration, setEstimatedDuration] = useState<number>(0);

  useEffect(() => {
    if (isOpen) {
      fetchBuses();
      fetchDrivers();
      if (route) {
        setName(route.name);
        setBusId(route.busId);
        setIsActive(route.isActive);
        fetchRouteStops(route.id);
        // Load schedule if exists
        fetchRouteSchedule(route.id);
      } else {
        setName("");
        setBusId(undefined);
        setDriverId(undefined);
        setIsActive(true);
        setStops([]);
        setSchedule({ startTime: "", endTime: "", days: [] });
      }
    }
  }, [isOpen, route]);

  // Generate route geometry when stops change
  useEffect(() => {
    if (stops.length > 1) {
      const coordinates = stops
        .sort((a, b) => a.order - b.order)
        .map((stop) => [stop.location.lng, stop.location.lat]);

      setRouteGeometry({
        type: "Feature",
        properties: {},
        geometry: {
          type: "LineString",
          coordinates,
        },
      });

      // Calculate route distance and estimated duration
      calculateRouteMetrics(coordinates);
    } else {
      setRouteGeometry(null);
      setRouteDistance(0);
      setEstimatedDuration(0);
    }
  }, [stops]);

  const calculateRouteMetrics = (coordinates: number[][]) => {
    let totalDistance = 0;

    for (let i = 0; i < coordinates.length - 1; i++) {
      const distance = calculateDistance(
        coordinates[i][1],
        coordinates[i][0],
        coordinates[i + 1][1],
        coordinates[i + 1][0],
      );
      totalDistance += distance;
    }

    setRouteDistance(totalDistance);
    // Estimate duration assuming average speed of 30 km/h in city
    setEstimatedDuration(Math.round((totalDistance / 30) * 60)); // in minutes
  };

  const fetchBuses = async () => {
    try {
      let query = supabase
        .from("buses")
        .select(
          `
          id, 
          plate_number, 
          capacity, 
          is_active,
          driver_id,
          tenant_id,
          driver:users(id, name)
        `,
        )
        .eq("is_active", true);

      // Filter by tenant if not admin
      if (user?.role !== "admin" && user?.tenant_id) {
        query = query.eq("tenant_id", user.tenant_id);
      }

      const { data, error } = await query;

      if (error) throw error;

      if (data) {
        const formattedBuses: Bus[] = data.map((bus) => ({
          id: bus.id,
          plateNumber: bus.plate_number,
          capacity: bus.capacity,
          schoolId: bus.tenant_id,
          driverId: bus.driver_id || undefined,
          isActive: bus.is_active,
          driver: bus.driver,
        }));
        setBuses(formattedBuses);
      }
    } catch (error) {
      console.error("Error fetching buses:", error);
      alert(t("routes.errorFetchingBuses"));
    }
  };

  const fetchDrivers = async () => {
    try {
      let query = supabase
        .from("users")
        .select("id, name, email")
        .eq("role", "driver")
        .eq("is_active", true);

      // Filter by tenant if not admin
      if (user?.role !== "admin" && user?.tenant_id) {
        query = query.eq("tenant_id", user.tenant_id);
      }

      const { data, error } = await query;

      if (error) throw error;
      setDrivers(data || []);
    } catch (error) {
      console.error("Error fetching drivers:", error);
    }
  };

  const fetchRouteSchedule = async (routeId: string) => {
    try {
      const { data, error } = await supabase
        .from("routes")
        .select("schedule")
        .eq("id", routeId)
        .single();

      if (error) throw error;

      if (data?.schedule) {
        const scheduleData = data.schedule as any;
        setSchedule({
          startTime: scheduleData.startTime || "",
          endTime: scheduleData.endTime || "",
          days: scheduleData.days || [],
        });
      }
    } catch (error) {
      console.error("Error fetching route schedule:", error);
    }
  };

  const fetchRouteStops = async (routeId: string) => {
    try {
      const { data, error } = await supabase
        .from("route_stops")
        .select("*")
        .eq("route_id", routeId)
        .order("order", { ascending: true });

      if (error) throw error;

      if (data) {
        const formattedStops: RouteStop[] = data.map((stop) => ({
          id: stop.id,
          name: stop.name,
          routeId: stop.route_id,
          order: stop.order,
          location: stop.location,
          students: [], // We'll fetch students separately if needed
          arrivalTime: stop.arrival_time || undefined,
        }));
        setStops(formattedStops);
      }
    } catch (error) {
      console.error("Error fetching route stops:", error);
    }
  };

  const getTenantIdForUser = async (): Promise<string | null> => {
    // If user has tenant_id, use it
    if (user?.tenant_id) {
      return user.tenant_id;
    }

    // For admin users, get first available tenant
    if (user?.role === 'admin') {
      try {
        const { data: tenants, error } = await supabase
          .from('tenants')
          .select('id')
          .eq('is_active', true)
          .limit(1);

        if (error) throw error;

        if (tenants && tenants.length > 0) {
          return tenants[0].id;
        }
      } catch (err) {
        console.error("Error fetching tenants:", err);
      }
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!name.trim()) {
      alert(t("routes.nameRequired"));
      return;
    }

    if (schedule.days.length === 0) {
      alert(t("routes.selectAtLeastOneDay"));
      return;
    }

    setIsLoading(true);

    try {
      const routeData = {
        name: name.trim(),
        bus_id: busId || null,
        is_active: isActive,
        schedule: {
          startTime: schedule.startTime,
          endTime: schedule.endTime,
          days: schedule.days,
          distance: routeDistance,
          estimatedDuration: estimatedDuration,
        },
        tenant_id: await getTenantIdForUser(),
      };

      let result;
      if (route) {
        // Update existing route
        result = await supabase
          .from("routes")
          .update(routeData)
          .eq("id", route.id)
          .select();
      } else {
        // Create new route
        result = await supabase.from("routes").insert([routeData]).select();
      }

      if (result.error) throw result.error;

      // Update bus driver assignment if both bus and driver are selected
      if (busId && driverId) {
        const { error: busUpdateError } = await supabase
          .from("buses")
          .update({ driver_id: driverId })
          .eq("id", busId);

        if (busUpdateError) {
          console.error("Error updating bus driver:", busUpdateError);
        }
      }

      // Show success message
      alert(route ? t("routes.routeUpdated") : t("routes.routeCreated"));
      onClose(true);
    } catch (error) {
      console.error("Error saving route:", error);
      alert(t("routes.saveError"));
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddStop = () => {
    if (!route) {
      alert(t("routes.saveRouteFirst"));
      return;
    }
    setCurrentStop(null);
    setIsStopModalOpen(true);
  };

  const handleEditStop = (stop: RouteStop) => {
    setCurrentStop(stop);
    setIsStopModalOpen(true);
  };

  const handleDeleteStop = async (stopId: string) => {
    if (window.confirm(t("routes.deleteStopConfirmation"))) {
      try {
        const { error } = await supabase
          .from("route_stops")
          .delete()
          .eq("id", stopId);

        if (error) throw error;

        // Refresh stops list
        if (route) {
          fetchRouteStops(route.id);
        }
      } catch (error) {
        console.error("Error deleting stop:", error);
      }
    }
  };

  const handleStopModalClose = (refreshData: boolean = false) => {
    setIsStopModalOpen(false);
    if (refreshData && route) {
      fetchRouteStops(route.id);
    }
  };

  const optimizeRoute = async () => {
    if (!route || stops.length < 3) return;

    setIsOptimizing(true);
    try {
      // Calculate distances between all stops
      const distances: { [key: string]: number } = {};

      for (let i = 0; i < stops.length; i++) {
        for (let j = i + 1; j < stops.length; j++) {
          const stop1 = stops[i];
          const stop2 = stops[j];
          const distance = calculateDistance(
            stop1.location.lat,
            stop1.location.lng,
            stop2.location.lat,
            stop2.location.lng,
          );
          distances[`${stop1.id}-${stop2.id}`] = distance;
          distances[`${stop2.id}-${stop1.id}`] = distance;
        }
      }

      // Simple nearest neighbor algorithm for route optimization
      const optimizedStops = [...stops];
      const visited = new Set<string>();
      const result: typeof stops = [];

      // Start with the first stop
      let currentStop = optimizedStops[0];
      result.push(currentStop);
      visited.add(currentStop.id);

      while (result.length < optimizedStops.length) {
        let nearestStop = null;
        let shortestDistance = Infinity;

        for (const stop of optimizedStops) {
          if (!visited.has(stop.id)) {
            const distance =
              distances[`${currentStop.id}-${stop.id}`] || Infinity;
            if (distance < shortestDistance) {
              shortestDistance = distance;
              nearestStop = stop;
            }
          }
        }

        if (nearestStop) {
          result.push(nearestStop);
          visited.add(nearestStop.id);
          currentStop = nearestStop;
        }
      }

      // Update stop orders
      const updatedStops = result.map((stop, index) => ({
        ...stop,
        order: index + 1,
      }));

      // Update stops in database
      for (const stop of updatedStops) {
        await supabase
          .from("route_stops")
          .update({ order: stop.order })
          .eq("id", stop.id);
      }

      // Refresh stops
      await fetchRouteStops(route.id);

      alert(t("routes.routeOptimized"));
    } catch (error) {
      console.error("Error optimizing route:", error);
      alert(t("routes.optimizationError"));
    } finally {
      setIsOptimizing(false);
    }
  };

  const calculateDistance = (
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number,
  ): number => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLng = ((lng2 - lng1) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((lat1 * Math.PI) / 180) *
        Math.cos((lat2 * Math.PI) / 180) *
        Math.sin(dLng / 2) *
        Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center border-b border-gray-200 dark:border-gray-700 p-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {route ? t("routes.editRoute") : t("routes.addRoute")}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onClose()}
            aria-label="Close"
          >
            <X size={20} />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="p-4">
          <div className="space-y-4">
            <div>
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                {t("routes.name")}
              </label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="busId"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {t("routes.assignBus")}
                </label>
                <select
                  id="busId"
                  value={busId || ""}
                  onChange={(e) => {
                    setBusId(e.target.value || undefined);
                    // Auto-select driver if bus has one assigned
                    const selectedBus = buses.find(
                      (b) => b.id === e.target.value,
                    );
                    if (selectedBus?.driverId) {
                      setDriverId(selectedBus.driverId);
                    }
                  }}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">{t("routes.noBusAssigned")}</option>
                  {buses.map((bus) => (
                    <option key={bus.id} value={bus.id}>
                      {bus.plateNumber} ({t("buses.capacity")}: {bus.capacity})
                      {bus.driver && ` - ${bus.driver.name}`}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label
                  htmlFor="driverId"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {t("routes.assignDriver")}
                </label>
                <select
                  id="driverId"
                  value={driverId || ""}
                  onChange={(e) => setDriverId(e.target.value || undefined)}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">{t("routes.noDriverAssigned")}</option>
                  {drivers.map((driver) => (
                    <option key={driver.id} value={driver.id}>
                      {driver.name} ({driver.email})
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Schedule Section */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                {t("routes.schedule")}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="startTime"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    {t("routes.startTime")}
                  </label>
                  <input
                    type="time"
                    id="startTime"
                    value={schedule.startTime}
                    onChange={(e) =>
                      setSchedule((prev) => ({
                        ...prev,
                        startTime: e.target.value,
                      }))
                    }
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label
                    htmlFor="endTime"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    {t("routes.endTime")}
                  </label>
                  <input
                    type="time"
                    id="endTime"
                    value={schedule.endTime}
                    onChange={(e) =>
                      setSchedule((prev) => ({
                        ...prev,
                        endTime: e.target.value,
                      }))
                    }
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              <div className="mt-3">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t("routes.operatingDays")}
                </label>
                <div className="flex flex-wrap gap-2">
                  {[
                    "monday",
                    "tuesday",
                    "wednesday",
                    "thursday",
                    "friday",
                    "saturday",
                    "sunday",
                  ].map((day) => (
                    <label key={day} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={schedule.days.includes(day)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSchedule((prev) => ({
                              ...prev,
                              days: [...prev.days, day],
                            }));
                          } else {
                            setSchedule((prev) => ({
                              ...prev,
                              days: prev.days.filter((d) => d !== day),
                            }));
                          }
                        }}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded mr-2"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        {t(`common.${day}`)}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                checked={isActive}
                onChange={(e) => setIsActive(e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label
                htmlFor="isActive"
                className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
              >
                {t("common.active")}
              </label>
            </div>

            {/* Route Metrics */}
            {stops.length > 1 && (
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  {t("routes.routeMetrics")}
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {stops.length}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {t("routes.totalStops")}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {routeDistance.toFixed(1)} km
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {t("routes.totalDistance")}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                      {estimatedDuration} min
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {t("routes.estimatedDuration")}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Route Optimization */}
            {route && stops.length > 2 && (
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={optimizeRoute}
                  disabled={isOptimizing}
                  leftIcon={
                    isOptimizing ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500" />
                    ) : (
                      <Navigation size={16} />
                    )
                  }
                >
                  {isOptimizing
                    ? t("routes.optimizing")
                    : t("routes.optimizeRoute")}
                </Button>

                {stops.length > 0 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowMap(!showMap)}
                    leftIcon={<Map size={16} />}
                  >
                    {showMap ? t("routes.hideMap") : t("routes.showMap")}
                  </Button>
                )}
              </div>
            )}

            {route && stops.length > 0 && (
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="showMap"
                  checked={showMap}
                  onChange={(e) => setShowMap(e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label
                  htmlFor="showMap"
                  className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
                >
                  {t("routes.showMap")}
                </label>
              </div>
            )}
          </div>

          {route && (
            <div className="mt-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {t("routes.stops")}
                </h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddStop}
                  leftIcon={<Plus size={16} />}
                >
                  {t("routes.addStop")}
                </Button>
              </div>

              {stops.length === 0 ? (
                <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                  {t("routes.noStops")}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th
                          scope="col"
                          className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                        >
                          {t("routes.order")}
                        </th>
                        <th
                          scope="col"
                          className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                        >
                          {t("routes.stopName")}
                        </th>
                        <th
                          scope="col"
                          className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                        >
                          {t("routes.arrivalTime")}
                        </th>
                        <th
                          scope="col"
                          className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                        >
                          {t("common.actions")}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {stops.map((stop) => (
                        <tr key={stop.id}>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                            {stop.order}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            {stop.name}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                            {stop.arrivalTime || t("routes.noArrivalTime")}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditStop(stop)}
                                aria-label={t("common.edit")}
                              >
                                <Edit
                                  size={16}
                                  className="text-gray-500 dark:text-gray-400"
                                />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteStop(stop.id)}
                                aria-label={t("common.delete")}
                              >
                                <Trash2 size={16} className="text-red-500" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}

          <div className="mt-6 flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => onClose()}
              disabled={isLoading}
            >
              {t("common.cancel")}
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin mr-2 h-4 w-4 border-b-2 border-white rounded-full"></div>
                  {t("common.saving")}
                </div>
              ) : route ? (
                t("common.save")
              ) : (
                t("common.create")
              )}
            </Button>
          </div>
        </form>

        {isStopModalOpen && route && (
          <RouteStopModal
            isOpen={isStopModalOpen}
            onClose={handleStopModalClose}
            routeId={route.id}
            stop={currentStop}
            existingStops={stops}
          />
        )}

        {/* Route Map */}
        {showMap && route && stops.length > 0 && (
          <div className="p-4">
            <div className="mb-4 flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {t("routes.routeVisualization")}
              </h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowMap(false)}
                leftIcon={<X size={16} />}
              >
                {t("common.close")}
              </Button>
            </div>
            <div className="h-64 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
              <MapboxMap
                initialViewState={{
                  latitude: stops.length > 0 ? stops[0].location.lat : 30.0444,
                  longitude: stops.length > 0 ? stops[0].location.lng : 31.2357,
                  zoom: stops.length > 0 ? 12 : 11,
                }}
                showNavigation={true}
              >
                {/* Route line */}
                {routeGeometry && (
                  <Source id="route" type="geojson" data={routeGeometry}>
                    <Layer
                      id="route-line"
                      type="line"
                      paint={{
                        "line-color": "#3B82F6",
                        "line-width": 4,
                        "line-opacity": 0.8,
                      }}
                    />
                  </Source>
                )}

                {/* Stop markers */}
                {stops.map((stop, index) => (
                  <Marker
                    key={stop.id}
                    latitude={stop.location.lat}
                    longitude={stop.location.lng}
                  >
                    <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium border-2 border-white shadow-lg">
                      {stop.order}
                    </div>
                  </Marker>
                ))}
              </MapboxMap>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RouteModal;
