{"app": {"name": "SchoolBus", "tagline": "Smart School Bus Management System"}, "auth": {"signIn": "Sign In", "signOut": "Sign Out", "email": "Email", "password": "Password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "resetPassword": "Reset Password", "resetPasswordEmailSent": "If an account with that email exists, you will receive password reset instructions.", "signUp": "Create Account", "errors": {"allFieldsRequired": "All fields are required", "invalidCredentials": "Invalid email or password"}}, "nav": {"dashboard": "Dashboard", "schools": "Schools", "buses": "Buses", "routes": "Routes", "students": "Students", "users": "Users", "allUsers": "All Users", "allBuses": "All Buses", "allRoutes": "All Routes", "allStudents": "All Students", "systemReports": "System Reports", "systemNotifications": "System Notifications", "reports": "Reports", "settings": "Settings", "profile": "Profile", "notifications": "Notifications", "smartNotifications": "Smart Notifications"}, "schools": {"addSchool": "Add School", "editSchool": "Edit School", "manageSchools": "Manage Schools", "name": "School Name", "domain": "Domain", "address": "Address", "contactNumber": "Contact Number", "isActive": "Active", "deleteConfirmation": "Are you sure you want to delete this school? This action cannot be undone.", "saving": "Saving...", "deleting": "Deleting..."}, "dashboard": {"overview": "Overview", "welcome": "Welcome", "activeSchools": "Active Schools", "activeBuses": "Active Buses", "totalStudents": "Total Students", "totalRoutes": "Total Routes", "recentActivity": "Recent Activity", "busesOnRoute": "Buses on Route", "todayAttendance": "Today's Attendance"}, "buses": {"isActive": "Bus is Active", "editBus": "Edit Bus", "busDetails": "Bus Details", "maintenanceRecords": "Maintenance Records", "maintenanceScheduleDescription": "Maintenance schedule for this bus", "scheduleMaintenance": "Schedule Maintenance", "upcomingMaintenance": "Upcoming Maintenance", "overdueMaintenance": "Overdue Maintenance", "completedMaintenance": "Completed Maintenance", "scheduleFirstMaintenance": "Schedule First Maintenance", "basicInformation": "Basic Information", "driverInformation": "Driver Information", "locationInformation": "Location Information", "lastLocation": "Last Location", "lastUpdated": "Last Updated", "manageBuses": "Manage Buses", "addBus": "Add Bus", "plateNumber": "Plate Number", "capacity": "Capacity", "driver": "Driver", "status": "Status", "active": "Active", "inactive": "Inactive", "maintenance": "Maintenance", "tracking": "Tracking", "details": "Details", "edit": "Edit", "delete": "Delete"}, "routes": {"noRouteAssigned": "No route assigned", "contactAdminForRoute": "Please contact admin to assign a route", "allStatus": "All Statuses", "allBuses": "All Buses", "busAssigned": "Bus Assigned", "busUnassigned": "Bus Unassigned", "assignDriver": "Assign Driver", "totalRoutes": "Total Routes", "activeRoutes": "Active Routes", "routesWithBuses": "Routes with Buses", "totalStops": "Total Stops", "routeInformation": "Route Information", "name": "Route Name", "noDriverAssigned": "No driver assigned", "noBusAssigned": "No bus assigned", "assignBus": "Assign Bus", "schedule": "Schedule", "operatingDays": "Operating Days", "noStops": "No stops assigned", "manageRoutes": "Manage Routes", "addRoute": "Add Route", "routeName": "Route Name", "stops": "Stops", "students": "Students", "editRoute": "Edit Route", "viewRoute": "View Route", "addStop": "Add Stop", "stopName": "Stop Name", "arrivalTime": "Arrival Time", "nameRequired": "Route name is required", "selectAtLeastOneDay": "Select at least one operating day", "orderAlreadyExists": "A stop with this order already exists", "locationRequired": "Location is required", "routeCreated": "Route created successfully", "routeUpdated": "Route updated successfully", "saveError": "Failed to save route. Please try again.", "manageRoutesDescription": "View, add, edit, and assign buses or drivers to routes. Manage stops and schedules for each route.", "startTime": "Start Time", "endTime": "End Time"}, "attendance": {"advancedAttendance": "Advanced Attendance", "advancedDescription": "Advanced attendance system with organized sessions and comprehensive reports", "totalStudents": "Total Students", "presentToday": "Present Today", "absentToday": "Absent Today", "lateToday": "Late Today", "attendanceRate": "Attendance Rate", "activeSessions": "Active Sessions", "overview": "Overview", "sessions": "Sessions", "reports": "Reports", "analytics": "Analytics", "activeSessionsDescription": "Currently active attendance sessions", "noActiveSessions": "No active sessions", "startFirstSession": "Start First Session", "driver": "Driver", "started": "Started", "present": "Present", "absent": "Absent", "total": "Total", "takePhoto": "Take Photo", "location": "Location", "endSession": "End Session", "todaySummary": "Today's Summary", "morningPickup": "Morning Pickup", "afternoonDropoff": "Afternoon Dropoff", "completed": "Completed", "inProgress": "In Progress", "averageTime": "Average Time", "minutes": "minutes", "recentAlerts": "Recent Alerts", "late": "Late", "exportReport": "Export Report", "startSession": "Start Session"}, "tracking": {"trackBusesRealTime": "Track buses in real time", "detailedView": "Detailed View", "liveTracking": "Live Tracking", "realTimeTracking": "Real-Time Tracking", "monitorAllBuses": "Monitor all buses in real time", "busLocation": "Bus Location", "lastUpdated": "Last Updated", "speed": "Speed", "status": {"title": "Status", "active": "Active", "stopped": "Stopped", "maintenance": "Maintenance", "emergency": "Emergency"}, "moving": "Moving", "stopped": "Stopped", "nextStop": "Next Stop", "eta": "ETA", "routeProgress": "Route Progress", "autoRefreshOn": "Auto Refresh On", "autoRefreshOff": "Auto Refresh Off", "hideAlerts": "<PERSON><PERSON>", "showAlerts": "Show Alerts", "hideTrails": "Hide Trails", "showTrails": "Show Trails", "mapPlaceholder": "Tracking Map", "mapIntegrationNote": "Map integration with mapping services coming soon", "activeBuses": "Active Buses", "busInformation": "Bus Information", "currentLocation": "Current Location", "nextStops": "Next Stops", "loading": "Loading...", "refreshData": "Refresh Data", "fullScreen": "Full Screen", "exitFullScreen": "Exit Full Screen", "busStatus": "Bus Status", "maintenance": "Maintenance", "emergency": "Emergency", "busList": "Bus List", "searchBuses": "Search Buses", "sortBy": "Sort By", "filters": "Filters", "clearFilters": "Clear Filters", "settings": "Settings", "routes": "Routes", "drivers": "Drivers", "toggleTrails": "Toggle Trails", "toggleStops": "Toggle Stops", "toggleTraffic": "Toggle Traffic", "showStops": "Show Stops", "showTraffic": "Show Traffic", "currentStatus": "Current Status", "currentSpeed": "Current Speed", "heading": "Heading", "accuracy": "Accuracy", "studentsOnBoard": "Students on Board", "routeInformation": "Route Information", "assignedRoute": "Assigned Route", "routeStops": "Route Stops", "alertsAndActions": "Alerts & Actions", "activeAlerts": "Active Alerts", "noActiveAlerts": "No Active Alerts", "quickActions": "Quick Actions", "callDriver": "Call Driver", "sendMessage": "Send Message", "noBusesFound": "No Buses Found", "tryDifferentSearch": "Try a different search", "stats": {"totalBuses": "Total Buses", "activeBuses": "Active Buses", "stoppedBuses": "Stopped Buses", "maintenanceBuses": "Maintenance Buses", "emergencyBuses": "Emergency Buses", "averageSpeed": "Average Speed", "totalStudents": "Total Students", "onTimePercentage": "On-Time Percentage", "activeAlerts": "Active Alerts"}, "actions": {"viewDetails": "View Details", "hideDetails": "Hide Details", "selectBus": "Select Bus", "trackBus": "Track Bus", "refreshData": "Refresh Data", "exportData": "Export Data", "printReport": "Print Report"}, "messages": {"dataLoaded": "Data loaded successfully", "dataError": "Error loading data", "noDataAvailable": "No data available", "connectionLost": "Connection lost", "reconnecting": "Reconnecting", "connected": "Connected successfully", "updating": "Updating", "updated": "Updated", "failed": "Operation failed"}, "time": {"justNow": "Just now", "minutesAgo": "{{count}} minutes ago", "hoursAgo": "{{count}} hours ago", "daysAgo": "{{count}} days ago", "lastSeen": "Last seen", "online": "Online", "offline": "Offline"}, "map": {"zoomIn": "Zoom In", "zoomOut": "Zoom Out", "resetView": "Reset View", "satellite": "Satellite", "street": "Street", "hybrid": "Hybrid", "terrain": "Terrain", "traffic": "Traffic", "busMarker": "<PERSON> Marker", "stopMarker": "Stop Marker", "routeLine": "Route Line"}, "sidebar": {"collapse": "Collapse Sidebar", "expand": "Expand Sidebar", "busCount": "Bus Count", "filteredCount": "Filtered Results", "allBuses": "All Buses", "selectedBus": "Selected Bus"}, "details": {"busDetails": "Bus Details", "driverInfo": "Driver Information", "routeInfo": "Route Information", "locationInfo": "Location Information", "statusInfo": "Status Information", "performanceInfo": "Performance Information", "maintenanceInfo": "Maintenance Information", "contactDriver": "Contact Driver", "emergencyContact": "Emergency Contact"}, "notifications": {"busArrived": "Bus Arrived", "busDelayed": "Bus Delayed", "routeChanged": "Route Changed", "maintenanceRequired": "Maintenance Required", "emergencyAlert": "Emergency Alert", "driverChanged": "Driver Changed", "scheduleUpdated": "Schedule Updated"}, "performance": {"onTime": "On Time", "delayed": "Delayed", "early": "Early", "efficiency": "Efficiency", "fuelConsumption": "Fuel Consumption", "distance": "Distance", "duration": "Duration", "averageSpeed": "Average Speed", "maxSpeed": "Max Speed", "stops": "Stops"}}, "students": {"status": "Status", "total": "Total Students", "editStudent": "Edit Student", "dailyView": "Daily View", "dashboardView": "Dashboard View", "pickup": "Pickup", "dropoff": "Dropoff", "attendanceDashboard": "Attendance Dashboard", "totalStudents": "Total Students", "averageAttendance": "Average Attendance", "attendanceDashboardDescription": "Overview of student attendance statistics", "searchStudents": "Search Students", "attendanceTrends": "Attendance Trends", "studentAttendance": "Student Attendance", "frequentAbsentees": "Frequent Absentees", "perfectAttendance": "Perfect Attendance", "attendanceRecords": "Attendance Records", "bulkEdit": "Bulk Edit", "attendanceRate": "Attendance Rate", "partial": "Partial Attendance", "manageStudents": "Manage Students", "addStudent": "Add Student", "name": "Name", "grade": "Grade", "parent": "Parent", "school": "School", "route": "Route", "stop": "Stop", "attendance": "Attendance", "present": "Present", "absent": "Absent"}, "users": {"bulkRole": "Bulk Role", "bulkStatus": "Bulk Status", "bulkDelete": "Bulk Delete", "newPassword": "New Password", "phone": "Phone", "manageUsers": "Manage Users", "addUser": "Add User", "name": "Name", "email": "Email", "role": "Role", "school": "School", "status": "Status", "active": "Active", "inactive": "Inactive", "admin": "Admin", "schoolManager": "School Manager", "supervisor": "Transport Supervisor", "driver": "Driver", "parent": "Parent", "student": "Student"}, "profile": {"myBus": "My Bus", "myRoute": "My Route", "studentsOnRoute": "Students on Route", "title": "Profile", "saveChanges": "Save Changes", "personalInfo": "Personal Information", "email": "Email", "phone": "Phone", "security": "Security", "changePassword": "Change Password", "name": "Name", "updateSuccess": "Profile updated successfully", "updateError": "Failed to update profile", "updating": "Updating...", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "updatePassword": "Update Password", "passwordUpdateSuccess": "Password updated successfully", "passwordUpdateError": "Failed to update password", "passwordsDoNotMatch": "Passwords do not match"}, "notifications": {"title": "Title", "new": "New Notifications", "markAsRead": "<PERSON> <PERSON>", "markAllAsRead": "<PERSON> as <PERSON>", "markAsUnread": "<PERSON> as Unread", "clearAll": "Clear All", "empty": "No notifications", "noUnreadNotifications": "No unread notifications", "notifications": "Notifications", "unread": "Unread", "read": "Read", "selected": "Selected", "selectAll": "Select All", "noNotificationsFound": "No notifications found", "searchNotifications": "Search notifications...", "deleteConfirmation": "Are you sure you want to delete the selected notifications?", "history": "Notification History", "preferences": "Notification Preferences", "preferencesDescription": "Customize how and when you receive notifications", "preferencesSaved": "Notification preferences saved successfully", "preferencesError": "Failed to save notification preferences", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "enablePush": "Enable Push Notifications", "pushPermissionRequired": "Push notification permission is required to receive notifications", "soundSettings": "Sound Settings", "volume": "Volume", "test": "Test", "grouping": "Notification Grouping", "groupingDescription": "Group similar notifications to reduce clutter", "timeWindow": "Time Window", "maxGroupSize": "Max Group Size", "geofence": "Location Alerts", "attendance": "Attendance", "maintenance": "Maintenance", "announcements": "Announcements", "templates": "Notification Templates", "templatesDescription": "Create and manage notification templates for different event types", "createTemplate": "Create Template", "editTemplate": "Edit Template", "templateName": "Template Name", "type": "Type", "message": "Message", "variables": "Variables", "clickToInsert": "Click to insert into message", "activeTemplate": "Active Template", "deleteTemplateConfirmation": "Are you sure you want to delete this template?", "duplicate": "Duplicate", "noTemplatesFound": "No templates found", "noTemplates": "No templates created yet", "createFirstTemplate": "Create your first notification template to get started", "searchTemplates": "Search templates...", "adminOnly": "Admin Access Required", "adminOnlyDescription": "Only administrators can manage notification templates", "smartNotifications": "Smart Notifications", "smartNotificationsTitle": "Smart Notification System", "smartNotificationsDescription": "Manage and configure smart notifications with custom rules and automatic routing", "notificationRules": "Notification Rules", "createRule": "Create Rule", "editRule": "Edit Rule", "ruleName": "Rule Name", "ruleDescription": "Rule Description", "trigger": "<PERSON><PERSON>", "conditions": "Conditions", "actions": "Actions", "priority": "Priority", "isActive": "Active", "triggers": {"event": "Event", "time": "Time", "condition": "Condition"}, "eventTypes": {"busDelay": "Bus Delay", "studentAbsent": "Student Absent", "maintenanceDue": "Maintenance Due", "emergencyAlert": "Emergency Alert", "routeChange": "Route Change"}, "conditionTypes": {"equals": "Equals", "notEquals": "Not Equals", "greaterThan": "Greater Than", "lessThan": "Less Than", "contains": "Contains"}, "actionTypes": {"sendNotification": "Send Notification", "sendEmail": "Send Email", "sendSMS": "Send SMS", "createAlert": "Create <PERSON><PERSON>"}, "recipients": "Recipients", "recipientTypes": {"role": "Role", "user": "User", "parent": "Parent", "driver": "Driver"}, "channels": "Delivery Channels", "channelTypes": {"push": "Push Notification", "email": "Email", "sms": "SMS", "inApp": "In-App"}, "scheduling": "Scheduling", "scheduleTypes": {"immediate": "Immediate", "delayed": "Delayed", "recurring": "Recurring"}, "analytics": "Notification Analytics", "analyticsDescription": "Statistics and performance analytics for notifications", "sentNotifications": "Sent Notifications", "deliveredNotifications": "Delivered Notifications", "openedNotifications": "Opened Notifications", "clickedNotifications": "Clicked Notifications", "deliveryRate": "Delivery Rate", "openRate": "Open Rate", "clickRate": "Click Rate", "failedNotifications": "Failed Notifications", "notificationHistory": "Notification History", "ruleExecution": "Rule Execution", "executionHistory": "Execution History", "lastExecuted": "Last Executed", "executionCount": "Execution Count", "successRate": "Success Rate"}, "settings": {"title": "Settings", "general": "General Settings", "appearance": "Appearance", "language": "Language", "selectLanguage": "Select Language", "theme": "Theme", "darkMode": "Dark Mode", "lightMode": "Light Mode", "notifications": "Notifications", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "routeUpdates": "Route Updates", "attendanceAlerts": "Attendance Alerts", "systemAnnouncements": "System Announcements", "notificationTypes": "Notification Types", "saveSettings": "Save Settings", "saveSuccess": "Setting<PERSON> saved successfully", "saveError": "Failed to save settings", "security": "Security", "changePassword": "Change Password", "twoFactorAuth": "Two-Factor Authentication"}, "evaluation": {"title": "Evaluation System", "description": "Manage evaluations and feedback for drivers, routes, and services", "evaluations": "Evaluations", "evaluationsDescription": "View and manage user evaluations and ratings", "reports": "Reports", "reportsDescription": "View satisfaction reports and analytics", "addEvaluation": "Add Evaluation", "evaluateDriver": "Evaluate Driver", "evaluateService": "Evaluate Service", "evaluateRoute": "Evaluate Route", "evaluate": "Evaluate", "evaluating": "Evaluating", "rating": "Rating", "comment": "Comment", "commentPlaceholder": "Share your experience and feedback...", "submit": "Submit Evaluation", "driver": "Driver", "service": "Service", "route": "Route", "generalService": "General Transport Service", "targetType": "Type", "noEvaluations": "No Evaluations Yet", "noEvaluationsDescription": "No evaluations have been submitted yet. Be the first to share your feedback!", "reviews": "reviews", "totalEvaluations": "Total Evaluations", "averageRating": "Average Rating", "basedOnEvaluations": "Based on {{count}} evaluations", "ratingDistribution": "Rating Distribution", "satisfactionReport": "User Satisfaction Report", "satisfactionReportDescription": "Comprehensive analysis of user satisfaction and feedback trends", "satisfactionTrend": "Satisfaction Trend", "ratings": {"poor": "Poor", "fair": "Fair", "good": "Good", "veryGood": "Very Good", "excellent": "Excellent"}, "satisfaction": {"excellent": "Excellent", "good": "Good", "average": "Average", "poor": "Poor", "critical": "Critical"}, "criteria": {"title": "Evaluation Criteria", "punctuality": "Punctuality", "safety": "Safety", "professionalism": "Professionalism", "communication": "Communication", "comfort": "Comfort", "cleanliness": "Cleanliness", "overall": "Overall Experience", "efficiency": "Route Efficiency", "timing": "Timing", "stops": "Stop Locations"}}, "complaints": {"complaints": "<PERSON><PERSON><PERSON><PERSON>", "complaintsDescription": "Submit and track complaints and suggestions", "submitComplaint": "Submit <PERSON><PERSON><PERSON><PERSON>", "submitDriverComplaint": "Submit <PERSON>", "submitServiceComplaint": "Submit Service Complaint", "submitRouteComplaint": "Submit Route Complaint", "submitGeneralComplaint": "Submit General Comp<PERSON>", "regarding": "Regarding", "title": "Title", "titlePlaceholder": "Brief description of the issue", "description": "Description", "descriptionPlaceholder": "Please provide detailed information about your complaint or suggestion...", "submit": "Submit <PERSON><PERSON><PERSON><PERSON>", "guidelines": "Guidelines", "guideline1": "Be specific and provide clear details", "guideline2": "Include date, time, and location if relevant", "guideline3": "<PERSON><PERSON><PERSON> respectful and constructive", "guideline4": "We will respond within 48 hours", "noComplaints": "No Complaints", "noComplaintsDescription": "No complaints have been submitted yet.", "response": "Response", "respond": "Respond", "responsePlaceholder": "Enter your response to this complaint...", "resolve": "Resolve", "inProgress": "Mark In Progress", "totalComplaints": "Total Complaints", "resolutionRate": "Resolution Rate", "status": {"title": "Status", "pending": "Pending", "inProgress": "In Progress", "resolved": "Resolved", "rejected": "Rejected"}, "type": {"title": "Type", "driver": "Driver", "service": "Service", "route": "Route", "general": "General"}}, "reports": {"title": "Reports", "hideFilters": "Hide Filters", "exportCSV": "Export CSV", "attendanceReport": "Attendance Report", "attendanceData": "Attendance Data", "totalStudents": "Total Students", "reportType": "Report Type", "attendance": "Attendance", "utilization": "Utilization", "performance": "Performance", "delays": "Delays", "summary": "Summary", "timeFrame": "Time Frame", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "dateRange": "Date Range", "school": "School", "generateAttendanceReports": "Generate Attendance Reports"}, "common": {"records": "Records", "select": "Select", "selectedCount": "Selected", "bulkActions": "Bulk Actions", "clearSelection": "Clear Selection", "noChange": "No Change", "setActive": "Set Active", "exportPDF": "Export PDF", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "active": "Active", "notes": "Notes (Optional)", "search": "Search", "filter": "Filter", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "add": "Add", "remove": "Remove", "back": "Back", "next": "Next", "finish": "Finish", "loading": "Loading", "noData": "No data available", "actions": "Actions", "confirm": "Confirm", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "all": "All", "seats": "seats", "showing": "Showing", "of": "of", "previous": "Previous", "update": "Update", "create": "Create", "duplicate": "Duplicate", "inactive": "Inactive", "more": "More", "less": "Less", "optional": "Optional", "submitting": "Submitting...", "anonymous": "Anonymous", "startDate": "Start Date", "endDate": "End Date", "exportCSV": "Export CSV"}, "stats": {"today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "total": "Total"}, "themes": {"manageThemes": "Manage Themes", "schoolTheme": "School Theme", "themeCustomization": "Theme Customization", "colors": "Colors", "typography": "Typography", "layout": "Layout", "branding": "Branding", "primary": "Primary Color", "secondary": "Secondary Color", "accent": "Accent Color", "background": "Background Color", "surface": "Surface Color", "text": "Text Color", "textSecondary": "Secondary Text Color", "sidebar": "Sidebar", "sidebarBackground": "Sidebar Background", "sidebarText": "Sidebar Text", "sidebarActive": "Active Item", "sidebarHover": "Hover Effect", "fontFamily": "Font Family", "fontSize": "Font Size", "fontWeight": "Font Weight", "borderRadius": "Border Radius", "spacing": "Spacing", "shadows": "Shadows", "schoolName": "School Name", "tagline": "Tagline", "logo": "Logo", "preview": "Preview", "previewMode": "Preview Mode", "saveTheme": "Save Theme", "resetTheme": "Reset Theme", "applyTheme": "Apply Theme", "themeApplied": "Theme applied successfully", "themeSaved": "Theme saved successfully", "themeReset": "Theme reset successfully", "saving": "Saving...", "applying": "Applying...", "resetting": "Resetting...", "updateAndSave": "Update & Save Theme", "permanentSave": "Save Permanently to Database", "livePreview": "Live Preview", "themeSettings": "Theme Settings", "customization": "Customization", "appearance": "Appearance"}, "maintenance": {"title": "Maintenance Management", "description": "Comprehensive bus maintenance management and smart scheduling", "overview": "Overview", "schedule": "Schedule", "alerts": "<PERSON><PERSON><PERSON>", "reports": "Reports", "totalMaintenance": "Total Maintenance", "scheduled": "Scheduled", "overdue": "Overdue", "totalCost": "Total Cost", "recentMaintenance": "Recent Maintenance", "upcomingMaintenance": "Upcoming Maintenance", "maintenanceAlerts": "Maintenance Alerts", "alertsDescription": "Due and overdue maintenance alerts", "noAlerts": "No maintenance alerts", "exportReport": "Export Report", "scheduleMaintenance": "Schedule Maintenance"}, "pageTitle": {"dashboard": "Dashboard", "notifications": "Notifications", "smartNotifications": "Smart Notifications", "maintenance": "Maintenance Management", "advancedAttendance": "Advanced Attendance", "realTimeTracking": "Real-Time Tracking", "schools": "School Management", "buses": "Bus Management", "routes": "Route Management", "students": "Student Management", "users": "User Management", "reports": "Reports", "settings": "Settings", "profile": "Profile", "themes": "Theme Management", "evaluation": "Evaluation System", "complaints": "Complaints & Suggestions"}}