/**
 * User Service
 * Handles all user-related API operations
 * Phase 2: Application Structure Reorganization
 */

import { BaseService } from '../base/BaseService';
import {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  UserListParams,
  APIResponse,
  PaginatedResponse,
  UserRole,
} from '../../api/types';

/**
 * User Service Class
 * Implements SOLID principles and provides comprehensive user management
 */
export class UserService extends BaseService {
  private readonly endpoint = '/users';

  /**
   * Get paginated list of users
   */
  async getUsers(params: UserListParams = {}): Promise<APIResponse<PaginatedResponse<User>>> {
    return this.getPaginated<User>(this.endpoint, params);
  }

  /**
   * Get user by ID
   */
  async getUserById(id: string): Promise<APIResponse<User>> {
    return this.get<User>(`${this.endpoint}/${id}`);
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(): Promise<APIResponse<User>> {
    return this.get<User>(`${this.endpoint}/me`);
  }

  /**
   * Create new user
   */
  async createUser(userData: CreateUserRequest): Promise<APIResponse<User>> {
    // Validate required fields
    const validation = this.validateCreateUserData(userData);
    if (!validation.isValid) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid user data',
          details: validation.errors,
        },
      };
    }

    return this.post<User>(this.endpoint, userData);
  }

  /**
   * Update user
   */
  async updateUser(id: string, userData: UpdateUserRequest): Promise<APIResponse<User>> {
    // Validate update data
    const validation = this.validateUpdateUserData(userData);
    if (!validation.isValid) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid update data',
          details: validation.errors,
        },
      };
    }

    return this.put<User>(`${this.endpoint}/${id}`, userData);
  }

  /**
   * Partially update user
   */
  async patchUser(id: string, userData: Partial<UpdateUserRequest>): Promise<APIResponse<User>> {
    return this.patch<User>(`${this.endpoint}/${id}`, userData);
  }

  /**
   * Delete user
   */
  async deleteUser(id: string): Promise<APIResponse<{ message: string }>> {
    return this.delete<{ message: string }>(`${this.endpoint}/${id}`);
  }

  /**
   * Activate/Deactivate user
   */
  async toggleUserStatus(id: string, isActive: boolean): Promise<APIResponse<User>> {
    return this.patch<User>(`${this.endpoint}/${id}`, { is_active: isActive });
  }

  /**
   * Get users by role
   */
  async getUsersByRole(role: UserRole, tenantId?: string): Promise<APIResponse<User[]>> {
    const params: UserListParams = { role };
    if (tenantId) {
      params.tenant_id = tenantId;
    }

    const response = await this.getUsers(params);
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<User[]>;
  }

  /**
   * Search users
   */
  async searchUsers(
    query: string,
    fields: string[] = ['name', 'email'],
    tenantId?: string
  ): Promise<APIResponse<User[]>> {
    const params: UserListParams = {
      search: query,
      search_fields: fields,
    };
    
    if (tenantId) {
      params.tenant_id = tenantId;
    }

    const response = await this.getUsers(params);
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<User[]>;
  }

  /**
   * Get user statistics
   */
  async getUserStats(tenantId?: string): Promise<APIResponse<UserStats>> {
    const params = tenantId ? { tenant_id: tenantId } : {};
    const queryString = this.buildQueryString(params);
    return this.get<UserStats>(`${this.endpoint}/stats${queryString}`);
  }

  /**
   * Bulk operations
   */
  async bulkCreateUsers(users: CreateUserRequest[]): Promise<APIResponse<BulkOperationResult<User>>> {
    return this.post<BulkOperationResult<User>>(`${this.endpoint}/bulk`, { users });
  }

  async bulkUpdateUsers(updates: Array<{ id: string; data: UpdateUserRequest }>): Promise<APIResponse<BulkOperationResult<User>>> {
    return this.put<BulkOperationResult<User>>(`${this.endpoint}/bulk`, { updates });
  }

  async bulkDeleteUsers(ids: string[]): Promise<APIResponse<BulkOperationResult<{ id: string }>>> {
    return this.delete<BulkOperationResult<{ id: string }>>(`${this.endpoint}/bulk?ids=${ids.join(',')}`);
  }

  /**
   * User preferences
   */
  async updateUserPreferences(id: string, preferences: any): Promise<APIResponse<User>> {
    return this.patch<User>(`${this.endpoint}/${id}/preferences`, preferences);
  }

  /**
   * User avatar
   */
  async uploadUserAvatar(id: string, file: File): Promise<APIResponse<{ avatar_url: string }>> {
    const formData = new FormData();
    formData.append('avatar', file);

    return this.request<{ avatar_url: string }>(`${this.endpoint}/${id}/avatar`, {
      method: 'POST',
      body: formData,
      headers: {}, // Let browser set Content-Type for FormData
    });
  }

  /**
   * Password management
   */
  async changePassword(
    id: string,
    currentPassword: string,
    newPassword: string
  ): Promise<APIResponse<{ message: string }>> {
    return this.post<{ message: string }>(`${this.endpoint}/${id}/change-password`, {
      current_password: currentPassword,
      new_password: newPassword,
    });
  }

  async resetPassword(email: string): Promise<APIResponse<{ message: string }>> {
    return this.post<{ message: string }>(`${this.endpoint}/reset-password`, { email });
  }

  /**
   * Validation methods
   */
  private validateCreateUserData(data: CreateUserRequest): ValidationResult {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length < 2) {
      errors.push('Name must be at least 2 characters long');
    }

    if (!data.email || !this.isValidEmail(data.email)) {
      errors.push('Valid email is required');
    }

    if (!data.role || !Object.values(UserRole).includes(data.role)) {
      errors.push('Valid role is required');
    }

    if (!data.tenant_id || data.tenant_id.trim().length === 0) {
      errors.push('Tenant ID is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private validateUpdateUserData(data: UpdateUserRequest): ValidationResult {
    const errors: string[] = [];

    if (data.name !== undefined && (!data.name || data.name.trim().length < 2)) {
      errors.push('Name must be at least 2 characters long');
    }

    if (data.email !== undefined && (!data.email || !this.isValidEmail(data.email))) {
      errors.push('Valid email is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

// ============================================================================
// Supporting Types
// ============================================================================

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

interface UserStats {
  total: number;
  active: number;
  inactive: number;
  by_role: Record<UserRole, number>;
  recent_registrations: number;
  last_updated: string;
}

interface BulkOperationResult<T> {
  successful: T[];
  failed: Array<{
    data: any;
    error: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

// ============================================================================
// Service Instance
// ============================================================================

// Export singleton instance
export const userService = new UserService();

// Export class for dependency injection
export { UserService };
