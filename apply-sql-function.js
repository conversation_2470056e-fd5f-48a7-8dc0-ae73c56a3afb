/**
 * تطبيق دالة SQL مباشرة على قاعدة البيانات
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applySQLFunction() {
  try {
    console.log('🔧 تطبيق الدالة المساعدة...');
    
    // قراءة ملف SQL
    const sqlContent = fs.readFileSync('create-exec-sql-function.sql', 'utf8');
    
    // تقسيم الاستعلامات
    const queries = sqlContent
      .split(';')
      .map(q => q.trim())
      .filter(q => q.length > 0 && !q.startsWith('--'));
    
    console.log(`📄 العثور على ${queries.length} استعلام`);
    
    for (let i = 0; i < queries.length; i++) {
      const query = queries[i];
      if (query.trim()) {
        try {
          console.log(`⚡ تنفيذ الاستعلام ${i + 1}...`);
          
          const { data, error } = await supabase.rpc('exec', {
            sql: query + ';'
          });
          
          if (error) {
            console.log(`❌ خطأ في الاستعلام ${i + 1}:`, error.message);
          } else {
            console.log(`✅ تم تنفيذ الاستعلام ${i + 1} بنجاح`);
          }
        } catch (err) {
          console.log(`❌ خطأ في الاستعلام ${i + 1}:`, err.message);
        }
      }
    }
    
    console.log('✅ انتهى تطبيق الدالة المساعدة');
    
  } catch (error) {
    console.error('❌ خطأ عام:', error);
  }
}

applySQLFunction();
