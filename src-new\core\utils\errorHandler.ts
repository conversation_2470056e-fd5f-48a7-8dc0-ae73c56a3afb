// Error handling utilities
export class AppError extends Error {
  constructor(
    message: string,
    public code?: string,
    public statusCode?: number,
  ) {
    super(message);
    this.name = "AppError";
  }
}

export const handleSupabaseError = (error: any): AppError => {
  if (error?.code) {
    switch (error.code) {
      case "PGRST116":
        return new AppError("No data found", "NOT_FOUND", 404);
      case "23505":
        return new AppError("Data already exists", "DUPLICATE", 409);
      case "23503":
        return new AppError("Referenced data not found", "FOREIGN_KEY", 400);
      case "42501":
        return new AppError("Permission denied", "UNAUTHORIZED", 403);
      default:
        return new AppError(error.message || "Database error", error.code, 500);
    }
  }
  return new AppError(error.message || "Unknown error", "UNKNOWN", 500);
};

export const logError = (error: Error, context?: string) => {
  console.error(`[${context || "APP"}] Error:`, {
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
  });
};
