/**
 * نظام الاختبار التفاعلي للتطبيق
 * Interactive Application Testing System
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎮 نظام الاختبار التفاعلي للتطبيق\n');

class InteractiveTestingSystem {
  constructor() {
    this.testSuites = [];
    this.currentSuite = null;
    this.results = [];
  }

  /**
   * بدء النظام التفاعلي
   */
  async startInteractiveTesting() {
    console.log('🚀 بدء الاختبار التفاعلي...\n');

    // إنشاء مجموعات الاختبار
    this.createTestSuites();

    // عرض القائمة الرئيسية
    this.showMainMenu();
  }

  /**
   * إنشاء مجموعات الاختبار
   */
  createTestSuites() {
    this.testSuites = [
      {
        id: 'basic',
        name: 'الاختبارات الأساسية',
        description: 'اختبار البنية والملفات الأساسية',
        tests: [
          { name: 'فحص البنية الأساسية', action: 'checkBasicStructure' },
          { name: 'فحص ملفات التكوين', action: 'checkConfigFiles' },
          { name: 'فحص مسارات الاستيراد', action: 'checkImportPaths' }
        ]
      },
      {
        id: 'components',
        name: 'اختبار المكونات',
        description: 'اختبار المكونات والصفحات',
        tests: [
          { name: 'فحص المكونات الأساسية', action: 'checkCoreComponents' },
          { name: 'فحص الصفحات الرئيسية', action: 'checkMainPages' },
          { name: 'فحص مكونات UI', action: 'checkUIComponents' }
        ]
      },
      {
        id: 'services',
        name: 'اختبار الخدمات',
        description: 'اختبار الخدمات وقاعدة البيانات',
        tests: [
          { name: 'فحص خدمات قاعدة البيانات', action: 'checkDatabaseServices' },
          { name: 'فحص خدمات الأمان', action: 'checkSecurityServices' },
          { name: 'فحص خدمات الإشعارات', action: 'checkNotificationServices' }
        ]
      },
      {
        id: 'functionality',
        name: 'اختبار الوظائف',
        description: 'اختبار الوظائف الأساسية',
        tests: [
          { name: 'اختبار تسجيل الدخول', action: 'testLogin' },
          { name: 'اختبار لوحة التحكم', action: 'testDashboard' },
          { name: 'اختبار إدارة الحافلات', action: 'testBusManagement' }
        ]
      },
      {
        id: 'performance',
        name: 'اختبار الأداء',
        description: 'اختبار أداء التطبيق',
        tests: [
          { name: 'فحص سرعة التحميل', action: 'checkLoadSpeed' },
          { name: 'فحص استهلاك الذاكرة', action: 'checkMemoryUsage' },
          { name: 'فحص حجم الملفات', action: 'checkFileSize' }
        ]
      }
    ];
  }

  /**
   * عرض القائمة الرئيسية
   */
  showMainMenu() {
    console.log('📋 اختر مجموعة الاختبار:\n');
    
    this.testSuites.forEach((suite, index) => {
      console.log(`${index + 1}. ${suite.name}`);
      console.log(`   ${suite.description}\n`);
    });

    console.log('0. تشغيل جميع الاختبارات');
    console.log('q. خروج\n');

    // في بيئة حقيقية، ستستخدم readline للتفاعل
    // هنا سنقوم بتشغيل جميع الاختبارات تلقائياً
    this.runAllTests();
  }

  /**
   * تشغيل جميع الاختبارات
   */
  async runAllTests() {
    console.log('🔄 تشغيل جميع الاختبارات...\n');

    for (const suite of this.testSuites) {
      await this.runTestSuite(suite);
    }

    this.generateFinalReport();
  }

  /**
   * تشغيل مجموعة اختبار
   */
  async runTestSuite(suite) {
    console.log(`\n🧪 تشغيل: ${suite.name}`);
    console.log('='.repeat(50));

    for (const test of suite.tests) {
      await this.runSingleTest(suite.id, test);
    }
  }

  /**
   * تشغيل اختبار واحد
   */
  async runSingleTest(suiteId, test) {
    console.log(`\n🔍 ${test.name}...`);

    try {
      const result = await this.executeTest(test.action);
      
      this.results.push({
        suite: suiteId,
        test: test.name,
        action: test.action,
        status: result.status,
        message: result.message,
        details: result.details || null,
        timestamp: new Date().toISOString()
      });

      const statusIcon = result.status === 'passed' ? '✅' : 
                        result.status === 'failed' ? '❌' : '⚠️';
      
      console.log(`${statusIcon} ${result.message}`);
      
      if (result.details) {
        console.log(`   التفاصيل: ${result.details}`);
      }

    } catch (error) {
      this.results.push({
        suite: suiteId,
        test: test.name,
        action: test.action,
        status: 'failed',
        message: `خطأ في التنفيذ: ${error.message}`,
        timestamp: new Date().toISOString()
      });

      console.log(`❌ خطأ في التنفيذ: ${error.message}`);
    }
  }

  /**
   * تنفيذ الاختبار
   */
  async executeTest(action) {
    switch (action) {
      case 'checkBasicStructure':
        return this.checkBasicStructure();
      
      case 'checkConfigFiles':
        return this.checkConfigFiles();
      
      case 'checkImportPaths':
        return this.checkImportPaths();
      
      case 'checkCoreComponents':
        return this.checkCoreComponents();
      
      case 'checkMainPages':
        return this.checkMainPages();
      
      case 'checkUIComponents':
        return this.checkUIComponents();
      
      case 'checkDatabaseServices':
        return this.checkDatabaseServices();
      
      case 'checkSecurityServices':
        return this.checkSecurityServices();
      
      case 'checkNotificationServices':
        return this.checkNotificationServices();
      
      case 'testLogin':
        return this.testLogin();
      
      case 'testDashboard':
        return this.testDashboard();
      
      case 'testBusManagement':
        return this.testBusManagement();
      
      case 'checkLoadSpeed':
        return this.checkLoadSpeed();
      
      case 'checkMemoryUsage':
        return this.checkMemoryUsage();
      
      case 'checkFileSize':
        return this.checkFileSize();
      
      default:
        return {
          status: 'failed',
          message: 'اختبار غير معروف'
        };
    }
  }

  /**
   * فحص البنية الأساسية
   */
  checkBasicStructure() {
    const requiredDirs = [
      'src/components', 'src/pages', 'src/contexts', 
      'src/services', 'src/lib', 'src/hooks', 'src/types'
    ];

    const missingDirs = requiredDirs.filter(dir => !fs.existsSync(dir));

    if (missingDirs.length === 0) {
      return {
        status: 'passed',
        message: 'جميع المجلدات الأساسية موجودة',
        details: `تم فحص ${requiredDirs.length} مجلد`
      };
    } else {
      return {
        status: 'failed',
        message: 'بعض المجلدات الأساسية مفقودة',
        details: `مفقود: ${missingDirs.join(', ')}`
      };
    }
  }

  /**
   * فحص ملفات التكوين
   */
  checkConfigFiles() {
    const configFiles = [
      'package.json', 'vite.config.ts', 'tsconfig.json',
      'src/main.tsx', 'src/App.tsx'
    ];

    const missingFiles = configFiles.filter(file => !fs.existsSync(file));

    if (missingFiles.length === 0) {
      return {
        status: 'passed',
        message: 'جميع ملفات التكوين موجودة',
        details: `تم فحص ${configFiles.length} ملف`
      };
    } else {
      return {
        status: 'failed',
        message: 'بعض ملفات التكوين مفقودة',
        details: `مفقود: ${missingFiles.join(', ')}`
      };
    }
  }

  /**
   * فحص مسارات الاستيراد
   */
  checkImportPaths() {
    const filesToCheck = ['src/main.tsx', 'src/App.tsx'];
    let totalImports = 0;
    let validImports = 0;

    for (const file of filesToCheck) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        const imports = this.extractImports(content);
        totalImports += imports.length;

        for (const importPath of imports) {
          if (this.validateImportPath(importPath, file)) {
            validImports++;
          }
        }
      }
    }

    if (validImports === totalImports) {
      return {
        status: 'passed',
        message: 'جميع مسارات الاستيراد صحيحة',
        details: `${validImports}/${totalImports} مسار صحيح`
      };
    } else {
      return {
        status: 'failed',
        message: 'بعض مسارات الاستيراد غير صحيحة',
        details: `${validImports}/${totalImports} مسار صحيح`
      };
    }
  }

  /**
   * فحص المكونات الأساسية
   */
  checkCoreComponents() {
    const coreComponents = [
      'src/contexts/AuthContext.tsx',
      'src/contexts/ThemeContext.tsx',
      'src/contexts/DatabaseContext.tsx'
    ];

    const existingComponents = coreComponents.filter(comp => fs.existsSync(comp));

    if (existingComponents.length === coreComponents.length) {
      return {
        status: 'passed',
        message: 'جميع المكونات الأساسية موجودة',
        details: `${existingComponents.length}/${coreComponents.length} مكون`
      };
    } else {
      return {
        status: 'warning',
        message: 'بعض المكونات الأساسية مفقودة',
        details: `${existingComponents.length}/${coreComponents.length} مكون موجود`
      };
    }
  }

  /**
   * فحص الصفحات الرئيسية
   */
  checkMainPages() {
    const mainPages = [
      'src/pages/login/LoginPage.tsx',
      'src/pages/dashboard/DashboardPage.tsx',
      'src/pages/dashboard/BusesPage.tsx'
    ];

    const existingPages = mainPages.filter(page => fs.existsSync(page));

    if (existingPages.length >= mainPages.length * 0.8) {
      return {
        status: 'passed',
        message: 'معظم الصفحات الرئيسية موجودة',
        details: `${existingPages.length}/${mainPages.length} صفحة`
      };
    } else {
      return {
        status: 'warning',
        message: 'بعض الصفحات الرئيسية مفقودة',
        details: `${existingPages.length}/${mainPages.length} صفحة موجودة`
      };
    }
  }

  /**
   * فحص مكونات UI
   */
  checkUIComponents() {
    const uiComponents = [
      'src/components/ui',
      'src/components/layout',
      'src/components/common'
    ];

    const existingComponents = uiComponents.filter(comp => fs.existsSync(comp));

    return {
      status: existingComponents.length > 0 ? 'passed' : 'warning',
      message: existingComponents.length > 0 ? 'مكونات UI موجودة' : 'مكونات UI قد تكون مفقودة',
      details: `${existingComponents.length}/${uiComponents.length} مجموعة مكونات`
    };
  }

  /**
   * فحص خدمات قاعدة البيانات
   */
  checkDatabaseServices() {
    const dbServices = [
      'src/lib/supabase.ts',
      'src/services/DatabaseService.ts'
    ];

    const existingServices = dbServices.filter(service => fs.existsSync(service));

    if (existingServices.length > 0) {
      return {
        status: 'passed',
        message: 'خدمات قاعدة البيانات موجودة',
        details: `${existingServices.length}/${dbServices.length} خدمة`
      };
    } else {
      return {
        status: 'failed',
        message: 'خدمات قاعدة البيانات مفقودة',
        details: 'لا توجد خدمات قاعدة بيانات'
      };
    }
  }

  /**
   * فحص خدمات الأمان
   */
  checkSecurityServices() {
    const securityServices = [
      'src/services/security',
      'src/middleware/authMiddleware.ts'
    ];

    const existingServices = securityServices.filter(service => fs.existsSync(service));

    return {
      status: existingServices.length > 0 ? 'passed' : 'warning',
      message: existingServices.length > 0 ? 'خدمات الأمان موجودة' : 'خدمات الأمان قد تكون مفقودة',
      details: `${existingServices.length}/${securityServices.length} خدمة أمان`
    };
  }

  /**
   * فحص خدمات الإشعارات
   */
  checkNotificationServices() {
    const notificationServices = [
      'src/lib/notificationService.ts',
      'src/lib/pushNotifications.ts'
    ];

    const existingServices = notificationServices.filter(service => fs.existsSync(service));

    return {
      status: existingServices.length > 0 ? 'passed' : 'warning',
      message: existingServices.length > 0 ? 'خدمات الإشعارات موجودة' : 'خدمات الإشعارات قد تكون مفقودة',
      details: `${existingServices.length}/${notificationServices.length} خدمة إشعارات`
    };
  }

  /**
   * اختبار تسجيل الدخول
   */
  testLogin() {
    // فحص وجود صفحة تسجيل الدخول ومكوناتها
    const loginFiles = [
      'src/pages/login/LoginPage.tsx',
      'src/contexts/AuthContext.tsx'
    ];

    const existingFiles = loginFiles.filter(file => fs.existsSync(file));

    if (existingFiles.length === loginFiles.length) {
      return {
        status: 'passed',
        message: 'مكونات تسجيل الدخول موجودة',
        details: 'صفحة تسجيل الدخول و AuthContext متوفران'
      };
    } else {
      return {
        status: 'failed',
        message: 'مكونات تسجيل الدخول مفقودة',
        details: `${existingFiles.length}/${loginFiles.length} مكون موجود`
      };
    }
  }

  /**
   * اختبار لوحة التحكم
   */
  testDashboard() {
    const dashboardFiles = [
      'src/pages/dashboard/DashboardPage.tsx'
    ];

    const existingFiles = dashboardFiles.filter(file => fs.existsSync(file));

    return {
      status: existingFiles.length > 0 ? 'passed' : 'failed',
      message: existingFiles.length > 0 ? 'لوحة التحكم موجودة' : 'لوحة التحكم مفقودة',
      details: `${existingFiles.length}/${dashboardFiles.length} صفحة لوحة تحكم`
    };
  }

  /**
   * اختبار إدارة الحافلات
   */
  testBusManagement() {
    const busFiles = [
      'src/pages/dashboard/BusesPage.tsx',
      'src/components/buses'
    ];

    const existingFiles = busFiles.filter(file => fs.existsSync(file));

    return {
      status: existingFiles.length > 0 ? 'passed' : 'warning',
      message: existingFiles.length > 0 ? 'مكونات إدارة الحافلات موجودة' : 'مكونات إدارة الحافلات قد تكون مفقودة',
      details: `${existingFiles.length}/${busFiles.length} مكون حافلات`
    };
  }

  /**
   * فحص سرعة التحميل
   */
  checkLoadSpeed() {
    // فحص حجم الملفات الأساسية
    const mainFiles = ['src/main.tsx', 'src/App.tsx'];
    let totalSize = 0;

    for (const file of mainFiles) {
      if (fs.existsSync(file)) {
        const stats = fs.statSync(file);
        totalSize += stats.size;
      }
    }

    const sizeKB = (totalSize / 1024).toFixed(2);

    return {
      status: totalSize < 50000 ? 'passed' : 'warning',
      message: totalSize < 50000 ? 'حجم الملفات الأساسية مناسب' : 'حجم الملفات الأساسية كبير',
      details: `${sizeKB} KB إجمالي`
    };
  }

  /**
   * فحص استهلاك الذاكرة
   */
  checkMemoryUsage() {
    const memUsage = process.memoryUsage();
    const usedMB = (memUsage.heapUsed / 1024 / 1024).toFixed(2);

    return {
      status: memUsage.heapUsed < 100 * 1024 * 1024 ? 'passed' : 'warning',
      message: 'استهلاك الذاكرة مقبول',
      details: `${usedMB} MB مستخدم`
    };
  }

  /**
   * فحص حجم الملفات
   */
  checkFileSize() {
    let totalSize = 0;
    let fileCount = 0;

    const countFiles = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const files = fs.readdirSync(dir);
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory()) {
          countFiles(filePath);
        } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
          totalSize += stats.size;
          fileCount++;
        }
      }
    };

    countFiles('src');

    const sizeMB = (totalSize / 1024 / 1024).toFixed(2);

    return {
      status: totalSize < 10 * 1024 * 1024 ? 'passed' : 'warning',
      message: 'حجم مشروع مناسب',
      details: `${sizeMB} MB في ${fileCount} ملف`
    };
  }

  /**
   * استخراج مسارات الاستيراد
   */
  extractImports(content) {
    const importRegex = /import.*from\s+['"`]([^'"`]+)['"`]/g;
    const imports = [];
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      imports.push(match[1]);
    }
    
    return imports;
  }

  /**
   * التحقق من صحة مسار الاستيراد
   */
  validateImportPath(importPath, fromFile) {
    if (!importPath.startsWith('.')) {
      return true; // مسار خارجي
    }

    const fromDir = path.dirname(fromFile);
    const resolvedPath = path.resolve(fromDir, importPath);
    
    const extensions = ['', '.ts', '.tsx', '.js', '.jsx'];
    
    for (const ext of extensions) {
      if (fs.existsSync(resolvedPath + ext)) {
        return true;
      }
    }
    
    const indexExtensions = ['/index.ts', '/index.tsx', '/index.js', '/index.jsx'];
    for (const indexExt of indexExtensions) {
      if (fs.existsSync(resolvedPath + indexExt)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * إنشاء التقرير النهائي
   */
  generateFinalReport() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportDir = path.join(process.cwd(), 'reports', 'interactive-testing');
    
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const stats = this.calculateStats();
    
    const report = {
      test_info: {
        timestamp: timestamp,
        total_tests: this.results.length,
        passed_tests: stats.passed,
        failed_tests: stats.failed,
        warning_tests: stats.warning,
        success_rate: stats.successRate
      },
      test_suites: this.testSuites.map(suite => ({
        id: suite.id,
        name: suite.name,
        results: this.results.filter(r => r.suite === suite.id)
      })),
      detailed_results: this.results,
      summary: this.generateSummary(stats)
    };

    const reportPath = path.join(reportDir, `interactive-test-report-${timestamp}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    this.showFinalSummary(stats);
    console.log(`\n📊 تم إنشاء التقرير التفصيلي: ${reportPath}`);
  }

  /**
   * حساب الإحصائيات
   */
  calculateStats() {
    const passed = this.results.filter(r => r.status === 'passed').length;
    const failed = this.results.filter(r => r.status === 'failed').length;
    const warning = this.results.filter(r => r.status === 'warning').length;
    const total = this.results.length;

    return {
      passed,
      failed,
      warning,
      total,
      successRate: total > 0 ? ((passed / total) * 100).toFixed(1) : 0
    };
  }

  /**
   * إنشاء الملخص
   */
  generateSummary(stats) {
    const recommendations = [];

    if (stats.failed > 0) {
      recommendations.push('إصلاح الاختبارات الفاشلة أولاً');
    }

    if (stats.warning > 0) {
      recommendations.push('مراجعة التحذيرات وإصلاحها إن أمكن');
    }

    if (stats.failed === 0 && stats.warning === 0) {
      recommendations.push('جميع الاختبارات نجحت - التطبيق جاهز للاستخدام');
    }

    return {
      overall_status: stats.failed === 0 ? (stats.warning === 0 ? 'excellent' : 'good') : 'needs_improvement',
      recommendations
    };
  }

  /**
   * عرض الملخص النهائي
   */
  showFinalSummary(stats) {
    console.log('\n' + '='.repeat(60));
    console.log('🎯 ملخص الاختبار التفاعلي النهائي');
    console.log('='.repeat(60));
    console.log(`🧪 إجمالي الاختبارات: ${stats.total}`);
    console.log(`✅ نجح: ${stats.passed}`);
    console.log(`❌ فشل: ${stats.failed}`);
    console.log(`⚠️ تحذيرات: ${stats.warning}`);
    console.log(`📈 معدل النجاح: ${stats.successRate}%`);
    
    if (stats.failed === 0 && stats.warning === 0) {
      console.log('\n🎉 ممتاز! جميع الاختبارات نجحت بدون تحذيرات!');
      console.log('✅ التطبيق جاهز للاستخدام والانتقال للمرحلة التالية.');
    } else if (stats.failed === 0) {
      console.log('\n✅ جيد! جميع الاختبارات الأساسية نجحت مع بعض التحذيرات.');
      console.log('⚠️ يُنصح بمراجعة التحذيرات وإصلاحها.');
    } else {
      console.log('\n⚠️ يحتاج تحسين! هناك اختبارات فاشلة تحتاج إصلاح.');
      console.log('🔧 يجب إصلاح المشاكل قبل المتابعة.');
    }
    
    console.log('\n🎯 الخطوات التالية:');
    if (stats.failed === 0 && stats.warning === 0) {
      console.log('1. ✅ اختبار التطبيق يدوياً في المتصفح');
      console.log('2. 🔒 البدء في المرحلة الثالثة (RLS)');
      console.log('3. 📚 توثيق البنية الحالية');
    } else if (stats.failed === 0) {
      console.log('1. ⚠️ مراجعة وإصلاح التحذيرات');
      console.log('2. 🧪 إعادة تشغيل الاختبار');
      console.log('3. ✅ اختبار التطبيق يدوياً');
    } else {
      console.log('1. 🔧 إصلاح الاختبارات الفاشلة');
      console.log('2. 🧪 إعادة تشغيل الاختبار');
      console.log('3. ✅ التأكد من عمل جميع المكونات');
    }
    console.log('='.repeat(60));
  }
}

/**
 * الدالة الرئيسية
 */
async function main() {
  try {
    const testingSystem = new InteractiveTestingSystem();
    await testingSystem.startInteractiveTesting();
  } catch (error) {
    console.error('💥 خطأ في نظام الاختبار التفاعلي:', error);
    process.exit(1);
  }
}

// تشغيل النظام
main();
