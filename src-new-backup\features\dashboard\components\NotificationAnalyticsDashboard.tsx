import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  BarChart3,
  TrendingUp,
  Eye,
  Bell,
  Users,
  Clock,
  Filter,
  Download,
  RefreshCw,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { supabase } from "../../lib/supabase";
import { cn } from "../../utils/cn";

interface NotificationAnalyticsDashboardProps {
  className?: string;
}

interface AnalyticsData {
  totalSent: number;
  totalOpened: number;
  openRate: number;
  byType: Record<string, { sent: number; opened: number; openRate: number }>;
  byDay: Array<{ date: string; sent: number; opened: number }>;
  recentActivity: Array<{
    id: string;
    event_type: string;
    notification_data: any;
    timestamp: string;
  }>;
}

const NOTIFICATION_TYPES = [
  { id: "geofence", name: "Location Alerts", color: "bg-blue-500" },
  { id: "attendance", name: "Attendance", color: "bg-green-500" },
  { id: "maintenance", name: "Maintenance", color: "bg-orange-500" },
  { id: "announcements", name: "Announcements", color: "bg-purple-500" },
  { id: "emergency", name: "Emergency", color: "bg-red-500" },
];

export const NotificationAnalyticsDashboard: React.FC<
  NotificationAnalyticsDashboardProps
> = ({ className = "" }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState("7d");
  const [selectedType, setSelectedType] = useState<string>("all");

  useEffect(() => {
    if (user?.role === "admin") {
      fetchAnalytics();
    } else {
      setLoading(false);
    }
  }, [user, dateRange, selectedType]);

  const fetchAnalytics = async () => {
    if (!user?.tenant_id) return;

    try {
      setLoading(true);
      setError(null);

      const endDate = new Date();
      const startDate = new Date();

      switch (dateRange) {
        case "1d":
          startDate.setDate(endDate.getDate() - 1);
          break;
        case "7d":
          startDate.setDate(endDate.getDate() - 7);
          break;
        case "30d":
          startDate.setDate(endDate.getDate() - 30);
          break;
        case "90d":
          startDate.setDate(endDate.getDate() - 90);
          break;
      }

      // Fetch notification analytics
      let query = supabase
        .from("notification_analytics")
        .select("*")
        .eq("tenant_id", user.tenant_id)
        .gte("timestamp", startDate.toISOString())
        .lte("timestamp", endDate.toISOString())
        .order("timestamp", { ascending: false });

      if (selectedType !== "all") {
        query = query.eq("notification_data->>type", selectedType);
      }

      const { data: analyticsData, error: analyticsError } = await query;

      if (analyticsError) throw analyticsError;

      // Process analytics data
      const processedData = processAnalyticsData(analyticsData || []);
      setAnalytics(processedData);
    } catch (err: any) {
      console.error("Error fetching analytics:", err);
      setError(err.message || "Failed to fetch analytics");
    } finally {
      setLoading(false);
    }
  };

  const processAnalyticsData = (data: any[]): AnalyticsData => {
    const sentEvents = data.filter((d) => d.event_type === "notification_sent");
    const openedEvents = data.filter(
      (d) => d.event_type === "notification_opened",
    );

    const totalSent = sentEvents.length;
    const totalOpened = openedEvents.length;
    const openRate = totalSent > 0 ? (totalOpened / totalSent) * 100 : 0;

    // Group by type
    const byType: Record<
      string,
      { sent: number; opened: number; openRate: number }
    > = {};

    NOTIFICATION_TYPES.forEach((type) => {
      const typeSent = sentEvents.filter(
        (e) => e.notification_data?.type === type.id,
      ).length;
      const typeOpened = openedEvents.filter(
        (e) => e.notification_data?.type === type.id,
      ).length;

      byType[type.id] = {
        sent: typeSent,
        opened: typeOpened,
        openRate: typeSent > 0 ? (typeOpened / typeSent) * 100 : 0,
      };
    });

    // Group by day
    const byDay: Array<{ date: string; sent: number; opened: number }> = [];
    const dayMap = new Map<string, { sent: number; opened: number }>();

    sentEvents.forEach((event) => {
      const date = new Date(event.timestamp).toISOString().split("T")[0];
      if (!dayMap.has(date)) {
        dayMap.set(date, { sent: 0, opened: 0 });
      }
      dayMap.get(date)!.sent++;
    });

    openedEvents.forEach((event) => {
      const date = new Date(event.timestamp).toISOString().split("T")[0];
      if (!dayMap.has(date)) {
        dayMap.set(date, { sent: 0, opened: 0 });
      }
      dayMap.get(date)!.opened++;
    });

    Array.from(dayMap.entries())
      .sort(([a], [b]) => a.localeCompare(b))
      .forEach(([date, stats]) => {
        byDay.push({ date, ...stats });
      });

    return {
      totalSent,
      totalOpened,
      openRate,
      byType,
      byDay,
      recentActivity: data.slice(0, 10),
    };
  };

  const exportData = async () => {
    if (!analytics) return;

    const csvData = [
      ["Date", "Type", "Event", "Count"],
      ...analytics.recentActivity.map((activity) => [
        new Date(activity.timestamp).toLocaleDateString(),
        activity.notification_data?.type || "unknown",
        activity.event_type,
        "1",
      ]),
    ];

    const csvContent = csvData.map((row) => row.join(",")).join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `notification-analytics-${dateRange}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (user?.role !== "admin") {
    return (
      <div className={cn("text-center py-12", className)}>
        <Bell size={48} className="mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Admin Access Required
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          Only administrators can view notification analytics
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-4">{error}</div>
        <Button onClick={fetchAnalytics} leftIcon={<RefreshCw size={16} />}>
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
            <BarChart3 size={20} className="mr-2" />
            Notification Analytics
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Track notification delivery and engagement metrics
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
          >
            <option value="1d">Last 24 hours</option>
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>

          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
          >
            <option value="all">All Types</option>
            {NOTIFICATION_TYPES.map((type) => (
              <option key={type.id} value={type.id}>
                {type.name}
              </option>
            ))}
          </select>

          <Button
            size="sm"
            variant="outline"
            onClick={exportData}
            leftIcon={<Download size={16} />}
          >
            Export
          </Button>
        </div>
      </div>

      {analytics && (
        <>
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <Bell
                    size={20}
                    className="text-blue-600 dark:text-blue-400"
                  />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Total Sent
                  </p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                    {analytics.totalSent.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <Eye
                    size={20}
                    className="text-green-600 dark:text-green-400"
                  />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Total Opened
                  </p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                    {analytics.totalOpened.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                  <TrendingUp
                    size={20}
                    className="text-purple-600 dark:text-purple-400"
                  />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Open Rate
                  </p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                    {analytics.openRate.toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                  <Users
                    size={20}
                    className="text-orange-600 dark:text-orange-400"
                  />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Engagement
                  </p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                    {analytics.totalOpened > 0 ? "High" : "Low"}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Notification Types Breakdown */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Performance by Type
            </h3>
            <div className="space-y-4">
              {NOTIFICATION_TYPES.map((type) => {
                const stats = analytics.byType[type.id] || {
                  sent: 0,
                  opened: 0,
                  openRate: 0,
                };
                return (
                  <div
                    key={type.id}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center space-x-3">
                      <div className={cn("w-3 h-3 rounded-full", type.color)} />
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {type.name}
                      </span>
                    </div>
                    <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
                      <span>Sent: {stats.sent}</span>
                      <span>Opened: {stats.opened}</span>
                      <span className="font-medium">
                        {stats.openRate.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Recent Activity
            </h3>
            <div className="space-y-3">
              {analytics.recentActivity.map((activity, index) => (
                <div
                  key={activity.id || index}
                  className="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700 last:border-0"
                >
                  <div className="flex items-center space-x-3">
                    <div className="p-1 bg-gray-100 dark:bg-gray-700 rounded">
                      <Clock
                        size={12}
                        className="text-gray-600 dark:text-gray-400"
                      />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {activity.event_type
                          .replace("_", " ")
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {activity.notification_data?.type || "Unknown"} •{" "}
                        {activity.notification_data?.title || "No title"}
                      </p>
                    </div>
                  </div>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {new Date(activity.timestamp).toLocaleString()}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default NotificationAnalyticsDashboard;
