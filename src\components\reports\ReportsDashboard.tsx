import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  BarChart3,
  FileText,
  Calendar,
  Settings,
  Download,
  Plus,
  Filter,
  Clock,
  Users,
  Bus,
  Route,
  TrendingUp,
  PieChart,
  Grid3X3,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { AttendanceReport } from "./AttendanceReport";
import { BusUtilizationReport } from "./BusUtilizationReport";
import { DriverPerformanceReport } from "./DriverPerformanceReport";
import { RouteDelayReport } from "./RouteDelayReport";
import { BusUtilizationChart } from "./BusUtilizationChart";
import { CombinedReportView } from "./CombinedReportView";
import { ReportScheduler } from "./ReportScheduler";
import { ReportTemplates } from "./ReportTemplates";
import {
  reportGeneratorService,
  ReportConfig,
} from "../../lib/reportGeneratorService";

type ReportType =
  | "attendance"
  | "bus_utilization"
  | "driver_performance"
  | "route_delays"
  | "combined";
type ViewMode = "dashboard" | "report" | "scheduler" | "templates";

interface ReportsDashboardProps {
  className?: string;
}

export const ReportsDashboard: React.FC<ReportsDashboardProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const [viewMode, setViewMode] = useState<ViewMode>("dashboard");
  const [selectedReportType, setSelectedReportType] =
    useState<ReportType>("attendance");
  const [isGenerating, setIsGenerating] = useState(false);
  const [quickStats, setQuickStats] = useState<any>(null);
  const [recentReports, setRecentReports] = useState<any[]>([]);

  useEffect(() => {
    if (tenant?.id) {
      loadQuickStats();
      loadRecentReports();
    }
  }, [tenant?.id]);

  const loadQuickStats = async () => {
    if (!tenant?.id) return;

    try {
      const endDate = new Date().toISOString().split("T")[0];
      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0];

      const config: ReportConfig = {
        type: "combined",
        format: "json",
        filters: {
          startDate,
          endDate,
          tenantId: tenant.id,
        },
      };

      const data = await reportGeneratorService.generateReportData(config);
      setQuickStats(data);
    } catch (error) {
      console.error("Error loading quick stats:", error);
    }
  };

  const loadRecentReports = async () => {
    // This would load recent report generation history from the database
    // For now, we'll use mock data
    setRecentReports([
      {
        id: "1",
        type: "attendance",
        name: "Daily Attendance Report",
        generated_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        format: "pdf",
      },
      {
        id: "2",
        type: "bus_utilization",
        name: "Weekly Bus Utilization",
        generated_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        format: "csv",
      },
      {
        id: "3",
        type: "driver_performance",
        name: "Monthly Driver Performance",
        generated_at: new Date(
          Date.now() - 3 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        format: "pdf",
      },
    ]);
  };

  const generateQuickReport = async (
    type: ReportType,
    format: "csv" | "pdf",
  ) => {
    if (!tenant?.id) return;

    setIsGenerating(true);
    try {
      const endDate = new Date().toISOString().split("T")[0];
      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0];

      const config: ReportConfig = {
        type,
        format,
        filters: {
          startDate,
          endDate,
          tenantId: tenant.id,
        },
        options: {
          includeCharts: true,
          includeStats: true,
          customTitle: `${type.replace("_", " ").toUpperCase()} Report`,
        },
      };

      if (format === "csv") {
        const csvData = await reportGeneratorService.exportToCSV(config);
        const blob = new Blob([csvData], { type: "text/csv" });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${type}_report_${startDate}_to_${endDate}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
      } else {
        const pdfBlob = await reportGeneratorService.exportToPDF(config);
        const url = window.URL.createObjectURL(pdfBlob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${type}_report_${startDate}_to_${endDate}.pdf`;
        a.click();
        window.URL.revokeObjectURL(url);
      }

      // Refresh recent reports
      loadRecentReports();
    } catch (error) {
      console.error("Error generating report:", error);
      alert("Failed to generate report. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const renderQuickStats = () => {
    if (!quickStats) return null;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {quickStats.attendance && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Total Attendance Records
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {quickStats.attendance.stats?.totalRecords || 0}
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </div>
        )}

        {quickStats.utilization && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Average Bus Utilization
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {quickStats.utilization.stats?.avgUtilization?.toFixed(1) ||
                    0}
                  %
                </p>
              </div>
              <Bus className="h-8 w-8 text-green-500" />
            </div>
          </div>
        )}

        {quickStats.performance && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Average On-Time Rate
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {quickStats.performance.stats?.avgOnTimeRate?.toFixed(1) || 0}
                  %
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </div>
        )}

        {quickStats.delays && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  On-Time Percentage
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {quickStats.delays.stats?.onTimePercentage?.toFixed(1) || 100}
                  %
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderDashboard = () => (
    <div className="space-y-8">
      {/* Quick Stats */}
      {renderQuickStats()}

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Quick Report Generation
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[
            {
              type: "attendance" as ReportType,
              icon: Users,
              label: "Attendance Report",
              color: "blue",
            },
            {
              type: "bus_utilization" as ReportType,
              icon: Bus,
              label: "Bus Utilization",
              color: "green",
            },
            {
              type: "driver_performance" as ReportType,
              icon: TrendingUp,
              label: "Driver Performance",
              color: "yellow",
            },
            {
              type: "route_delays" as ReportType,
              icon: Clock,
              label: "Route Delays",
              color: "red",
            },
          ].map(({ type, icon: Icon, label, color }) => (
            <div
              key={type}
              className="border border-gray-200 dark:border-gray-600 rounded-lg p-4"
            >
              <div className="flex items-center mb-3">
                <Icon className={`h-5 w-5 text-${color}-500 mr-2`} />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {label}
                </span>
              </div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => generateQuickReport(type, "csv")}
                  disabled={isGenerating}
                  className="flex-1"
                >
                  CSV
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => generateQuickReport(type, "pdf")}
                  disabled={isGenerating}
                  className="flex-1"
                >
                  PDF
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Reports */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Recent Reports
        </h3>
        {recentReports.length > 0 ? (
          <div className="space-y-3">
            {recentReports.map((report) => (
              <div
                key={report.id}
                className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg"
              >
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-gray-500 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {report.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(report.generated_at).toLocaleString()} •{" "}
                      {report.format.toUpperCase()}
                    </p>
                  </div>
                </div>
                <Button size="sm" variant="outline">
                  <Download size={14} className="mr-1" />
                  Download
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 dark:text-gray-400 text-center py-4">
            No recent reports found. Generate your first report above.
          </p>
        )}
      </div>
    </div>
  );

  const renderReportView = () => {
    switch (selectedReportType) {
      case "attendance":
        return <AttendanceReport />;
      case "bus_utilization":
        return (
          <div className="space-y-6">
            <BusUtilizationReport />
            <BusUtilizationChart />
          </div>
        );
      case "driver_performance":
        return <DriverPerformanceReport />;
      case "route_delays":
        return <RouteDelayReport />;
      case "combined":
        return <CombinedReportView />;
      default:
        return <AttendanceReport />;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Reports Dashboard
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Generate, schedule, and manage comprehensive reports for your school
            transportation system.
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === "templates" ? "default" : "outline"}
            onClick={() => setViewMode("templates")}
            leftIcon={<FileText size={16} />}
          >
            Templates
          </Button>
          <Button
            variant={viewMode === "scheduler" ? "default" : "outline"}
            onClick={() => setViewMode("scheduler")}
            leftIcon={<Calendar size={16} />}
          >
            Scheduler
          </Button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8">
          {[
            { key: "dashboard", label: "Dashboard", icon: BarChart3 },
            { key: "report", label: "Detailed Reports", icon: FileText },
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setViewMode(key as ViewMode)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                viewMode === key
                  ? "border-primary-500 text-primary-600 dark:text-primary-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              <Icon size={16} className="mr-2" />
              {label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      {viewMode === "dashboard" && renderDashboard()}

      {viewMode === "report" && (
        <div className="space-y-6">
          {/* Report Type Selector */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="flex flex-wrap gap-2">
              {[
                {
                  type: "attendance" as ReportType,
                  icon: Users,
                  label: "Attendance",
                },
                {
                  type: "bus_utilization" as ReportType,
                  icon: Bus,
                  label: "Bus Utilization",
                },
                {
                  type: "driver_performance" as ReportType,
                  icon: TrendingUp,
                  label: "Driver Performance",
                },
                {
                  type: "route_delays" as ReportType,
                  icon: Clock,
                  label: "Route Delays",
                },
                {
                  type: "combined" as ReportType,
                  icon: Grid3X3,
                  label: "Combined View",
                },
              ].map(({ type, icon: Icon, label }) => (
                <button
                  key={type}
                  onClick={() => setSelectedReportType(type)}
                  className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedReportType === type
                      ? "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                  }`}
                >
                  <Icon size={16} className="mr-2" />
                  {label}
                </button>
              ))}
            </div>
          </div>

          {/* Report Content */}
          {renderReportView()}
        </div>
      )}

      {viewMode === "scheduler" && <ReportScheduler />}
      {viewMode === "templates" && <ReportTemplates />}
    </div>
  );
};

export default ReportsDashboard;
