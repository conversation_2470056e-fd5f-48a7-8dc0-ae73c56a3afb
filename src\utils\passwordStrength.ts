/**
 * خدمة فحص قوة كلمة المرور
 * Password Strength Validation Service
 * 
 * المرحلة 1: تأمين الدخول والسياسات الأمنية الأساسية
 */

export interface PasswordStrengthResult {
  score: number; // 0-100
  level: 'very-weak' | 'weak' | 'fair' | 'good' | 'strong';
  feedback: string[];
  isValid: boolean;
  requirements: {
    minLength: boolean;
    hasUppercase: boolean;
    hasLowercase: boolean;
    hasNumbers: boolean;
    hasSpecialChars: boolean;
    noCommonPatterns: boolean;
  };
}

export interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  forbiddenPatterns: string[];
  forbiddenPasswords: string[];
}

export class PasswordStrengthValidator {
  private static readonly DEFAULT_POLICY: PasswordPolicy = {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    forbiddenPatterns: [
      'password', 'admin', 'user', 'login', 'qwerty', 'abc123', '123456',
      'password123', 'admin123', 'user123', 'test', 'demo'
    ],
    forbiddenPasswords: [
      '12345678', 'password', 'admin123', 'qwerty123', 'abc12345',
      'password1', 'admin1234', 'user1234', 'test1234', 'demo1234'
    ]
  };

  /**
   * فحص قوة كلمة المرور الرئيسي
   */
  static validatePassword(
    password: string, 
    policy: Partial<PasswordPolicy> = {}
  ): PasswordStrengthResult {
    const activePolicy = { ...this.DEFAULT_POLICY, ...policy };
    const feedback: string[] = [];
    let score = 0;

    // فحص المتطلبات الأساسية
    const requirements = {
      minLength: password.length >= activePolicy.minLength,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumbers: /[0-9]/.test(password),
      hasSpecialChars: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
      noCommonPatterns: !this.hasCommonPatterns(password, activePolicy.forbiddenPatterns)
    };

    // حساب النقاط والملاحظات
    if (requirements.minLength) {
      score += 20;
    } else {
      feedback.push(`كلمة المرور يجب أن تكون ${activePolicy.minLength} أحرف على الأقل`);
    }

    if (requirements.hasUppercase) {
      score += 15;
    } else if (activePolicy.requireUppercase) {
      feedback.push('يجب أن تحتوي على حرف كبير واحد على الأقل (A-Z)');
    }

    if (requirements.hasLowercase) {
      score += 15;
    } else if (activePolicy.requireLowercase) {
      feedback.push('يجب أن تحتوي على حرف صغير واحد على الأقل (a-z)');
    }

    if (requirements.hasNumbers) {
      score += 15;
    } else if (activePolicy.requireNumbers) {
      feedback.push('يجب أن تحتوي على رقم واحد على الأقل (0-9)');
    }

    if (requirements.hasSpecialChars) {
      score += 15;
    } else if (activePolicy.requireSpecialChars) {
      feedback.push('يجب أن تحتوي على رمز خاص واحد على الأقل (!@#$%^&*)');
    }

    if (requirements.noCommonPatterns) {
      score += 10;
    } else {
      feedback.push('كلمة المرور تحتوي على كلمات شائعة أو أنماط ضعيفة');
    }

    // فحص إضافي للقوة
    score += this.calculateComplexityBonus(password);

    // فحص كلمات المرور المحظورة
    if (this.isForbiddenPassword(password, activePolicy.forbiddenPasswords)) {
      score = Math.max(0, score - 30);
      feedback.push('كلمة المرور هذه محظورة أو شائعة جداً');
    }

    // تحديد مستوى القوة
    const level = this.getStrengthLevel(score);
    
    // إضافة نصائح تحسين
    if (score < 80) {
      feedback.push(...this.getImprovementSuggestions(password, requirements));
    }

    const isValid = score >= 60 && Object.values(requirements).every(req => req);

    return {
      score: Math.min(100, Math.max(0, score)),
      level,
      feedback,
      isValid,
      requirements
    };
  }

  /**
   * فحص الأنماط الشائعة
   */
  private static hasCommonPatterns(password: string, patterns: string[]): boolean {
    const lowerPassword = password.toLowerCase();
    return patterns.some(pattern => lowerPassword.includes(pattern.toLowerCase()));
  }

  /**
   * فحص كلمات المرور المحظورة
   */
  private static isForbiddenPassword(password: string, forbidden: string[]): boolean {
    const lowerPassword = password.toLowerCase();
    return forbidden.some(forbiddenPass => 
      lowerPassword === forbiddenPass.toLowerCase() ||
      lowerPassword.includes(forbiddenPass.toLowerCase())
    );
  }

  /**
   * حساب نقاط إضافية للتعقيد
   */
  private static calculateComplexityBonus(password: string): number {
    let bonus = 0;

    // طول إضافي
    if (password.length >= 12) bonus += 5;
    if (password.length >= 16) bonus += 5;

    // تنوع الأحرف
    const uniqueChars = new Set(password).size;
    if (uniqueChars >= password.length * 0.7) bonus += 5;

    // عدم التكرار
    if (!/(.)\1{2,}/.test(password)) bonus += 5;

    // عدم التسلسل
    if (!this.hasSequentialChars(password)) bonus += 5;

    return bonus;
  }

  /**
   * فحص التسلسل في الأحرف
   */
  private static hasSequentialChars(password: string): boolean {
    const sequences = ['abc', '123', 'qwe', 'asd', 'zxc'];
    const lowerPassword = password.toLowerCase();
    
    return sequences.some(seq => {
      for (let i = 0; i <= lowerPassword.length - 3; i++) {
        const substr = lowerPassword.substr(i, 3);
        if (substr === seq || substr === seq.split('').reverse().join('')) {
          return true;
        }
      }
      return false;
    });
  }

  /**
   * تحديد مستوى القوة
   */
  private static getStrengthLevel(score: number): PasswordStrengthResult['level'] {
    if (score >= 90) return 'strong';
    if (score >= 70) return 'good';
    if (score >= 50) return 'fair';
    if (score >= 30) return 'weak';
    return 'very-weak';
  }

  /**
   * اقتراحات التحسين
   */
  private static getImprovementSuggestions(
    password: string, 
    requirements: PasswordStrengthResult['requirements']
  ): string[] {
    const suggestions: string[] = [];

    if (password.length < 12) {
      suggestions.push('استخدم كلمة مرور أطول (12 حرف أو أكثر) لأمان أفضل');
    }

    if (!requirements.hasSpecialChars) {
      suggestions.push('أضف رموز خاصة مثل !@#$%^&* لزيادة القوة');
    }

    if (password.length < 10) {
      suggestions.push('فكر في استخدام عبارة مرور بدلاً من كلمة واحدة');
    }

    if (!/[0-9].*[0-9]/.test(password)) {
      suggestions.push('استخدم أكثر من رقم واحد');
    }

    return suggestions;
  }

  /**
   * الحصول على لون مؤشر القوة
   */
  static getStrengthColor(level: PasswordStrengthResult['level']): string {
    const colors = {
      'very-weak': '#ef4444', // أحمر
      'weak': '#f97316',      // برتقالي
      'fair': '#eab308',      // أصفر
      'good': '#22c55e',      // أخضر فاتح
      'strong': '#16a34a'     // أخضر غامق
    };
    return colors[level];
  }

  /**
   * الحصول على نص مستوى القوة
   */
  static getStrengthText(level: PasswordStrengthResult['level']): string {
    const texts = {
      'very-weak': 'ضعيف جداً',
      'weak': 'ضعيف',
      'fair': 'متوسط',
      'good': 'جيد',
      'strong': 'قوي'
    };
    return texts[level];
  }
}
