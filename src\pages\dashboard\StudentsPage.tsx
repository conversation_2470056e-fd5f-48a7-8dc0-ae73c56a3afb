import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Plus, Loader2 } from "lucide-react";
import { Navbar } from "../../components/layout/Navbar";
import { Sidebar } from "../../components/layout/Sidebar";
import { Button } from "../../components/ui/Button";
import { StudentList } from "../../components/students/StudentList";
import { StudentModal } from "../../components/students/StudentModal";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/api";

export const StudentsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { loading, error, refreshData, tenant } = useDatabase();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<
    Tables<"students"> | undefined
  >();

  const handleOpenModal = (student?: Tables<"students">) => {
    setSelectedStudent(student);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setSelectedStudent(undefined);
    setIsModalOpen(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin text-primary-500" />
          <span className="text-gray-600 dark:text-gray-300">
            {t("common.loading")}
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-error-600 dark:text-error-400">
          {error.message}
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />

        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {t("students.manageStudents")}
                </h1>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  إدارة شاملة للطلاب تتضمن إنشاء حسابات المستخدمين والمعلومات الأكاديمية
                </p>
                <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
                  <p className="text-sm text-green-700 dark:text-green-300">
                    ✅ <strong>المكان الصحيح:</strong> هذه هي الصفحة الوحيدة لإدارة الطلاب. يتم إنشاء حساب المستخدم وسجل الطالب معاً.
                  </p>
                </div>
              </div>

              <div className="mt-4 md:mt-0">
                <Button
                  className="flex items-center gap-2"
                  onClick={() => handleOpenModal()}
                >
                  <Plus size={16} />
                  {t("students.addStudent")}
                </Button>
              </div>
            </div>

            <StudentList onRefresh={refreshData} />

            {isModalOpen && (
              <StudentModal
                isOpen={isModalOpen}
                onClose={handleCloseModal}
                onSubmit={async (data) => {
                  try {
                    if (selectedStudent) {
                      // Update existing student
                      const updateData = { ...data };
                      delete updateData.password;
                      delete updateData.email;

                      const { error } = await supabase
                        .from("students")
                        .update(updateData)
                        .eq("id", selectedStudent.id);

                      if (error) throw error;
                    } else {
                      // Create new student with auth account
                      if (!data.password || data.password.trim() === "") {
                        throw new Error(
                          "Password is required for new students",
                        );
                      }
                      if (!data.email || data.email.trim() === "") {
                        throw new Error("Email is required for new students");
                      }

                      // Ensure tenant_id is available
                      if (!tenant?.id) {
                        throw new Error("No tenant information available. Please refresh and try again.");
                      }

                      // Prepare student user data
                      const studentUserData = {
                        email: data.email.trim(),
                        password: data.password.trim(),
                        name: data.name?.trim(),
                        role: "student",
                        tenant_id: tenant.id,
                        phone: null,
                        is_active: true,
                      };

                      console.log("Creating student user with data:", {
                        ...studentUserData,
                        password: !!studentUserData.password,
                      });

                      // Create student using ultra simple RPC function
                      console.log("إنشاء طالب جديد...");
                      console.log("Tenant ID:", tenant.id);
                      console.log("Student data:", {
                        name: data.name?.trim(),
                        grade: data.grade,
                        tenant_id: tenant.id
                      });

                      // استخدام دالة CRUD المحسنة والمصلحة لإنشاء الطالب
                      console.log("🔍 StudentsPage: Creating student with data:", {
                        email: data.email.trim(),
                        name: data.name?.trim(),
                        grade: data.grade,
                        tenant_id: tenant?.id,
                        user_role: user?.role
                      });

                      const { data: studentResult, error: studentError } = await supabase
                        .rpc('create_student_final', {
                          student_email: data.email.trim(),
                          student_password: data.password.trim(),
                          student_name: data.name?.trim(),
                          student_grade: data.grade,
                          student_tenant_id: tenant?.id || null,
                          student_parent_id: data.parent_id || null,
                          student_route_stop_id: data.route_stop_id || null
                        });

                      console.log("✅ StudentsPage: نتيجة إنشاء الطالب:", {
                        studentResult,
                        studentError,
                      });

                      if (studentError) {
                        console.error("❌ StudentsPage: خطأ في إنشاء الطالب:", studentError);
                        throw new Error(
                          studentError.message || "فشل في إنشاء الطالب",
                        );
                      }

                      if (!studentResult?.success) {
                        console.error("❌ StudentsPage: فشل في إنشاء الطالب:", studentResult);
                        throw new Error(
                          studentResult?.message || studentResult?.error || "فشل في إنشاء الطالب",
                        );
                      }

                      console.log("🎉 StudentsPage: تم إنشاء الطالب بنجاح:", studentResult);
                    }

                    await refreshData();
                    handleCloseModal();
                  } catch (error) {
                    console.error("Error saving student:", error);
                    // Add Arabic error message
                    const errorMessage = error instanceof Error ? error.message : "خطأ غير معروف";
                    throw new Error(`فشل في إنشاء الطالب: ${errorMessage}`);
                  }
                }}
                student={selectedStudent}
              />
            )}
          </div>
        </main>
      </div>
    </div>
  );
};
