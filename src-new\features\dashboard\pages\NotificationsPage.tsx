import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Bell, History, Settings, MessageSquare } from "lucide-react";
import { ResponsiveLayout } from "../../components/layout/ResponsiveLayout";
import { NotificationHistory } from "../../components/notifications/NotificationHistory";
import { NotificationPreferences } from "../../components/notifications/NotificationPreferences";
import { NotificationTemplates } from "../../components/notifications/NotificationTemplates";
import { useAuth } from "../../contexts/AuthContext";
import { usePermissions } from "../../hooks/usePermissions";
import { Permission } from "../../lib/rbac";

export const NotificationsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { hasPermission } = usePermissions();
  const [activeTab, setActiveTab] = useState<
    "history" | "preferences" | "templates"
  >("history");

  const tabs = [
    { id: "history", label: t("notifications.history"), icon: History },
    {
      id: "preferences",
      label: t("notifications.preferences"),
      icon: Settings,
    },
    ...(hasPermission(Permission.NOTIFICATIONS_MANAGE_TEMPLATES)
      ? [
          {
            id: "templates",
            label: t("notifications.templates"),
            icon: MessageSquare,
          },
        ]
      : []),
  ];

  return (
    <ResponsiveLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center mb-6">
          <Bell
            size={24}
            className="mr-3 text-primary-600 dark:text-primary-400"
          />
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t("notifications.title")}
          </h1>
        </div>

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? "border-primary-500 text-primary-600 dark:text-primary-400"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                    }`}
                  >
                    <Icon size={16} className="mr-2" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === "history" && <NotificationHistory />}
        {activeTab === "preferences" && <NotificationPreferences />}
        {activeTab === "templates" &&
          hasPermission(Permission.NOTIFICATIONS_MANAGE_TEMPLATES) && (
            <NotificationTemplates />
          )}
      </div>
    </ResponsiveLayout>
  );
};

export default NotificationsPage;
