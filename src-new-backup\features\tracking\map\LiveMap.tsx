import React, { useEffect, useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { InteractiveMap } from "../maps/InteractiveMap";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";
import { subscribeToBusLocation } from "../../lib/api";
import type { Tables } from "../../lib/api";
import type { RealtimeChannel } from "@supabase/supabase-js";

interface LiveMapProps {
  selectedBusId?: string;
  onBusSelect?: (bus: Tables<"buses">) => void;
  showRoutes?: boolean;
  showGeofences?: boolean;
  showTraffic?: boolean;
  showSpeedInfo?: boolean;
  showETA?: boolean;
  className?: string;
}

interface BusMetrics {
  speed: number;
  heading: number;
  lastUpdate: Date;
  isMoving: boolean;
  distanceTraveled: number;
  eta?: { [stopId: string]: number };
}

export const LiveMap: React.FC<LiveMapProps> = ({
  selectedBusId,
  onBusSelect,
  showRoutes = false,
  showGeofences = false,
  showTraffic = false,
  showSpeedInfo = true,
  showETA = true,
  className = "",
}) => {
  const { t } = useTranslation();
  const { buses, routes } = useDatabase();
  const [realtimeBuses, setRealtimeBuses] = useState<Tables<"buses">[]>([]);
  const [subscriptions, setSubscriptions] = useState<RealtimeChannel[]>([]);
  const [busMetrics, setBusMetrics] = useState<Map<string, BusMetrics>>(new Map());
  const [previousLocations, setPreviousLocations] = useState<
    Map<string, { lat: number; lng: number; timestamp: Date }>
  >(new Map());

  // Calculate distance between two points (Haversine formula)
  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lng2 - lng1) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  };

  // Calculate bearing between two points
  const calculateBearing = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    const dLng = ((lng2 - lng1) * Math.PI) / 180;
    const lat1Rad = (lat1 * Math.PI) / 180;
    const lat2Rad = (lat2 * Math.PI) / 180;

    const y = Math.sin(dLng) * Math.cos(lat2Rad);
    const x =
      Math.cos(lat1Rad) * Math.sin(lat2Rad) -
      Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLng);

    let bearing = (Math.atan2(y, x) * 180) / Math.PI;
    return (bearing + 360) % 360;
  };

  // Update bus metrics when location changes
  const updateBusMetrics = useCallback(
    (bus: Tables<"buses">) => {
      if (!bus.last_location) return;

      const location = bus.last_location as any;
      const currentLat = location.coordinates[1];
      const currentLng = location.coordinates[0];
      const currentTime = new Date();

      const previousData = previousLocations.get(bus.id);

      let speed = 0;
      let heading = 0;
      let distanceTraveled = 0;
      let isMoving = false;

      if (previousData) {
        const timeDiff = (currentTime.getTime() - previousData.timestamp.getTime()) / 1000;
        const distance = calculateDistance(
          previousData.lat,
          previousData.lng,
          currentLat,
          currentLng,
        );

        if (timeDiff > 0) {
          speed = (distance / timeDiff) * 3.6; // Convert m/s to km/h
          isMoving = speed > 1; // Consider moving if speed > 1 km/h

          heading = calculateBearing(
            previousData.lat,
            previousData.lng,
            currentLat,
            currentLng,
          );
        }

        const existingMetrics = busMetrics.get(bus.id);
        distanceTraveled = (existingMetrics?.distanceTraveled || 0) + distance;
      }

      // Calculate ETA for each stop if route exists
      const route = routes.find((r) => r.bus_id === bus.id);
      let eta: { [stopId: string]: number } = {};

      if (route?.stops && speed > 0) {
        route.stops.forEach((stop) => {
          const stopLocation = stop.location as any;
          const distanceToStop = calculateDistance(
            currentLat,
            currentLng,
            stopLocation.coordinates[1],
            stopLocation.coordinates[0],
          );
          eta[stop.id] = Math.round((distanceToStop / 1000 / speed) * 60);
        });
      }

      setBusMetrics(
        (prev) =>
          new Map(
            prev.set(bus.id, {
              speed,
              heading,
              lastUpdate: currentTime,
              isMoving,
              distanceTraveled,
              eta,
            }),
          ),
      );

      setPreviousLocations(
        (prev) =>
          new Map(
            prev.set(bus.id, {
              lat: currentLat,
              lng: currentLng,
              timestamp: currentTime,
            }),
          ),
      );
    },
    [previousLocations, busMetrics, routes],
  );

  // Simulate real-time location updates for demo
  const simulateLocationUpdate = useCallback(
    async (bus: Tables<"buses">) => {
      if (!bus.last_location) return;

      const location = bus.last_location as any;
      const currentLat = location.coordinates[1];
      const currentLng = location.coordinates[0];

      // Simulate small movement (±0.0001 degrees ≈ ±11 meters)
      const newLat = currentLat + (Math.random() - 0.5) * 0.0002;
      const newLng = currentLng + (Math.random() - 0.5) * 0.0002;

      try {
        const updatedBus = {
          ...bus,
          last_location: {
            type: "Point",
            coordinates: [newLng, newLat],
          },
          last_updated: new Date().toISOString(),
        };

        setRealtimeBuses((prev) =>
          prev.map((b) => (b.id === bus.id ? updatedBus : b)),
        );

        updateBusMetrics(updatedBus);
      } catch (error) {
        console.error("Error updating bus location:", error);
      }
    },
    [],
  );

  // Initialize real-time subscriptions
  useEffect(() => {
    setRealtimeBuses(buses);

    // Clean up existing subscriptions
    subscriptions.forEach((sub) => sub.unsubscribe());

    // Set up real-time subscriptions for each bus
    const newSubscriptions = buses.map((bus) => {
      return subscribeToBusLocation(bus.id, (payload) => {
        if (payload.eventType === "UPDATE") {
          const updatedBus = payload.new as Tables<"buses">;
          setRealtimeBuses((prev) =>
            prev.map((b) => (b.id === updatedBus.id ? updatedBus : b)),
          );
          updateBusMetrics(updatedBus);
        }
      });
    });

    setSubscriptions(newSubscriptions);

    // Set up interval for real-time updates simulation
    const updateInterval = setInterval(() => {
      buses.forEach((bus) => {
        if (bus.last_location) {
          simulateLocationUpdate(bus);
        }
      });
    }, 5000); // Update every 5 seconds

    return () => {
      newSubscriptions.forEach((sub) => sub.unsubscribe());
      clearInterval(updateInterval);
    };
  }, [buses, simulateLocationUpdate, updateBusMetrics]);

  // Convert bus data to map format
  const getMapBusData = () => {
    return realtimeBuses
      .filter(bus => bus.last_location)
      .map(bus => {
        const location = bus.last_location as any;
        const metrics = busMetrics.get(bus.id);
        const route = routes.find(r => r.bus_id === bus.id);
        
        return {
          id: bus.id,
          plateNumber: bus.plate_number,
          latitude: location.coordinates[1],
          longitude: location.coordinates[0],
          speed: metrics?.speed || 0,
          heading: metrics?.heading || 0,
          status: metrics?.isMoving ? 'moving' : 'stopped',
          driver: bus.driver_name || 'Unknown Driver',
          route: route?.name || 'No Route',
          studentsCount: bus.capacity || 0,
          lastUpdate: bus.last_updated || new Date().toISOString(),
          nextStop: route?.stops?.[0]?.name,
          eta: metrics?.eta ? Object.values(metrics.eta)[0]?.toString() + ' min' : undefined,
        };
      });
  };

  // Convert route data to map format
  const getMapRouteData = () => {
    return routes
      .filter(route => route.stops && route.stops.length > 1)
      .map(route => ({
        id: route.id,
        name: route.name,
        coordinates: route.stops!.map(stop => {
          const location = stop.location as any;
          return [location.coordinates[0], location.coordinates[1]] as [number, number];
        }),
        stops: route.stops!.map((stop, index) => ({
          id: stop.id,
          name: stop.name,
          latitude: (stop.location as any).coordinates[1],
          longitude: (stop.location as any).coordinates[0],
          order: index,
          studentsCount: 0,
        })),
        color: '#3B82F6',
      }));
  };

  return (
    <InteractiveMap
      buses={getMapBusData()}
      routes={showRoutes ? getMapRouteData() : []}
      selectedBus={selectedBusId}
      onBusSelect={(busId) => {
        const bus = realtimeBuses.find(b => b.id === busId);
        if (bus && onBusSelect) {
          onBusSelect(bus);
        }
      }}
      showRoutes={showRoutes}
      showStops={showRoutes}
      showTrails={false}
      autoRefresh={true}
      className={className}
    />
  );
};
