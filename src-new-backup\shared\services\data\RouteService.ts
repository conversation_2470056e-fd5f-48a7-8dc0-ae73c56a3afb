/**
 * Route Service
 * Handles all route-related API operations
 * Phase 2: Application Structure Reorganization
 */

import { BaseService } from '../base/BaseService';
import {
  Route,
  CreateRouteRequest,
  UpdateRouteRequest,
  RouteStop,
  APIResponse,
  PaginatedResponse,
  PaginationParams,
} from '../../api/types';

export interface RouteListParams extends PaginationParams {
  bus_id?: string;
  tenant_id?: string;
  is_active?: boolean;
  search?: string;
  search_fields?: string[];
}

/**
 * Route Service Class
 * Implements comprehensive route management operations
 */
export class RouteService extends BaseService {
  private readonly endpoint = '/routes';

  /**
   * Get paginated list of routes
   */
  async getRoutes(params: RouteListParams = {}): Promise<APIResponse<PaginatedResponse<Route>>> {
    return this.getPaginated<Route>(this.endpoint, params);
  }

  /**
   * Get route by ID
   */
  async getRouteById(id: string): Promise<APIResponse<Route>> {
    return this.get<Route>(`${this.endpoint}/${id}`);
  }

  /**
   * Get routes assigned to current user (driver)
   */
  async getMyRoutes(): Promise<APIResponse<Route[]>> {
    const response = await this.get<PaginatedResponse<Route>>(`${this.endpoint}/my-routes`);
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<Route[]>;
  }

  /**
   * Create new route
   */
  async createRoute(routeData: CreateRouteRequest): Promise<APIResponse<Route>> {
    const validation = this.validateCreateRouteData(routeData);
    if (!validation.isValid) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid route data',
          details: validation.errors,
        },
      };
    }

    return this.post<Route>(this.endpoint, routeData);
  }

  /**
   * Update route
   */
  async updateRoute(id: string, routeData: UpdateRouteRequest): Promise<APIResponse<Route>> {
    const validation = this.validateUpdateRouteData(routeData);
    if (!validation.isValid) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid update data',
          details: validation.errors,
        },
      };
    }

    return this.put<Route>(`${this.endpoint}/${id}`, routeData);
  }

  /**
   * Delete route
   */
  async deleteRoute(id: string): Promise<APIResponse<{ message: string }>> {
    return this.delete<{ message: string }>(`${this.endpoint}/${id}`);
  }

  /**
   * Assign bus to route
   */
  async assignBus(routeId: string, busId: string): Promise<APIResponse<Route>> {
    return this.patch<Route>(`${this.endpoint}/${routeId}/bus`, { bus_id: busId });
  }

  /**
   * Add stop to route
   */
  async addStop(routeId: string, stop: Omit<RouteStop, 'id' | 'students'>): Promise<APIResponse<Route>> {
    return this.post<Route>(`${this.endpoint}/${routeId}/stops`, stop);
  }

  /**
   * Update route stop
   */
  async updateStop(
    routeId: string,
    stopId: string,
    stopData: Partial<Omit<RouteStop, 'id' | 'students'>>
  ): Promise<APIResponse<Route>> {
    return this.put<Route>(`${this.endpoint}/${routeId}/stops/${stopId}`, stopData);
  }

  /**
   * Remove stop from route
   */
  async removeStop(routeId: string, stopId: string): Promise<APIResponse<Route>> {
    return this.delete<Route>(`${this.endpoint}/${routeId}/stops/${stopId}`);
  }

  /**
   * Reorder route stops
   */
  async reorderStops(routeId: string, stopIds: string[]): Promise<APIResponse<Route>> {
    return this.patch<Route>(`${this.endpoint}/${routeId}/stops/reorder`, { stop_ids: stopIds });
  }

  /**
   * Get route optimization suggestions
   */
  async getOptimizationSuggestions(routeId: string): Promise<APIResponse<RouteOptimization>> {
    return this.get<RouteOptimization>(`${this.endpoint}/${routeId}/optimize`);
  }

  /**
   * Apply route optimization
   */
  async applyOptimization(routeId: string, optimization: RouteOptimization): Promise<APIResponse<Route>> {
    return this.post<Route>(`${this.endpoint}/${routeId}/apply-optimization`, optimization);
  }

  /**
   * Get route statistics
   */
  async getRouteStats(id?: string, tenantId?: string): Promise<APIResponse<RouteStats>> {
    const params: any = {};
    if (tenantId) params.tenant_id = tenantId;
    
    const queryString = this.buildQueryString(params);
    const endpoint = id 
      ? `${this.endpoint}/${id}/stats${queryString}`
      : `${this.endpoint}/stats${queryString}`;
    
    return this.get<RouteStats>(endpoint);
  }

  /**
   * Start route trip
   */
  async startTrip(routeId: string, tripType: 'morning' | 'afternoon'): Promise<APIResponse<RouteTrip>> {
    return this.post<RouteTrip>(`${this.endpoint}/${routeId}/start-trip`, { trip_type: tripType });
  }

  /**
   * End route trip
   */
  async endTrip(routeId: string, tripId: string): Promise<APIResponse<RouteTrip>> {
    return this.patch<RouteTrip>(`${this.endpoint}/${routeId}/trips/${tripId}/end`, {});
  }

  /**
   * Get route trips history
   */
  async getTripsHistory(
    routeId: string,
    startDate: string,
    endDate: string
  ): Promise<APIResponse<RouteTrip[]>> {
    const params = this.buildQueryString({ start_date: startDate, end_date: endDate });
    return this.get<RouteTrip[]>(`${this.endpoint}/${routeId}/trips${params}`);
  }

  /**
   * Get available routes (no bus assigned)
   */
  async getAvailableRoutes(tenantId?: string): Promise<APIResponse<Route[]>> {
    const params = tenantId ? { tenant_id: tenantId } : {};
    const queryString = this.buildQueryString(params);
    return this.get<Route[]>(`${this.endpoint}/available${queryString}`);
  }

  /**
   * Search routes
   */
  async searchRoutes(
    query: string,
    fields: string[] = ['name', 'description'],
    tenantId?: string
  ): Promise<APIResponse<Route[]>> {
    const params: RouteListParams = {
      search: query,
      search_fields: fields,
    };
    if (tenantId) params.tenant_id = tenantId;

    const response = await this.getRoutes(params);
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<Route[]>;
  }

  /**
   * Get routes by bus
   */
  async getRoutesByBus(busId: string): Promise<APIResponse<Route[]>> {
    const response = await this.getRoutes({ bus_id: busId });
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<Route[]>;
  }

  /**
   * Bulk operations
   */
  async bulkUpdateRouteStatus(
    routeIds: string[],
    isActive: boolean
  ): Promise<APIResponse<BulkOperationResult<Route>>> {
    return this.patch<BulkOperationResult<Route>>(`${this.endpoint}/bulk/status`, {
      route_ids: routeIds,
      is_active: isActive,
    });
  }

  /**
   * Validation methods
   */
  private validateCreateRouteData(data: CreateRouteRequest): ValidationResult {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length < 2) {
      errors.push('Route name must be at least 2 characters long');
    }

    if (!data.stops || data.stops.length < 2) {
      errors.push('Route must have at least 2 stops');
    }

    if (!data.schedule || !data.schedule.morning_start || !data.schedule.morning_end) {
      errors.push('Morning schedule is required');
    }

    if (!data.tenant_id || data.tenant_id.trim().length === 0) {
      errors.push('Tenant ID is required');
    }

    // Validate stops
    if (data.stops) {
      data.stops.forEach((stop, index) => {
        if (!stop.name || stop.name.trim().length < 2) {
          errors.push(`Stop ${index + 1}: Name is required`);
        }
        if (!stop.location || !stop.location.latitude || !stop.location.longitude) {
          errors.push(`Stop ${index + 1}: Valid location is required`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private validateUpdateRouteData(data: UpdateRouteRequest): ValidationResult {
    const errors: string[] = [];

    if (data.name !== undefined && (!data.name || data.name.trim().length < 2)) {
      errors.push('Route name must be at least 2 characters long');
    }

    if (data.stops !== undefined && data.stops.length < 2) {
      errors.push('Route must have at least 2 stops');
    }

    // Validate stops if provided
    if (data.stops) {
      data.stops.forEach((stop, index) => {
        if (!stop.name || stop.name.trim().length < 2) {
          errors.push(`Stop ${index + 1}: Name is required`);
        }
        if (!stop.location || !stop.location.latitude || !stop.location.longitude) {
          errors.push(`Stop ${index + 1}: Valid location is required`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// ============================================================================
// Supporting Types
// ============================================================================

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

interface RouteStats {
  total_routes: number;
  active_routes: number;
  total_stops: number;
  total_distance: number;
  average_duration: number;
  total_students: number;
  completion_rate: number;
  last_updated: string;
}

interface RouteOptimization {
  optimized_stops: RouteStop[];
  estimated_time_saved: number;
  estimated_distance_saved: number;
  fuel_savings: number;
  suggestions: string[];
}

interface RouteTrip {
  id: string;
  route_id: string;
  trip_type: 'morning' | 'afternoon';
  started_at: string;
  ended_at?: string;
  status: 'in_progress' | 'completed' | 'cancelled';
  stops_completed: number;
  total_stops: number;
  students_picked_up: number;
  students_dropped_off: number;
}

interface BulkOperationResult<T> {
  successful: T[];
  failed: Array<{
    data: any;
    error: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

// ============================================================================
// Service Instance
// ============================================================================

export const routeService = new RouteService();
export { RouteService };
