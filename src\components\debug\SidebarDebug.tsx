/**
 * Sidebar Debug Component
 * Debug component to check sidebar items and permissions
 * Phase 3: UI/UX Enhancement - Debug
 */

import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole } from '../../types';

export const SidebarDebug: React.FC = () => {
  const { user, tenant } = useAuth();

  // Check theme access permissions
  const canAccessThemes = user?.role === UserRole.ADMIN || user?.role === UserRole.SCHOOL_MANAGER;

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-sm z-50">
      <h3 className="font-bold text-sm mb-2">🔍 Sidebar Debug</h3>

      <div className="space-y-2 text-xs">
        <div>
          <strong>User:</strong> {user?.name || 'Not logged in'}
        </div>

        <div>
          <strong>Role:</strong> {user?.role || 'None'}
        </div>

        <div>
          <strong>Tenant:</strong> {tenant?.name || 'None'}
        </div>

        <div className="border-t pt-2">
          <strong>Theme Access:</strong>
          <div className={canAccessThemes ? 'text-green-600' : 'text-red-600'}>
            {canAccessThemes ? '✅ Can access themes' : '❌ Cannot access themes'}
          </div>
        </div>

        <div className="border-t pt-2">
          <strong>Expected Links:</strong>
          <ul className="ml-2">
            {user?.role === UserRole.ADMIN && (
              <li className="text-green-600">✅ إدارة الثيمات (/admin/themes)</li>
            )}
            {user?.role === UserRole.SCHOOL_MANAGER && (
              <li className="text-green-600">✅ ثيم المدرسة (/school/theme)</li>
            )}
            <li className="text-green-600">✅ التقارير</li>
          </ul>
        </div>

        <div className="border-t pt-2">
          <strong>Debug Info:</strong>
          <div className="text-xs">
            <div>UserRole.ADMIN: "{UserRole.ADMIN}"</div>
            <div>UserRole.SCHOOL_MANAGER: "{UserRole.SCHOOL_MANAGER}"</div>
            <div>Current user.role: "{user?.role}"</div>
            <div>Match Admin: {user?.role === UserRole.ADMIN ? 'YES' : 'NO'}</div>
            <div>Match School Manager: {user?.role === UserRole.SCHOOL_MANAGER ? 'YES' : 'NO'}</div>
          </div>
        </div>

        <div className="border-t pt-2">
          <strong>Feature Flags:</strong>
          <div>
            Reports: {tenant?.settings?.features?.reports !== false ? '✅' : '❌'}
          </div>
        </div>
      </div>
    </div>
  );
};
