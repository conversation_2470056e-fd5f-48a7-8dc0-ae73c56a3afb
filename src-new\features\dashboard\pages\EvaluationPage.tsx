import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Star,
  AlertTriangle,
  BarChart3,
  Plus,
  MessageSquare,
} from "lucide-react";
import { ResponsiveLayout } from "../../components/layout/ResponsiveLayout";
import { Button } from "../../components/ui/Button";
import { EvaluationForm } from "../../components/evaluation/EvaluationForm";
import { ComplaintForm } from "../../components/evaluation/ComplaintForm";
import { EvaluationsList } from "../../components/evaluation/EvaluationsList";
import { ComplaintsList } from "../../components/evaluation/ComplaintsList";
import { UserSatisfactionReport } from "../../components/evaluation/UserSatisfactionReport";
import { useAuth } from "../../contexts/AuthContext";

type TabType = "evaluations" | "complaints" | "reports";

const EvaluationPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>("evaluations");
  const [showEvaluationForm, setShowEvaluationForm] = useState(false);
  const [showComplaintForm, setShowComplaintForm] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleFormSuccess = () => {
    setRefreshKey((prev) => prev + 1);
  };

  const tabs = [
    {
      id: "evaluations" as TabType,
      label: t("evaluation.evaluations"),
      icon: Star,
      description: t("evaluation.evaluationsDescription"),
    },
    {
      id: "complaints" as TabType,
      label: t("complaints.complaints"),
      icon: AlertTriangle,
      description: t("complaints.complaintsDescription"),
    },
    {
      id: "reports" as TabType,
      label: t("evaluation.reports"),
      icon: BarChart3,
      description: t("evaluation.reportsDescription"),
    },
  ];

  const canManageComplaints =
    user?.role === "admin" ||
    user?.role === "school_manager" ||
    user?.role === "supervisor";

  return (
    <ResponsiveLayout>
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t("evaluation.title")}
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {t("evaluation.description")}
            </p>
          </div>

          <div className="flex gap-2">
            {activeTab === "evaluations" && (
              <Button
                onClick={() => setShowEvaluationForm(true)}
                leftIcon={<Plus size={16} />}
              >
                {t("evaluation.addEvaluation")}
              </Button>
            )}
            {activeTab === "complaints" && (
              <Button
                onClick={() => setShowComplaintForm(true)}
                leftIcon={<MessageSquare size={16} />}
                variant="outline"
              >
                {t("complaints.submitComplaint")}
              </Button>
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? "border-primary-500 text-primary-600 dark:text-primary-400"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                    }`}
                  >
                    <Icon size={16} className="mr-2" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>
          <div className="mt-2">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {tabs.find((tab) => tab.id === activeTab)?.description}
            </p>
          </div>
        </div>

        {/* Tab Content */}
        <div key={refreshKey}>
          {activeTab === "evaluations" && (
            <EvaluationsList showFilters={true} />
          )}

          {activeTab === "complaints" && (
            <ComplaintsList
              showFilters={true}
              showActions={canManageComplaints}
            />
          )}

          {activeTab === "reports" && <UserSatisfactionReport />}
        </div>

        {/* Modals */}
        {showEvaluationForm && (
          <EvaluationForm
            targetType="service"
            targetId="general"
            targetName={t("evaluation.generalService")}
            onClose={() => setShowEvaluationForm(false)}
            onSuccess={handleFormSuccess}
          />
        )}

        {showComplaintForm && (
          <ComplaintForm
            targetType="general"
            targetId="general"
            onClose={() => setShowComplaintForm(false)}
            onSuccess={handleFormSuccess}
          />
        )}
      </div>
    </ResponsiveLayout>
  );
};

export { EvaluationPage };
export default EvaluationPage;
