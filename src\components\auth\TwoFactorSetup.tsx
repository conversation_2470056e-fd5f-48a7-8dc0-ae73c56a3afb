/**
 * مكون إعداد التحقق الثنائي
 * Two-Factor Authentication Setup Component
 * 
 * المرحلة 1: تأمين الدخول والسياسات الأمنية الأساسية
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Shield, 
  Smartphone, 
  Mail, 
  Copy, 
  Check, 
  AlertTriangle,
  Download,
  QrCode
} from 'lucide-react';
import { Button } from '../ui/Button';
import { TwoFactorAuthService, TwoFactorSetup } from '../../services/security/TwoFactorAuth';
import { useAuth } from '../../contexts/AuthContext';

interface TwoFactorSetupProps {
  onComplete: () => void;
  onCancel: () => void;
}

export const TwoFactorSetupComponent: React.FC<TwoFactorSetupProps> = ({
  onComplete,
  onCancel
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [step, setStep] = useState<'method' | 'setup' | 'verify'>('method');
  const [method, setMethod] = useState<'totp' | 'email'>('totp');
  const [setupData, setSetupData] = useState<TwoFactorSetup | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [copiedSecret, setCopiedSecret] = useState(false);
  const [copiedBackupCodes, setCopiedBackupCodes] = useState(false);

  const twoFactorService = TwoFactorAuthService.getInstance();

  useEffect(() => {
    if (step === 'setup' && method === 'totp' && user) {
      setupTOTP();
    }
  }, [step, method, user]);

  const setupTOTP = async () => {
    if (!user) return;

    setLoading(true);
    setError('');

    try {
      const setup = await twoFactorService.setupTwoFactor(user.id);
      setSetupData(setup);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'حدث خطأ في الإعداد');
    } finally {
      setLoading(false);
    }
  };

  const handleVerification = async () => {
    if (!user || !verificationCode.trim()) {
      setError('يرجى إدخال رمز التحقق');
      return;
    }

    setLoading(true);
    setError('');

    try {
      if (method === 'totp') {
        const success = await twoFactorService.enableTwoFactor(user.id, verificationCode);
        if (success) {
          onComplete();
        }
      } else if (method === 'email') {
        const success = await twoFactorService.verifyEmailCode(user.id, verificationCode);
        if (success) {
          onComplete();
        }
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'رمز التحقق غير صحيح');
    } finally {
      setLoading(false);
    }
  };

  const sendEmailCode = async () => {
    if (!user) return;

    setLoading(true);
    setError('');

    try {
      await twoFactorService.sendEmailVerificationCode(user.id, user.email);
      setStep('verify');
    } catch (error) {
      setError(error instanceof Error ? error.message : 'فشل في إرسال رمز التحقق');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (text: string, type: 'secret' | 'backup') => {
    try {
      await navigator.clipboard.writeText(text);
      if (type === 'secret') {
        setCopiedSecret(true);
        setTimeout(() => setCopiedSecret(false), 2000);
      } else {
        setCopiedBackupCodes(true);
        setTimeout(() => setCopiedBackupCodes(false), 2000);
      }
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const downloadBackupCodes = () => {
    if (!setupData) return;

    const content = `رموز النسخ الاحتياطي للتحقق الثنائي\n\n${setupData.backupCodes.join('\n')}\n\nاحتفظ بهذه الرموز في مكان آمن. كل رمز يمكن استخدامه مرة واحدة فقط.`;
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'backup-codes.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // اختيار طريقة التحقق
  if (step === 'method') {
    return (
      <div className="max-w-md mx-auto bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
        <div className="text-center mb-6">
          <Shield className="w-12 h-12 text-primary-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            إعداد التحقق الثنائي
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            اختر طريقة التحقق المفضلة لديك
          </p>
        </div>

        <div className="space-y-4">
          <button
            onClick={() => {
              setMethod('totp');
              setStep('setup');
            }}
            className="w-full p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-primary-500 transition-colors"
          >
            <div className="flex items-center space-x-3 space-x-reverse">
              <Smartphone className="w-6 h-6 text-primary-500" />
              <div className="text-right">
                <h3 className="font-medium text-gray-900 dark:text-white">
                  تطبيق المصادقة
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Google Authenticator أو تطبيق مشابه
                </p>
              </div>
            </div>
          </button>

          <button
            onClick={() => {
              setMethod('email');
              sendEmailCode();
            }}
            className="w-full p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-primary-500 transition-colors"
          >
            <div className="flex items-center space-x-3 space-x-reverse">
              <Mail className="w-6 h-6 text-primary-500" />
              <div className="text-right">
                <h3 className="font-medium text-gray-900 dark:text-white">
                  البريد الإلكتروني
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  إرسال رمز التحقق عبر البريد الإلكتروني
                </p>
              </div>
            </div>
          </button>
        </div>

        <div className="mt-6 flex justify-between">
          <Button variant="outline" onClick={onCancel}>
            إلغاء
          </Button>
        </div>
      </div>
    );
  }

  // إعداد TOTP
  if (step === 'setup' && method === 'totp') {
    return (
      <div className="max-w-lg mx-auto bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
        <div className="text-center mb-6">
          <QrCode className="w-12 h-12 text-primary-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            إعداد تطبيق المصادقة
          </h2>
        </div>

        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">جاري الإعداد...</p>
          </div>
        ) : setupData ? (
          <div className="space-y-6">
            {/* QR Code */}
            <div className="text-center">
              <div className="bg-white p-4 rounded-lg inline-block">
                <img 
                  src={setupData.qrCode} 
                  alt="QR Code" 
                  className="w-48 h-48 mx-auto"
                />
              </div>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                امسح هذا الرمز باستخدام تطبيق Google Authenticator
              </p>
            </div>

            {/* المفتاح السري */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                أو أدخل المفتاح السري يدوياً:
              </label>
              <div className="flex items-center space-x-2 space-x-reverse">
                <input
                  type="text"
                  value={setupData.secret}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-sm font-mono"
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => copyToClipboard(setupData.secret, 'secret')}
                >
                  {copiedSecret ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                </Button>
              </div>
            </div>

            {/* رموز النسخ الاحتياطي */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  رموز النسخ الاحتياطي:
                </label>
                <div className="flex space-x-2 space-x-reverse">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(setupData.backupCodes.join('\n'), 'backup')}
                  >
                    {copiedBackupCodes ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={downloadBackupCodes}
                  >
                    <Download className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-3">
                <div className="flex items-start space-x-2 space-x-reverse">
                  <AlertTriangle className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm text-yellow-800 dark:text-yellow-200 font-medium">
                      احفظ هذه الرموز في مكان آمن!
                    </p>
                    <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-1">
                      يمكنك استخدام هذه الرموز للدخول إذا فقدت هاتفك
                    </p>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {setupData.backupCodes.map((code, index) => (
                  <div key={index} className="bg-gray-100 dark:bg-gray-700 p-2 rounded text-center font-mono text-sm">
                    {code}
                  </div>
                ))}
              </div>
            </div>

            <Button
              onClick={() => setStep('verify')}
              className="w-full"
            >
              متابعة للتحقق
            </Button>
          </div>
        ) : null}

        {error && (
          <div className="mt-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
            <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
          </div>
        )}
      </div>
    );
  }

  // التحقق من الرمز
  if (step === 'verify') {
    return (
      <div className="max-w-md mx-auto bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
        <div className="text-center mb-6">
          <Shield className="w-12 h-12 text-primary-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            تحقق من الرمز
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            {method === 'totp' 
              ? 'أدخل الرمز من تطبيق المصادقة'
              : 'أدخل الرمز المرسل إلى بريدك الإلكتروني'
            }
          </p>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              رمز التحقق
            </label>
            <input
              type="text"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              placeholder="000000"
              maxLength={6}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-center font-mono text-lg tracking-widest focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
            </div>
          )}

          <div className="flex space-x-3 space-x-reverse">
            <Button
              variant="outline"
              onClick={() => setStep(method === 'totp' ? 'setup' : 'method')}
              className="flex-1"
            >
              رجوع
            </Button>
            <Button
              onClick={handleVerification}
              disabled={loading || !verificationCode.trim()}
              className="flex-1"
            >
              {loading ? 'جاري التحقق...' : 'تفعيل'}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return null;
};
