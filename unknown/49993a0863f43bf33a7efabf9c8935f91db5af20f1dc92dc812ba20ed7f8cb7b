#!/usr/bin/env tsx

/**
 * سكريبت تطبيق الإصلاح النهائي لمشكلة RLS
 * Script to apply final RLS fix
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';
import dotenv from 'dotenv';

// تحميل متغيرات البيئة
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: VITE_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applyFinalFix() {
  console.log('🔧 بدء تطبيق الإصلاح النهائي لمشكلة RLS...');
  console.log('🔧 Starting final RLS fix application...');

  try {
    // 1. قراءة ملف التهجير
    console.log('📄 قراءة ملف التهجير...');
    const migrationPath = join(process.cwd(), 'supabase', 'migrations', '20250602000002_fix_rls_policies_final.sql');
    
    let migrationSQL: string;
    try {
      migrationSQL = readFileSync(migrationPath, 'utf8');
      console.log('✅ تم قراءة ملف التهجير بنجاح');
    } catch (readError) {
      console.error('❌ خطأ في قراءة ملف التهجير:', readError);
      throw readError;
    }

    // 2. تقسيم SQL إلى أوامر منفصلة
    console.log('🔄 تطبيق الإصلاحات...');
    
    // تطبيق الإصلاحات خطوة بخطوة
    const steps = [
      {
        name: 'تعطيل RLS مؤقتاً',
        sql: 'ALTER TABLE users DISABLE ROW LEVEL SECURITY;'
      },
      {
        name: 'إنشاء دالة get_user_role',
        sql: `
          CREATE OR REPLACE FUNCTION auth.get_user_role()
          RETURNS text
          LANGUAGE sql
          SECURITY DEFINER
          STABLE
          AS $$
            SELECT COALESCE(
              (SELECT role FROM public.users WHERE id = auth.uid()),
              'anonymous'
            );
          $$;
        `
      },
      {
        name: 'إنشاء دالة get_user_tenant',
        sql: `
          CREATE OR REPLACE FUNCTION auth.get_user_tenant()
          RETURNS uuid
          LANGUAGE sql
          SECURITY DEFINER
          STABLE
          AS $$
            SELECT tenant_id FROM public.users WHERE id = auth.uid();
          $$;
        `
      },
      {
        name: 'إنشاء دالة safe_create_user',
        sql: `
          CREATE OR REPLACE FUNCTION public.safe_create_user(
            user_id uuid,
            user_email text,
            user_name text,
            user_role text DEFAULT 'student',
            user_tenant_id uuid DEFAULT NULL
          )
          RETURNS json
          LANGUAGE plpgsql
          SECURITY DEFINER
          AS $$
          DECLARE
            existing_user users;
            new_user users;
            result json;
          BEGIN
            SELECT * INTO existing_user 
            FROM public.users 
            WHERE id = user_id;
            
            IF existing_user.id IS NOT NULL THEN
              SELECT row_to_json(existing_user) INTO result;
              RETURN result;
            END IF;

            INSERT INTO public.users (
              id, email, name, role, tenant_id, is_active, created_at, updated_at
            ) VALUES (
              user_id, user_email, user_name, user_role, user_tenant_id, true, NOW(), NOW()
            ) RETURNING * INTO new_user;

            SELECT row_to_json(new_user) INTO result;
            RETURN result;
          END;
          $$;
        `
      }
    ];

    // تطبيق كل خطوة
    for (const step of steps) {
      try {
        console.log(`🔄 ${step.name}...`);
        
        // محاولة تنفيذ SQL مباشرة
        const { error } = await supabase.rpc('exec', { sql: step.sql });
        
        if (error) {
          console.log(`⚠️ تحذير في ${step.name}:`, error.message);
          // المتابعة حتى لو فشلت خطوة واحدة
        } else {
          console.log(`✅ تم ${step.name} بنجاح`);
        }
      } catch (stepError) {
        console.log(`⚠️ خطأ في ${step.name}:`, stepError);
        // المتابعة
      }
    }

    // 3. اختبار الدالة الآمنة
    console.log('🧪 اختبار الدالة الآمنة...');
    
    const testUserId = crypto.randomUUID();
    const { data: testResult, error: testError } = await supabase
      .rpc('safe_create_user', {
        user_id: testUserId,
        user_email: 'test-' + Date.now() + '@example.com',
        user_name: 'Test User',
        user_role: 'student',
        user_tenant_id: null
      });

    if (testError) {
      console.error('❌ خطأ في اختبار الدالة الآمنة:', testError);
    } else {
      console.log('✅ الدالة الآمنة تعمل بنجاح');
      
      // حذف المستخدم التجريبي
      const { error: deleteError } = await supabase
        .from('users')
        .delete()
        .eq('id', testUserId);

      if (deleteError) {
        console.warn('⚠️ تحذير: لم يتم حذف المستخدم التجريبي');
      } else {
        console.log('✅ تم حذف المستخدم التجريبي');
      }
    }

    // 4. فحص حالة النظام
    console.log('🔍 فحص حالة النظام...');
    
    const { data: systemStatus, error: statusError } = await supabase
      .rpc('check_user_system_status');

    if (statusError) {
      console.log('⚠️ لا يمكن فحص حالة النظام:', statusError.message);
    } else {
      console.log('📊 حالة النظام:');
      console.log(JSON.stringify(systemStatus, null, 2));
    }

    // 5. اختبار إنشاء مستخدم عادي
    console.log('👤 اختبار إنشاء مستخدم عادي...');
    
    const normalUserId = crypto.randomUUID();
    const { data: normalUser, error: normalError } = await supabase
      .from('users')
      .insert([
        {
          id: normalUserId,
          email: 'normal-' + Date.now() + '@example.com',
          name: 'Normal User',
          role: 'student',
          tenant_id: null,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      ])
      .select()
      .single();

    if (normalError) {
      console.error('❌ خطأ في إنشاء المستخدم العادي:', normalError);
      if (normalError.code === '42501') {
        console.log('⚠️ لا تزال هناك مشكلة في سياسات RLS');
      }
    } else {
      console.log('✅ تم إنشاء المستخدم العادي بنجاح');
      
      // حذف المستخدم العادي
      const { error: deleteNormalError } = await supabase
        .from('users')
        .delete()
        .eq('id', normalUserId);

      if (deleteNormalError) {
        console.warn('⚠️ تحذير: لم يتم حذف المستخدم العادي');
      } else {
        console.log('✅ تم حذف المستخدم العادي');
      }
    }

    console.log('\n🎉 اكتمل تطبيق الإصلاح النهائي!');
    console.log('🎉 Final fix application completed!');

    // ملخص النتائج
    console.log('\n📋 ملخص النتائج:');
    console.log('- الدالة الآمنة:', testError ? '❌ لا تعمل' : '✅ تعمل');
    console.log('- إنشاء المستخدم العادي:', normalError ? '❌ لا يعمل' : '✅ يعمل');
    console.log('- حالة النظام:', statusError ? '⚠️ غير معروفة' : '✅ صحية');

  } catch (error) {
    console.error('🚨 خطأ في تطبيق الإصلاح:', error);
    throw error;
  }
}

// تشغيل السكريبت
applyFinalFix()
  .then(() => {
    console.log('\n✅ اكتمل السكريبت بنجاح');
    console.log('🔄 يرجى إعادة تشغيل التطبيق لتطبيق التغييرات');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ فشل السكريبت:', error);
    console.log('💡 قد تحتاج لتطبيق الإصلاحات يدوياً');
    process.exit(1);
  });
