/**
 * Base Service Class
 * Provides common functionality for all services
 * Phase 2: Application Structure Reorganization
 */

import { APIResponse, PaginatedResponse, PaginationParams } from '../../api/types';
import { env } from '../../utils/env';

export interface ServiceConfig {
  baseURL: string;
  timeout: number;
  retries: number;
  headers?: Record<string, string>;
}

export interface RequestOptions {
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
  cache?: boolean;
  cacheTTL?: number;
}

/**
 * Base Service Class
 * Implements common patterns for API communication
 */
export abstract class BaseService {
  protected config: ServiceConfig;
  protected cache: Map<string, { data: any; expires: number }> = new Map();

  constructor(config: Partial<ServiceConfig> = {}) {
    this.config = {
      baseURL: env.api.url,
      timeout: 30000,
      retries: 3,
      ...config,
    };
  }

  /**
   * Make HTTP request with error handling and retries
   */
  protected async request<T>(
    endpoint: string,
    options: RequestInit & RequestOptions = {}
  ): Promise<APIResponse<T>> {
    const url = `${this.config.baseURL}${endpoint}`;
    const cacheKey = `${options.method || 'GET'}:${url}:${JSON.stringify(options.body)}`;

    // Check cache for GET requests
    if ((!options.method || options.method === 'GET') && options.cache !== false) {
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const requestOptions: RequestInit = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...this.config.headers,
        ...options.headers,
      },
      ...options,
    };

    let lastError: Error;
    const maxRetries = options.retries ?? this.config.retries;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const controller = new AbortController();
        const timeout = options.timeout ?? this.config.timeout;

        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url, {
          ...requestOptions,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        const result = await this.handleResponse<T>(response);

        // Cache successful GET requests
        if (result.success && (!options.method || options.method === 'GET')) {
          this.setCache(cacheKey, result, options.cacheTTL);
        }

        return result;
      } catch (error) {
        lastError = error as Error;

        // Don't retry on certain errors
        if (this.shouldNotRetry(error as Error)) {
          break;
        }

        // Wait before retry (exponential backoff)
        if (attempt < maxRetries) {
          await this.delay(Math.pow(2, attempt) * 1000);
        }
      }
    }

    return {
      success: false,
      error: {
        code: 'REQUEST_FAILED',
        message: lastError.message,
        details: { attempts: maxRetries + 1 },
      },
    };
  }

  /**
   * Handle HTTP response and convert to APIResponse
   */
  private async handleResponse<T>(response: Response): Promise<APIResponse<T>> {
    try {
      const data = await response.json();

      if (response.ok) {
        return {
          success: true,
          data,
          meta: {
            timestamp: new Date().toISOString(),
            requestId: response.headers.get('x-request-id') || '',
            version: response.headers.get('x-api-version') || 'v1',
          },
        };
      } else {
        return {
          success: false,
          error: {
            code: data.error?.code || `HTTP_${response.status}`,
            message: data.error?.message || response.statusText,
            details: data.error?.details,
          },
        };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'PARSE_ERROR',
          message: 'Failed to parse response',
          details: { originalError: (error as Error).message },
        },
      };
    }
  }

  /**
   * Check if error should not be retried
   */
  private shouldNotRetry(error: Error): boolean {
    // Don't retry on authentication errors, validation errors, etc.
    const noRetryErrors = ['401', '403', '422', 'AbortError'];
    return noRetryErrors.some(code => error.message.includes(code));
  }

  /**
   * Delay execution for specified milliseconds
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Cache management
   */
  private getFromCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && cached.expires > Date.now()) {
      return cached.data;
    }
    if (cached) {
      this.cache.delete(key);
    }
    return null;
  }

  private setCache(key: string, data: any, ttl: number = 300000): void {
    // Default TTL: 5 minutes
    this.cache.set(key, {
      data,
      expires: Date.now() + ttl,
    });

    // Clean up expired cache entries
    if (this.cache.size > 100) {
      this.cleanupCache();
    }
  }

  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (value.expires <= now) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Common CRUD operations
   */
  protected async get<T>(endpoint: string, options?: RequestOptions): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET', ...options });
  }

  protected async post<T>(
    endpoint: string,
    data: any,
    options?: RequestOptions
  ): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
      ...options,
    });
  }

  protected async put<T>(
    endpoint: string,
    data: any,
    options?: RequestOptions
  ): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
      ...options,
    });
  }

  protected async patch<T>(
    endpoint: string,
    data: any,
    options?: RequestOptions
  ): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data),
      ...options,
    });
  }

  protected async delete<T>(endpoint: string, options?: RequestOptions): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE', ...options });
  }

  /**
   * Build query string from parameters
   */
  protected buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, v.toString()));
        } else {
          searchParams.append(key, value.toString());
        }
      }
    });

    const queryString = searchParams.toString();
    return queryString ? `?${queryString}` : '';
  }

  /**
   * Handle paginated requests
   */
  protected async getPaginated<T>(
    endpoint: string,
    params: PaginationParams = {},
    options?: RequestOptions
  ): Promise<APIResponse<PaginatedResponse<T>>> {
    const queryString = this.buildQueryString(params);
    return this.get<PaginatedResponse<T>>(`${endpoint}${queryString}`, options);
  }

  /**
   * Set authentication token
   */
  public setAuthToken(token: string): void {
    this.config.headers = {
      ...this.config.headers,
      Authorization: `Bearer ${token}`,
    };
  }

  /**
   * Remove authentication token
   */
  public removeAuthToken(): void {
    if (this.config.headers) {
      delete this.config.headers.Authorization;
    }
  }

  /**
   * Clear cache
   */
  public clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get service health status
   */
  public async healthCheck(): Promise<APIResponse<{ status: string; timestamp: string }>> {
    return this.get('/health');
  }
}
