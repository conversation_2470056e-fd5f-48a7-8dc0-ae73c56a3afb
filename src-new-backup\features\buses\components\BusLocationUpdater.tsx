import React, { useEffect, useState } from "react";
import { supabase } from "../../../shared/services/lib/supabase";
import { useAuth } from "../../contexts/AuthContext";

/**
 * Component to simulate GPS location updates for buses
 * In a real application, this would be replaced by actual GPS tracking devices
 */
export const BusLocationUpdater: React.FC = () => {
  const { user } = useAuth();
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (!user?.tenant_id) return;

    const updateBusLocations = async () => {
      try {
        setIsUpdating(true);

        // Get all active buses for this tenant
        const { data: buses, error: busError } = await supabase
          .from("buses")
          .select("id, plate_number, last_location")
          .eq("tenant_id", user.tenant_id)
          .eq("is_active", true);

        if (busError || !buses) return;

        // Update each bus with a simulated location
        for (const bus of buses) {
          // Generate random coordinates around a central point (you can adjust this)
          const baseLat = 24.7136; // Riyadh coordinates
          const baseLng = 46.6753;
          const randomLat = baseLat + (Math.random() - 0.5) * 0.1; // ±0.05 degrees
          const randomLng = baseLng + (Math.random() - 0.5) * 0.1;

          const newLocation = {
            type: "Point",
            coordinates: [randomLng, randomLat],
          };

          await supabase
            .from("buses")
            .update({
              last_location: newLocation,
              last_updated: new Date().toISOString(),
            })
            .eq("id", bus.id);
        }
      } catch (error) {
        console.error("Error updating bus locations:", error);
      } finally {
        setIsUpdating(false);
      }
    };

    // Update locations every 30 seconds
    const interval = setInterval(updateBusLocations, 30000);

    // Initial update
    updateBusLocations();

    return () => clearInterval(interval);
  }, [user?.tenant_id]);

  // This component doesn't render anything visible
  return null;
};

export default BusLocationUpdater;
