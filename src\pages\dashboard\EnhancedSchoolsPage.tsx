/**
 * صفحة إدارة المدارس المحسنة - Enhanced Schools Management Page
 * تدعم النظام الجديد للصلاحيات مع جميع العمليات المطلوبة
 */

import React, { useState, useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import {
  Building2,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Plus,
  Loader2,
  UserPlus,
  Download,
  Upload,
  MoreVertical,
  CheckSquare,
  Square,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Settings,
} from "lucide-react";
import { Navbar } from "../../components/layout/Navbar";
import { Sidebar } from "../../components/layout/Sidebar";
import { Button } from "../../components/ui/Button";
import { Pagination, usePagination } from "../../components/ui/Pagination";
import { SchoolModal } from "../../components/schools/SchoolModal";
import { SchoolManagerAssignment } from "../../components/schools/SchoolManagerAssignment";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { usePermissions, Permission } from "../../hooks/usePermissions";
import { PermissionGuard } from "../../components/auth/PermissionGuard";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/api";

interface BulkAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  action: (selectedIds: string[]) => void;
  permission?: Permission;
  confirmMessage?: string;
}

export const EnhancedSchoolsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { tenants, loading, error, refreshData } = useDatabase();
  const { 
    isAdmin, 
    isSchoolManager, 
    isSupervisor,
    hasPermission 
  } = usePermissions();

  // State management
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<"all" | "active" | "inactive">("all");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedSchool, setSelectedSchool] = useState<any | undefined>();
  const [isDeleting, setIsDeleting] = useState(false);
  const [isManagerAssignmentOpen, setIsManagerAssignmentOpen] = useState(false);
  const [schoolForManagerAssignment, setSchoolForManagerAssignment] = useState<any | undefined>();
  const [selectedSchools, setSelectedSchools] = useState<Set<string>>(new Set());
  const [isProcessing, setIsProcessing] = useState(false);
  const [showBulkActions, setShowBulkActions] = useState(false);

  // تصفية المدارس حسب الصلاحيات
  const filteredSchools = useMemo(() => {
    let schools = tenants;

    // تطبيق فلترة الصلاحيات
    if (!isAdmin && user?.tenant_id) {
      schools = tenants.filter(school => school.id === user.tenant_id);
    }

    // تطبيق فلترة البحث والحالة
    return schools.filter((school) => {
      const matchesSearch = !searchQuery || 
        school.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (school.contact_number && school.contact_number.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (school.address && school.address.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesStatus = filterStatus === "all" ||
        (filterStatus === "active" && school.is_active) ||
        (filterStatus === "inactive" && !school.is_active);

      return matchesSearch && matchesStatus;
    });
  }, [tenants, user, isAdmin, searchQuery, filterStatus]);

  // Pagination
  const { currentPage, totalPages, startIndex, endIndex, goToPage } = usePagination(filteredSchools.length, 10);
  const paginatedSchools = filteredSchools.slice(startIndex, endIndex);

  // تحديد/إلغاء تحديد جميع المدارس
  const handleSelectAll = useCallback(() => {
    if (selectedSchools.size === paginatedSchools.length) {
      setSelectedSchools(new Set());
    } else {
      setSelectedSchools(new Set(paginatedSchools.map(school => school.id)));
    }
  }, [selectedSchools.size, paginatedSchools]);

  // تحديد/إلغاء تحديد مدرسة واحدة
  const handleSelectSchool = useCallback((schoolId: string) => {
    const newSelected = new Set(selectedSchools);
    if (newSelected.has(schoolId)) {
      newSelected.delete(schoolId);
    } else {
      newSelected.add(schoolId);
    }
    setSelectedSchools(newSelected);
  }, [selectedSchools]);

  // العمليات المجمعة
  const bulkActions: BulkAction[] = [
    {
      id: 'activate',
      label: 'تفعيل المحدد',
      icon: <CheckCircle className="h-4 w-4" />,
      action: handleBulkActivate,
      permission: Permission.TENANTS_EDIT,
      confirmMessage: 'هل أنت متأكد من تفعيل المدارس المحددة؟'
    },
    {
      id: 'deactivate',
      label: 'إلغاء تفعيل المحدد',
      icon: <XCircle className="h-4 w-4" />,
      action: handleBulkDeactivate,
      permission: Permission.TENANTS_EDIT,
      confirmMessage: 'هل أنت متأكد من إلغاء تفعيل المدارس المحددة؟'
    },
    {
      id: 'delete',
      label: 'حذف المحدد',
      icon: <Trash2 className="h-4 w-4" />,
      action: handleBulkDelete,
      permission: Permission.TENANTS_DELETE,
      confirmMessage: 'هل أنت متأكد من حذف المدارس المحددة؟ هذا الإجراء لا يمكن التراجع عنه.'
    },
    {
      id: 'export',
      label: 'تصدير المحدد',
      icon: <Download className="h-4 w-4" />,
      action: handleBulkExport,
    }
  ];

  // دوال العمليات المجمعة
  async function handleBulkActivate(selectedIds: string[]) {
    setIsProcessing(true);
    try {
      const { error } = await supabase
        .from('tenants')
        .update({ is_active: true })
        .in('id', selectedIds);

      if (error) throw error;
      
      await refreshData();
      setSelectedSchools(new Set());
      // إظهار رسالة نجاح
    } catch (error) {
      console.error('Error activating schools:', error);
      // إظهار رسالة خطأ
    } finally {
      setIsProcessing(false);
    }
  }

  async function handleBulkDeactivate(selectedIds: string[]) {
    setIsProcessing(true);
    try {
      const { error } = await supabase
        .from('tenants')
        .update({ is_active: false })
        .in('id', selectedIds);

      if (error) throw error;
      
      await refreshData();
      setSelectedSchools(new Set());
    } catch (error) {
      console.error('Error deactivating schools:', error);
    } finally {
      setIsProcessing(false);
    }
  }

  async function handleBulkDelete(selectedIds: string[]) {
    setIsProcessing(true);
    try {
      // استخدام دالة الحذف المجمع النهائية الجديدة
      const { data, error } = await supabase.rpc('bulk_delete_tenants_final', {
        tenant_ids_to_delete: selectedIds
      });

      if (error) throw error;

      if (data.success) {
        alert(`تم حذف ${data.success_count} مدرسة من أصل ${data.total_requested} بنجاح!`);
      } else {
        alert(`فشل في حذف المدارس:\n${data.errors.join('\n')}`);
      }

      await refreshData();
      setSelectedSchools(new Set());
    } catch (error: any) {
      console.error('Error deleting schools:', error);
      alert(`حدث خطأ أثناء حذف المدارس: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  }

  function handleBulkExport(selectedIds: string[]) {
    const selectedSchoolsData = filteredSchools.filter(school => selectedIds.includes(school.id));
    const csvContent = generateCSV(selectedSchoolsData);
    downloadCSV(csvContent, 'schools.csv');
  }

  // دوال مساعدة للتصدير
  function generateCSV(schools: any[]): string {
    const headers = ['اسم المدرسة', 'العنوان', 'رقم الاتصال', 'النطاق', 'الحالة', 'تاريخ الإنشاء'];
    const rows = schools.map(school => [
      school.name,
      school.address || '',
      school.contact_number || '',
      school.domain || '',
      school.is_active ? 'نشط' : 'غير نشط',
      new Date(school.created_at).toLocaleDateString('ar')
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  function downloadCSV(content: string, filename: string) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
  }

  // دوال إدارة النماذج
  const handleOpenModal = useCallback((school?: any) => {
    setSelectedSchool(school);
    setIsModalOpen(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setSelectedSchool(undefined);
    setIsModalOpen(false);
  }, []);

  const handleOpenManagerAssignment = useCallback((school: any) => {
    setSchoolForManagerAssignment(school);
    setIsManagerAssignmentOpen(true);
  }, []);

  const handleCloseManagerAssignment = useCallback(() => {
    setSchoolForManagerAssignment(undefined);
    setIsManagerAssignmentOpen(false);
  }, []);

  // دالة حفظ المدرسة
  const handleSubmit = async (data: Partial<Tables<"tenants">>) => {
    try {
      if (selectedSchool) {
        // تحديث مدرسة موجودة
        const { error } = await supabase
          .from("tenants")
          .update(data)
          .eq("id", selectedSchool.id);

        if (error) throw error;
      } else {
        // إضافة مدرسة جديدة
        const { error } = await supabase.from("tenants").insert([data]);
        if (error) throw error;
      }

      await refreshData();
      handleCloseModal();
    } catch (error: any) {
      console.error("Error saving school:", error);

      // معالجة أخطاء RLS
      if (error?.code === '42501') {
        alert('خطأ في الصلاحيات: ليس لديك صلاحية لتنفيذ هذا الإجراء. تأكد من أنك مسجل دخول كأدمن.');
      } else if (error?.message?.includes('duplicate key')) {
        alert('خطأ: اسم المدرسة أو النطاق موجود مسبقاً.');
      } else if (error?.message) {
        alert(`خطأ: ${error.message}`);
      } else {
        alert('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.');
      }

      throw error;
    }
  };

  // دالة حذف مدرسة واحدة
  const handleDelete = async (id: string) => {
    console.log('🗑️ handleDelete called with ID:', id);
    console.log('🔒 isDeleting state:', isDeleting);
    console.log('👤 isAdmin:', isAdmin);

    if (!confirm("هل أنت متأكد من حذف هذه المدرسة؟ سيتم حذف جميع البيانات المرتبطة بها (المستخدمين، الطلاب، الحافلات، إلخ).")) {
      console.log('❌ User cancelled deletion');
      return;
    }

    console.log('✅ User confirmed deletion, proceeding...');
    setIsDeleting(true);

    try {
      console.log('🚀 Calling delete_tenant_final with ID:', id);

      // استخدام دالة الحذف النهائية الجديدة
      const { data, error } = await supabase.rpc('delete_tenant_final', {
        tenant_id_to_delete: id
      });

      console.log('📊 Response data:', data);
      console.log('❗ Response error:', error);

      if (error) {
        console.error('💥 Supabase RPC error:', error);
        throw error;
      }

      if (data && data.success) {
        console.log('🎉 Delete successful:', data.message);
        alert(`تم حذف المدرسة بنجاح!\nالبيانات المحذوفة:\n- المستخدمون: ${data.deleted_data.users}\n- الطلاب: ${data.deleted_data.students}\n- الحافلات: ${data.deleted_data.buses}\n- المسارات: ${data.deleted_data.routes}`);
        console.log('🔄 Refreshing data...');
        await refreshData();
        console.log('✅ Data refreshed successfully');
      } else {
        console.error('❌ Delete failed:', data?.error || 'Unknown error');
        alert(`فشل حذف المدرسة: ${data?.error || 'خطأ غير معروف'}`);
      }
    } catch (error: any) {
      console.error("💥 Error deleting school:", error);
      alert(`حدث خطأ أثناء حذف المدرسة: ${error.message}`);
    } finally {
      console.log('🏁 Setting isDeleting to false');
      setIsDeleting(false);
    }
  };

  // دالة تغيير حالة المدرسة
  const handleToggleStatus = async (school: any) => {
    try {
      const { error } = await supabase
        .from("tenants")
        .update({ is_active: !school.is_active })
        .eq("id", school.id);

      if (error) throw error;
      await refreshData();
    } catch (error) {
      console.error("Error toggling school status:", error);
    }
  };

  // التحقق من الصلاحيات للوصول للصفحة
  if (!isAdmin && !isSchoolManager && !isSupervisor) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            غير مصرح بالوصول
          </h2>
          <p className="text-gray-500 dark:text-gray-400">
            ليس لديك صلاحية للوصول إلى هذه الصفحة.
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin text-primary-500" />
          <span className="text-gray-600 dark:text-gray-300">جاري تحميل المدارس...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            خطأ في تحميل البيانات
          </h2>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            {error.message}
          </p>
          <Button onClick={refreshData} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            إعادة المحاولة
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />

        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            {/* رأس الصفحة */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  إدارة المدارس
                </h1>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {isAdmin
                    ? "إدارة جميع المدارس في النظام"
                    : isSchoolManager
                      ? "إدارة مدرستك"
                      : "عرض معلومات المدرسة"
                  }
                </p>
              </div>

              <div className="mt-4 md:mt-0 flex items-center gap-3">
                {/* زر تحديث */}
                <Button
                  variant="outline"
                  onClick={refreshData}
                  className="flex items-center gap-2"
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                  تحديث
                </Button>

                {/* زر إضافة مدرسة */}
                {isAdmin && (
                  <Button
                    onClick={() => handleOpenModal()}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    إضافة مدرسة
                  </Button>
                )}
              </div>
            </div>

            {/* شريط العمليات المجمعة */}
            {selectedSchools.size > 0 && (
              <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                      تم تحديد {selectedSchools.size} مدرسة
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedSchools(new Set())}
                    >
                      إلغاء التحديد
                    </Button>
                  </div>

                  <div className="flex items-center gap-2">
                    {bulkActions.map((action) => {
                      // تحقق من الصلاحيات حسب نوع العملية
                      const hasPermissionForAction =
                        action.id === 'export' || // التصدير متاح للجميع
                        (action.id === 'delete' && isAdmin) || // الحذف للأدمن فقط
                        ((action.id === 'activate' || action.id === 'deactivate') && (isAdmin || isSchoolManager)); // التفعيل للأدمن ومدير المدرسة

                      if (!hasPermissionForAction) return null;

                      return (
                        <Button
                          key={action.id}
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (action.confirmMessage && !confirm(action.confirmMessage)) return;
                            action.action(Array.from(selectedSchools));
                          }}
                          disabled={isProcessing}
                          className="flex items-center gap-2"
                        >
                          {action.icon}
                          {action.label}
                        </Button>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}

            {/* البحث والفلترة */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                  <div className="relative max-w-xs w-full">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      placeholder="البحث في المدارس..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Filter className="h-5 w-5 text-gray-400" />
                      </div>
                      <select
                        className="block pl-10 pr-8 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                        value={filterStatus}
                        onChange={(e) => setFilterStatus(e.target.value as "all" | "active" | "inactive")}
                      >
                        <option value="all">جميع المدارس</option>
                        <option value="active">المدارس النشطة</option>
                        <option value="inactive">المدارس غير النشطة</option>
                      </select>
                    </div>

                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {filteredSchools.length} مدرسة
                    </div>
                  </div>
                </div>
              </div>

              {/* جدول المدارس */}
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      {/* عمود التحديد */}
                      <th scope="col" className="px-6 py-3 text-left">
                        <button
                          onClick={handleSelectAll}
                          className="flex items-center justify-center w-5 h-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                          {selectedSchools.size === paginatedSchools.length && paginatedSchools.length > 0 ? (
                            <CheckSquare className="w-5 h-5" />
                          ) : (
                            <Square className="w-5 h-5" />
                          )}
                        </button>
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        المدرسة
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        معلومات الاتصال
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        تاريخ الإنشاء
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {paginatedSchools.length > 0 ? (
                      paginatedSchools.map((school) => (
                        <tr
                          key={school.id}
                          className={`hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                            selectedSchools.has(school.id) ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                          }`}
                        >
                          {/* عمود التحديد */}
                          <td className="px-6 py-4 whitespace-nowrap">
                            <button
                              onClick={() => handleSelectSchool(school.id)}
                              className="flex items-center justify-center w-5 h-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                            >
                              {selectedSchools.has(school.id) ? (
                                <CheckSquare className="w-5 h-5 text-blue-600" />
                              ) : (
                                <Square className="w-5 h-5" />
                              )}
                            </button>
                          </td>

                          {/* معلومات المدرسة */}
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                {school.logo_url ? (
                                  <img
                                    src={school.logo_url}
                                    alt={school.name}
                                    className="h-10 w-10 rounded-md object-cover"
                                  />
                                ) : (
                                  <div className="h-10 w-10 bg-primary-100 dark:bg-primary-800/20 rounded-md flex items-center justify-center text-primary-600 dark:text-primary-400">
                                    <Building2 className="h-5 w-5" />
                                  </div>
                                )}
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {school.name}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  {school.address || "لا يوجد عنوان"}
                                </div>
                              </div>
                            </div>
                          </td>

                          {/* معلومات الاتصال */}
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 dark:text-white">
                              {school.contact_number || "لا يوجد رقم"}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {school.domain || "لا يوجد نطاق"}
                            </div>
                          </td>

                          {/* الحالة */}
                          <td className="px-6 py-4 whitespace-nowrap">
                            <button
                              onClick={() => handleToggleStatus(school)}
                              disabled={!isAdmin && !isSchoolManager}
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors ${
                                school.is_active
                                  ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100 hover:bg-green-200 dark:hover:bg-green-700"
                                  : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 hover:bg-red-200 dark:hover:bg-red-700"
                              } ${!isAdmin && !isSchoolManager ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
                            >
                              {school.is_active ? (
                                <>
                                  <CheckCircle className="w-3 h-3 mr-1" />
                                  نشطة
                                </>
                              ) : (
                                <>
                                  <XCircle className="w-3 h-3 mr-1" />
                                  غير نشطة
                                </>
                              )}
                            </button>
                          </td>

                          {/* تاريخ الإنشاء */}
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {new Date(school.created_at).toLocaleDateString('ar')}
                          </td>

                          {/* الإجراءات */}
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex items-center justify-end gap-2">
                              {/* عرض */}
                              <button
                                className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 p-1"
                                title="عرض التفاصيل"
                                onClick={() => handleOpenModal(school)}
                              >
                                <Eye className="h-4 w-4" />
                              </button>

                              {/* تعيين مدير */}
                              {isAdmin && (
                                <button
                                  className="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300 p-1"
                                  title="تعيين مدير"
                                  onClick={() => handleOpenManagerAssignment(school)}
                                >
                                  <UserPlus className="h-4 w-4" />
                                </button>
                              )}

                              {/* تعديل */}
                              {(isAdmin || isSchoolManager) && (
                                <button
                                  className="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300 p-1"
                                  title="تعديل"
                                  onClick={() => handleOpenModal(school)}
                                >
                                  <Edit className="h-4 w-4" />
                                </button>
                              )}

                              {/* حذف */}
                              {isAdmin && (
                                <button
                                  className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 p-1 transition-colors"
                                  title="حذف"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    console.log('🖱️ Delete button clicked for school:', school.name, 'ID:', school.id);
                                    console.log('🔒 Button disabled state:', isDeleting);
                                    console.log('👤 isAdmin check:', isAdmin);
                                    handleDelete(school.id);
                                  }}
                                  disabled={isDeleting}
                                  style={{
                                    opacity: isDeleting ? 0.5 : 1,
                                    cursor: isDeleting ? 'not-allowed' : 'pointer'
                                  }}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                          {loading ? (
                            <div className="flex items-center justify-center">
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              جاري تحميل المدارس...
                            </div>
                          ) : searchQuery || filterStatus !== "all" ? (
                            "لا توجد نتائج مطابقة للبحث"
                          ) : (
                            <div className="py-8">
                              <Building2 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                              <p className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                لا توجد مدارس
                              </p>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                {isAdmin
                                  ? "ابدأ بإضافة أول مدرسة في النظام"
                                  : "لا توجد بيانات مدارس متاحة لحسابك"
                                }
                              </p>
                            </div>
                          )}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      عرض {startIndex + 1} - {endIndex} من {filteredSchools.length} مدرسة
                    </div>
                    <Pagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={goToPage}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>

      {/* النماذج */}
      <SchoolModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleSubmit}
        school={selectedSchool}
      />

      {schoolForManagerAssignment && (
        <SchoolManagerAssignment
          tenant={schoolForManagerAssignment}
          onClose={handleCloseManagerAssignment}
          onSuccess={() => {
            refreshData();
            handleCloseManagerAssignment();
          }}
        />
      )}
    </div>
  );
};
