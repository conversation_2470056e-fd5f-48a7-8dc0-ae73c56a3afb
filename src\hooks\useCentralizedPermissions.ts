/**
 * Hook للصلاحيات المركزية المحدث
 * Updated Centralized Permissions Hook
 * 
 * يستخدم النظام الأمني الجديد من المرحلة الأولى
 * Uses the new security system from Phase 1
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { 
  CentralizedPermissionService, 
  ResourceType, 
  Action, 
  PermissionContext,
  PermissionCheckResult 
} from '../services/CentralizedPermissionService';

interface UsePermissionsOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  cacheResults?: boolean;
}

interface PermissionState {
  isLoading: boolean;
  error: string | null;
  lastChecked: Date | null;
}

export const useCentralizedPermissions = (options: UsePermissionsOptions = {}) => {
  const { user, isLoading: authLoading } = useAuth();
  const [state, setState] = useState<PermissionState>({
    isLoading: false,
    error: null,
    lastChecked: null
  });

  const permissionService = useMemo(() => 
    CentralizedPermissionService.getInstance(), 
    []
  );

  const {
    autoRefresh = false,
    refreshInterval = 5 * 60 * 1000, // 5 minutes
    cacheResults = true
  } = options;

  // فحص صلاحية واحدة
  const checkPermission = useCallback(async (
    resourceType: ResourceType,
    action: Action,
    context?: PermissionContext
  ): Promise<PermissionCheckResult> => {
    if (!user) {
      return {
        allowed: false,
        reason: 'User not authenticated'
      };
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const result = await permissionService.checkPermission(
        user,
        resourceType,
        action,
        context
      );

      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        lastChecked: new Date() 
      }));

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));

      return {
        allowed: false,
        reason: errorMessage
      };
    }
  }, [user, permissionService]);

  // فحص عدة صلاحيات
  const checkMultiplePermissions = useCallback(async (
    permissions: Array<{
      resourceType: ResourceType;
      action: Action;
      context?: PermissionContext;
    }>
  ): Promise<PermissionCheckResult[]> => {
    if (!user) {
      return permissions.map(() => ({
        allowed: false,
        reason: 'User not authenticated'
      }));
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const results = await permissionService.checkMultiplePermissions(
        user,
        permissions
      );

      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        lastChecked: new Date() 
      }));

      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));

      return permissions.map(() => ({
        allowed: false,
        reason: errorMessage
      }));
    }
  }, [user, permissionService]);

  // فحص الوصول للمستأجر
  const canAccessTenant = useCallback(async (tenantId: string): Promise<boolean> => {
    if (!user) return false;

    try {
      return await permissionService.canAccessTenant(user, tenantId);
    } catch (error) {
      console.error('Error checking tenant access:', error);
      return false;
    }
  }, [user, permissionService]);

  // فحص إدارة المستخدمين
  const canManageUsers = useCallback(async (targetTenantId?: string): Promise<boolean> => {
    if (!user) return false;

    try {
      return await permissionService.canManageUsers(user, targetTenantId);
    } catch (error) {
      console.error('Error checking user management permission:', error);
      return false;
    }
  }, [user, permissionService]);

  // فحص ما إذا كان المستخدم أدمن
  const isSystemAdmin = useCallback(async (): Promise<boolean> => {
    if (!user) return false;

    try {
      return await permissionService.isSystemAdmin(user);
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  }, [user, permissionService]);

  // فحص ما إذا كان المستخدم مدير مدرسة
  const isSchoolManager = useCallback(async (): Promise<boolean> => {
    if (!user) return false;

    try {
      return await permissionService.isSchoolManager(user);
    } catch (error) {
      console.error('Error checking school manager status:', error);
      return false;
    }
  }, [user, permissionService]);

  // الحصول على ملخص الصلاحيات
  const getPermissionSummary = useCallback(async (
    resourceType: ResourceType
  ) => {
    if (!user) {
      return {
        canCreate: false,
        canRead: false,
        canUpdate: false,
        canDelete: false
      };
    }

    try {
      return await permissionService.getPermissionSummary(user, resourceType);
    } catch (error) {
      console.error('Error getting permission summary:', error);
      return {
        canCreate: false,
        canRead: false,
        canUpdate: false,
        canDelete: false
      };
    }
  }, [user, permissionService]);

  // مسح التخزين المؤقت
  const clearCache = useCallback(() => {
    permissionService.clearCache();
  }, [permissionService]);

  // مسح التخزين المؤقت للمستخدم الحالي
  const clearUserCache = useCallback(() => {
    if (user) {
      permissionService.clearUserCache(user.id);
    }
  }, [user, permissionService]);

  // تسجيل حدث أمني
  const logSecurityEvent = useCallback(async (
    eventType: string,
    severity: 'INFO' | 'WARNING' | 'ERROR',
    description: string,
    metadata?: Record<string, any>
  ) => {
    try {
      await permissionService.logSecurityEvent(
        eventType,
        severity,
        description,
        user?.id,
        user?.tenant_id,
        metadata
      );
    } catch (error) {
      console.error('Error logging security event:', error);
    }
  }, [user, permissionService]);

  // تحديث تلقائي
  useEffect(() => {
    if (!autoRefresh || !user) return;

    const interval = setInterval(() => {
      clearUserCache();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, user, clearUserCache]);

  return {
    // الحالة
    isLoading: state.isLoading || authLoading,
    error: state.error,
    lastChecked: state.lastChecked,
    hasValidUser: !!user && !authLoading,

    // دوال فحص الصلاحيات
    checkPermission,
    checkMultiplePermissions,
    canAccessTenant,
    canManageUsers,
    isSystemAdmin,
    isSchoolManager,
    getPermissionSummary,

    // إدارة التخزين المؤقت
    clearCache,
    clearUserCache,

    // تسجيل الأحداث
    logSecurityEvent,

    // معلومات المستخدم
    user,
    userRole: user?.role,
    userTenantId: user?.tenant_id
  };
};

// Hook مبسط للفحص السريع
export const useQuickPermissionCheck = (
  resourceType: ResourceType,
  action: Action,
  context?: PermissionContext
) => {
  const { checkPermission, isLoading, hasValidUser } = useCentralizedPermissions();
  const [result, setResult] = useState<PermissionCheckResult | null>(null);

  useEffect(() => {
    if (!hasValidUser) {
      setResult({
        allowed: false,
        reason: 'User not authenticated'
      });
      return;
    }

    const performCheck = async () => {
      const permissionResult = await checkPermission(resourceType, action, context);
      setResult(permissionResult);
    };

    performCheck();
  }, [checkPermission, resourceType, action, context, hasValidUser]);

  return {
    allowed: result?.allowed ?? false,
    reason: result?.reason,
    isLoading,
    hasValidUser
  };
};

export default useCentralizedPermissions;
