import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { X } from "lucide-react";
import { Button } from "../ui/Button";
import { useDatabase } from "../../contexts/DatabaseContext";
import { useAuth } from "../../contexts/AuthContext";
import type { Tables } from "../../lib/api";
import type { UserRole } from "../../types";

interface UserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: Partial<Tables<"users">>) => Promise<void>;
  user?: Tables<"users">;
  bulkMode?: "role" | "status" | null;
}

export const UserModal: React.FC<UserModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  user,
  bulkMode = null,
}) => {
  const { t } = useTranslation();
  const { tenant } = useDatabase();
  const { user: currentUser } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<
    Partial<Tables<"users">> & { password?: string }
  >({
    name: "",
    email: "",
    role: "school_manager",
    phone: "",
    password: "",
    is_active: true,
    tenant_id: tenant?.id || currentUser?.tenant_id,
  });

  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name,
        email: user.email,
        role: user.role,
        phone: user.phone || "",
        password: "", // Don't populate password for editing
        is_active: user.is_active,
        tenant_id: user.tenant_id,
      });
    } else if (bulkMode) {
      // في التعديل الجماعي، ابدأ بقيم فارغة
      setFormData({
        role: "school_manager",
        is_active: true,
      });
    } else {
      setFormData({
        name: "",
        email: "",
        role: "school_manager",
        phone: "",
        password: "",
        is_active: true,
        tenant_id: tenant?.id || currentUser?.tenant_id,
      });
    }
  }, [user, tenant, bulkMode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      console.log('🚀 UserModal: Starting form submission', {
        mode,
        userId: user?.id,
        currentUserRole: currentUser?.role,
        currentUserTenantId: currentUser?.tenant_id,
        formDataTenantId: formData.tenant_id,
        isAdmin,
        isSchoolManager
      });

      // التحقق من الحقول المطلوبة
      if (!formData.name || formData.name.trim() === "") {
        alert("الاسم مطلوب");
        return;
      }

      if (!formData.email || formData.email.trim() === "") {
        alert("البريد الإلكتروني مطلوب");
        return;
      }

      // التحقق من صيغة البريد الإلكتروني
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email.trim())) {
        alert("يرجى إدخال بريد إلكتروني صحيح");
        return;
      }

      // التحقق من كلمة المرور للمستخدمين الجدد
      if (mode === "create" && (!formData.password || formData.password.trim() === "")) {
        alert("كلمة المرور مطلوبة");
        return;
      }

      if (mode === "create" && formData.password && formData.password.length < 6) {
        alert("كلمة المرور يجب أن تكون 6 أحرف على الأقل");
        return;
      }

      // تحديد tenant_id المناسب
      let finalTenantId = formData.tenant_id;

      if (mode === "create") {
        if (isAdmin) {
          // الأدمن يمكنه اختيار أي مدرسة أو عدم اختيار (null)
          finalTenantId = formData.tenant_id || null;
        } else if (isSchoolManager) {
          // مدير المدرسة يمكنه إنشاء مستخدمين لمدرسته فقط
          finalTenantId = currentUser?.tenant_id;
          if (!finalTenantId) {
            throw new Error("لا يمكن تحديد المدرسة. يرجى تسجيل الدخول مرة أخرى.");
          }
        } else {
          throw new Error("ليس لديك صلاحية لإنشاء مستخدمين جدد.");
        }
      }

      const submitData = {
        ...formData,
        tenant_id: finalTenantId,
        email: formData.email.trim(),
        name: formData.name.trim(),
      };

      console.log('📤 UserModal: Submitting final data:', {
        ...submitData,
        password: submitData.password ? '***' : undefined
      });

      await onSubmit(submitData);
      onClose();

    } catch (error) {
      console.error("💥 Error submitting user:", error);

      let errorMessage = "حدث خطأ غير متوقع";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === "string") {
        errorMessage = error;
      } else if (typeof error === "object" && error !== null) {
        errorMessage = JSON.stringify(error);
      }

      alert("خطأ: " + errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {mode === "view" ? "عرض المستخدم" :
             mode === "edit" ? "تعديل المستخدم" :
             "إضافة مستخدم جديد"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4">
          <div className="space-y-4">
            {/* الاسم */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                الاسم *
              </label>
              <input
                type="text"
                id="name"
                value={formData.name || ""}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                required
                disabled={mode === "view"}
                placeholder="أدخل اسم المستخدم"
              />
            </div>

            {/* البريد الإلكتروني */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                البريد الإلكتروني *
              </label>
              <input
                type="email"
                id="email"
                value={formData.email || ""}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                required
                disabled={mode === "view"}
                placeholder="<EMAIL>"
              />
            </div>

            {/* اختيار المدرسة (للأدمن فقط) */}
            {isAdmin && mode === "create" && (
              <div>
                <label htmlFor="tenant_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  المدرسة
                </label>
                <select
                  id="tenant_id"
                  value={formData.tenant_id || ""}
                  onChange={(e) => setFormData({ ...formData, tenant_id: e.target.value || null })}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                >
                  <option value="">بدون مدرسة محددة</option>
                  {tenants?.map((tenant) => (
                    <option key={tenant.id} value={tenant.id}>
                      {tenant.name}
                    </option>
                  ))}
                </select>
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  اختر مدرسة محددة أو اتركها فارغة لمستخدم عام
                </p>
              </div>
            )}

            {/* عرض المدرسة (للعرض والتعديل) */}
            {(mode === "view" || mode === "edit") && formData.tenant_id && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  المدرسة
                </label>
                <div className="mt-1 p-2 bg-gray-50 dark:bg-gray-700 rounded-md text-sm text-gray-900 dark:text-white">
                  {tenants?.find(t => t.id === formData.tenant_id)?.name || "غير محدد"}
                </div>
              </div>
            )}

            {/* الدور */}
            <div>
              <label htmlFor="role" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                الدور *
              </label>
              <select
                id="role"
                value={formData.role || "supervisor"}
                onChange={(e) => setFormData({ ...formData, role: e.target.value as UserRole })}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                required
                disabled={mode === "view"}
              >
                {isAdmin && <option value="admin">مدير النظام</option>}
                <option value="school_manager">مدير المدرسة</option>
                <option value="supervisor">مشرف</option>
                <option value="driver">سائق</option>
                <option value="parent">ولي أمر</option>
              </select>
            </div>

            {/* رقم الهاتف */}
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                رقم الهاتف
              </label>
              <input
                type="tel"
                id="phone"
                value={formData.phone || ""}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                disabled={mode === "view"}
                placeholder="+966501234567"
              />
            </div>

            {/* كلمة المرور */}
            {mode !== "view" && (
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {mode === "edit" ? "كلمة المرور الجديدة" : "كلمة المرور"}
                  {mode === "create" && " *"}
                </label>
                <input
                  type="password"
                  id="password"
                  value={formData.password || ""}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  required={mode === "create"}
                  placeholder={mode === "edit" ? "اتركها فارغة للاحتفاظ بكلمة المرور الحالية" : "أدخل كلمة مرور قوية"}
                  minLength={6}
                />
                {mode === "create" && (
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    يجب أن تكون كلمة المرور 6 أحرف على الأقل
                  </p>
                )}
              </div>
            )}

            {/* حالة التفعيل */}
            {mode !== "view" && (
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={formData.is_active ?? true}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="is_active" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  حساب نشط
                </label>
              </div>
            )}

            {/* عرض حالة التفعيل */}
            {mode === "view" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  حالة الحساب
                </label>
                <div className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  formData.is_active
                    ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                    : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                }`}>
                  {formData.is_active ? "نشط" : "غير نشط"}
                </div>
              </div>
            )}

          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              إلغاء
            </Button>
            {mode !== "view" && (
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "جاري الحفظ..." :
                 mode === "create" ? "إنشاء المستخدم" : "حفظ التغييرات"}
              </Button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};
