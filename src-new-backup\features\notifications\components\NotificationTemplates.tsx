import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  MessageSquare,
  Plus,
  Edit,
  Trash2,
  Copy,
  Search,
  Filter,
  Save,
  X,
  AlertTriangle,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { usePermissions } from "../../hooks/usePermissions";
import { Permission } from "../../lib/rbac";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/database.types";

interface NotificationTemplatesProps {
  className?: string;
}

interface TemplateFormData {
  name: string;
  type: "geofence" | "attendance" | "maintenance" | "announcements";
  title: string;
  message: string;
  variables: string[];
}

const defaultFormData: TemplateFormData = {
  name: "",
  type: "geofence",
  title: "",
  message: "",
  variables: [],
};

const templateTypes = [
  { value: "geofence", label: "Location Alerts" },
  { value: "attendance", label: "Attendance" },
  { value: "maintenance", label: "Maintenance" },
  { value: "announcements", label: "Announcements" },
];

const commonVariables = {
  geofence: ["busNumber", "stopName", "studentName", "time"],
  attendance: ["studentName", "busNumber", "time", "date"],
  maintenance: ["busNumber", "maintenanceType", "scheduledDate", "cost"],
  announcements: ["title", "date", "schoolName", "contactInfo"],
};

export const NotificationTemplates: React.FC<NotificationTemplatesProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { hasPermission } = usePermissions();
  const [templates, setTemplates] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<any | null>(null);
  const [formData, setFormData] = useState<TemplateFormData>(defaultFormData);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    if (hasPermission(Permission.NOTIFICATIONS_MANAGE_TEMPLATES)) {
      fetchTemplates();
    } else {
      setLoading(false);
    }
  }, [user, hasPermission]);

  const fetchTemplates = async () => {
    if (!user?.tenant_id) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("notification_templates")
        .select("*")
        .eq("tenant_id", user.tenant_id)
        .order("created_at", { ascending: false });

      if (error) throw error;
      setTemplates(data || []);
    } catch (error) {
      console.error("Error fetching templates:", error);
    } finally {
      setLoading(false);
    }
  };

  const openModal = (template?: any) => {
    if (template) {
      setEditingTemplate(template);
      setFormData({
        name: template.name,
        type: template.type as any,
        title: template.title,
        message: template.message,
        variables: template.variables || [],
      });
    } else {
      setEditingTemplate(null);
      setFormData(defaultFormData);
    }
    setError("");
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setEditingTemplate(null);
    setFormData(defaultFormData);
    setError("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !formData.name?.trim() ||
      !formData.title?.trim() ||
      !formData.message?.trim()
    ) {
      setError(t("common.requiredFieldsMissing"));
      return;
    }

    if (!user?.tenant_id) {
      setError("User tenant information is missing");
      return;
    }

    setSaving(true);
    setError("");

    try {
      const templateData = {
        name: formData.name.trim(),
        type: formData.type,
        title: formData.title.trim(),
        message: formData.message.trim(),
        variables: formData.variables || [],
        tenant_id: user.tenant_id,
        is_active: true,
      };

      if (editingTemplate) {
        const { error } = await supabase
          .from("notification_templates")
          .update({
            ...templateData,
            updated_at: new Date().toISOString(),
          })
          .eq("id", editingTemplate.id);

        if (error) throw error;
      } else {
        const { error } = await supabase
          .from("notification_templates")
          .insert(templateData);

        if (error) throw error;
      }

      await fetchTemplates();
      closeModal();
    } catch (error: any) {
      console.error("Error saving template:", error);
      setError(error.message || "Failed to save template");
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (templateId: string) => {
    if (!window.confirm(t("notifications.deleteTemplateConfirmation"))) return;

    try {
      const { error } = await supabase
        .from("notification_templates")
        .delete()
        .eq("id", templateId);

      if (error) throw error;
      await fetchTemplates();
    } catch (error) {
      console.error("Error deleting template:", error);
    }
  };

  const handleDuplicate = (template: any) => {
    setEditingTemplate(null);
    setFormData({
      name: `${template.name} (Copy)`,
      type: template.type as any,
      title: template.title,
      message: template.message,
      variables: template.variables || [],
    });
    setIsModalOpen(true);
  };

  const insertVariable = (variable: string) => {
    const newMessage = formData.message + `{{${variable}}}`;
    setFormData((prev) => ({ ...prev, message: newMessage }));

    if (!formData.variables.includes(variable)) {
      setFormData((prev) => ({
        ...prev,
        variables: [...prev.variables, variable],
      }));
    }
  };

  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.message.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType = filterType === "all" || template.type === filterType;

    return matchesSearch && matchesType;
  });

  if (!hasPermission(Permission.NOTIFICATIONS_MANAGE_TEMPLATES)) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <AlertTriangle size={48} className="mx-auto mb-4 text-yellow-500" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {t("notifications.adminOnly")}
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          {t("notifications.adminOnlyDescription")}
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
            <MessageSquare size={20} className="mr-2" />
            {t("notifications.templates")}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {t("notifications.templatesDescription")}
          </p>
        </div>
        <Button onClick={() => openModal()} leftIcon={<Plus size={16} />}>
          {t("notifications.createTemplate")}
        </Button>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={16} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder={t("notifications.searchTemplates")}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Filter size={16} className="text-gray-400" />
            </div>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="block pl-10 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="all">{t("common.all")} Types</option>
              {templateTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Templates List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        {filteredTemplates.length === 0 ? (
          <div className="p-8 text-center text-gray-500 dark:text-gray-400">
            <MessageSquare size={48} className="mx-auto mb-4 opacity-50" />
            {templates.length === 0 ? (
              <div>
                <p className="mb-2">{t("notifications.noTemplates")}</p>
                <p className="text-sm">
                  {t("notifications.createFirstTemplate")}
                </p>
              </div>
            ) : (
              <p>{t("notifications.noTemplatesFound")}</p>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredTemplates.map((template) => (
              <div key={template.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {template.name}
                      </h3>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400">
                        {
                          templateTypes.find((t) => t.value === template.type)
                            ?.label
                        }
                      </span>
                      {template.is_active && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                          {t("notifications.activeTemplate")}
                        </span>
                      )}
                    </div>
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {template.title}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      {template.message}
                    </p>
                    {template.variables && template.variables.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {template.variables.map((variable) => (
                          <span
                            key={variable}
                            className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                          >
                            {`{{${variable}}}`}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => openModal(template)}
                      title={t("notifications.editTemplate")}
                    >
                      <Edit size={16} />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleDuplicate(template)}
                      title={t("notifications.duplicate")}
                    >
                      <Copy size={16} />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleDelete(template.id)}
                      className="text-red-600 hover:text-red-700"
                      title={t("common.delete")}
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Template Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center border-b border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {editingTemplate
                  ? t("notifications.editTemplate")
                  : t("notifications.createTemplate")}
              </h2>
              <Button variant="ghost" size="sm" onClick={closeModal}>
                <X size={20} />
              </Button>
            </div>

            <form onSubmit={handleSubmit} className="p-6">
              {error && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3 mb-4">
                  <p className="text-sm text-red-600 dark:text-red-400">
                    {error}
                  </p>
                </div>
              )}

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t("notifications.templateName")}
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, name: e.target.value }))
                    }
                    className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t("notifications.type")}
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        type: e.target.value as any,
                      }))
                    }
                    className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  >
                    {templateTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t("notifications.title")}
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        title: e.target.value,
                      }))
                    }
                    className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t("notifications.message")}
                  </label>
                  <textarea
                    value={formData.message}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        message: e.target.value,
                      }))
                    }
                    rows={4}
                    className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t("notifications.variables")}
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                    {t("notifications.clickToInsert")}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {commonVariables[formData.type].map((variable) => (
                      <button
                        key={variable}
                        type="button"
                        onClick={() => insertVariable(variable)}
                        className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400 hover:bg-primary-200 dark:hover:bg-primary-900/40 transition-colors"
                      >
                        {`{{${variable}}}`}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={closeModal}
                  disabled={saving}
                >
                  {t("common.cancel")}
                </Button>
                <Button
                  type="submit"
                  disabled={saving}
                  leftIcon={<Save size={16} />}
                >
                  {saving ? t("common.saving") : t("common.save")}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationTemplates;
