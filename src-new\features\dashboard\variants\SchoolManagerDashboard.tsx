/**
 * School Manager Dashboard Component
 * Provides comprehensive school-level management view with enhanced RBAC
 */

import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import {
  Users,
  Bus,
  Route,
  ClipboardCheck,
  TrendingUp,
  AlertTriangle,
  MapPin,
  Bell,
  School,
  BarChart3,
  Settings,
  Calendar,
  FileText,
} from "lucide-react";
import { StatCard } from "../dashboard/StatCard";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { useRBACEnhancedSecurity } from "../../hooks/useRBACEnhancedSecurity";
import { EnhancedPermissionGuard } from "../auth/EnhancedPermissionGuard";
import { UserRole } from "../../types";
import { Permission, ResourceType } from "../../lib/rbac";

interface SchoolManagerDashboardProps {
  className?: string;
}

export const SchoolManagerDashboard: React.FC<SchoolManagerDashboardProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const { users, buses, routes, students, attendance } = useDatabase();
  const { filterDataByRole, logSecurityEvent, canAccessRoute } =
    useRBACEnhancedSecurity();

  // Log school manager dashboard access
  React.useEffect(() => {
    logSecurityEvent("school_manager_dashboard_access", {
      timestamp: new Date().toISOString(),
      component: "SchoolManagerDashboard",
      tenantId: user?.tenant_id,
    });
  }, [logSecurityEvent, user?.tenant_id]);

  // Filter data to school scope with enhanced security
  const stats = useMemo(() => {
    const filteredUsers = filterDataByRole(
      users,
      ResourceType.USER,
      "id",
      "normal",
    );
    const filteredBuses = filterDataByRole(
      buses,
      ResourceType.BUS,
      "driver_id",
      "normal",
    );
    const filteredRoutes = filterDataByRole(
      routes,
      ResourceType.ROUTE,
      undefined,
      "normal",
    );
    const filteredStudents = filterDataByRole(
      students,
      ResourceType.STUDENT,
      "parent_id",
      "normal",
    );
    const filteredAttendance = filterDataByRole(
      attendance,
      ResourceType.ATTENDANCE,
      "student_id",
      "normal",
    );

    // Get today's attendance
    const today = new Date().toDateString();
    const todayAttendance = filteredAttendance.filter(
      (a) => new Date(a.recorded_at).toDateString() === today,
    );

    return {
      totalUsers: filteredUsers.length,
      activeUsers: filteredUsers.filter((u) => u.is_active).length,
      totalBuses: filteredBuses.length,
      activeBuses: filteredBuses.filter((b) => b.is_active).length,
      totalRoutes: filteredRoutes.length,
      activeRoutes: filteredRoutes.filter((r) => r.is_active).length,
      totalStudents: filteredStudents.length,
      activeStudents: filteredStudents.filter((s) => s.is_active).length,
      drivers: filteredUsers.filter((u) => u.role === UserRole.DRIVER).length,
      parents: filteredUsers.filter((u) => u.role === UserRole.PARENT).length,
      supervisors: filteredUsers.filter((u) => u.role === UserRole.SUPERVISOR)
        .length,
      busesWithDrivers: filteredBuses.filter((b) => b.driver_id).length,
      routesWithBuses: filteredRoutes.filter((r) => r.bus_id).length,
      todayAttendance: todayAttendance.length,
      presentToday: todayAttendance.filter((a) => a.type === "pickup").length,
    };
  }, [users, buses, routes, students, attendance, filterDataByRole]);

  // Get tenant branding
  const tenantName =
    tenant?.settings?.branding?.schoolName || tenant?.name || "School";
  const tenantLogo = tenant?.settings?.branding?.logo || tenant?.logo_url;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Enhanced School Overview Header */}
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {tenantLogo ? (
              <img
                src={tenantLogo}
                alt={tenantName}
                className="h-12 w-12 rounded-lg object-cover bg-white p-1"
              />
            ) : (
              <div className="h-12 w-12 bg-blue-700 rounded-lg flex items-center justify-center">
                <School className="h-6 w-6" />
              </div>
            )}
            <div>
              <h1 className="text-2xl font-bold">{tenantName}</h1>
              <p className="text-blue-100">
                {tenant?.settings?.branding?.tagline ||
                  "School Management Dashboard"}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-700 text-blue-100">
              <BarChart3 className="h-3 w-3 mr-1" />
              School Manager
            </span>
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-500 text-white">
              Active
            </span>
          </div>
        </div>
      </div>

      {/* Enhanced Key Metrics with Permission Guards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <EnhancedPermissionGuard
          permissions={[Permission.STUDENTS_VIEW_TENANT]}
          componentKey="StudentModal.view"
          auditAccess={true}
        >
          <StatCard
            title="Total Students"
            value={stats.totalStudents}
            icon={<Users size={24} />}
            trend={{
              value: stats.activeStudents,
              isPositive: true,
              label: "Active",
            }}
            className="bg-gradient-to-r from-blue-500 to-blue-600 text-white"
          />
        </EnhancedPermissionGuard>

        <EnhancedPermissionGuard
          permissions={[Permission.BUSES_VIEW_TENANT]}
          componentKey="BusModal.view"
          auditAccess={true}
        >
          <StatCard
            title="School Buses"
            value={stats.totalBuses}
            icon={<Bus size={24} />}
            trend={{
              value: stats.activeBuses,
              isPositive: true,
              label: "Active",
            }}
            className="bg-gradient-to-r from-green-500 to-green-600 text-white"
          />
        </EnhancedPermissionGuard>

        <EnhancedPermissionGuard
          permissions={[Permission.ROUTES_VIEW_TENANT]}
          componentKey="RouteModal.view"
          auditAccess={true}
        >
          <StatCard
            title="Active Routes"
            value={stats.totalRoutes}
            icon={<Route size={24} />}
            trend={{
              value: stats.activeRoutes,
              isPositive: true,
              label: "Running",
            }}
            className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white"
          />
        </EnhancedPermissionGuard>

        <EnhancedPermissionGuard
          permissions={[Permission.USERS_VIEW_TENANT]}
          componentKey="UserModal.view"
          auditAccess={true}
        >
          <StatCard
            title="Staff Members"
            value={stats.totalUsers}
            icon={<Users size={24} />}
            trend={{
              value: stats.activeUsers,
              isPositive: true,
              label: "Active",
            }}
            className="bg-gradient-to-r from-purple-500 to-purple-600 text-white"
          />
        </EnhancedPermissionGuard>
      </div>

      {/* Enhanced Staff Distribution */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <StatCard
          title="Drivers"
          value={stats.drivers}
          icon={<Bus size={24} />}
          className="border-blue-200 bg-blue-50 dark:bg-blue-900/20"
        />
        <StatCard
          title="Parents"
          value={stats.parents}
          icon={<Users size={24} />}
          className="border-green-200 bg-green-50 dark:bg-green-900/20"
        />
        <StatCard
          title="Supervisors"
          value={stats.supervisors}
          icon={<ClipboardCheck size={24} />}
          className="border-purple-200 bg-purple-50 dark:bg-purple-900/20"
        />
        <StatCard
          title="Today's Attendance"
          value={`${stats.presentToday}/${stats.totalStudents}`}
          icon={<Calendar size={24} />}
          className="border-orange-200 bg-orange-50 dark:bg-orange-900/20"
        />
      </div>

      {/* Enhanced Operational Status and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Fleet Status
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Buses with Drivers
              </span>
              <div className="flex items-center">
                <span className="text-sm font-medium text-gray-900 dark:text-white mr-2">
                  {stats.busesWithDrivers}/{stats.totalBuses}
                </span>
                <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${stats.totalBuses > 0 ? (stats.busesWithDrivers / stats.totalBuses) * 100 : 0}%`,
                    }}
                  ></div>
                </div>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Routes with Buses
              </span>
              <div className="flex items-center">
                <span className="text-sm font-medium text-gray-900 dark:text-white mr-2">
                  {stats.routesWithBuses}/{stats.totalRoutes}
                </span>
                <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${stats.totalRoutes > 0 ? (stats.routesWithBuses / stats.totalRoutes) * 100 : 0}%`,
                    }}
                  ></div>
                </div>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Today's Attendance Rate
              </span>
              <div className="flex items-center">
                <span className="text-sm font-medium text-gray-900 dark:text-white mr-2">
                  {stats.totalStudents > 0
                    ? Math.round(
                        (stats.presentToday / stats.totalStudents) * 100,
                      )
                    : 0}
                  %
                </span>
                <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${stats.totalStudents > 0 ? (stats.presentToday / stats.totalStudents) * 100 : 0}%`,
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Quick Actions
          </h3>
          <div className="space-y-3">
            {canAccessRoute("/dashboard/users").allowed && (
              <Link
                to="/dashboard/users"
                className="w-full flex items-center justify-between p-3 text-left bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
              >
                <div className="flex items-center">
                  <Users className="h-5 w-5 text-blue-500 mr-3" />
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    Manage Users
                  </span>
                </div>
              </Link>
            )}

            {canAccessRoute("/dashboard/buses").allowed && (
              <Link
                to="/dashboard/buses"
                className="w-full flex items-center justify-between p-3 text-left bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
              >
                <div className="flex items-center">
                  <Bus className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    Fleet Management
                  </span>
                </div>
              </Link>
            )}

            {canAccessRoute("/dashboard/tracking").allowed && (
              <Link
                to="/dashboard/tracking"
                className="w-full flex items-center justify-between p-3 text-left bg-yellow-50 dark:bg-yellow-900/20 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors"
              >
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 text-yellow-500 mr-3" />
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    Live Tracking
                  </span>
                </div>
              </Link>
            )}

            {canAccessRoute("/dashboard/reports").allowed && (
              <Link
                to="/dashboard/reports"
                className="w-full flex items-center justify-between p-3 text-left bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors"
              >
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-purple-500 mr-3" />
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    View Reports
                  </span>
                </div>
              </Link>
            )}
          </div>
        </div>
      </div>

      {/* System Alerts and Notifications */}
      <EnhancedPermissionGuard
        permissions={[
          Permission.NOTIFICATIONS_VIEW_ALL,
          Permission.NOTIFICATIONS_SEND_TENANT,
        ]}
        componentKey="NotificationTemplates"
        fallback={
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 text-center">
            <Bell className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Notification management requires additional permissions.
            </p>
          </div>
        }
      >
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <Bell className="h-5 w-5 mr-2" />
            System Alerts
          </h3>
          <div className="space-y-3">
            {stats.busesWithDrivers < stats.totalBuses && (
              <div className="flex items-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-yellow-500 mr-3" />
                <div>
                  <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    {stats.totalBuses - stats.busesWithDrivers} buses need
                    driver assignment
                  </p>
                  <p className="text-xs text-yellow-600 dark:text-yellow-300">
                    Assign drivers to ensure full fleet operation
                  </p>
                </div>
              </div>
            )}

            {stats.routesWithBuses < stats.totalRoutes && (
              <div className="flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-blue-500 mr-3" />
                <div>
                  <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    {stats.totalRoutes - stats.routesWithBuses} routes need bus
                    assignment
                  </p>
                  <p className="text-xs text-blue-600 dark:text-blue-300">
                    Assign buses to complete route setup
                  </p>
                </div>
              </div>
            )}

            {stats.busesWithDrivers === stats.totalBuses &&
              stats.routesWithBuses === stats.totalRoutes && (
                <div className="flex items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-green-500 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-green-800 dark:text-green-200">
                      All systems operational
                    </p>
                    <p className="text-xs text-green-600 dark:text-green-300">
                      Fleet is fully configured and ready
                    </p>
                  </div>
                </div>
              )}
          </div>
        </div>
      </EnhancedPermissionGuard>
    </div>
  );
};

export default SchoolManagerDashboard;
