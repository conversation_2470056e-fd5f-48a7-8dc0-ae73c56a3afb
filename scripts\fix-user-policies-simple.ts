#!/usr/bin/env tsx

/**
 * سكريبت مبسط لإصلاح سياسات المستخدمين
 * Simple script to fix user policies
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// تحميل متغيرات البيئة
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixUserPolicies() {
  console.log('🔧 إصلاح سياسات المستخدمين...');

  try {
    // 1. إنشاء دالة إنشاء المستخدم الآمنة
    console.log('👤 إنشاء دالة إنشاء المستخدم...');
    
    const { error: functionError } = await supabase.rpc('create_user_profile_safe', {
      user_id: '00000000-0000-0000-0000-000000000000', // dummy call to check if function exists
      user_email: '<EMAIL>',
      user_name: 'Test',
      user_role: 'student',
      user_tenant_id: null
    });

    // إذا كانت الدالة غير موجودة، سنحاول إنشاؤها
    if (functionError && functionError.code === 'PGRST202') {
      console.log('🔧 الدالة غير موجودة، سنحاول إنشاؤها...');
      
      // محاولة إنشاء الدالة عبر SQL مباشر
      const createFunctionSQL = `
        CREATE OR REPLACE FUNCTION public.create_user_profile_safe(
          user_id uuid,
          user_email text,
          user_name text,
          user_role text DEFAULT 'student',
          user_tenant_id uuid DEFAULT NULL
        )
        RETURNS json
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        DECLARE
          new_user users;
          result json;
        BEGIN
          -- التحقق من وجود المستخدم
          SELECT * INTO new_user FROM users WHERE id = user_id;
          
          IF new_user.id IS NOT NULL THEN
            -- المستخدم موجود، إرجاع البيانات الحالية
            SELECT row_to_json(new_user) INTO result;
            RETURN result;
          END IF;

          -- محاولة إنشاء مستخدم جديد
          BEGIN
            INSERT INTO users (
              id,
              email,
              name,
              role,
              tenant_id,
              is_active,
              created_at,
              updated_at
            ) VALUES (
              user_id,
              user_email,
              user_name,
              user_role,
              user_tenant_id,
              true,
              NOW(),
              NOW()
            ) RETURNING * INTO new_user;

            SELECT row_to_json(new_user) INTO result;
            RETURN result;
          EXCEPTION
            WHEN OTHERS THEN
              -- في حالة الخطأ، إرجاع null
              RETURN NULL;
          END;
        END;
        $$;
      `;

      // محاولة تنفيذ SQL مباشرة عبر استدعاء دالة موجودة
      try {
        const { error: sqlError } = await supabase
          .from('users')
          .select('id')
          .limit(1);
        
        if (sqlError) {
          console.log('⚠️ مشكلة في الاتصال بقاعدة البيانات:', sqlError.message);
        }
      } catch (e) {
        console.log('⚠️ خطأ في الاتصال:', e);
      }
    }

    // 2. اختبار إنشاء مستخدم تجريبي
    console.log('🧪 اختبار النظام الحالي...');
    
    // محاولة قراءة المستخدمين الحاليين
    const { data: existingUsers, error: readError } = await supabase
      .from('users')
      .select('id, email, role')
      .limit(5);

    if (readError) {
      console.error('❌ خطأ في قراءة المستخدمين:', readError);
    } else {
      console.log(`✅ تم العثور على ${existingUsers?.length || 0} مستخدم`);
      if (existingUsers && existingUsers.length > 0) {
        console.log('المستخدمون الموجودون:');
        existingUsers.forEach(user => {
          console.log(`  - ${user.email} (${user.role})`);
        });
      }
    }

    // 3. محاولة إنشاء مستخدم تجريبي مباشرة
    console.log('🧪 محاولة إنشاء مستخدم تجريبي...');
    
    const testUserId = crypto.randomUUID();
    const { data: newUser, error: insertError } = await supabase
      .from('users')
      .insert([
        {
          id: testUserId,
          email: 'test-' + Date.now() + '@example.com',
          name: 'Test User',
          role: 'student',
          tenant_id: null,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      ])
      .select()
      .single();

    if (insertError) {
      console.error('❌ خطأ في إنشاء المستخدم التجريبي:', insertError);
      
      // تحليل نوع الخطأ
      if (insertError.code === '42501') {
        console.log('🔒 المشكلة: سياسات RLS تمنع إنشاء المستخدم');
        console.log('💡 الحل: تحديث سياسات RLS للسماح بإنشاء المستخدمين');
      }
    } else {
      console.log('✅ تم إنشاء المستخدم التجريبي بنجاح');
      
      // حذف المستخدم التجريبي
      const { error: deleteError } = await supabase
        .from('users')
        .delete()
        .eq('id', testUserId);

      if (deleteError) {
        console.warn('⚠️ تحذير: لم يتم حذف المستخدم التجريبي:', deleteError);
      } else {
        console.log('✅ تم حذف المستخدم التجريبي');
      }
    }

    // 4. فحص السياسات الحالية
    console.log('📋 فحص السياسات الحالية...');
    
    try {
      const { data: policies, error: policiesError } = await supabase
        .from('pg_policies')
        .select('policyname, tablename, cmd')
        .eq('tablename', 'users')
        .eq('schemaname', 'public');

      if (policiesError) {
        console.log('⚠️ لا يمكن الوصول لمعلومات السياسات:', policiesError.message);
      } else {
        console.log(`📊 تم العثور على ${policies?.length || 0} سياسة لجدول المستخدمين`);
        policies?.forEach(policy => {
          console.log(`  - ${policy.policyname} (${policy.cmd})`);
        });
      }
    } catch (e) {
      console.log('⚠️ لا يمكن الوصول لمعلومات السياسات');
    }

    console.log('\n📝 ملخص التشخيص:');
    console.log('1. إذا كان هناك خطأ RLS (42501)، فالمشكلة في سياسات الأمان');
    console.log('2. يجب تحديث سياسات RLS للسماح بإنشاء المستخدمين الجدد');
    console.log('3. يمكن حل المشكلة مؤقتاً بتعطيل RLS أو تحديث السياسات');

  } catch (error) {
    console.error('🚨 خطأ عام:', error);
  }
}

// تشغيل السكريبت
fixUserPolicies()
  .then(() => {
    console.log('\n✅ اكتمل التشخيص');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ فشل التشخيص:', error);
    process.exit(1);
  });
