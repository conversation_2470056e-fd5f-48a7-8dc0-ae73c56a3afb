/**
 * Advanced Theme Provider
 * Comprehensive theme management with tenant support and RTL
 * Phase 3: UI/UX Enhancement
 */

import React, { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import { BaseTheme, lightTheme, darkTheme, themeUtils } from '../../design-system/themes/base/BaseTheme';
import { TenantTheme, TenantThemeConfig, tenantThemeManager } from '../../design-system/themes/tenant/TenantTheme';
import { RTLProvider } from '../../i18n/rtl/RTLProvider';

export interface ThemeContextValue {
  // Current theme
  theme: BaseTheme | TenantTheme;
  mode: 'light' | 'dark';
  
  // Theme management
  setTheme: (theme: BaseTheme | TenantTheme) => void;
  setMode: (mode: 'light' | 'dark') => void;
  toggleMode: () => void;
  
  // Tenant theme management
  tenantId: string | null;
  setTenantTheme: (tenantId: string, config: TenantThemeConfig) => void;
  applyTenantTheme: (tenantId: string) => boolean;
  removeTenantTheme: (tenantId: string) => void;
  
  // Theme utilities
  getToken: (path: string) => any;
  generateCSS: () => string;
  exportTheme: () => any;
  
  // System preferences
  systemPreference: 'light' | 'dark';
  respectSystemPreference: boolean;
  setRespectSystemPreference: (respect: boolean) => void;
  
  // Theme persistence
  persistTheme: boolean;
  setPersistTheme: (persist: boolean) => void;
}

const ThemeContext = createContext<ThemeContextValue | null>(null);

export interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: BaseTheme;
  defaultMode?: 'light' | 'dark';
  tenantId?: string;
  persistTheme?: boolean;
  respectSystemPreference?: boolean;
  storageKey?: string;
}

/**
 * Theme storage utilities
 */
const STORAGE_KEYS = {
  THEME_MODE: 'theme-mode',
  TENANT_ID: 'tenant-id',
  RESPECT_SYSTEM: 'respect-system-preference',
  PERSIST_THEME: 'persist-theme',
};

const themeStorage = {
  get: (key: string): string | null => {
    try {
      return localStorage.getItem(key);
    } catch {
      return null;
    }
  },
  
  set: (key: string, value: string): void => {
    try {
      localStorage.setItem(key, value);
    } catch {
      // Ignore storage errors
    }
  },
  
  remove: (key: string): void => {
    try {
      localStorage.removeItem(key);
    } catch {
      // Ignore storage errors
    }
  },
};

/**
 * Detect system theme preference
 */
function getSystemPreference(): 'light' | 'dark' {
  if (typeof window === 'undefined') return 'light';
  
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
}

/**
 * Advanced Theme Provider
 */
export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme,
  defaultMode = 'light',
  tenantId: initialTenantId,
  persistTheme = true,
  respectSystemPreference = true,
  storageKey = 'app-theme',
}) => {
  // System preference detection
  const [systemPreference, setSystemPreference] = useState<'light' | 'dark'>(getSystemPreference);
  const [respectSystem, setRespectSystem] = useState(respectSystemPreference);
  const [persistEnabled, setPersistEnabled] = useState(persistTheme);
  
  // Theme state
  const [currentTheme, setCurrentTheme] = useState<BaseTheme | TenantTheme>(() => {
    // Try to restore from storage
    if (persistTheme) {
      const savedMode = themeStorage.get(STORAGE_KEYS.THEME_MODE) as 'light' | 'dark' | null;
      const savedTenantId = themeStorage.get(STORAGE_KEYS.TENANT_ID);
      const savedRespectSystem = themeStorage.get(STORAGE_KEYS.RESPECT_SYSTEM) === 'true';
      
      if (savedRespectSystem && respectSystemPreference) {
        return getSystemPreference() === 'dark' ? darkTheme : lightTheme;
      }
      
      if (savedTenantId) {
        const tenantTheme = tenantThemeManager.getTenantTheme(savedTenantId);
        if (tenantTheme) return tenantTheme;
      }
      
      if (savedMode) {
        return savedMode === 'dark' ? darkTheme : lightTheme;
      }
    }
    
    return defaultTheme || (defaultMode === 'dark' ? darkTheme : lightTheme);
  });
  
  const [tenantId, setTenantId] = useState<string | null>(initialTenantId || null);

  /**
   * Listen for system preference changes
   */
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      const newPreference = e.matches ? 'dark' : 'light';
      setSystemPreference(newPreference);
      
      if (respectSystem) {
        const newTheme = newPreference === 'dark' ? darkTheme : lightTheme;
        setCurrentTheme(newTheme);
      }
    };
    
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [respectSystem]);

  /**
   * Apply theme to document
   */
  useEffect(() => {
    if ('tenant' in currentTheme) {
      // Apply tenant theme
      tenantThemeManager.setCurrentTheme(currentTheme.tenant.id);
    } else {
      // Apply base theme
      themeUtils.applyTheme(currentTheme);
    }
  }, [currentTheme]);

  /**
   * Persist theme settings
   */
  useEffect(() => {
    if (!persistEnabled) return;
    
    const mode = currentTheme.mode;
    themeStorage.set(STORAGE_KEYS.THEME_MODE, mode);
    themeStorage.set(STORAGE_KEYS.RESPECT_SYSTEM, respectSystem.toString());
    themeStorage.set(STORAGE_KEYS.PERSIST_THEME, persistEnabled.toString());
    
    if (tenantId) {
      themeStorage.set(STORAGE_KEYS.TENANT_ID, tenantId);
    } else {
      themeStorage.remove(STORAGE_KEYS.TENANT_ID);
    }
  }, [currentTheme.mode, tenantId, respectSystem, persistEnabled]);

  /**
   * Set theme
   */
  const setTheme = useCallback((theme: BaseTheme | TenantTheme) => {
    setCurrentTheme(theme);
    
    if ('tenant' in theme) {
      setTenantId(theme.tenant.id);
    } else {
      setTenantId(null);
    }
  }, []);

  /**
   * Set mode
   */
  const setMode = useCallback((mode: 'light' | 'dark') => {
    if ('tenant' in currentTheme) {
      // For tenant themes, create new theme with different mode
      const newConfig = {
        ...currentTheme.tenant,
        baseTheme: mode,
      };
      const newTheme = tenantThemeManager.createTenantTheme(newConfig);
      setCurrentTheme(newTheme);
    } else {
      // For base themes, switch to appropriate theme
      const newTheme = mode === 'dark' ? darkTheme : lightTheme;
      setCurrentTheme(newTheme);
    }
  }, [currentTheme]);

  /**
   * Toggle mode
   */
  const toggleMode = useCallback(() => {
    const newMode = currentTheme.mode === 'light' ? 'dark' : 'light';
    setMode(newMode);
  }, [currentTheme.mode, setMode]);

  /**
   * Set tenant theme
   */
  const setTenantTheme = useCallback((tenantId: string, config: TenantThemeConfig) => {
    const theme = tenantThemeManager.createTenantTheme(config);
    setTheme(theme);
  }, [setTheme]);

  /**
   * Apply tenant theme
   */
  const applyTenantTheme = useCallback((tenantId: string): boolean => {
    const theme = tenantThemeManager.getTenantTheme(tenantId);
    if (theme) {
      setTheme(theme);
      return true;
    }
    return false;
  }, [setTheme]);

  /**
   * Remove tenant theme
   */
  const removeTenantTheme = useCallback((tenantId: string) => {
    tenantThemeManager.clearThemes();
    // Reset to base theme
    const baseTheme = currentTheme.mode === 'dark' ? darkTheme : lightTheme;
    setTheme(baseTheme);
  }, [currentTheme.mode, setTheme]);

  /**
   * Get token value by path
   */
  const getToken = useCallback((path: string): any => {
    const pathParts = path.split('.');
    let value: any = currentTheme;
    
    for (const part of pathParts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part];
      } else {
        return undefined;
      }
    }
    
    return value;
  }, [currentTheme]);

  /**
   * Generate CSS from current theme
   */
  const generateCSS = useCallback((): string => {
    const properties = themeUtils.generateCSSCustomProperties(currentTheme);
    
    return Object.entries(properties)
      .map(([property, value]) => `  ${property}: ${value};`)
      .join('\n');
  }, [currentTheme]);

  /**
   * Export current theme
   */
  const exportTheme = useCallback(() => {
    if ('tenant' in currentTheme) {
      return {
        type: 'tenant',
        config: currentTheme.tenant,
        branding: currentTheme.branding,
      };
    } else {
      return {
        type: 'base',
        mode: currentTheme.mode,
        theme: currentTheme,
      };
    }
  }, [currentTheme]);

  /**
   * Set respect system preference
   */
  const setRespectSystemPreference = useCallback((respect: boolean) => {
    setRespectSystem(respect);
    
    if (respect) {
      const newTheme = systemPreference === 'dark' ? darkTheme : lightTheme;
      setCurrentTheme(newTheme);
    }
  }, [systemPreference]);

  /**
   * Set persist theme
   */
  const setPersistTheme = useCallback((persist: boolean) => {
    setPersistEnabled(persist);
    
    if (!persist) {
      // Clear stored settings
      Object.values(STORAGE_KEYS).forEach(key => {
        themeStorage.remove(key);
      });
    }
  }, []);

  const contextValue: ThemeContextValue = {
    theme: currentTheme,
    mode: currentTheme.mode,
    setTheme,
    setMode,
    toggleMode,
    tenantId,
    setTenantTheme,
    applyTenantTheme,
    removeTenantTheme,
    getToken,
    generateCSS,
    exportTheme,
    systemPreference,
    respectSystemPreference: respectSystem,
    setRespectSystemPreference,
    persistTheme: persistEnabled,
    setPersistTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <RTLProvider
        defaultDirection={('tenant' in currentTheme && currentTheme.tenant.rtl) ? 'rtl' : 'ltr'}
        defaultLanguage={('tenant' in currentTheme && currentTheme.tenant.locale) || 'en'}
      >
        {children}
      </RTLProvider>
    </ThemeContext.Provider>
  );
};

/**
 * Hook to use theme context
 */
export const useTheme = (): ThemeContextValue => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

/**
 * Hook to use design tokens
 */
export const useTokens = () => {
  const { theme, getToken } = useTheme();
  
  return {
    colors: theme.colors,
    typography: theme.typography,
    spacing: theme.spacing,
    shadows: theme.shadows,
    borderRadius: theme.borderRadius,
    zIndex: theme.zIndex,
    transitions: theme.transitions,
    breakpoints: theme.breakpoints,
    getToken,
  };
};

/**
 * HOC for theme support
 */
export function withTheme<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  const ThemedComponent = (props: P) => {
    const theme = useTheme();
    return <Component {...props} theme={theme} />;
  };

  ThemedComponent.displayName = `withTheme(${Component.displayName || Component.name})`;
  return ThemedComponent;
}
