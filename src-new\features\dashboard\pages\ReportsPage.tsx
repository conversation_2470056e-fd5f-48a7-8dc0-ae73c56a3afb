import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Calendar,
  Filter,
  Download,
  BarChart2,
  Users,
  Bus,
  PieChart,
} from "lucide-react";
import { supabase } from "../../lib/supabase";
import { Button } from "../../components/ui/Button";
import { ResponsiveLayout } from "../../components/layout/ResponsiveLayout";
import { AttendanceReport } from "../../components/reports/AttendanceReport";
import { BusUtilizationReport } from "../../components/reports/BusUtilizationReport";
import { DriverPerformanceReport } from "../../components/reports/DriverPerformanceReport";
import { RouteDelayReport } from "../../components/reports/RouteDelayReport";

type ReportType =
  | "attendance"
  | "utilization"
  | "performance"
  | "delays"
  | "summary";
type TimeFrame = "daily" | "weekly" | "monthly";

interface AttendanceData {
  date: string;
  pickups: number;
  dropoffs: number;
  total: number;
  absentRate: number;
}

interface UtilizationData {
  busId: string;
  plateNumber: string;
  capacity: number;
  utilization: number;
  trips: number;
  fuelConsumption: number;
}

interface SummaryData {
  totalStudents: number;
  totalBuses: number;
  averageAttendance: number;
  averageUtilization: number;
  totalRoutes: number;
}

const ReportsPage: React.FC = () => {
  const { t } = useTranslation();
  const [reportType, setReportType] = useState<ReportType>("attendance");
  const [timeFrame, setTimeFrame] = useState<TimeFrame>("weekly");
  const [startDate, setStartDate] = useState<string>(
    new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0],
  );
  const [endDate, setEndDate] = useState<string>(
    new Date().toISOString().split("T")[0],
  );
  const [schoolId, setSchoolId] = useState<string>("");
  const [schools, setSchools] = useState<{ id: string; name: string }[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(true);

  // Data states
  const [attendanceData, setAttendanceData] = useState<AttendanceData[]>([]);
  const [utilizationData, setUtilizationData] = useState<UtilizationData[]>([]);
  const [summaryData, setSummaryData] = useState<SummaryData | null>(null);

  useEffect(() => {
    fetchSchools();
  }, []);

  useEffect(() => {
    if (reportType === "summary") {
      fetchSummaryData();
    }
  }, [reportType, timeFrame, startDate, endDate, schoolId]);

  const fetchSchools = async () => {
    try {
      const { data, error } = await supabase
        .from("tenants")
        .select("id, name")
        .eq("is_active", true);

      if (error) throw error;

      if (data) {
        setSchools(
          data.map((school) => ({ id: school.id, name: school.name })),
        );
        if (data.length > 0 && !schoolId) {
          setSchoolId(data[0].id);
        }
      }
    } catch (error) {
      console.error("Error fetching schools:", error);
    }
  };

  const fetchAttendanceData = async () => {
    setIsLoading(true);
    try {
      // In a real application, this would fetch actual data from the database
      // For demonstration, we'll generate sample data
      const days = getDateRange(startDate, endDate);
      const sampleData: AttendanceData[] = days.map((date) => {
        const pickups = Math.floor(Math.random() * 50) + 20;
        const dropoffs = Math.floor(Math.random() * 50) + 20;
        const total = pickups + dropoffs;
        const absentRate = Math.floor(Math.random() * 15); // 0-15% absent rate
        return {
          date,
          pickups,
          dropoffs,
          total,
          absentRate,
        };
      });

      setAttendanceData(sampleData);
    } catch (error) {
      console.error("Error fetching attendance data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUtilizationData = async () => {
    setIsLoading(true);
    try {
      // In a real application, this would fetch actual data from the database
      // For demonstration, we'll generate sample data
      const { data, error } = await supabase
        .from("buses")
        .select("id, plate_number, capacity")
        .eq("tenant_id", schoolId)
        .eq("is_active", true);

      if (error) throw error;

      if (data) {
        const sampleData: UtilizationData[] = data.map((bus) => {
          const utilization = Math.floor(Math.random() * 80) + 20; // 20-100%
          const trips = Math.floor(Math.random() * 20) + 5; // 5-25 trips
          const fuelConsumption = Math.floor(Math.random() * 50) + 10; // 10-60 liters
          return {
            busId: bus.id,
            plateNumber: bus.plate_number,
            capacity: bus.capacity,
            utilization,
            trips,
            fuelConsumption,
          };
        });

        setUtilizationData(sampleData);
      }
    } catch (error) {
      console.error("Error fetching utilization data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSummaryData = async () => {
    setIsLoading(true);
    try {
      // In a real application, this would fetch actual summary data
      // For demonstration, we'll generate sample data
      const summaryData: SummaryData = {
        totalStudents: Math.floor(Math.random() * 500) + 200,
        totalBuses: Math.floor(Math.random() * 20) + 5,
        averageAttendance: Math.floor(Math.random() * 20) + 80, // 80-100%
        averageUtilization: Math.floor(Math.random() * 30) + 60, // 60-90%
        totalRoutes: Math.floor(Math.random() * 15) + 5,
      };

      setSummaryData(summaryData);
    } catch (error) {
      console.error("Error fetching summary data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getDateRange = (start: string, end: string): string[] => {
    const dates: string[] = [];
    let currentDate = new Date(start);
    const endDate = new Date(end);

    while (currentDate <= endDate) {
      dates.push(currentDate.toISOString().split("T")[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
  };

  const handleExportCSV = () => {
    let csvContent = "";
    let filename = "";

    if (reportType === "attendance") {
      // Create CSV for attendance data
      csvContent = "Date,Pickups,Dropoffs,Total,Absent Rate (%)\n";
      attendanceData.forEach((row) => {
        csvContent += `${row.date},${row.pickups},${row.dropoffs},${row.total},${row.absentRate}\n`;
      });
      filename = `attendance_report_${startDate}_to_${endDate}.csv`;
    } else if (reportType === "utilization") {
      // Create CSV for utilization data
      csvContent =
        "Bus ID,Plate Number,Capacity,Utilization %,Trips,Fuel Consumption (L)\n";
      utilizationData.forEach((row) => {
        csvContent += `${row.busId},${row.plateNumber},${row.capacity},${row.utilization}%,${row.trips},${row.fuelConsumption}\n`;
      });
      filename = `bus_utilization_report_${startDate}_to_${endDate}.csv`;
    } else {
      // Create CSV for summary data
      if (!summaryData) return;

      csvContent = "Metric,Value\n";
      csvContent += `Total Students,${summaryData.totalStudents}\n`;
      csvContent += `Total Buses,${summaryData.totalBuses}\n`;
      csvContent += `Average Attendance,${summaryData.averageAttendance}%\n`;
      csvContent += `Average Bus Utilization,${summaryData.averageUtilization}%\n`;
      csvContent += `Total Routes,${summaryData.totalRoutes}\n`;
      filename = `summary_report_${startDate}_to_${endDate}.csv`;
    }

    // Create download link
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleTimeFrameChange = (newTimeFrame: TimeFrame) => {
    setTimeFrame(newTimeFrame);

    const today = new Date();
    let start = new Date();

    switch (newTimeFrame) {
      case "daily":
        start = today;
        break;
      case "weekly":
        start = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "monthly":
        start = new Date(
          today.getFullYear(),
          today.getMonth() - 1,
          today.getDate(),
        );
        break;
    }

    setStartDate(start.toISOString().split("T")[0]);
    setEndDate(today.toISOString().split("T")[0]);
  };

  const renderSummaryCards = () => {
    if (!summaryData) return null;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {t("reports.students")}
            </h3>
            <Users size={24} className="text-primary-500" />
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white">
            {summaryData.totalStudents}
          </div>
          <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {t("reports.averageAttendance")}: {summaryData.averageAttendance}%
          </div>
        </div>

        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {t("reports.buses")}
            </h3>
            <Bus size={24} className="text-primary-500" />
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white">
            {summaryData.totalBuses}
          </div>
          <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {t("reports.averageUtilization")}: {summaryData.averageUtilization}%
          </div>
        </div>

        <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {t("reports.routes")}
            </h3>
            <BarChart2 size={24} className="text-primary-500" />
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white">
            {summaryData.totalRoutes}
          </div>
          <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {t("reports.activeRoutes")}:{" "}
            {Math.floor(summaryData.totalRoutes * 0.8)}
          </div>
        </div>
      </div>
    );
  };

  return (
    <ResponsiveLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t("reports.title")}
          </h1>
          <div className="flex space-x-2">
            <Button
              onClick={() => setShowFilters(!showFilters)}
              variant="outline"
              leftIcon={<Filter size={16} />}
            >
              {showFilters
                ? t("reports.hideFilters")
                : t("reports.showFilters")}
            </Button>
            <Button
              onClick={handleExportCSV}
              variant="outline"
              leftIcon={<Download size={16} />}
            >
              {t("reports.exportCSV")}
            </Button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div>
                <label
                  htmlFor="reportType"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {t("reports.reportType")}
                </label>
                <div className="flex flex-wrap gap-2">
                  <button
                    type="button"
                    onClick={() => setReportType("attendance")}
                    className={`flex items-center justify-center px-3 py-1 rounded-md text-sm ${reportType === "attendance" ? "bg-primary-100 text-primary-700 border border-primary-300 dark:bg-primary-900 dark:text-primary-300 dark:border-primary-700" : "bg-gray-100 text-gray-700 border border-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600"}`}
                  >
                    <Users size={14} className="mr-1" />
                    {t("reports.attendance")}
                  </button>
                  <button
                    type="button"
                    onClick={() => setReportType("utilization")}
                    className={`flex items-center justify-center px-3 py-1 rounded-md text-sm ${reportType === "utilization" ? "bg-primary-100 text-primary-700 border border-primary-300 dark:bg-primary-900 dark:text-primary-300 dark:border-primary-700" : "bg-gray-100 text-gray-700 border border-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600"}`}
                  >
                    <Bus size={14} className="mr-1" />
                    {t("reports.utilization")}
                  </button>
                  <button
                    type="button"
                    onClick={() => setReportType("performance")}
                    className={`flex items-center justify-center px-3 py-1 rounded-md text-sm ${reportType === "performance" ? "bg-primary-100 text-primary-700 border border-primary-300 dark:bg-primary-900 dark:text-primary-300 dark:border-primary-700" : "bg-gray-100 text-gray-700 border border-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600"}`}
                  >
                    <Users size={14} className="mr-1" />
                    {t("reports.performance")}
                  </button>
                  <button
                    type="button"
                    onClick={() => setReportType("delays")}
                    className={`flex items-center justify-center px-3 py-1 rounded-md text-sm ${reportType === "delays" ? "bg-primary-100 text-primary-700 border border-primary-300 dark:bg-primary-900 dark:text-primary-300 dark:border-primary-700" : "bg-gray-100 text-gray-700 border border-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600"}`}
                  >
                    <Calendar size={14} className="mr-1" />
                    {t("reports.delays")}
                  </button>
                  <button
                    type="button"
                    onClick={() => setReportType("summary")}
                    className={`flex items-center justify-center px-3 py-1 rounded-md text-sm ${reportType === "summary" ? "bg-primary-100 text-primary-700 border border-primary-300 dark:bg-primary-900 dark:text-primary-300 dark:border-primary-700" : "bg-gray-100 text-gray-700 border border-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600"}`}
                  >
                    <PieChart size={14} className="mr-1" />
                    {t("reports.summary")}
                  </button>
                </div>
              </div>

              <div>
                <label
                  htmlFor="timeFrame"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {t("reports.timeFrame")}
                </label>
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={() => handleTimeFrameChange("daily")}
                    className={`px-3 py-1 rounded-md text-sm ${timeFrame === "daily" ? "bg-primary-100 text-primary-700 border border-primary-300 dark:bg-primary-900 dark:text-primary-300 dark:border-primary-700" : "bg-gray-100 text-gray-700 border border-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600"}`}
                  >
                    {t("reports.daily")}
                  </button>
                  <button
                    type="button"
                    onClick={() => handleTimeFrameChange("weekly")}
                    className={`px-3 py-1 rounded-md text-sm ${timeFrame === "weekly" ? "bg-primary-100 text-primary-700 border border-primary-300 dark:bg-primary-900 dark:text-primary-300 dark:border-primary-700" : "bg-gray-100 text-gray-700 border border-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600"}`}
                  >
                    {t("reports.weekly")}
                  </button>
                  <button
                    type="button"
                    onClick={() => handleTimeFrameChange("monthly")}
                    className={`px-3 py-1 rounded-md text-sm ${timeFrame === "monthly" ? "bg-primary-100 text-primary-700 border border-primary-300 dark:bg-primary-900 dark:text-primary-300 dark:border-primary-700" : "bg-gray-100 text-gray-700 border border-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600"}`}
                  >
                    {t("reports.monthly")}
                  </button>
                </div>
              </div>

              <div>
                <label
                  htmlFor="dateRange"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {t("reports.dateRange")}
                </label>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Calendar size={16} className="text-gray-400" />
                    </div>
                    <input
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                      className="pl-10 w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
                    />
                  </div>
                  <span className="text-gray-500 dark:text-gray-400">-</span>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Calendar size={16} className="text-gray-400" />
                    </div>
                    <input
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                      className="pl-10 w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
                    />
                  </div>
                </div>
              </div>

              <div>
                <label
                  htmlFor="school"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {t("reports.school")}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Filter size={16} className="text-gray-400" />
                  </div>
                  <select
                    id="school"
                    value={schoolId}
                    onChange={(e) => setSchoolId(e.target.value)}
                    className="pl-10 w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  >
                    {schools.map((school) => (
                      <option key={school.id} value={school.id}>
                        {school.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          )}

          {reportType === "attendance" ? (
            <AttendanceReport />
          ) : reportType === "utilization" ? (
            <BusUtilizationReport />
          ) : reportType === "performance" ? (
            <DriverPerformanceReport />
          ) : reportType === "delays" ? (
            <RouteDelayReport />
          ) : isLoading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
            </div>
          ) : reportType === "summary" ? (
            renderSummaryCards()
          ) : (
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <BarChart2 size={20} className="mr-2" />
                {t("reports.attendanceReport")}
              </h2>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                      >
                        {t("reports.date")}
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                      >
                        {t("reports.pickups")}
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                      >
                        {t("reports.dropoffs")}
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                      >
                        {t("reports.total")}
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                      >
                        {t("reports.absentRate")}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {attendanceData.map((row, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                          {row.date}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {row.pickups}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {row.dropoffs}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {row.total}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              row.absentRate > 10
                                ? "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                                : row.absentRate > 5
                                  ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100"
                                  : "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                            }`}
                          >
                            {row.absentRate}%
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </ResponsiveLayout>
  );
};

export { ReportsPage };
export default ReportsPage;
