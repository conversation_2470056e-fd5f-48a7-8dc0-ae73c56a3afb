/**
 * إعدادات RBAC المركزية المبسطة
 * Simplified Centralized RBAC Configuration
 */

import { UserRole } from "../types";
import { Permission } from "./rbac";

/**
 * خريطة صلاحيات المسارات - مبسطة
 * Route Permission Mapping - Simplified
 */
export const ROUTE_PERMISSIONS: Record<string, Permission[]> = {
  "/dashboard": [], // متاح للجميع
  "/dashboard/schools": [Permission.TENANTS_VIEW],
  "/dashboard/users": [Permission.USERS_VIEW],
  "/dashboard/buses": [Permission.BUSES_VIEW],
  "/dashboard/routes": [Permission.ROUTES_VIEW],
  "/dashboard/students": [Permission.STUDENTS_VIEW],
  "/dashboard/attendance": [Permission.ATTENDANCE_VIEW],
  "/dashboard/reports": [Permission.REPORTS_VIEW],
  "/dashboard/notifications": [Permission.NOTIFICATIONS_VIEW],
  "/dashboard/maintenance": [Permission.MAINTENANCE_VIEW],
  "/dashboard/profile": [], // متاح للجميع
  "/dashboard/settings": [], // متاح للجميع
};

/**
 * صلاحيات المكونات - مبسطة
 * Component Permissions - Simplified
 */
export const COMPONENT_PERMISSIONS: Record<string, Permission[]> = {
  "SchoolModal.create": [Permission.TENANTS_CREATE],
  "SchoolModal.edit": [Permission.TENANTS_EDIT],
  "SchoolModal.delete": [Permission.TENANTS_DELETE],

  "UserModal.create": [Permission.USERS_CREATE],
  "UserModal.edit": [Permission.USERS_EDIT],
  "UserModal.delete": [Permission.USERS_DELETE],

  "BusModal.create": [Permission.BUSES_CREATE],
  "BusModal.edit": [Permission.BUSES_EDIT],
  "BusModal.delete": [Permission.BUSES_DELETE],

  "StudentModal.create": [Permission.STUDENTS_CREATE],
  "StudentModal.edit": [Permission.STUDENTS_EDIT],
  "StudentModal.delete": [Permission.STUDENTS_DELETE],

  "RouteModal.create": [Permission.ROUTES_CREATE],
  "RouteModal.edit": [Permission.ROUTES_EDIT],
  "RouteModal.delete": [Permission.ROUTES_DELETE],

  "AttendanceForm": [Permission.ATTENDANCE_CREATE],
  "ReportExport": [Permission.REPORTS_EXPORT],
  "NotificationSend": [Permission.NOTIFICATIONS_CREATE],
};

/**
 * إعدادات التنقل - مبسطة
 * Navigation Configuration - Simplified
 */
export const NAVIGATION_CONFIG: Record<string, { permissions: Permission[]; roles: UserRole[] }> = {
  dashboard: { permissions: [], roles: ['admin', 'school_manager', 'supervisor', 'driver', 'parent', 'student'] },
  schools: { permissions: [Permission.TENANTS_VIEW], roles: ['admin'] },
  users: { permissions: [Permission.USERS_VIEW], roles: ['admin', 'school_manager'] },
  buses: { permissions: [Permission.BUSES_VIEW], roles: ['admin', 'school_manager', 'supervisor', 'driver'] },
  routes: { permissions: [Permission.ROUTES_VIEW], roles: ['admin', 'school_manager', 'supervisor', 'driver'] },
  students: { permissions: [Permission.STUDENTS_VIEW], roles: ['admin', 'school_manager', 'supervisor', 'parent'] },
  attendance: { permissions: [Permission.ATTENDANCE_VIEW], roles: ['admin', 'school_manager', 'supervisor', 'driver'] },
  reports: { permissions: [Permission.REPORTS_VIEW], roles: ['admin', 'school_manager'] },
  notifications: { permissions: [Permission.NOTIFICATIONS_VIEW], roles: ['admin', 'school_manager', 'supervisor'] },
  maintenance: { permissions: [Permission.MAINTENANCE_VIEW], roles: ['admin', 'school_manager', 'supervisor', 'driver'] },
};

export default {
  ROUTE_PERMISSIONS,
  COMPONENT_PERMISSIONS,
  NAVIGATION_CONFIG,
};
