/**
 * Test Page for Debugging
 * Complete test page to debug sidebar and routing issues
 * Phase 3: UI/UX Enhancement - Debug
 */

import React, { useState } from 'react';
import { SimpleSidebar } from '../components/debug/SimpleSidebar';
import { SidebarDebug } from '../components/debug/SidebarDebug';
import { RouteDebug } from '../components/debug/RouteDebug';
import { useAuth } from '../contexts/AuthContext';
import { UserRole } from '../types';
import { RefreshCw, Eye, EyeOff } from 'lucide-react';

export const TestPage: React.FC = () => {
  const { user } = useAuth();
  const [showOriginalSidebar, setShowOriginalSidebar] = useState(false);
  const [showDebugInfo, setShowDebugInfo] = useState(true);

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            يرجى تسجيل الدخول أولاً
          </h1>
          <a
            href="/login"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            تسجيل الدخول
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* Sidebar */}
      {!showOriginalSidebar && <SimpleSidebar />}
      
      {/* Main Content */}
      <div className="flex-1 ml-64 p-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🧪 صفحة اختبار النظام
          </h1>
          <p className="text-gray-600">
            صفحة شاملة لاختبار جميع وظائف النظام والتأكد من عمل الروابط والتقارير
          </p>
        </div>

        {/* Controls */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">أدوات التحكم</h2>
          <div className="flex flex-wrap gap-4">
            <button
              type="button"
              onClick={() => setShowOriginalSidebar(!showOriginalSidebar)}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              {showOriginalSidebar ? <EyeOff size={16} /> : <Eye size={16} />}
              {showOriginalSidebar ? 'إخفاء' : 'إظهار'} الـ Sidebar الأصلي
            </button>
            
            <button
              type="button"
              onClick={() => setShowDebugInfo(!showDebugInfo)}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              {showDebugInfo ? <EyeOff size={16} /> : <Eye size={16} />}
              {showDebugInfo ? 'إخفاء' : 'إظهار'} معلومات التشخيص
            </button>
            
            <button
              type="button"
              onClick={() => window.location.reload()}
              className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              <RefreshCw size={16} />
              إعادة تحميل الصفحة
            </button>
          </div>
        </div>

        {/* User Info */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">معلومات المستخدم</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">الاسم</label>
              <p className="mt-1 text-lg text-gray-900">{user.name}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">الدور</label>
              <p className="mt-1 text-lg text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded">
                {user.role}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">البريد الإلكتروني</label>
              <p className="mt-1 text-lg text-gray-900">{user.email}</p>
            </div>
          </div>
        </div>

        {/* Expected Links */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">الروابط المتوقعة</h2>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span>التقارير</span>
              <span className="text-green-600 font-semibold">✅ يجب أن تظهر لجميع الأدوار</span>
            </div>
            
            {user.role === UserRole.ADMIN && (
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <span>إدارة الثيمات</span>
                <span className="text-blue-600 font-semibold">✅ يجب أن تظهر للأدمن فقط</span>
              </div>
            )}
            
            {user.role === UserRole.SCHOOL_MANAGER && (
              <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <span>ثيم المدرسة</span>
                <span className="text-purple-600 font-semibold">✅ يجب أن تظهر لمدير المدرسة فقط</span>
              </div>
            )}
            
            {!([UserRole.ADMIN, UserRole.SCHOOL_MANAGER].includes(user.role as UserRole)) && (
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span>خيارات الثيمات</span>
                <span className="text-gray-600 font-semibold">❌ يجب ألا تظهر لهذا الدور</span>
              </div>
            )}
          </div>
        </div>

        {/* Test Links */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">اختبار الروابط</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <a
              href="/dashboard/reports"
              className="block p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
            >
              <div className="font-semibold text-green-800">التقارير</div>
              <div className="text-sm text-green-600">/dashboard/reports</div>
            </a>
            
            {user.role === UserRole.ADMIN && (
              <a
                href="/admin/themes"
                className="block p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
              >
                <div className="font-semibold text-blue-800">إدارة الثيمات</div>
                <div className="text-sm text-blue-600">/admin/themes</div>
              </a>
            )}
            
            {user.role === UserRole.SCHOOL_MANAGER && (
              <a
                href="/school/theme"
                className="block p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors"
              >
                <div className="font-semibold text-purple-800">ثيم المدرسة</div>
                <div className="text-sm text-purple-600">/school/theme</div>
              </a>
            )}
            
            <a
              href="/dashboard"
              className="block p-4 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div className="font-semibold text-gray-800">لوحة التحكم</div>
              <div className="text-sm text-gray-600">/dashboard</div>
            </a>
          </div>
        </div>

        {/* Current Path */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">المسار الحالي</h2>
          <div className="bg-gray-100 p-4 rounded-lg">
            <code className="text-lg font-mono">{window.location.pathname}</code>
          </div>
        </div>
      </div>

      {/* Debug Components */}
      {showDebugInfo && (
        <>
          <SidebarDebug />
          <RouteDebug />
        </>
      )}
    </div>
  );
};
