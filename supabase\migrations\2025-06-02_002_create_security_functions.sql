-- إنشاء دوال الأمان
-- Create Security Functions

-- دالة الحصول على معرف المستأجر الحالي
CREATE OR REPLACE FUNCTION get_current_tenant_id()
RETURNS UUID
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN (
    SELECT tenant_id
    FROM user_roles
    WHERE user_id = auth.uid()
    AND is_active = true
    LIMIT 1
  );
END;
$$;

-- دالة الحصول على دور المستخدم الحالي
CREATE OR REPLACE FUNCTION get_current_user_role()
RETURNS TEXT
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN (
    SELECT role
    FROM user_roles
    WHERE user_id = auth.uid()
    AND is_active = true
    LIMIT 1
  );
END;
$$;

-- دالة التحقق من كون المستخدم مدير مستأجر
CREATE OR REPLACE FUNCTION is_tenant_admin()
RETURNS BOOLEAN
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN (
    SELECT role IN ('tenant_admin', 'system_admin')
    FROM user_roles
    WHERE user_id = auth.uid()
    AND is_active = true
    LIMIT 1
  );
END;
$$;

-- دالة التحقق من كون المستخدم مدير نظام
CREATE OR REPLACE FUNCTION is_system_admin()
RETURNS BOOLEAN
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN (
    SELECT role = 'system_admin'
    FROM user_roles
    WHERE user_id = auth.uid()
    AND is_active = true
    LIMIT 1
  );
END;
$$;

-- دالة التحقق من إمكانية الوصول لبيانات الطالب
CREATE OR REPLACE FUNCTION can_access_student(student_id UUID)
RETURNS BOOLEAN
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
DECLARE
  user_role TEXT;
  user_tenant_id UUID;
  student_tenant_id UUID;
BEGIN
  -- الحصول على دور المستخدم ومعرف المستأجر
  SELECT role, tenant_id INTO user_role, user_tenant_id
  FROM user_roles
  WHERE user_id = auth.uid() AND is_active = true;

  -- إذا كان مدير نظام، يمكنه الوصول لكل شيء
  IF user_role = 'system_admin' THEN
    RETURN true;
  END IF;

  -- الحصول على معرف مستأجر الطالب
  SELECT tenant_id INTO student_tenant_id
  FROM students
  WHERE id = student_id;

  -- التحقق من نفس المستأجر
  IF user_tenant_id != student_tenant_id THEN
    RETURN false;
  END IF;

  -- التحقق حسب الدور
  CASE user_role
    WHEN 'tenant_admin', 'school_manager' THEN
      RETURN true;
    WHEN 'driver' THEN
      -- السائق يمكنه رؤية الطلاب في حافلته فقط
      RETURN EXISTS (
        SELECT 1 FROM bus_assignments ba
        JOIN buses b ON ba.bus_id = b.id
        WHERE ba.student_id = student_id
        AND b.driver_id = auth.uid()
      );
    WHEN 'parent' THEN
      -- ولي الأمر يمكنه رؤية أطفاله فقط
      RETURN EXISTS (
        SELECT 1 FROM students s
        WHERE s.id = student_id
        AND s.parent_id = auth.uid()
      );
    WHEN 'student' THEN
      -- الطالب يمكنه رؤية بياناته فقط
      RETURN EXISTS (
        SELECT 1 FROM students s
        WHERE s.id = student_id
        AND s.user_id = auth.uid()
      );
    ELSE
      RETURN false;
  END CASE;
END;
$$;

-- دالة التحقق من إمكانية الوصول لبيانات الحافلة
CREATE OR REPLACE FUNCTION can_access_bus(bus_id UUID)
RETURNS BOOLEAN
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
DECLARE
  user_role TEXT;
  user_tenant_id UUID;
  bus_tenant_id UUID;
BEGIN
  -- الحصول على دور المستخدم ومعرف المستأجر
  SELECT role, tenant_id INTO user_role, user_tenant_id
  FROM user_roles
  WHERE user_id = auth.uid() AND is_active = true;

  -- إذا كان مدير نظام، يمكنه الوصول لكل شيء
  IF user_role = 'system_admin' THEN
    RETURN true;
  END IF;

  -- الحصول على معرف مستأجر الحافلة
  SELECT tenant_id INTO bus_tenant_id
  FROM buses
  WHERE id = bus_id;

  -- التحقق من نفس المستأجر
  IF user_tenant_id != bus_tenant_id THEN
    RETURN false;
  END IF;

  -- التحقق حسب الدور
  CASE user_role
    WHEN 'tenant_admin', 'school_manager' THEN
      RETURN true;
    WHEN 'driver' THEN
      -- السائق يمكنه رؤية حافلته فقط
      RETURN EXISTS (
        SELECT 1 FROM buses b
        WHERE b.id = bus_id
        AND b.driver_id = auth.uid()
      );
    WHEN 'parent', 'student' THEN
      -- ولي الأمر والطالب يمكنهما رؤية حافلة الطالب فقط
      RETURN EXISTS (
        SELECT 1 FROM bus_assignments ba
        JOIN students s ON ba.student_id = s.id
        WHERE ba.bus_id = bus_id
        AND (s.parent_id = auth.uid() OR s.user_id = auth.uid())
      );
    ELSE
      RETURN false;
  END CASE;
END;
$$;

-- دالة تسجيل محاولات الوصول للتدقيق
CREATE OR REPLACE FUNCTION audit_log_access(
  table_name TEXT,
  operation TEXT,
  record_id UUID
)
RETURNS VOID
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  INSERT INTO audit_logs (
    user_id,
    table_name,
    operation,
    record_id,
    ip_address,
    user_agent,
    created_at
  ) VALUES (
    auth.uid(),
    table_name,
    operation,
    record_id,
    current_setting('request.headers', true)::json->>'x-forwarded-for',
    current_setting('request.headers', true)::json->>'user-agent',
    NOW()
  );
END;
$$;

-- دالة التحقق من الصلاحيات
CREATE OR REPLACE FUNCTION has_permission(
  permission_name TEXT,
  resource_name TEXT DEFAULT NULL
)
RETURNS BOOLEAN
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- الحصول على دور المستخدم
  SELECT role INTO user_role
  FROM user_roles
  WHERE user_id = auth.uid() AND is_active = true;

  -- التحقق من وجود الصلاحية
  RETURN EXISTS (
    SELECT 1 FROM role_permissions rp
    WHERE rp.role = user_role
    AND (rp.permission = permission_name OR rp.permission = 'all')
    AND (rp.resource = resource_name OR rp.resource = '*' OR resource_name IS NULL)
  );
END;
$$;

