-- إنشاء دوال الأمان
-- Create Security Functions

CREATE OR REPLACE FUNCTION get_current_tenant_id()
RETURNS UUID
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN (SELECT tenant_id FROM user_roles WHERE user_id = auth.uid() LIMIT 1);
END;
$$;

CREATE OR REPLACE FUNCTION get_current_user_role()
RETURNS TEXT
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN (SELECT role FROM user_roles WHERE user_id = auth.uid() LIMIT 1);
END;
$$;

CREATE OR REPLACE FUNCTION is_tenant_admin()
RETURNS BOOLEAN
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN (SELECT role = 'tenant_admin' FROM user_roles WHERE user_id = auth.uid() LIMIT 1);
END;
$$;

