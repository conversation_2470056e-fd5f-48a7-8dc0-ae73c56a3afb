{"update_info": {"timestamp": "2025-06-02T09-00-40-482Z", "total_mappings": 34, "updated_files": 0, "errors": 0}, "import_mappings": {"src/types": "src-new/core/types", "src/utils": "src-new/core/utils", "src/hooks": "src-new/core/hooks", "src/contexts": "src-new/core/contexts", "src/config": "src-new/core/constants", "src/services": "src-new/shared/services", "src/lib": "src-new/shared/services/lib", "src/middleware": "src-new/shared/services/middleware", "src/components/ui": "src-new/shared/components/ui", "src/components/layout": "src-new/shared/layouts", "src/components/common": "src-new/shared/components/common", "src/design-system": "src-new/shared/components/design-system", "src/components/auth": "src-new/features/auth/components", "src/pages/auth": "src-new/features/auth/pages", "src/pages/login": "src-new/features/auth/pages/login", "src/components/security": "src-new/features/auth/security", "src/components/dashboard": "src-new/features/dashboard/components", "src/components/dashboards": "src-new/features/dashboard/variants", "src/pages/dashboard": "src-new/features/dashboard/pages", "src/components/admin": "src-new/features/dashboard/admin", "src/components/buses": "src-new/features/buses/components", "src/components/routes": "src-new/features/routes/components", "src/components/drivers": "src-new/features/drivers/components", "src/components/tracking": "src-new/features/tracking/components", "src/components/students": "src-new/features/students/components", "src/components/schools": "src-new/features/schools/components", "src/pages/school": "src-new/features/schools/pages", "src/components/notifications": "src-new/features/notifications/components", "src/components/reports": "src-new/features/reports/components", "src/components/maintenance": "src-new/features/maintenance/components", "src/components/maps": "src-new/features/tracking/maps", "src/components/map": "src-new/features/tracking/map", "src/themes": "src-new/assets/themes", "src/i18n": "src-new/assets/locales"}, "updated_files": [], "errors": [], "next_steps": ["اختبار التطبيق للتأكد من عمل جميع الاستيرادات", "إصلاح أي مسارات مفقودة يدوياً", "تحديث ملفات التكوين إذا لزم الأمر", "حذ<PERSON> البنية القديمة بعد التأكد"]}