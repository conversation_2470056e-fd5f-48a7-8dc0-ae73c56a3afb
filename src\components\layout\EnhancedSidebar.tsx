/**
 * Enhanced Sidebar with Theme Management Options
 * Updated sidebar with theme customization access based on user roles
 * Phase 3: UI/UX Enhancement - Navigation
 */

import React, { useState, useCallback } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { usePermissionService } from '../../hooks/usePermissionService';
import { UserRole } from '../../api/types';
import { ThemePermission } from '../../lib/rbac';

// Icons
import {
  Home,
  Users,
  Building2,
  Bus,
  MapPin,
  GraduationCap,
  BarChart3,
  Settings,
  CheckCircle,
  Palette,
  Shield,
  FileText,
  Bell,
  HelpCircle,
  LogOut,
  ChevronDown,
  ChevronRight,
} from 'lucide-react';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  current: boolean;
  badge?: number;
  children?: NavigationItem[];
  permission?: string;
}

interface SidebarProps {
  isOpen?: boolean;
  onToggle?: () => void;
  className?: string;
}

/**
 * Enhanced Sidebar Component
 */
export const EnhancedSidebar: React.FC<SidebarProps> = ({
  isOpen = true,
  onToggle,
  className,
}) => {
  const { user, logout } = useAuth();
  const { pathname } = useLocation();
  const { hasPermission } = usePermissionService();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  /**
   * Toggle expanded state for navigation items with children
   */
  const toggleExpanded = useCallback((itemName: string) => {
    setExpandedItems(prev => 
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  }, []);

  /**
   * Get navigation items based on user role and permissions
   */
  const getNavigationItems = useCallback((): NavigationItem[] => {
    const baseItems: NavigationItem[] = [
      {
        name: 'لوحة التحكم',
        href: '/dashboard',
        icon: Home,
        current: pathname === '/dashboard',
      },
    ];

    switch (user?.role) {
      case UserRole.SUPER_ADMIN:
        return [
          ...baseItems,
          {
            name: 'إدارة المستخدمين',
            href: '/admin/users',
            icon: Users,
            current: pathname.startsWith('/admin/users'),
          },
          {
            name: 'إدارة المدارس',
            href: '/admin/schools',
            icon: Building2,
            current: pathname.startsWith('/admin/schools'),
          },
          {
            name: 'إدارة الثيمات',
            href: '/admin/themes',
            icon: Palette,
            current: pathname.startsWith('/admin/themes'),
            permission: ThemePermission.MANAGE_ALL_THEMES,
          },
          {
            name: 'التقارير والإحصائيات',
            href: '/admin/reports',
            icon: BarChart3,
            current: pathname.startsWith('/admin/reports'),
            children: [
              {
                name: 'تقارير المدارس',
                href: '/admin/reports/schools',
                icon: Building2,
                current: pathname === '/admin/reports/schools',
              },
              {
                name: 'تقارير الحضور',
                href: '/admin/reports/attendance',
                icon: CheckCircle,
                current: pathname === '/admin/reports/attendance',
              },
              {
                name: 'تقارير الثيمات',
                href: '/admin/reports/themes',
                icon: Palette,
                current: pathname === '/admin/reports/themes',
              },
            ],
          },
          {
            name: 'الأمان والصلاحيات',
            href: '/admin/security',
            icon: Shield,
            current: pathname.startsWith('/admin/security'),
          },
          {
            name: 'إعدادات النظام',
            href: '/admin/settings',
            icon: Settings,
            current: pathname.startsWith('/admin/settings'),
          },
        ];

      case UserRole.SCHOOL_MANAGER:
        return [
          ...baseItems,
          {
            name: 'الطلاب',
            href: '/school/students',
            icon: GraduationCap,
            current: pathname.startsWith('/school/students'),
          },
          {
            name: 'الحافلات',
            href: '/school/buses',
            icon: Bus,
            current: pathname.startsWith('/school/buses'),
          },
          {
            name: 'الطرق والمسارات',
            href: '/school/routes',
            icon: MapPin,
            current: pathname.startsWith('/school/routes'),
          },
          {
            name: 'ثيم المدرسة',
            href: '/school/theme',
            icon: Palette,
            current: pathname.startsWith('/school/theme'),
            permission: ThemePermission.MANAGE_OWN_THEME,
          },
          {
            name: 'التقارير',
            href: '/school/reports',
            icon: BarChart3,
            current: pathname.startsWith('/school/reports'),
            children: [
              {
                name: 'تقرير الحضور',
                href: '/school/reports/attendance',
                icon: CheckCircle,
                current: pathname === '/school/reports/attendance',
              },
              {
                name: 'تقرير الحافلات',
                href: '/school/reports/buses',
                icon: Bus,
                current: pathname === '/school/reports/buses',
              },
              {
                name: 'تقرير الطلاب',
                href: '/school/reports/students',
                icon: GraduationCap,
                current: pathname === '/school/reports/students',
              },
            ],
          },
          {
            name: 'إعدادات المدرسة',
            href: '/school/settings',
            icon: Settings,
            current: pathname.startsWith('/school/settings'),
          },
        ];

      case UserRole.DRIVER:
        return [
          ...baseItems,
          {
            name: 'مساراتي',
            href: '/driver/routes',
            icon: MapPin,
            current: pathname.startsWith('/driver/routes'),
          },
          {
            name: 'تسجيل الحضور',
            href: '/driver/attendance',
            icon: CheckCircle,
            current: pathname.startsWith('/driver/attendance'),
          },
          {
            name: 'التقارير',
            href: '/driver/reports',
            icon: FileText,
            current: pathname.startsWith('/driver/reports'),
          },
        ];

      case UserRole.PARENT:
        return [
          ...baseItems,
          {
            name: 'أطفالي',
            href: '/parent/children',
            icon: Users,
            current: pathname.startsWith('/parent/children'),
          },
          {
            name: 'تتبع الحافلة',
            href: '/parent/tracking',
            icon: MapPin,
            current: pathname.startsWith('/parent/tracking'),
          },
          {
            name: 'الإشعارات',
            href: '/parent/notifications',
            icon: Bell,
            current: pathname.startsWith('/parent/notifications'),
          },
        ];

      case UserRole.TEACHER:
        return [
          ...baseItems,
          {
            name: 'طلابي',
            href: '/teacher/students',
            icon: GraduationCap,
            current: pathname.startsWith('/teacher/students'),
          },
          {
            name: 'الحضور',
            href: '/teacher/attendance',
            icon: CheckCircle,
            current: pathname.startsWith('/teacher/attendance'),
          },
        ];

      default:
        return baseItems;
    }
  }, [user?.role, pathname]);

  /**
   * Filter navigation items based on permissions
   */
  const filteredNavigationItems = getNavigationItems().filter(item => {
    if (item.permission) {
      return hasPermission(item.permission as any);
    }
    return true;
  });

  /**
   * Render navigation item
   */
  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.name);
    const paddingLeft = level * 16;

    return (
      <div key={item.name}>
        {hasChildren ? (
          <button
            onClick={() => toggleExpanded(item.name)}
            className={`w-full flex items-center justify-between px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              item.current
                ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
            }`}
            style={{ paddingLeft: `${16 + paddingLeft}px` }}
          >
            <div className="flex items-center">
              <item.icon className="ml-3 h-5 w-5 flex-shrink-0" />
              <span>{item.name}</span>
              {item.badge && (
                <span className="mr-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {item.badge}
                </span>
              )}
            </div>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </button>
        ) : (
          <Link
            to={item.href}
            className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              item.current
                ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
            }`}
            style={{ paddingLeft: `${16 + paddingLeft}px` }}
          >
            <item.icon className="ml-3 h-5 w-5 flex-shrink-0" />
            <span>{item.name}</span>
            {item.badge && (
              <span className="mr-auto inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {item.badge}
              </span>
            )}
          </Link>
        )}

        {/* Render children if expanded */}
        {hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children!.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`flex flex-col h-full bg-white border-l border-gray-200 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <Bus className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">نظام الحافلات</h2>
            <p className="text-xs text-gray-500">{user?.school?.name || 'المدرسة'}</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-2 overflow-y-auto">
        {filteredNavigationItems.map(item => renderNavigationItem(item))}
      </nav>

      {/* User Info & Logout */}
      <div className="border-t border-gray-200 p-4">
        <div className="flex items-center space-x-3 mb-3">
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <Users className="w-4 h-4 text-gray-600" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user?.name}
            </p>
            <p className="text-xs text-gray-500 truncate">
              {user?.role === UserRole.SUPER_ADMIN ? 'مدير النظام' :
               user?.role === UserRole.SCHOOL_MANAGER ? 'مدير المدرسة' :
               user?.role === UserRole.DRIVER ? 'سائق' :
               user?.role === UserRole.PARENT ? 'ولي أمر' :
               user?.role === UserRole.TEACHER ? 'معلم' : 'مستخدم'}
            </p>
          </div>
        </div>

        <div className="flex space-x-2">
          <Link
            to="/help"
            className="flex-1 flex items-center justify-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors"
          >
            <HelpCircle className="w-4 h-4 ml-1" />
            مساعدة
          </Link>
          
          <button
            onClick={logout}
            className="flex-1 flex items-center justify-center px-3 py-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
          >
            <LogOut className="w-4 h-4 ml-1" />
            خروج
          </button>
        </div>
      </div>
    </div>
  );
};
