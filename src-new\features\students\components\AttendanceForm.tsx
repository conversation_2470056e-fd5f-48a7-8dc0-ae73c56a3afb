import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { X, MapPin, Bus } from "lucide-react";
import { Button } from "../ui/Button";
import { useDatabase } from "../../contexts/DatabaseContext";
import type { Tables } from "../../lib/api";

interface AttendanceFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: Partial<Tables<"attendance">>) => Promise<void>;
  student: Tables<"students">;
}

export const AttendanceForm: React.FC<AttendanceFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  student,
}) => {
  const { t } = useTranslation();
  const { tenant, buses } = useDatabase();
  const [isLoading, setIsLoading] = useState(false);
  const [location, setLocation] = useState<GeolocationPosition | null>(null);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<Tables<"attendance">>>({
    tenant_id: tenant?.id || "",
    student_id: student?.id || "",
    bus_id: "",
    type: "pickup",
    recorded_by: "",
  });

  useEffect(() => {
    if (student) {
      setFormData((prev) => ({
        ...prev,
        student_id: student.id,
        tenant_id: tenant?.id || "",
      }));
    }
  }, [student, tenant]);

  useEffect(() => {
    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation(position);
          setLocationError(null);
        },
        (error) => {
          console.error("Error getting location:", error);
          setLocationError(t("tracking.locationError"));
        },
      );
    } else {
      setLocationError(t("tracking.noGeolocation"));
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!location) return;

    setIsLoading(true);
    try {
      await onSubmit({
        ...formData,
        location: `POINT(${location.coords.longitude} ${location.coords.latitude})`,
        recorded_at: new Date().toISOString(),
        recorded_by: formData.recorded_by || "system",
      });
      onClose();
    } catch (error) {
      console.error("Error recording attendance:", error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t("students.recordAttendance")}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("students.name")}
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {student?.name || "Unknown Student"}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("students.grade")}
              </label>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Grade {student?.grade || "N/A"}
              </p>
            </div>

            <div>
              <label
                htmlFor="bus"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                {t("buses.selectBus")}
              </label>
              <select
                id="bus"
                value={formData.bus_id}
                onChange={(e) =>
                  setFormData({ ...formData, bus_id: e.target.value })
                }
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                required
              >
                <option value="">{t("common.select")}</option>
                {buses.map((bus) => (
                  <option key={bus.id} value={bus.id}>
                    {bus.plate_number} - Capacity: {bus.capacity}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label
                htmlFor="type"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                {t("attendance.type")}
              </label>
              <select
                id="type"
                value={formData.type}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    type: e.target.value as "pickup" | "dropoff",
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                required
              >
                <option value="pickup">{t("students.pickup")}</option>
                <option value="dropoff">{t("students.dropoff")}</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t("tracking.location")}
              </label>
              {locationError ? (
                <div className="bg-error-50 dark:bg-error-900/20 border border-error-200 dark:border-error-800 rounded-md p-3">
                  <div className="flex items-center">
                    <MapPin size={16} className="text-error-500" />
                    <span className="ml-2 text-sm text-error-600 dark:text-error-400">
                      {locationError}
                    </span>
                  </div>
                  <p className="mt-1 text-xs text-error-500 dark:text-error-400">
                    {t("tracking.locationRequiredForAttendance")}
                  </p>
                </div>
              ) : location ? (
                <div className="bg-accent-50 dark:bg-accent-900/20 border border-accent-200 dark:border-accent-800 rounded-md p-3">
                  <div className="flex items-center">
                    <MapPin size={16} className="text-accent-500" />
                    <span className="ml-2 text-sm text-accent-700 dark:text-accent-300">
                      {t("tracking.locationDetected")}
                    </span>
                  </div>
                  <p className="mt-1 text-xs text-accent-600 dark:text-accent-400">
                    Lat: {location.coords.latitude.toFixed(6)}, Lng:{" "}
                    {location.coords.longitude.toFixed(6)}
                    <br />
                    Accuracy: ±{location.coords.accuracy.toFixed(0)}m
                  </p>
                </div>
              ) : (
                <div className="bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-700 rounded-md p-3">
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500" />
                    <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                      {t("tracking.detectingLocation")}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              {t("common.cancel")}
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !location}
              className="flex items-center gap-2"
            >
              <Bus size={16} />
              {isLoading ? t("common.saving") : t("common.save")}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
