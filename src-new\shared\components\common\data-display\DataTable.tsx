/**
 * Data Table Component
 * Reusable table component with sorting, loading states, and actions
 * Phase 2: Application Structure Reorganization
 */

import React, { useState, useMemo } from 'react';
import { cn } from '../../../utils/cn';
import { ChevronUp, ChevronDown, Loader2 } from 'lucide-react';

export interface TableColumn<T = any> {
  key: string;
  title: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (item: T, index: number) => React.ReactNode;
  className?: string;
}

export interface DataTableProps<T = any> {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  emptyMessage?: string;
  sortable?: boolean;
  onSort?: (key: string, direction: 'asc' | 'desc') => void;
  onRowClick?: (item: T, index: number) => void;
  rowClassName?: (item: T, index: number) => string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  striped?: boolean;
  bordered?: boolean;
  hoverable?: boolean;
}

interface SortState {
  key: string | null;
  direction: 'asc' | 'desc';
}

/**
 * Data Table Component
 * Implements Open/Closed Principle - extensible through props, closed for modification
 */
export function DataTable<T = any>({
  data,
  columns,
  loading = false,
  emptyMessage = 'No data available',
  sortable = true,
  onSort,
  onRowClick,
  rowClassName,
  className,
  size = 'md',
  striped = true,
  bordered = true,
  hoverable = true,
}: DataTableProps<T>) {
  const [sortState, setSortState] = useState<SortState>({
    key: null,
    direction: 'asc',
  });

  /**
   * Handle column sort
   */
  const handleSort = (columnKey: string) => {
    if (!sortable) return;

    const column = columns.find(col => col.key === columnKey);
    if (!column?.sortable) return;

    const newDirection = 
      sortState.key === columnKey && sortState.direction === 'asc' 
        ? 'desc' 
        : 'asc';

    setSortState({
      key: columnKey,
      direction: newDirection,
    });

    if (onSort) {
      onSort(columnKey, newDirection);
    }
  };

  /**
   * Sort data locally if no external sort handler
   */
  const sortedData = useMemo(() => {
    if (!sortable || !sortState.key || onSort) {
      return data;
    }

    return [...data].sort((a, b) => {
      const aValue = a[sortState.key as keyof T];
      const bValue = b[sortState.key as keyof T];

      if (aValue === bValue) return 0;

      const comparison = aValue < bValue ? -1 : 1;
      return sortState.direction === 'asc' ? comparison : -comparison;
    });
  }, [data, sortState, sortable, onSort]);

  /**
   * Size classes
   */
  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  const paddingClasses = {
    sm: 'px-2 py-1',
    md: 'px-4 py-2',
    lg: 'px-6 py-3',
  };

  /**
   * Render table header
   */
  const renderHeader = () => (
    <thead className="bg-gray-50 dark:bg-gray-800">
      <tr>
        {columns.map((column) => (
          <th
            key={column.key}
            className={cn(
              'font-medium text-gray-900 dark:text-gray-100',
              paddingClasses[size],
              sizeClasses[size],
              column.align === 'center' && 'text-center',
              column.align === 'right' && 'text-right',
              column.sortable && sortable && 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700',
              bordered && 'border-b border-gray-200 dark:border-gray-700',
              column.className
            )}
            style={{ width: column.width }}
            onClick={() => column.sortable && handleSort(column.key)}
          >
            <div className="flex items-center space-x-1">
              <span>{column.title}</span>
              {column.sortable && sortable && (
                <div className="flex flex-col">
                  <ChevronUp
                    className={cn(
                      'w-3 h-3',
                      sortState.key === column.key && sortState.direction === 'asc'
                        ? 'text-blue-600'
                        : 'text-gray-400'
                    )}
                  />
                  <ChevronDown
                    className={cn(
                      'w-3 h-3 -mt-1',
                      sortState.key === column.key && sortState.direction === 'desc'
                        ? 'text-blue-600'
                        : 'text-gray-400'
                    )}
                  />
                </div>
              )}
            </div>
          </th>
        ))}
      </tr>
    </thead>
  );

  /**
   * Render table body
   */
  const renderBody = () => {
    if (loading) {
      return (
        <tbody>
          <tr>
            <td
              colSpan={columns.length}
              className={cn(
                'text-center text-gray-500',
                paddingClasses[size],
                sizeClasses[size]
              )}
            >
              <div className="flex items-center justify-center space-x-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Loading...</span>
              </div>
            </td>
          </tr>
        </tbody>
      );
    }

    if (sortedData.length === 0) {
      return (
        <tbody>
          <tr>
            <td
              colSpan={columns.length}
              className={cn(
                'text-center text-gray-500',
                paddingClasses[size],
                sizeClasses[size]
              )}
            >
              {emptyMessage}
            </td>
          </tr>
        </tbody>
      );
    }

    return (
      <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
        {sortedData.map((item, index) => (
          <tr
            key={index}
            className={cn(
              'transition-colors',
              striped && index % 2 === 1 && 'bg-gray-50 dark:bg-gray-800',
              hoverable && 'hover:bg-gray-100 dark:hover:bg-gray-700',
              onRowClick && 'cursor-pointer',
              rowClassName && rowClassName(item, index)
            )}
            onClick={() => onRowClick && onRowClick(item, index)}
          >
            {columns.map((column) => (
              <td
                key={column.key}
                className={cn(
                  'text-gray-900 dark:text-gray-100',
                  paddingClasses[size],
                  sizeClasses[size],
                  column.align === 'center' && 'text-center',
                  column.align === 'right' && 'text-right',
                  bordered && 'border-b border-gray-200 dark:border-gray-700',
                  column.className
                )}
                style={{ width: column.width }}
              >
                {column.render ? column.render(item, index) : item[column.key as keyof T]}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    );
  };

  return (
    <div className={cn('overflow-hidden', className)}>
      <div className="overflow-x-auto">
        <table
          className={cn(
            'min-w-full divide-y divide-gray-200 dark:divide-gray-700',
            bordered && 'border border-gray-200 dark:border-gray-700 rounded-lg'
          )}
        >
          {renderHeader()}
          {renderBody()}
        </table>
      </div>
    </div>
  );
}

/**
 * Table with built-in pagination
 */
export interface PaginatedTableProps<T = any> extends DataTableProps<T> {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
}

export function PaginatedTable<T = any>({
  currentPage,
  totalPages,
  pageSize,
  totalItems,
  onPageChange,
  onPageSizeChange,
  ...tableProps
}: PaginatedTableProps<T>) {
  return (
    <div className="space-y-4">
      <DataTable {...tableProps} />
      
      {/* Pagination will be implemented separately */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-700 dark:text-gray-300">
          Showing {Math.min((currentPage - 1) * pageSize + 1, totalItems)} to{' '}
          {Math.min(currentPage * pageSize, totalItems)} of {totalItems} results
        </div>
        
        <div className="flex items-center space-x-2">
          <select
            value={pageSize}
            onChange={(e) => onPageSizeChange(Number(e.target.value))}
            className="border border-gray-300 rounded px-2 py-1 text-sm"
          >
            <option value={10}>10</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
          
          <button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage <= 1}
            className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50"
          >
            Previous
          </button>
          
          <span className="px-3 py-1 text-sm">
            Page {currentPage} of {totalPages}
          </span>
          
          <button
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage >= totalPages}
            className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
}
