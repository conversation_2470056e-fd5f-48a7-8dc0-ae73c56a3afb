/**
 * Attendance Service
 * Handles all attendance-related API operations
 * Phase 2: Application Structure Reorganization
 */

import { BaseService } from '../base/BaseService';
import {
  APIResponse,
  PaginatedResponse,
  PaginationParams,
} from '../../api/types';

export interface AttendanceRecord {
  id: string;
  student_id: string;
  route_id: string;
  date: string;
  pickup_time?: string;
  dropoff_time?: string;
  pickup_status: AttendanceStatus;
  dropoff_status: AttendanceStatus;
  pickup_location?: string;
  dropoff_location?: string;
  driver_id: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export enum AttendanceStatus {
  PRESENT = 'present',
  ABSENT = 'absent',
  LATE = 'late',
  EARLY = 'early',
  NO_SHOW = 'no_show',
}

export interface AttendanceListParams extends PaginationParams {
  student_id?: string;
  route_id?: string;
  driver_id?: string;
  date_from?: string;
  date_to?: string;
  status?: AttendanceStatus;
  tenant_id?: string;
}

export interface CreateAttendanceRequest {
  student_id: string;
  route_id: string;
  date: string;
  pickup_status: AttendanceStatus;
  dropoff_status?: AttendanceStatus;
  pickup_time?: string;
  dropoff_time?: string;
  pickup_location?: string;
  dropoff_location?: string;
  notes?: string;
}

export interface UpdateAttendanceRequest {
  pickup_status?: AttendanceStatus;
  dropoff_status?: AttendanceStatus;
  pickup_time?: string;
  dropoff_time?: string;
  pickup_location?: string;
  dropoff_location?: string;
  notes?: string;
}

/**
 * Attendance Service Class
 * Implements comprehensive attendance tracking and reporting
 */
export class AttendanceService extends BaseService {
  private readonly endpoint = '/attendance';

  /**
   * Get paginated list of attendance records
   */
  async getAttendanceRecords(params: AttendanceListParams = {}): Promise<APIResponse<PaginatedResponse<AttendanceRecord>>> {
    return this.getPaginated<AttendanceRecord>(this.endpoint, params);
  }

  /**
   * Get attendance record by ID
   */
  async getAttendanceById(id: string): Promise<APIResponse<AttendanceRecord>> {
    return this.get<AttendanceRecord>(`${this.endpoint}/${id}`);
  }

  /**
   * Create attendance record
   */
  async createAttendance(attendanceData: CreateAttendanceRequest): Promise<APIResponse<AttendanceRecord>> {
    const validation = this.validateCreateAttendanceData(attendanceData);
    if (!validation.isValid) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid attendance data',
          details: validation.errors,
        },
      };
    }

    return this.post<AttendanceRecord>(this.endpoint, attendanceData);
  }

  /**
   * Update attendance record
   */
  async updateAttendance(id: string, attendanceData: UpdateAttendanceRequest): Promise<APIResponse<AttendanceRecord>> {
    return this.put<AttendanceRecord>(`${this.endpoint}/${id}`, attendanceData);
  }

  /**
   * Delete attendance record
   */
  async deleteAttendance(id: string): Promise<APIResponse<{ message: string }>> {
    return this.delete<{ message: string }>(`${this.endpoint}/${id}`);
  }

  /**
   * Record pickup
   */
  async recordPickup(
    studentId: string,
    routeId: string,
    status: AttendanceStatus,
    location?: string,
    notes?: string
  ): Promise<APIResponse<AttendanceRecord>> {
    return this.post<AttendanceRecord>(`${this.endpoint}/pickup`, {
      student_id: studentId,
      route_id: routeId,
      pickup_status: status,
      pickup_time: new Date().toISOString(),
      pickup_location: location,
      notes,
    });
  }

  /**
   * Record dropoff
   */
  async recordDropoff(
    attendanceId: string,
    status: AttendanceStatus,
    location?: string,
    notes?: string
  ): Promise<APIResponse<AttendanceRecord>> {
    return this.patch<AttendanceRecord>(`${this.endpoint}/${attendanceId}/dropoff`, {
      dropoff_status: status,
      dropoff_time: new Date().toISOString(),
      dropoff_location: location,
      notes,
    });
  }

  /**
   * Get attendance for specific date
   */
  async getAttendanceByDate(date: string, routeId?: string, tenantId?: string): Promise<APIResponse<AttendanceRecord[]>> {
    const params: AttendanceListParams = { date_from: date, date_to: date };
    if (routeId) params.route_id = routeId;
    if (tenantId) params.tenant_id = tenantId;

    const response = await this.getAttendanceRecords(params);
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<AttendanceRecord[]>;
  }

  /**
   * Get attendance for student
   */
  async getStudentAttendance(
    studentId: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<APIResponse<AttendanceRecord[]>> {
    const params: AttendanceListParams = { student_id: studentId };
    if (dateFrom) params.date_from = dateFrom;
    if (dateTo) params.date_to = dateTo;

    const response = await this.getAttendanceRecords(params);
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<AttendanceRecord[]>;
  }

  /**
   * Get attendance for route
   */
  async getRouteAttendance(
    routeId: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<APIResponse<AttendanceRecord[]>> {
    const params: AttendanceListParams = { route_id: routeId };
    if (dateFrom) params.date_from = dateFrom;
    if (dateTo) params.date_to = dateTo;

    const response = await this.getAttendanceRecords(params);
    if (response.success && response.data) {
      return {
        success: true,
        data: response.data.data,
      };
    }
    return response as APIResponse<AttendanceRecord[]>;
  }

  /**
   * Get attendance statistics
   */
  async getAttendanceStats(
    dateFrom?: string,
    dateTo?: string,
    tenantId?: string
  ): Promise<APIResponse<AttendanceStats>> {
    const params: any = {};
    if (dateFrom) params.date_from = dateFrom;
    if (dateTo) params.date_to = dateTo;
    if (tenantId) params.tenant_id = tenantId;

    const queryString = this.buildQueryString(params);
    return this.get<AttendanceStats>(`${this.endpoint}/stats${queryString}`);
  }

  /**
   * Get student attendance summary
   */
  async getStudentAttendanceSummary(
    studentId: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<APIResponse<StudentAttendanceSummary>> {
    const params: any = {};
    if (dateFrom) params.date_from = dateFrom;
    if (dateTo) params.date_to = dateTo;

    const queryString = this.buildQueryString(params);
    return this.get<StudentAttendanceSummary>(`${this.endpoint}/student/${studentId}/summary${queryString}`);
  }

  /**
   * Generate attendance report
   */
  async generateAttendanceReport(
    reportType: 'daily' | 'weekly' | 'monthly',
    dateFrom: string,
    dateTo: string,
    filters?: {
      route_id?: string;
      student_id?: string;
      tenant_id?: string;
    }
  ): Promise<APIResponse<AttendanceReport>> {
    const params = {
      report_type: reportType,
      date_from: dateFrom,
      date_to: dateTo,
      ...filters,
    };

    return this.post<AttendanceReport>(`${this.endpoint}/reports`, params);
  }

  /**
   * Bulk attendance operations
   */
  async bulkCreateAttendance(
    attendanceRecords: CreateAttendanceRequest[]
  ): Promise<APIResponse<BulkOperationResult<AttendanceRecord>>> {
    return this.post<BulkOperationResult<AttendanceRecord>>(`${this.endpoint}/bulk`, {
      attendance_records: attendanceRecords,
    });
  }

  async bulkUpdateAttendance(
    updates: Array<{ id: string; data: UpdateAttendanceRequest }>
  ): Promise<APIResponse<BulkOperationResult<AttendanceRecord>>> {
    return this.put<BulkOperationResult<AttendanceRecord>>(`${this.endpoint}/bulk`, { updates });
  }

  /**
   * Get today's attendance for driver
   */
  async getTodayAttendanceForDriver(): Promise<APIResponse<AttendanceRecord[]>> {
    const today = new Date().toISOString().split('T')[0];
    return this.get<AttendanceRecord[]>(`${this.endpoint}/driver/today?date=${today}`);
  }

  /**
   * Validation methods
   */
  private validateCreateAttendanceData(data: CreateAttendanceRequest): ValidationResult {
    const errors: string[] = [];

    if (!data.student_id || data.student_id.trim().length === 0) {
      errors.push('Student ID is required');
    }

    if (!data.route_id || data.route_id.trim().length === 0) {
      errors.push('Route ID is required');
    }

    if (!data.date || !this.isValidDate(data.date)) {
      errors.push('Valid date is required');
    }

    if (!data.pickup_status || !Object.values(AttendanceStatus).includes(data.pickup_status)) {
      errors.push('Valid pickup status is required');
    }

    if (data.dropoff_status && !Object.values(AttendanceStatus).includes(data.dropoff_status)) {
      errors.push('Valid dropoff status is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
  }
}

// ============================================================================
// Supporting Types
// ============================================================================

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

interface AttendanceStats {
  total_records: number;
  present_count: number;
  absent_count: number;
  late_count: number;
  early_count: number;
  no_show_count: number;
  attendance_rate: number;
  punctuality_rate: number;
  by_route: Record<string, {
    total: number;
    present: number;
    attendance_rate: number;
  }>;
  by_date: Record<string, {
    total: number;
    present: number;
    attendance_rate: number;
  }>;
  last_updated: string;
}

interface StudentAttendanceSummary {
  student_id: string;
  total_days: number;
  present_days: number;
  absent_days: number;
  late_days: number;
  early_days: number;
  no_show_days: number;
  attendance_rate: number;
  punctuality_rate: number;
  recent_records: AttendanceRecord[];
}

interface AttendanceReport {
  id: string;
  report_type: string;
  date_from: string;
  date_to: string;
  generated_at: string;
  summary: AttendanceStats;
  details: AttendanceRecord[];
  charts_data: {
    daily_attendance: Array<{ date: string; present: number; absent: number }>;
    route_performance: Array<{ route_name: string; attendance_rate: number }>;
    status_distribution: Array<{ status: string; count: number }>;
  };
}

interface BulkOperationResult<T> {
  successful: T[];
  failed: Array<{
    data: any;
    error: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

// ============================================================================
// Service Instance
// ============================================================================

export const attendanceService = new AttendanceService();
export { AttendanceService };
