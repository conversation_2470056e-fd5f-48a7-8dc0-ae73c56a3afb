import React, { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { supabase } from "../../lib/supabase";
import { Button } from "../ui/Button";
import {
  Shield,
  AlertTriangle,
  Activity,
  Users,
  Lock,
  Eye,
  RefreshCw,
  TrendingUp,
  Clock,
  MapPin,
} from "lucide-react";
import { RBACSecurityAudit } from "../../lib/rbacAudit";
import { DatabaseSecurityAudit } from "../../lib/databaseSecurityAudit";
import { generatePermissionsReport } from "../../lib/permissionMatrix";

interface SecurityMetrics {
  totalEvents: number;
  criticalEvents: number;
  activeThreats: number;
  userSessions: number;
  permissionViolations: number;
  riskScore: number;
}

interface SecurityEvent {
  id: string;
  event_type: string;
  severity: string;
  description: string;
  timestamp: string;
  user_id?: string;
  metadata?: any;
}

export const SecurityMonitoringDashboard: React.FC = () => {
  const { user } = useAuth();
  const [metrics, setMetrics] = useState<SecurityMetrics>({
    totalEvents: 0,
    criticalEvents: 0,
    activeThreats: 0,
    userSessions: 0,
    permissionViolations: 0,
    riskScore: 0,
  });
  const [recentEvents, setRecentEvents] = useState<SecurityEvent[]>([]);
  const [auditReport, setAuditReport] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<
    "overview" | "events" | "audit" | "reports"
  >("overview");

  // Only show for admin users
  if (!user || user.role !== "admin") {
    return null;
  }

  const fetchSecurityMetrics = async () => {
    setLoading(true);
    try {
      // Fetch security events (mock data for demo)
      const mockMetrics: SecurityMetrics = {
        totalEvents: 156,
        criticalEvents: 3,
        activeThreats: 1,
        userSessions: 24,
        permissionViolations: 7,
        riskScore: 75,
      };

      const mockEvents: SecurityEvent[] = [
        {
          id: "1",
          event_type: "permission_violation",
          severity: "high",
          description:
            "User attempted to access admin panel without permissions",
          timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
          user_id: "user-123",
          metadata: { resource: "admin_panel", action: "access" },
        },
        {
          id: "2",
          event_type: "suspicious_login",
          severity: "medium",
          description: "Login from unusual IP address",
          timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
          user_id: "user-456",
          metadata: { ip: "*************", location: "Unknown" },
        },
        {
          id: "3",
          event_type: "rate_limit_exceeded",
          severity: "low",
          description: "API rate limit exceeded",
          timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(),
          user_id: "user-789",
          metadata: { endpoint: "/api/users", requests: 150 },
        },
      ];

      setMetrics(mockMetrics);
      setRecentEvents(mockEvents);
    } catch (error) {
      console.error("Error fetching security metrics:", error);
    } finally {
      setLoading(false);
    }
  };

  const generateAuditReport = () => {
    const report = RBACSecurityAudit.generateAuditReport();
    const dbValidation = DatabaseSecurityAudit.validateSchemaForRBAC();
    const permissionsReport = generatePermissionsReport();

    setAuditReport({
      rbacAudit: report,
      databaseValidation: dbValidation,
      permissionsReport,
    });
  };

  useEffect(() => {
    fetchSecurityMetrics();
    generateAuditReport();

    // Refresh every 30 seconds
    const interval = setInterval(fetchSecurityMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical":
        return "text-red-600 bg-red-100";
      case "high":
        return "text-red-500 bg-red-50";
      case "medium":
        return "text-yellow-600 bg-yellow-100";
      case "low":
        return "text-green-600 bg-green-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case "critical":
        return "🚨";
      case "high":
        return "⚠️";
      case "medium":
        return "🟡";
      case "low":
        return "🟢";
      default:
        return "ℹ️";
    }
  };

  const getRiskScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Shield className="h-8 w-8 text-blue-600" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Security Monitoring Dashboard
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Real-time security monitoring and threat detection
            </p>
          </div>
        </div>
        <Button
          onClick={fetchSecurityMetrics}
          disabled={loading}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw size={16} className={loading ? "animate-spin" : ""} />
          Refresh
        </Button>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
        {[
          { id: "overview", label: "Overview", icon: Activity },
          { id: "events", label: "Security Events", icon: AlertTriangle },
          { id: "audit", label: "RBAC Audit", icon: Lock },
          { id: "reports", label: "Reports", icon: Eye },
        ].map(({ id, label, icon: Icon }) => (
          <button
            key={id}
            onClick={() => setActiveTab(id as any)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === id
                ? "bg-white dark:bg-gray-800 text-blue-600 shadow-sm"
                : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
            }`}
          >
            <Icon size={16} />
            {label}
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === "overview" && (
        <div className="space-y-6">
          {/* Security Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Security Score</p>
                  <p
                    className={`text-3xl font-bold ${getRiskScoreColor(metrics.riskScore)}`}
                  >
                    {metrics.riskScore}/100
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-200" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Active Threats
                  </p>
                  <p className="text-3xl font-bold text-red-600">
                    {metrics.activeThreats}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Active Sessions
                  </p>
                  <p className="text-3xl font-bold text-green-600">
                    {metrics.userSessions}
                  </p>
                </div>
                <Users className="h-8 w-8 text-green-500" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Total Events (24h)
                  </p>
                  <p className="text-3xl font-bold text-blue-600">
                    {metrics.totalEvents}
                  </p>
                </div>
                <Activity className="h-8 w-8 text-blue-500" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Critical Events
                  </p>
                  <p className="text-3xl font-bold text-red-600">
                    {metrics.criticalEvents}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Permission Violations
                  </p>
                  <p className="text-3xl font-bold text-yellow-600">
                    {metrics.permissionViolations}
                  </p>
                </div>
                <Lock className="h-8 w-8 text-yellow-500" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Security Events Tab */}
      {activeTab === "events" && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Recent Security Events
          </h3>
          <div className="space-y-3">
            {recentEvents.map((event) => (
              <div
                key={event.id}
                className="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <span className="text-2xl">
                      {getSeverityIcon(event.severity)}
                    </span>
                    <div>
                      <div className="flex items-center gap-2">
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(event.severity)}`}
                        >
                          {event.severity.toUpperCase()}
                        </span>
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {event.event_type.replace("_", " ").toUpperCase()}
                        </span>
                      </div>
                      <p className="text-gray-900 dark:text-white font-medium mt-1">
                        {event.description}
                      </p>
                      <div className="flex items-center gap-4 mt-2 text-sm text-gray-500 dark:text-gray-400">
                        <div className="flex items-center gap-1">
                          <Clock size={14} />
                          {new Date(event.timestamp).toLocaleString()}
                        </div>
                        {event.user_id && (
                          <div className="flex items-center gap-1">
                            <Users size={14} />
                            User: {event.user_id.slice(0, 8)}...
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* RBAC Audit Tab */}
      {activeTab === "audit" && auditReport && (
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            RBAC Security Audit
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                Overall Score
              </h4>
              <p
                className={`text-3xl font-bold ${getRiskScoreColor(auditReport.rbacAudit.overallSecurityScore)}`}
              >
                {auditReport.rbacAudit.overallSecurityScore}/100
              </p>
            </div>

            <div className="bg-white dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                Compliance Status
              </h4>
              <p
                className={`text-lg font-medium ${
                  auditReport.rbacAudit.complianceStatus === "compliant"
                    ? "text-green-600"
                    : auditReport.rbacAudit.complianceStatus ===
                        "needs_attention"
                      ? "text-yellow-600"
                      : "text-red-600"
                }`}
              >
                {auditReport.rbacAudit.complianceStatus
                  .replace("_", " ")
                  .toUpperCase()}
              </p>
            </div>

            <div className="bg-white dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                Active Threats
              </h4>
              <p className="text-3xl font-bold text-red-600">
                {auditReport.rbacAudit.realTimeThreats?.length || 0}
              </p>
            </div>
          </div>

          {auditReport.rbacAudit.actionableItems && (
            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900 dark:text-white">
                Action Items
              </h4>

              {auditReport.rbacAudit.actionableItems.immediate.length > 0 && (
                <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
                  <h5 className="font-medium text-red-800 dark:text-red-200 mb-2">
                    🚨 Immediate Actions Required
                  </h5>
                  <ul className="space-y-1">
                    {auditReport.rbacAudit.actionableItems.immediate.map(
                      (item: string, index: number) => (
                        <li
                          key={index}
                          className="text-red-700 dark:text-red-300 text-sm"
                        >
                          {item}
                        </li>
                      ),
                    )}
                  </ul>
                </div>
              )}

              {auditReport.rbacAudit.actionableItems.shortTerm.length > 0 && (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
                  <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                    ⚠️ Short-term Improvements
                  </h5>
                  <ul className="space-y-1">
                    {auditReport.rbacAudit.actionableItems.shortTerm.map(
                      (item: string, index: number) => (
                        <li
                          key={index}
                          className="text-yellow-700 dark:text-yellow-300 text-sm"
                        >
                          {item}
                        </li>
                      ),
                    )}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Reports Tab */}
      {activeTab === "reports" && auditReport && (
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Security Reports
          </h3>

          <div className="bg-white dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">
              Permissions Report
            </h4>
            <pre className="text-sm bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-auto max-h-96 whitespace-pre-wrap">
              {auditReport.permissionsReport}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

export default SecurityMonitoringDashboard;
