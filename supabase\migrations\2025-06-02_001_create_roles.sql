-- إنشاء الأدوار والصلاحيات
-- Create Roles and Permissions

-- التأكد من وجود جدول tenants أولاً
CREATE TABLE IF NOT EXISTS tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الأدوار
CREATE TABLE IF NOT EXISTS user_roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('system_admin', 'tenant_admin', 'school_manager', 'driver', 'parent', 'student')),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, tenant_id)
);

-- إنشاء جدول صلاحيات الأدوار
CREATE TABLE IF NOT EXISTS role_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  role TEXT NOT NULL,
  permission TEXT NOT NULL,
  resource TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(role, permission, resource)
);

-- إدراج الصلاحيات الأساسية
INSERT INTO role_permissions (role, permission, resource) VALUES
-- صلاحيات مدير النظام
('system_admin', 'all', '*'),
-- صلاحيات مدير المدرسة
('tenant_admin', 'read', 'tenant'),
('tenant_admin', 'write', 'tenant'),
('tenant_admin', 'manage', 'users'),
('tenant_admin', 'manage', 'buses'),
('tenant_admin', 'manage', 'students'),
('tenant_admin', 'manage', 'routes'),
('tenant_admin', 'read', 'reports'),
-- صلاحيات مدير المدرسة
('school_manager', 'read', 'tenant'),
('school_manager', 'manage', 'students'),
('school_manager', 'read', 'buses'),
('school_manager', 'read', 'routes'),
('school_manager', 'read', 'attendance'),
-- صلاحيات السائق
('driver', 'read', 'bus'),
('driver', 'write', 'attendance'),
('driver', 'read', 'route'),
('driver', 'read', 'students'),
-- صلاحيات ولي الأمر
('parent', 'read', 'student'),
('parent', 'read', 'attendance'),
('parent', 'read', 'notifications'),
('parent', 'read', 'bus_location'),
-- صلاحيات الطالب
('student', 'read', 'self'),
('student', 'read', 'bus_location'),
('student', 'read', 'notifications')
ON CONFLICT (role, permission, resource) DO NOTHING;

-- إنشاء فهارس للأدوار
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_tenant_id ON user_roles(tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role ON user_roles(role);
CREATE INDEX IF NOT EXISTS idx_user_roles_active ON user_roles(is_active);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role ON role_permissions(role);

-- إنشاء trigger لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_roles_updated_at BEFORE UPDATE ON user_roles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

