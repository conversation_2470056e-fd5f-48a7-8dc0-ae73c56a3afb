import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Bell,
  Search,
  Filter,
  Calendar,
  Check,
  Check<PERSON>he<PERSON>,
  Trash2,
  Download,
  Eye,
  EyeOff,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON>,
  <PERSON>ch,
  MessageSquare,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/database.types";

interface NotificationHistoryProps {
  className?: string;
}

type FilterType =
  | "all"
  | "geofence"
  | "attendance"
  | "maintenance"
  | "announcements";
type FilterStatus = "all" | "read" | "unread";

export const NotificationHistory: React.FC<NotificationHistoryProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Tables<"notifications">[]>(
    [],
  );
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<FilterType>("all");
  const [filterStatus, setFilterStatus] = useState<FilterStatus>("all");
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    end: new Date().toISOString().split("T")[0],
  });
  const [selectedNotifications, setSelectedNotifications] = useState<
    Set<string>
  >(new Set());
  const [isDeleting, setIsDeleting] = useState(false);
  const [isMarkingRead, setIsMarkingRead] = useState(false);

  useEffect(() => {
    if (user?.id) {
      fetchNotifications();
    }
  }, [user?.id, dateRange, filterType, filterStatus]);

  const fetchNotifications = async () => {
    if (!user) return;

    try {
      setLoading(true);

      let query = supabase
        .from("notifications")
        .select("*")
        .eq("user_id", user.id)
        .gte("created_at", `${dateRange.start}T00:00:00.000Z`)
        .lte("created_at", `${dateRange.end}T23:59:59.999Z`)
        .order("created_at", { ascending: false });

      // Apply status filter
      if (filterStatus === "read") {
        query = query.eq("read", true);
      } else if (filterStatus === "unread") {
        query = query.eq("read", false);
      }

      const { data, error } = await query;

      if (error) {
        console.error("Error fetching notification history:", error);
        setNotifications([]);
        return;
      }

      let filteredData = data || [];

      // Apply type filter
      if (filterType !== "all") {
        filteredData = filteredData.filter((notification) => {
          const metadata = notification.metadata as any;
          return metadata?.type === filterType;
        });
      }

      // Apply search filter
      if (searchQuery.trim()) {
        const searchLower = searchQuery.toLowerCase().trim();
        filteredData = filteredData.filter(
          (notification) =>
            notification.title?.toLowerCase().includes(searchLower) ||
            notification.message?.toLowerCase().includes(searchLower),
        );
      }

      setNotifications(filteredData);
    } catch (error) {
      console.error("Error fetching notification history:", error);
      setNotifications([]);
    } finally {
      setLoading(false);
    }
  };

  const getNotificationIcon = (notification: Tables<"notifications">) => {
    const metadata = notification.metadata as any;
    const type = metadata?.type;

    switch (type) {
      case "geofence":
        return <Bell size={16} className="text-blue-600 dark:text-blue-400" />;
      case "attendance":
        return (
          <Users size={16} className="text-green-600 dark:text-green-400" />
        );
      case "maintenance":
        return (
          <Wrench size={16} className="text-orange-600 dark:text-orange-400" />
        );
      case "announcements":
        return (
          <MessageSquare
            size={16}
            className="text-purple-600 dark:text-purple-400"
          />
        );
      default:
        return <Bell size={16} className="text-gray-600 dark:text-gray-400" />;
    }
  };

  const getNotificationTypeLabel = (notification: Tables<"notifications">) => {
    const metadata = notification.metadata as any;
    const type = metadata?.type || "general";
    return t(`notifications.${type}`);
  };

  const handleSelectNotification = (id: string) => {
    const newSelected = new Set(selectedNotifications);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedNotifications(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedNotifications.size === notifications.length) {
      setSelectedNotifications(new Set());
    } else {
      setSelectedNotifications(new Set(notifications.map((n) => n.id)));
    }
  };

  const handleMarkAsRead = async (ids?: string[]) => {
    const targetIds = ids || Array.from(selectedNotifications);
    if (targetIds.length === 0) return;

    setIsMarkingRead(true);
    try {
      const { error } = await supabase
        .from("notifications")
        .update({ read: true })
        .in("id", targetIds);

      if (error) throw error;

      // Update local state
      setNotifications((prev) =>
        prev.map((n) => (targetIds.includes(n.id) ? { ...n, read: true } : n)),
      );
      setSelectedNotifications(new Set());
    } catch (error) {
      console.error("Error marking notifications as read:", error);
    } finally {
      setIsMarkingRead(false);
    }
  };

  const handleMarkAsUnread = async (ids?: string[]) => {
    const targetIds = ids || Array.from(selectedNotifications);
    if (targetIds.length === 0) return;

    setIsMarkingRead(true);
    try {
      const { error } = await supabase
        .from("notifications")
        .update({ read: false })
        .in("id", targetIds);

      if (error) throw error;

      // Update local state
      setNotifications((prev) =>
        prev.map((n) => (targetIds.includes(n.id) ? { ...n, read: false } : n)),
      );
      setSelectedNotifications(new Set());
    } catch (error) {
      console.error("Error marking notifications as unread:", error);
    } finally {
      setIsMarkingRead(false);
    }
  };

  const handleDelete = async (ids?: string[]) => {
    const targetIds = ids || Array.from(selectedNotifications);
    if (targetIds.length === 0) return;

    if (!window.confirm(t("notifications.deleteConfirmation"))) return;

    setIsDeleting(true);
    try {
      const { error } = await supabase
        .from("notifications")
        .delete()
        .in("id", targetIds);

      if (error) throw error;

      // Update local state
      setNotifications((prev) => prev.filter((n) => !targetIds.includes(n.id)));
      setSelectedNotifications(new Set());
    } catch (error) {
      console.error("Error deleting notifications:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const exportNotifications = () => {
    const headers = ["Date", "Type", "Title", "Message", "Status"];

    const csvContent = [
      headers.join(","),
      ...notifications.map((notification) => {
        const metadata = notification.metadata as any;
        return [
          new Date(notification.created_at).toLocaleString(),
          metadata?.type || "general",
          `"${notification.title.replace(/"/g, '""')}"`,
          `"${notification.message.replace(/"/g, '""')}"`,
          notification.read ? "Read" : "Unread",
        ].join(",");
      }),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `notifications_${dateRange.start}_to_${dateRange.end}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const unreadCount = notifications.filter((n) => !n.read).length;
  const selectedCount = selectedNotifications.size;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <Bell size={24} className="mr-2" />
            {t("notifications.history")}
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {notifications.length} {t("notifications.notifications")} •{" "}
            {unreadCount} {t("notifications.unread")}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            onClick={exportNotifications}
            variant="outline"
            size="sm"
            leftIcon={<Download size={16} />}
            disabled={notifications.length === 0}
          >
            {t("common.export")}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={16} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder={t("notifications.searchNotifications")}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>

          {/* Type Filter */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Filter size={16} className="text-gray-400" />
            </div>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as FilterType)}
              className="block w-full pl-10 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="all">{t("common.all")} Types</option>
              <option value="geofence">{t("notifications.geofence")}</option>
              <option value="attendance">
                {t("notifications.attendance")}
              </option>
              <option value="maintenance">
                {t("notifications.maintenance")}
              </option>
              <option value="announcements">
                {t("notifications.announcements")}
              </option>
            </select>
          </div>

          {/* Status Filter */}
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as FilterStatus)}
            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          >
            <option value="all">{t("common.all")} Status</option>
            <option value="unread">{t("notifications.unread")}</option>
            <option value="read">{t("notifications.read")}</option>
          </select>

          {/* Date Range */}
          <div className="flex space-x-2">
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) =>
                setDateRange((prev) => ({ ...prev, start: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) =>
                setDateRange((prev) => ({ ...prev, end: e.target.value }))
              }
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedCount > 0 && (
        <div className="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-primary-700 dark:text-primary-300">
              {selectedCount} {t("notifications.selected")}
            </span>
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleMarkAsRead()}
                disabled={isMarkingRead}
                leftIcon={<Check size={14} />}
              >
                {t("notifications.markAsRead")}
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleMarkAsUnread()}
                disabled={isMarkingRead}
                leftIcon={<EyeOff size={14} />}
              >
                {t("notifications.markAsUnread")}
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={() => handleDelete()}
                disabled={isDeleting}
                leftIcon={<Trash2 size={14} />}
              >
                {t("common.delete")}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Notifications List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        {/* List Header */}
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={
                  selectedCount === notifications.length &&
                  notifications.length > 0
                }
                onChange={handleSelectAll}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {t("notifications.selectAll")}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="ghost"
                onClick={() =>
                  handleMarkAsRead(
                    notifications.filter((n) => !n.read).map((n) => n.id),
                  )
                }
                disabled={unreadCount === 0 || isMarkingRead}
                leftIcon={<CheckCheck size={14} />}
              >
                {t("notifications.markAllAsRead")}
              </Button>
            </div>
          </div>
        </div>

        {/* Notifications */}
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto" />
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              {t("common.loading")}
            </p>
          </div>
        ) : notifications.length === 0 ? (
          <div className="p-8 text-center text-gray-500 dark:text-gray-400">
            <Bell size={48} className="mx-auto mb-4 opacity-50" />
            <p>{t("notifications.noNotificationsFound")}</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                  notification.read ? "opacity-75" : ""
                } ${
                  selectedNotifications.has(notification.id)
                    ? "bg-primary-50 dark:bg-primary-900/20"
                    : ""
                }`}
              >
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    checked={selectedNotifications.has(notification.id)}
                    onChange={() => handleSelectNotification(notification.id)}
                    className="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />

                  <div className="flex-shrink-0 mt-1">
                    {getNotificationIcon(notification)}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {notification.title}
                        </p>
                        {!notification.read && (
                          <div className="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0" />
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                          {getNotificationTypeLabel(notification)}
                        </span>
                        <div className="flex items-center space-x-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleMarkAsRead([notification.id])}
                            disabled={notification.read}
                            className="p-1 h-auto"
                            title={t("notifications.markAsRead")}
                          >
                            <Eye size={14} />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleDelete([notification.id])}
                            className="p-1 h-auto text-red-600 hover:text-red-700"
                            title={t("common.delete")}
                          >
                            <Trash2 size={14} />
                          </Button>
                        </div>
                      </div>
                    </div>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                      {notification.message}
                    </p>
                    <div className="mt-2 flex items-center justify-between">
                      <p className="text-xs text-gray-500 dark:text-gray-500">
                        {new Date(notification.created_at).toLocaleString()}
                      </p>
                      {notification.read && (
                        <span className="text-xs text-gray-400 dark:text-gray-500 flex items-center">
                          <Check size={12} className="mr-1" />
                          {t("notifications.read")}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationHistory;
