import React from "react";
import { usePermissions } from "../../hooks/usePermissions";
import { Permission, DataScope, ResourceType, Action } from "../../lib/rbac";
import { UserRole } from "../../types";

interface PermissionGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  // فحص الصلاحيات
  permission?: Permission;
  permissions?: Permission[];
  // فحص نطاق البيانات
  dataScope?: DataScope;
  dataScopes?: DataScope[];
  // فحص العمليات
  resource?: ResourceType;
  action?: Action;
  context?: {
    resourceOwnerId?: string;
    resourceTenantId?: string;
  };
  // فحص الأدوار
  roles?: UserRole[];
  excludeRoles?: UserRole[];
  // منطق الفحص
  requireAll?: boolean; // true = AND, false = OR
}

/**
 * مكون حماية الصلاحيات - يعرض المحتوى فقط إذا كان المستخدم لديه الصلاحيات المطلوبة
 */
export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  fallback = null,
  permission,
  permissions = [],
  dataScope,
  dataScopes = [],
  resource,
  action,
  context,
  roles = [],
  excludeRoles = [],
  requireAll = false,
}) => {
  const {
    hasPermission,
    hasDataScope,
    canPerformAction,
    isAdmin,
    isSchoolManager,
    isSupervisor,
    isDriver,
    isParent,
    isStudent,
  } = usePermissions();

  // تحديد الدور الحالي
  const userRole = isAdmin ? UserRole.ADMIN :
                   isSchoolManager ? UserRole.SCHOOL_MANAGER :
                   isSupervisor ? UserRole.SUPERVISOR :
                   isDriver ? UserRole.DRIVER :
                   isParent ? UserRole.PARENT :
                   isStudent ? UserRole.STUDENT :
                   null;

  // فحص الأدوار المستبعدة
  if (excludeRoles.length > 0 && userRole && excludeRoles.includes(userRole)) {
    return <>{fallback}</>;
  }

  // فحص الأدوار المطلوبة
  if (roles.length > 0 && userRole && !roles.includes(userRole)) {
    return <>{fallback}</>;
  }

  const checks: boolean[] = [];

  // فحص الصلاحية الواحدة
  if (permission) {
    checks.push(hasPermission(permission));
  }

  // فحص الصلاحيات المتعددة
  if (permissions.length > 0) {
    const permissionChecks = permissions.map((p) => hasPermission(p));
    if (requireAll) {
      checks.push(permissionChecks.every(Boolean));
    } else {
      checks.push(permissionChecks.some(Boolean));
    }
  }

  // فحص نطاق البيانات الواحد
  if (dataScope) {
    checks.push(hasDataScope(dataScope));
  }

  // فحص نطاقات البيانات المتعددة
  if (dataScopes.length > 0) {
    const scopeChecks = dataScopes.map((s) => hasDataScope(s));
    if (requireAll) {
      checks.push(scopeChecks.every(Boolean));
    } else {
      checks.push(scopeChecks.some(Boolean));
    }
  }

  // فحص العمليات
  if (resource && action) {
    checks.push(canPerformAction(resource, action));
  }

  // إذا لم يتم تحديد أي فحوصات، اعرض المحتوى
  if (checks.length === 0) {
    return <>{children}</>;
  }

  // تطبيق منطق الفحص
  const hasAccess = requireAll ? checks.every(Boolean) : checks.some(Boolean);

  return hasAccess ? <>{children}</> : <>{fallback}</>;
};

/**
 * مكون حماية الأدوار - يعرض المحتوى فقط للأدوار المحددة
 */
export const RoleGuard: React.FC<{
  children: React.ReactNode;
  roles: UserRole[];
  fallback?: React.ReactNode;
}> = ({ children, roles, fallback = null }) => {
  return (
    <PermissionGuard roles={roles} fallback={fallback}>
      {children}
    </PermissionGuard>
  );
};

/**
 * مكون حماية الأدمن - يعرض المحتوى فقط للأدمن
 */
export const AdminGuard: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback = null }) => {
  return (
    <RoleGuard roles={[UserRole.ADMIN]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
};

/**
 * مكون حماية مدير المدرسة - يعرض المحتوى لمدير المدرسة والأدمن
 */
export const SchoolManagerGuard: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback = null }) => {
  return (
    <RoleGuard
      roles={[UserRole.ADMIN, UserRole.SCHOOL_MANAGER]}
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  );
};

/**
 * مكون حماية المشرف - يعرض المحتوى للمشرف ومن هم أعلى منه
 */
export const SupervisorGuard: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback = null }) => {
  return (
    <RoleGuard
      roles={[UserRole.ADMIN, UserRole.SCHOOL_MANAGER, UserRole.SUPERVISOR]}
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  );
};

/**
 * مكون حماية السائق - يعرض المحتوى للسائق فقط
 */
export const DriverGuard: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback = null }) => {
  return (
    <RoleGuard roles={[UserRole.DRIVER]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
};

/**
 * مكون حماية ولي الأمر - يعرض المحتوى لولي الأمر فقط
 */
export const ParentGuard: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback = null }) => {
  return (
    <RoleGuard roles={[UserRole.PARENT]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
};

export default PermissionGuard;
