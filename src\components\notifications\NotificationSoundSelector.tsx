import React, { useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Volume2, Play, Pause, Check } from "lucide-react";
import { Button } from "../ui/Button";
import { cn } from "../../utils/cn";

interface NotificationSoundSelectorProps {
  selectedSound: string;
  onSoundChange: (sound: string) => void;
  volume?: number;
  onVolumeChange?: (volume: number) => void;
  className?: string;
  disabled?: boolean;
}

const AVAILABLE_SOUNDS = [
  { id: "none", name: "None", file: null },
  { id: "notification", name: "Default", file: "/sounds/notification.mp3" },
  { id: "bell", name: "Bell", file: "/sounds/bell.mp3" },
  { id: "chime", name: "Chi<PERSON>", file: "/sounds/chime.mp3" },
  { id: "ding", name: "Ding", file: "/sounds/ding.mp3" },
  { id: "alert", name: "Alert", file: "/sounds/alert.mp3" },
];

export const NotificationSoundSelector: React.FC<
  NotificationSoundSelectorProps
> = ({
  selectedSound,
  onSoundChange,
  volume = 0.7,
  onVolumeChange,
  className = "",
  disabled = false,
}) => {
  const { t } = useTranslation();
  const [playingSound, setPlayingSound] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const playSound = async (soundId: string) => {
    const sound = AVAILABLE_SOUNDS.find((s) => s.id === soundId);
    if (!sound?.file) return;

    try {
      // Stop any currently playing sound
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }

      // Create new audio instance
      const audio = new Audio(sound.file);
      audio.volume = volume;
      audioRef.current = audio;

      setPlayingSound(soundId);

      audio.addEventListener("ended", () => {
        setPlayingSound(null);
      });

      audio.addEventListener("error", () => {
        console.error(`Failed to play sound: ${sound.file}`);
        setPlayingSound(null);
      });

      await audio.play();
    } catch (error) {
      console.error("Error playing sound:", error);
      setPlayingSound(null);
    }
  };

  const stopSound = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    setPlayingSound(null);
  };

  const handleSoundSelect = (soundId: string) => {
    if (disabled) return;
    onSoundChange(soundId);
    if (soundId !== "none") {
      playSound(soundId);
    }
  };

  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(event.target.value);
    onVolumeChange?.(newVolume);

    // Update current audio volume if playing
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center space-x-2 mb-3">
        <Volume2 size={16} className="text-gray-600 dark:text-gray-400" />
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {t("notifications.soundSettings")}
        </span>
      </div>

      {/* Sound Options */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
        {AVAILABLE_SOUNDS.map((sound) => {
          const isSelected = selectedSound === sound.id;
          const isPlaying = playingSound === sound.id;

          return (
            <div
              key={sound.id}
              className={cn(
                "flex items-center justify-between p-3 rounded-lg border transition-all duration-200 cursor-pointer",
                isSelected
                  ? "border-primary-500 bg-primary-50 dark:bg-primary-900/20"
                  : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600",
                disabled && "opacity-50 cursor-not-allowed",
              )}
              onClick={() => handleSoundSelect(sound.id)}
            >
              <div className="flex items-center space-x-3">
                <div
                  className={cn(
                    "w-4 h-4 rounded-full border-2 flex items-center justify-center",
                    isSelected
                      ? "border-primary-500 bg-primary-500"
                      : "border-gray-300 dark:border-gray-600",
                  )}
                >
                  {isSelected && <Check size={10} className="text-white" />}
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {sound.name}
                </span>
              </div>

              {sound.file && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation();
                    if (isPlaying) {
                      stopSound();
                    } else {
                      playSound(sound.id);
                    }
                  }}
                  disabled={disabled}
                  className="p-1 h-auto"
                  title={isPlaying ? "Stop preview" : "Preview sound"}
                >
                  {isPlaying ? <Pause size={14} /> : <Play size={14} />}
                </Button>
              )}
            </div>
          );
        })}
      </div>

      {/* Volume Control */}
      {onVolumeChange && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("notifications.volume")}
            </label>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {Math.round(volume * 100)}%
            </span>
          </div>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={volume}
            onChange={handleVolumeChange}
            disabled={disabled}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider"
          />
        </div>
      )}

      {/* Test Button */}
      {selectedSound !== "none" && (
        <div className="pt-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              if (playingSound === selectedSound) {
                stopSound();
              } else {
                playSound(selectedSound);
              }
            }}
            disabled={disabled}
            className="flex items-center gap-2"
          >
            {playingSound === selectedSound ? (
              <Pause size={16} />
            ) : (
              <Play size={16} />
            )}
            {playingSound === selectedSound ? "Stop Preview" : "Test Sound"}
          </Button>
        </div>
      )}
    </div>
  );
};

export default NotificationSoundSelector;
