-- إنشاء دالة لتنفيذ استعلامات SQL ديناميكية
-- Create function to execute dynamic SQL queries

-- حذف الدالة إذا كانت موجودة
DROP FUNCTION IF EXISTS exec_sql(text);

-- إنشاء الدالة الجديدة
CREATE OR REPLACE FUNCTION exec_sql(sql_query text)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result json;
    rec record;
    query_type text;
BEGIN
    -- التحقق من صحة الاستعلام
    IF sql_query IS NULL OR trim(sql_query) = '' THEN
        RAISE EXCEPTION 'SQL query cannot be empty';
    END IF;
    
    -- تحديد نوع الاستعلام
    query_type := upper(trim(split_part(sql_query, ' ', 1)));
    
    -- تنفيذ الاستعلام حسب النوع
    CASE query_type
        WHEN 'SELECT' THEN
            -- للاستعلامات SELECT، إرجاع النتائج
            EXECUTE sql_query INTO result;
            RETURN result;
            
        WHEN 'INSERT', 'UPDATE', 'DELETE' THEN
            -- للعمليات DML، تنفيذ الاستعلام وإرجاع عدد الصفوف المتأثرة
            EXECUTE sql_query;
            GET DIAGNOSTICS rec = ROW_COUNT;
            RETURN json_build_object('affected_rows', rec);
            
        WHEN 'CREATE', 'ALTER', 'DROP' THEN
            -- للعمليات DDL، تنفيذ الاستعلام وإرجاع رسالة نجاح
            EXECUTE sql_query;
            RETURN json_build_object('status', 'success', 'message', 'DDL operation completed');
            
        ELSE
            -- للاستعلامات الأخرى، تنفيذ عادي
            EXECUTE sql_query;
            RETURN json_build_object('status', 'success', 'message', 'Query executed');
    END CASE;
    
EXCEPTION
    WHEN OTHERS THEN
        -- في حالة حدوث خطأ، إرجاع تفاصيل الخطأ
        RETURN json_build_object(
            'error', true,
            'message', SQLERRM,
            'detail', SQLSTATE,
            'query', sql_query
        );
END;
$$;

-- منح الصلاحيات للدالة
GRANT EXECUTE ON FUNCTION exec_sql(text) TO service_role;
GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated;

-- إنشاء دالة للتحقق من حالة RLS
CREATE OR REPLACE FUNCTION check_rls_status()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result json;
    table_info record;
    rls_info json[] := '{}';
BEGIN
    -- فحص حالة RLS لجميع الجداول
    FOR table_info IN 
        SELECT schemaname, tablename, rowsecurity 
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY tablename
    LOOP
        rls_info := rls_info || json_build_object(
            'table', table_info.tablename,
            'rls_enabled', table_info.rowsecurity
        );
    END LOOP;
    
    result := json_build_object(
        'status', 'success',
        'tables', rls_info
    );
    
    RETURN result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'error', true,
            'message', SQLERRM
        );
END;
$$;

-- منح الصلاحيات
GRANT EXECUTE ON FUNCTION check_rls_status() TO service_role;
GRANT EXECUTE ON FUNCTION check_rls_status() TO authenticated;

-- إنشاء دالة لفحص السياسات
CREATE OR REPLACE FUNCTION get_table_policies(table_name text)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result json;
    policy_info record;
    policies json[] := '{}';
BEGIN
    -- الحصول على جميع السياسات للجدول المحدد
    FOR policy_info IN 
        SELECT policyname, cmd, permissive, roles, qual, with_check
        FROM pg_policies 
        WHERE tablename = table_name
        ORDER BY policyname
    LOOP
        policies := policies || json_build_object(
            'name', policy_info.policyname,
            'command', policy_info.cmd,
            'permissive', policy_info.permissive,
            'roles', policy_info.roles,
            'using', policy_info.qual,
            'with_check', policy_info.with_check
        );
    END LOOP;
    
    result := json_build_object(
        'table', table_name,
        'policies', policies,
        'count', array_length(policies, 1)
    );
    
    RETURN result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'error', true,
            'message', SQLERRM
        );
END;
$$;

-- منح الصلاحيات
GRANT EXECUTE ON FUNCTION get_table_policies(text) TO service_role;
GRANT EXECUTE ON FUNCTION get_table_policies(text) TO authenticated;

-- إنشاء دالة لفحص صحة قاعدة البيانات
CREATE OR REPLACE FUNCTION database_health_check()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result json;
    table_info record;
    tables_status json[] := '{}';
    total_tables int := 0;
    total_records int := 0;
BEGIN
    -- فحص الجداول الأساسية
    FOR table_info IN 
        SELECT 
            t.table_name,
            COALESCE(s.n_tup_ins, 0) as insert_count,
            COALESCE(s.n_tup_upd, 0) as update_count,
            COALESCE(s.n_tup_del, 0) as delete_count,
            COALESCE(s.n_live_tup, 0) as live_tuples
        FROM information_schema.tables t
        LEFT JOIN pg_stat_user_tables s ON s.relname = t.table_name
        WHERE t.table_schema = 'public' 
        AND t.table_type = 'BASE TABLE'
        AND t.table_name IN ('users', 'schools', 'buses', 'routes', 'students', 'notifications')
        ORDER BY t.table_name
    LOOP
        total_tables := total_tables + 1;
        total_records := total_records + table_info.live_tuples;
        
        tables_status := tables_status || json_build_object(
            'table', table_info.table_name,
            'records', table_info.live_tuples,
            'inserts', table_info.insert_count,
            'updates', table_info.update_count,
            'deletes', table_info.delete_count
        );
    END LOOP;
    
    result := json_build_object(
        'status', 'healthy',
        'timestamp', now(),
        'summary', json_build_object(
            'total_tables', total_tables,
            'total_records', total_records
        ),
        'tables', tables_status
    );
    
    RETURN result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'error', true,
            'message', SQLERRM,
            'timestamp', now()
        );
END;
$$;

-- منح الصلاحيات
GRANT EXECUTE ON FUNCTION database_health_check() TO service_role;
GRANT EXECUTE ON FUNCTION database_health_check() TO authenticated;

-- رسالة تأكيد
SELECT 'تم إنشاء الدوال المساعدة بنجاح' as message;
