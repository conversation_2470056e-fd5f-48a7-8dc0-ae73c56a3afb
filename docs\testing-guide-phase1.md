# دليل اختبار المرحلة الأولى - الميزات الأمنية 🧪

## نظرة عامة
هذا الدليل يوضح كيفية اختبار جميع الميزات الأمنية المطبقة في المرحلة الأولى من المشروع.

---

## 🚀 طرق الاختبار المتاحة

### 1. الاختبار التلقائي (Automated Testing)
```bash
# تشغيل جميع اختبارات المرحلة الأولى
npm run test:phase1

# تطبيق التهجيرات أولاً (إذا لم تكن مطبقة)
npm run phase1:security
```

### 2. الاختبار التفاعلي (Interactive Testing)
```bash
# تشغيل التطبيق
npm run dev

# الانتقال إلى صفحة الاختبار
http://localhost:5173/dashboard/security-test
```

### 3. الاختبار اليدوي (Manual Testing)
اختبار الميزات مباشرة في واجهة المستخدم.

---

## 🔒 اختبار فحص قوة كلمة المرور

### الاختبار التلقائي:
- ✅ كلمة مرور ضعيفة (`123`) - يجب أن ترفض
- ✅ كلمة مرور قوية (`MyStr0ng!P@ssw0rd2024`) - يجب أن تقبل
- ✅ كلمة مرور محظورة (`password123`) - يجب أن ترفض
- ✅ فحص المتطلبات (أحرف كبيرة، صغيرة، أرقام، رموز)

### الاختبار اليدوي:
1. **انتقل إلى صفحة التسجيل**: `/login` ثم "إنشاء حساب جديد"
2. **اختبر كلمات مرور مختلفة**:
   - `123` - يجب أن تظهر "ضعيف جداً"
   - `password` - يجب أن تظهر "ضعيف"
   - `Password123` - يجب أن تظهر "متوسط"
   - `MyStr0ng!P@ssw0rd2024` - يجب أن تظهر "قوي"
3. **تحقق من المؤشر البصري**: شريط التقدم يتغير حسب القوة
4. **راجع النصائح**: تظهر نصائح تحسين ديناميكية

### النتائج المتوقعة:
- مؤشر بصري يتغير لونه (أحمر → أصفر → أخضر)
- نقاط من 0-100 تظهر بوضوح
- قائمة متطلبات مع أيقونات ✅/❌
- نصائح تحسين مفيدة

---

## 🛡️ اختبار حماية Brute-force

### الاختبار التلقائي:
- ✅ تسجيل محاولة ناجحة
- ✅ تسجيل محاولة فاشلة
- ✅ فحص الحظر قبل الوصول للحد
- ✅ محاولات متعددة فاشلة
- ✅ الحصول على إحصائيات الحماية

### الاختبار اليدوي:
1. **محاولات دخول فاشلة**:
   - انتقل إلى `/login`
   - أدخل بريد إلكتروني صحيح وكلمة مرور خاطئة
   - كرر 5 مرات بسرعة
   - يجب أن تظهر رسالة حظر

2. **مراقبة الإحصائيات**:
   - انتقل إلى `/dashboard/security-test`
   - شغل اختبار "حماية Brute-force"
   - راجع الإحصائيات في لوحة الأدمن

### النتائج المتوقعة:
- حظر تلقائي بعد 5 محاولات فاشلة
- رسالة واضحة عن سبب الحظر ومدته
- تسجيل دقيق لجميع المحاولات
- إحصائيات شاملة للأدمن

---

## 🔐 اختبار التحقق الثنائي (2FA)

### الاختبار التلقائي:
- ✅ إعداد التحقق الثنائي
- ✅ إنشاء QR Code ورموز احتياطية
- ✅ التحقق من رمز TOTP
- ✅ إرسال رمز البريد الإلكتروني

### الاختبار اليدوي:
1. **إعداد 2FA**:
   - سجل دخول كمدير
   - انتقل إلى الإعدادات → الأمان
   - اختر "تفعيل التحقق الثنائي"
   - امسح QR Code بتطبيق Google Authenticator

2. **اختبار TOTP**:
   - أدخل الرمز من التطبيق
   - يجب أن يتم قبول الرمز الصحيح
   - احفظ رموز النسخ الاحتياطية

3. **اختبار البريد الإلكتروني**:
   - اختر "إرسال رمز عبر البريد"
   - تحقق من وصول الرمز
   - أدخل الرمز للتحقق

### النتائج المتوقعة:
- QR Code واضح وقابل للمسح
- 10 رموز نسخ احتياطية
- قبول الرموز الصحيحة ورفض الخاطئة
- واجهة سهلة الاستخدام

---

## 🕵️ اختبار مراقبة السلوك الشاذ

### الاختبار التلقائي:
- ✅ تحليل محاولة دخول عادية
- ✅ تحليل محاولة دخول مشبوهة
- ✅ الحصول على التنبيهات

### الاختبار اليدوي:
1. **محاكاة سلوك عادي**:
   - سجل دخول في الأوقات المعتادة
   - استخدم نفس الجهاز والمتصفح
   - راقب عدم ظهور تنبيهات

2. **محاكاة سلوك شاذ**:
   - سجل دخول في وقت غير معتاد (3 صباحاً)
   - استخدم متصفح أو جهاز مختلف
   - استخدم VPN لتغيير الموقع

3. **مراجعة التنبيهات**:
   - انتقل إلى لوحة الأدمن
   - راجع تنبيهات السلوك الشاذ
   - تحقق من دقة المعلومات

### النتائج المتوقعة:
- كشف تلقائي للسلوك الشاذ
- نقاط مخاطر دقيقة (0-100)
- تنبيهات مفصلة مع السبب
- تحديث تلقائي لأنماط السلوك

---

## 💻 اختبار إدارة الجلسات

### الاختبار التلقائي:
- ✅ إنشاء جلسة جديدة
- ✅ تحديث نشاط الجلسة
- ✅ التحقق من صحة الجلسة
- ✅ الحصول على الجلسات النشطة
- ✅ إنهاء الجلسة

### الاختبار اليدوي:
1. **مراقبة الجلسات**:
   - سجل دخول من أجهزة مختلفة
   - انتقل إلى الإعدادات → إدارة الجلسات
   - راجع قائمة الجلسات النشطة

2. **إنهاء الجلسات**:
   - أنهِ جلسة محددة
   - أنهِ جميع الجلسات الأخرى
   - تحقق من تسجيل الخروج التلقائي

3. **اختبار الحدود**:
   - سجل دخول من أكثر من 5 أجهزة
   - تحقق من إنهاء الجلسات القديمة تلقائياً

### النتائج المتوقعة:
- معلومات دقيقة عن كل جلسة
- إنهاء فوري للجلسات المحددة
- حد أقصى 5 جلسات متزامنة
- تنظيف تلقائي للجلسات المنتهية

---

## 🗄️ اختبار قاعدة البيانات

### الجداول المطلوب فحصها:
- `login_attempts` - محاولات تسجيل الدخول
- `user_blocks` - حظر المستخدمين
- `ip_blocks` - حظر عناوين IP
- `security_events` - الأحداث الأمنية
- `user_sessions` - جلسات المستخدمين
- `user_2fa_config` - إعدادات التحقق الثنائي
- `email_verification_codes` - رموز التحقق
- `user_behavior_patterns` - أنماط السلوك
- `anomaly_alerts` - تنبيهات الشذوذ

### الدوال المطلوب اختبارها:
- `log_security_event()` - تسجيل الأحداث الأمنية
- `check_login_attempts()` - فحص محاولات الدخول
- `analyze_user_behavior()` - تحليل السلوك
- `create_anomaly_alert()` - إنشاء تنبيه شذوذ

---

## 📊 تفسير النتائج

### معدل النجاح المتوقع:
- **90-100%**: ممتاز - جميع الميزات تعمل بشكل صحيح
- **70-89%**: جيد - بعض المشاكل البسيطة
- **50-69%**: متوسط - مشاكل تحتاج إصلاح
- **أقل من 50%**: ضعيف - مشاكل كبيرة تحتاج إصلاح فوري

### الأخطاء الشائعة:
1. **خطأ في الاتصال بقاعدة البيانات**
   - تحقق من متغيرات البيئة
   - تأكد من تطبيق التهجيرات

2. **فشل في إرسال البريد الإلكتروني**
   - متوقع في البيئة التجريبية
   - يحتاج إعداد خدمة إرسال حقيقية

3. **مشاكل في الصلاحيات**
   - تحقق من RLS policies
   - تأكد من صحة دور المستخدم

---

## 🔧 استكشاف الأخطاء

### إذا فشل اختبار معين:
1. **راجع رسالة الخطأ** في التفاصيل
2. **تحقق من console logs** في المتصفح
3. **راجع قاعدة البيانات** للتأكد من وجود الجداول
4. **أعد تشغيل التهجيرات** إذا لزم الأمر

### أوامر مفيدة للتشخيص:
```bash
# إعادة تطبيق التهجيرات
npm run phase1:security

# فحص حالة قاعدة البيانات
# (يتطلب الوصول المباشر لـ Supabase)

# مراجعة logs التطبيق
npm run dev
# ثم راجع console في المتصفح
```

---

## 📋 قائمة مراجعة الاختبار

### قبل البدء:
- [ ] تطبيق جميع التهجيرات
- [ ] التأكد من متغيرات البيئة
- [ ] وجود اتصال بقاعدة البيانات
- [ ] تشغيل التطبيق بنجاح

### أثناء الاختبار:
- [ ] تشغيل الاختبار التلقائي
- [ ] اختبار كل ميزة يدوياً
- [ ] مراجعة النتائج والأخطاء
- [ ] توثيق أي مشاكل

### بعد الاختبار:
- [ ] حفظ تقرير النتائج
- [ ] إصلاح أي مشاكل مكتشفة
- [ ] إعادة تشغيل الاختبارات
- [ ] توثيق التحسينات

---

## 🎯 الخطوات التالية

بعد نجاح جميع الاختبارات:
1. **الانتقال للمرحلة الثانية** - تنظيف النظام
2. **أو المرحلة الثالثة** - إعادة بناء RLS
3. **تطبيق الميزات في الإنتاج** مع الحذر

---

**📞 الدعم**: في حالة وجود مشاكل، راجع الوثائق أو اتصل بفريق التطوير.
