import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Bus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  MapPin,
  Plus,
  Loader2,
  RefreshCw,
} from "lucide-react";
import { Navbar } from "../../components/layout/Navbar";
import { Sidebar } from "../../components/layout/Sidebar";
import { Button } from "../../components/ui/Button";
import { Pagination, usePagination } from "../../components/ui/Pagination";
import { BusModal } from "../../components/buses/BusModal";
import { useAuth } from "../../contexts/AuthContext";
import { useDatabase } from "../../contexts/DatabaseContext";
import { supabase } from "../../lib/supabase";
import type { Tables } from "../../lib/api";

interface BusWithDriver extends Tables<"buses"> {
  driver?: Tables<"users">;
  tenant?: {
    id: string;
    name: string;
  };
}

export const BusesPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { users } = useDatabase();
  const [buses, setBuses] = useState<BusWithDriver[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "active" | "inactive"
  >("all");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedBus, setSelectedBus] = useState<Tables<"buses"> | null>(null);
  const [modalMode, setModalMode] = useState<"add" | "edit">("add");

  useEffect(() => {
    fetchBuses();
  }, [user?.tenant_id, user?.role]);

  const fetchBuses = async () => {
    // Admin users don't need tenant_id to view buses
    if (!user?.tenant_id && user?.role !== "admin") {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      console.log("BusesPage: Fetching buses for user:", {
        id: user?.id,
        role: user?.role,
        tenant_id: user?.tenant_id,
      });

      // For admin users, use RPC function to bypass RLS
      if (user?.role === "admin") {
        console.log("BusesPage: Admin user - fetching all buses via RPC");

        const { data, error } = await supabase.rpc(
          "get_all_buses_with_details",
        );

        if (error) {
          console.error("BusesPage: Error fetching buses via RPC:", error);
          // Fallback to direct query for admin
          const { data: fallbackData, error: fallbackError } = await supabase
            .from("buses")
            .select(
              `
              *,
              driver:users(*),
              tenant:tenants(id, name)
            `,
            )
            .order("created_at", { ascending: false });

          if (fallbackError) {
            throw fallbackError;
          }
          setBuses(fallbackData || []);
          return;
        }

        console.log("BusesPage: Admin buses fetched via RPC:", {
          count: data?.length || 0,
        });
        setBuses(data || []);
        return;
      }

      // For non-admin users, use regular query with tenant filter
      let query = supabase.from("buses").select(
        `
          *,
          driver:users(*),
          tenant:tenants(id, name)
        `,
      );

      if (user?.tenant_id) {
        query = query.eq("tenant_id", user.tenant_id);
        console.log("BusesPage: Filtering by tenant_id:", user.tenant_id);
      }

      const { data, error } = await query.order("created_at", {
        ascending: false,
      });

      if (error) {
        console.error("BusesPage: Error fetching buses:", error);
        throw error;
      }

      console.log("BusesPage: Fetched buses:", {
        count: data?.length || 0,
        buses: data?.map((b) => ({
          id: b.id,
          plate_number: b.plate_number,
          tenant_id: b.tenant_id,
          tenant_name: b.tenant?.name,
        })),
      });
      setBuses(data || []);
    } catch (error) {
      console.error("BusesPage: Error fetching buses:", error);
      setBuses([]);
      alert(
        "Failed to load buses. Please check your permissions and try again.",
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (busId: string) => {
    if (!confirm(t("buses.confirmDelete"))) return;

    try {
      const { error } = await supabase.from("buses").delete().eq("id", busId);

      if (error) throw error;
      await fetchBuses();
    } catch (error) {
      console.error("Error deleting bus:", error);
      alert(t("buses.deleteError"));
    }
  };

  const handleEdit = (bus: Tables<"buses">) => {
    setSelectedBus(bus);
    setModalMode("edit");
    setIsModalOpen(true);
  };

  const handleAdd = () => {
    setSelectedBus(null);
    setModalMode("add");
    setIsModalOpen(true);
  };

  const handleModalClose = (refreshData?: boolean) => {
    setIsModalOpen(false);
    setSelectedBus(null);
    if (refreshData) {
      fetchBuses();
    }
  };

  // Filter buses based on search query and filters
  const filteredBuses = buses
    .filter((bus) => {
      // Filter by status
      if (filterStatus === "active") return bus.is_active;
      if (filterStatus === "inactive") return !bus.is_active;
      return true;
    })
    .filter((bus) => {
      // Search by plate number
      if (!searchQuery) return true;
      return bus.plate_number.toLowerCase().includes(searchQuery.toLowerCase());
    });

  // Pagination
  const { currentPage, totalPages, startIndex, endIndex, goToPage } =
    usePagination(filteredBuses.length, 10);

  const paginatedBuses = filteredBuses.slice(startIndex, endIndex);

  // Get driver name from bus data
  const getDriverName = (bus: BusWithDriver) => {
    return bus.driver?.name || "-";
  };

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar />

        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            {/* Debug Info for Admin */}
            {user?.role === "admin" && (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                      Admin View - All Buses Across Schools
                    </h3>
                    <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                      Total buses: {buses.length} | Filtered:{" "}
                      {filteredBuses.length}
                    </p>
                  </div>
                  <Button
                    onClick={fetchBuses}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <RefreshCw size={16} />
                    Refresh
                  </Button>
                </div>
              </div>
            )}

            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {t("buses.manageBuses")}
                  {user?.role === "admin" && " (All Schools)"}
                </h1>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {t("common.view")}, {t("common.add")}, {t("common.edit")}{" "}
                  {t("buses.manageBuses").toLowerCase()}
                </p>
              </div>

              <div className="mt-4 md:mt-0">
                <Button onClick={handleAdd} className="flex items-center gap-2">
                  <Plus size={16} />
                  {t("buses.addBus")}
                </Button>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                  <div className="relative max-w-xs w-full">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search size={18} className="text-gray-400" />
                    </div>
                    <input
                      type="text"
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      placeholder={`${t("common.search")} ${t("buses.manageBuses").toLowerCase()}`}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Filter size={18} className="text-gray-400" />
                      </div>
                      <select
                        className="block pl-10 pr-8 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                        value={filterStatus}
                        onChange={(e) =>
                          setFilterStatus(
                            e.target.value as "all" | "active" | "inactive",
                          )
                        }
                      >
                        <option value="all">
                          {t("common.filter")}: {t("common.all")}
                        </option>
                        <option value="active">{t("buses.active")}</option>
                        <option value="inactive">{t("buses.inactive")}</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              {loading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin text-primary-500 mx-auto" />
                    <p className="text-gray-500 dark:text-gray-400 mt-2">
                      Loading buses...
                    </p>
                  </div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                        >
                          {t("buses.plateNumber")}
                        </th>
                        {user?.role === "admin" && (
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                          >
                            School
                          </th>
                        )}
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                        >
                          {t("buses.driver")}
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                        >
                          {t("buses.capacity")}
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                        >
                          {t("buses.status")}
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                        >
                          {t("common.actions")}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {paginatedBuses.length > 0 ? (
                        paginatedBuses.map((bus) => (
                          <tr
                            key={bus.id}
                            className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                          >
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10 bg-primary-100 dark:bg-primary-800/20 rounded-md flex items-center justify-center text-primary-600 dark:text-primary-400">
                                  <Bus size={20} />
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                                    {bus.plate_number}
                                  </div>
                                  <div className="text-sm text-gray-500 dark:text-gray-400">
                                    {bus.notes && bus.notes.length > 30
                                      ? `${bus.notes.substring(0, 30)}...`
                                      : bus.notes || "No notes"}
                                  </div>
                                </div>
                              </div>
                            </td>
                            {user?.role === "admin" && (
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                                  {bus.tenant?.name || "Unknown School"}
                                </div>
                              </td>
                            )}
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900 dark:text-white">
                                {getDriverName(bus)}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900 dark:text-white">
                                {bus.capacity} {t("common.seats")}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span
                                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                  bus.is_active
                                    ? "bg-accent-100 text-accent-800 dark:bg-accent-800 dark:text-accent-100"
                                    : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                                }`}
                              >
                                {bus.is_active
                                  ? t("buses.active")
                                  : t("buses.inactive")}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex items-center justify-end space-x-2">
                                <button
                                  className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 p-1"
                                  title={t("common.view")}
                                  onClick={() =>
                                    (window.location.href = `/dashboard/buses/${bus.id}`)
                                  }
                                >
                                  <Eye size={18} />
                                </button>
                                <button
                                  className="text-warning-600 hover:text-warning-900 dark:text-warning-400 dark:hover:text-warning-300 p-1"
                                  title={t("common.edit")}
                                  onClick={() => handleEdit(bus)}
                                >
                                  <Edit size={18} />
                                </button>
                                <button
                                  className="text-error-600 hover:text-error-900 dark:text-error-400 dark:hover:text-error-300 p-1"
                                  title={t("common.delete")}
                                  onClick={() => handleDelete(bus.id)}
                                >
                                  <Trash2 size={18} />
                                </button>
                                <button
                                  className="text-secondary-600 hover:text-secondary-900 dark:text-secondary-400 dark:hover:text-secondary-300 p-1"
                                  title={t("tracking.liveTracking")}
                                  onClick={() =>
                                    (window.location.href = `/dashboard/tracking?bus=${bus.id}`)
                                  }
                                >
                                  <MapPin size={18} />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td
                            colSpan={user?.role === "admin" ? 6 : 5}
                            className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400"
                          >
                            {searchQuery || filterStatus !== "all"
                              ? t("common.noResultsFound")
                              : t("buses.noBusesFound")}
                            <div className="mt-2 text-xs text-gray-400">
                              <p>Total buses in database: {buses.length}</p>
                              {user?.role === "admin" && (
                                <p>
                                  Viewing as admin - showing all buses across
                                  all schools
                                </p>
                              )}
                            </div>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {t("common.showing")} {startIndex + 1} - {endIndex}{" "}
                      {t("common.of")} {filteredBuses.length}{" "}
                      {t("buses.manageBuses").toLowerCase()}
                    </div>
                    <Pagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={goToPage}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>

      {/* Bus Modal */}
      <BusModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        bus={selectedBus}
        mode={modalMode}
      />
    </div>
  );
};
