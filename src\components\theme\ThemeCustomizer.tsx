/**
 * Theme Customizer Component
 * Main component for customizing themes
 * Phase 3: UI/UX Enhancement - Theme Customization
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useCustomTheme } from '../../contexts/CustomThemeContext';
import { ThemeService, ThemeConfig } from '../../services/ThemeService';
import { TenantService } from '../../services/TenantService';
import { Navbar } from '../layout/Navbar';
import { Sidebar } from '../layout/Sidebar';
import {
  Palette,
  Save,
  RotateCcw,
  Eye,
  Download,
  Upload,
  Paintbrush,
  Type,
  Layout,
  Image as ImageIcon,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface ThemeCustomizerProps {
  type: 'admin' | 'school';
}

interface ThemeConfig {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    // Sidebar specific colors
    sidebarBackground: string;
    sidebarText: string;
    sidebarActiveBackground: string;
    sidebarActiveText: string;
    sidebarHoverBackground: string;
  };
  typography: {
    fontFamily: string;
    fontSize: string;
    fontWeight: string;
  };
  layout: {
    borderRadius: string;
    spacing: string;
    shadows: boolean;
  };
  branding: {
    logo?: string;
    schoolName?: string;
    tagline?: string;
  };
}

const defaultTheme: ThemeConfig = {
  name: 'الثيم الافتراضي',
  colors: {
    primary: '#3b82f6',
    secondary: '#64748b',
    accent: '#10b981',
    background: '#ffffff',
    surface: '#f8fafc',
    text: '#1e293b',
    textSecondary: '#64748b',
    // Sidebar colors
    sidebarBackground: '#ffffff',
    sidebarText: '#374151',
    sidebarActiveBackground: '#dbeafe',
    sidebarActiveText: '#1d4ed8',
    sidebarHoverBackground: '#f3f4f6',
  },
  typography: {
    fontFamily: 'Cairo, sans-serif',
    fontSize: '16px',
    fontWeight: '400',
  },
  layout: {
    borderRadius: '8px',
    spacing: '16px',
    shadows: true,
  },
  branding: {
    schoolName: 'مدرستي',
    tagline: 'التعليم هو المستقبل',
  },
};

export const ThemeCustomizer: React.FC<ThemeCustomizerProps> = ({ type }) => {
  const { t, i18n } = useTranslation();
  const { user, tenant, refreshTenant } = useAuth();
  const { direction } = useTheme();
  const { theme, setTheme, refreshTheme } = useCustomTheme();
  const isRTL = direction === 'rtl' || i18n.language === 'ar';
  const [currentTheme, setCurrentTheme] = useState<ThemeConfig>(defaultTheme);
  const [previewMode, setPreviewMode] = useState(false);
  const [activeTab, setActiveTab] = useState<'colors' | 'typography' | 'layout' | 'branding'>('colors');
  const [saving, setSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<string | null>(null);
  const [schoolNameChanged, setSchoolNameChanged] = useState(false);
  const [originalSchoolName, setOriginalSchoolName] = useState<string>('');

  // Load current theme
  useEffect(() => {
    const loadTheme = async () => {
      if (user?.tenant_id) {
        const savedTheme = await ThemeService.getThemeByTenant(user.tenant_id);
        if (savedTheme) {
          setCurrentTheme(savedTheme);
          console.log('Loaded saved theme:', savedTheme);
        } else {
          // Set default with tenant info
          const schoolName = tenant?.name || 'مدرستي';
          setOriginalSchoolName(schoolName);
          setCurrentTheme(prev => ({
            ...prev,
            branding: {
              ...prev.branding,
              schoolName,
            },
          }));
        }
      }
    };

    loadTheme();
  }, [user?.tenant_id, tenant]);

  // Track school name changes
  useEffect(() => {
    if (originalSchoolName && currentTheme.branding.schoolName !== originalSchoolName) {
      setSchoolNameChanged(true);
    } else {
      setSchoolNameChanged(false);
    }
  }, [currentTheme.branding.schoolName, originalSchoolName]);

  // Apply theme preview
  const applyPreview = () => {
    if (previewMode) {
      document.documentElement.style.setProperty('--color-primary', currentTheme.colors.primary);
      document.documentElement.style.setProperty('--color-secondary', currentTheme.colors.secondary);
      document.documentElement.style.setProperty('--color-accent', currentTheme.colors.accent);
      document.documentElement.style.setProperty('--border-radius', currentTheme.layout.borderRadius);

      // Apply full theme for better preview including sidebar colors
      ThemeService.applyTheme(currentTheme);

      // Force Chrome to update sidebar colors immediately
      setTimeout(() => {
        ThemeService.forceChromeStyleUpdate(currentTheme);
      }, 50);
    }
  };

  useEffect(() => {
    if (previewMode) {
      applyPreview();
    } else {
      // Reset to original theme
      document.documentElement.style.removeProperty('--color-primary');
      document.documentElement.style.removeProperty('--color-secondary');
      document.documentElement.style.removeProperty('--color-accent');
      document.documentElement.style.removeProperty('--border-radius');
    }
  }, [previewMode, currentTheme]);

  const handleColorChange = (colorKey: keyof ThemeConfig['colors'], value: string) => {
    const newTheme = {
      ...currentTheme,
      colors: {
        ...currentTheme.colors,
        [colorKey]: value,
      },
    };

    setCurrentTheme(newTheme);

    // If it's a sidebar color and preview mode is on, apply immediately
    if (colorKey.startsWith('sidebar') && previewMode) {
      setTimeout(() => {
        ThemeService.applyTheme(newTheme);
        ThemeService.forceChromeStyleUpdate(newTheme);
      }, 50);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setSaveMessage(null);

      if (!user?.tenant_id || !user?.id) {
        setSaveMessage('خطأ: لا يمكن تحديد المدرسة أو المستخدم');
        return;
      }

      console.log('Saving theme to database for tenant:', user.tenant_id);
      console.log('Theme data:', currentTheme);
      console.log('School name changed:', schoolNameChanged);

      // Update school name in database if it changed
      const currentSchoolName = currentTheme.branding.schoolName?.trim();
      const hasSchoolNameChanged = currentSchoolName && currentSchoolName !== originalSchoolName;

      if (hasSchoolNameChanged) {
        console.log('Updating school name from', originalSchoolName, 'to', currentSchoolName);
        try {
          await TenantService.updateSchoolName(user.tenant_id, currentSchoolName);
          setOriginalSchoolName(currentSchoolName);
          setSchoolNameChanged(false);

          console.log('✅ School name updated successfully');
        } catch (error) {
          console.error('Error updating school name:', error);
          setSaveMessage(`❌ خطأ في تحديث اسم المدرسة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
          setTimeout(() => setSaveMessage(null), 5000);
          return;
        }
      }

      const themeName = `ثيم ${currentTheme.branding.schoolName || 'المدرسة'} - ${new Date().toLocaleDateString('ar')}`;
      const description = `ثيم محفوظ في ${new Date().toLocaleString('ar')}`;

      // Save to database permanently
      const themeId = await ThemeService.saveThemeToDatabase(
        currentTheme,
        themeName,
        user.tenant_id,
        user.id,
        {
          description,
          setAsActive: true,
        }
      );

      console.log('✅ Theme saved to database with ID:', themeId);

      // Apply theme immediately
      ThemeService.applyTheme(currentTheme);
      setTheme(currentTheme);

      // Refresh theme context to update all components
      await refreshTheme();

      // Refresh tenant data to reflect the new name
      await refreshTenant();

      const successMessage = hasSchoolNameChanged
        ? '✅ تم تحديث الثيم واسم المدرسة بنجاح! الآن اسم المدرسة محدث في قاعدة البيانات وسيظهر في جميع أنحاء التطبيق. 🎉'
        : '✅ تم تحديث الثيم بنجاح! الثيم محفوظ بشكل دائم في قاعدة البيانات. 🎨';

      setSaveMessage(successMessage);

      // Clear message after 5 seconds
      setTimeout(() => setSaveMessage(null), 5000);
    } catch (error) {
      console.error('Error saving theme to database:', error);
      setSaveMessage(`❌ حدث خطأ أثناء حفظ الثيم: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
      setTimeout(() => setSaveMessage(null), 5000);
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    setCurrentTheme(defaultTheme);
    setPreviewMode(false);
  };

  const tabs = [
    { id: 'colors', label: t('themes.colors'), icon: <Palette size={16} /> },
    { id: 'typography', label: t('themes.typography'), icon: <Type size={16} /> },
    { id: 'layout', label: t('themes.layout'), icon: <Layout size={16} /> },
    { id: 'branding', label: t('themes.branding'), icon: <Paintbrush size={16} /> },
  ];

  return (
    <div className={`w-full ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-4 md:p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Palette className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {type === 'admin' ? t('themes.manageThemes') : t('themes.schoolTheme')}
              </h1>
              <p className="text-gray-600">
                {type === 'admin'
                  ? t('themes.manageThemes')
                  : t('themes.themeCustomization')
                }
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <button
              type="button"
              onClick={() => setPreviewMode(!previewMode)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                previewMode 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Eye size={16} />
              {previewMode ? 'إيقاف المعاينة' : 'معاينة'}
            </button>
            
            <button
              type="button"
              onClick={handleReset}
              className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              <RotateCcw size={16} />
              إعادة تعيين
            </button>
            
            <button
              type="button"
              onClick={handleSave}
              disabled={saving}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                saving
                  ? 'bg-gray-400 text-white cursor-not-allowed'
                  : 'bg-green-600 text-white hover:bg-green-700'
              }`}
            >
              <Save size={16} />
              {saving ? t('themes.saving') : t('themes.updateAndSave')}
            </button>
          </div>
        </div>

        {/* Save Message */}
        {saveMessage && (
          <div className={`mt-4 p-4 rounded-lg ${
            saveMessage.includes('نجاح')
              ? 'bg-green-50 border border-green-200 text-green-800'
              : 'bg-red-50 border border-red-200 text-red-800'
          }`}>
            <div className="flex items-center gap-2">
              {saveMessage.includes('نجاح') ? (
                <CheckCircle size={20} />
              ) : (
                <AlertCircle size={20} />
              )}
              <span>{saveMessage}</span>
            </div>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-4 gap-4 md:gap-6">
        {/* Controls */}
        <div className="xl:col-span-3">
          <div className="bg-white rounded-lg shadow-sm">
            {/* Tabs */}
            <div className="border-b border-gray-200">
              <nav className="flex flex-wrap gap-2 md:space-x-8 px-4 md:px-6 overflow-x-auto">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    type="button"
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {tab.icon}
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-4 md:p-6">
              {activeTab === 'colors' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900">تخصيص الألوان</h3>
                  
                  <div className="space-y-8">
                    {/* Basic Colors */}
                    <div>
                      <h4 className="text-md font-semibold text-gray-800 mb-4">الألوان الأساسية</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {Object.entries(currentTheme.colors)
                          .filter(([key]) => !key.startsWith('sidebar'))
                          .map(([key, value]) => (
                          <div key={key} className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700">
                              {key === 'primary' && 'اللون الأساسي'}
                              {key === 'secondary' && 'اللون الثانوي'}
                              {key === 'accent' && 'لون التمييز'}
                              {key === 'background' && 'لون الخلفية'}
                              {key === 'surface' && 'لون السطح'}
                              {key === 'text' && 'لون النص'}
                              {key === 'textSecondary' && 'لون النص الثانوي'}
                            </label>
                            <div className="flex items-center gap-3">
                              <input
                                type="color"
                                value={value}
                                onChange={(e) => handleColorChange(key as keyof ThemeConfig['colors'], e.target.value)}
                                className="w-12 h-12 rounded-lg border border-gray-300 cursor-pointer"
                                title={`اختر ${key === 'primary' ? 'اللون الأساسي' : key}`}
                              />
                              <input
                                type="text"
                                value={value}
                                onChange={(e) => handleColorChange(key as keyof ThemeConfig['colors'], e.target.value)}
                                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="#000000"
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Sidebar Colors - Enhanced Visibility */}
                    <div className="bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 border-4 border-indigo-300 rounded-2xl p-8 shadow-2xl mt-8">
                      <div className="text-center mb-6">
                        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl shadow-lg mb-4">
                          <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <h4 className="text-2xl font-black text-indigo-900 mb-2">
                          🎨 ألوان القائمة الجانبية (Sidebar)
                        </h4>
                        <p className="text-lg text-indigo-700 font-semibold">
                          تحكم كامل في مظهر القائمة الجانبية
                        </p>
                      </div>
                      <div className="bg-gradient-to-r from-purple-100 to-blue-100 border border-purple-300 rounded-xl p-4 mb-6">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-2xl">🎨</span>
                          <p className="text-sm text-purple-800 font-bold">
                            تحكم في ألوان القائمة الجانبية لتناسب هوية مدرستك
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-lg">👈</span>
                          <p className="text-xs text-purple-700">
                            هذه الألوان تؤثر على القائمة الجانبية الموجودة على {isRTL ? 'يمين' : 'يسار'} الشاشة
                          </p>
                        </div>
                        <div className="mt-2 p-2 bg-white bg-opacity-50 rounded-lg">
                          <p className="text-xs text-purple-600 font-medium">
                            💡 نصيحة: جرب ألوان متناسقة مع ألوان مدرستك الأساسية
                          </p>
                        </div>
                      </div>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {Object.entries(currentTheme.colors)
                          .filter(([key]) => key.startsWith('sidebar'))
                          .map(([key, value]) => (
                          <div key={key} className="bg-white rounded-xl p-6 border-2 border-indigo-200 shadow-lg hover:shadow-xl transition-all duration-300 space-y-4">
                            <label className="block text-lg font-bold text-indigo-800 flex items-center gap-3 mb-2">
                              {key === 'sidebarBackground' && (
                                <>
                                  <div className="w-6 h-6 bg-gray-100 rounded-lg border-2 border-gray-300 shadow-sm"></div>
                                  🏠 خلفية القائمة الجانبية
                                </>
                              )}
                              {key === 'sidebarText' && (
                                <>
                                  <div className="w-6 h-6 bg-gray-600 rounded-lg shadow-sm"></div>
                                  📝 نص القائمة الجانبية
                                </>
                              )}
                              {key === 'sidebarActiveBackground' && (
                                <>
                                  <div className="w-6 h-6 bg-blue-100 rounded-lg border-2 border-blue-300 shadow-sm"></div>
                                  ⭐ خلفية العنصر النشط
                                </>
                              )}
                              {key === 'sidebarActiveText' && (
                                <>
                                  <div className="w-6 h-6 bg-blue-600 rounded-lg shadow-sm"></div>
                                  ✨ نص العنصر النشط
                                </>
                              )}
                              {key === 'sidebarHoverBackground' && (
                                <>
                                  <div className="w-6 h-6 bg-gray-50 rounded-lg border-2 border-gray-200 shadow-sm"></div>
                                  👆 خلفية عند التمرير
                                </>
                              )}
                            </label>
                            <p className="text-sm text-gray-600 mb-3">
                              {key === 'sidebarBackground' && 'اللون الأساسي لخلفية القائمة الجانبية'}
                              {key === 'sidebarText' && 'لون النصوص في القائمة الجانبية'}
                              {key === 'sidebarActiveBackground' && 'لون خلفية العنصر المحدد حالياً'}
                              {key === 'sidebarActiveText' && 'لون نص العنصر المحدد حالياً'}
                              {key === 'sidebarHoverBackground' && 'لون الخلفية عند تمرير الماوس'}
                            </p>
                            <div className="flex items-center gap-4">
                              <div className="relative">
                                <input
                                  type="color"
                                  value={value}
                                  onChange={(e) => handleColorChange(key as keyof ThemeConfig['colors'], e.target.value)}
                                  className="w-16 h-16 rounded-xl border-4 border-indigo-300 cursor-pointer shadow-lg hover:shadow-xl transition-all duration-300"
                                  title={`اختر ${key === 'sidebarBackground' ? 'خلفية القائمة الجانبية' : key}`}
                                />
                                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center">
                                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                                  </svg>
                                </div>
                              </div>
                              <div className="flex-1">
                                <input
                                  type="text"
                                  value={value}
                                  onChange={(e) => handleColorChange(key as keyof ThemeConfig['colors'], e.target.value)}
                                  className="w-full px-4 py-3 border-2 border-indigo-200 rounded-xl focus:ring-4 focus:ring-indigo-100 focus:border-indigo-500 transition-all duration-300 text-lg font-mono"
                                  placeholder="#000000"
                                />
                                <p className="text-xs text-gray-500 mt-1">أدخل كود اللون أو استخدم منتقي الألوان</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'typography' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900">تخصيص الخطوط</h3>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        نوع الخط
                      </label>
                      <select
                        value={currentTheme.typography.fontFamily}
                        onChange={(e) => setCurrentTheme(prev => ({
                          ...prev,
                          typography: { ...prev.typography, fontFamily: e.target.value }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="Cairo, sans-serif">Cairo (الافتراضي)</option>
                        <option value="Amiri, serif">Amiri</option>
                        <option value="Tajawal, sans-serif">Tajawal</option>
                        <option value="Almarai, sans-serif">Almarai</option>
                        <option value="Noto Sans Arabic, sans-serif">Noto Sans Arabic</option>
                        <option value="IBM Plex Sans Arabic, sans-serif">IBM Plex Sans Arabic</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        حجم الخط الأساسي: {currentTheme.typography.fontSize}
                      </label>
                      <input
                        type="range"
                        min="12"
                        max="20"
                        step="1"
                        value={parseInt(currentTheme.typography.fontSize)}
                        onChange={(e) => setCurrentTheme(prev => ({
                          ...prev,
                          typography: { ...prev.typography, fontSize: `${e.target.value}px` }
                        }))}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>صغير (12px)</span>
                        <span>متوسط (16px)</span>
                        <span>كبير (20px)</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        وزن الخط
                      </label>
                      <select
                        value={currentTheme.typography.fontWeight}
                        onChange={(e) => setCurrentTheme(prev => ({
                          ...prev,
                          typography: { ...prev.typography, fontWeight: e.target.value }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="300">خفيف (300)</option>
                        <option value="400">عادي (400)</option>
                        <option value="500">متوسط (500)</option>
                        <option value="600">سميك (600)</option>
                        <option value="700">عريض (700)</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'layout' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900">تخصيص التخطيط</h3>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        استدارة الحدود: {currentTheme.layout.borderRadius}
                      </label>
                      <input
                        type="range"
                        min="0"
                        max="20"
                        step="2"
                        value={parseInt(currentTheme.layout.borderRadius)}
                        onChange={(e) => setCurrentTheme(prev => ({
                          ...prev,
                          layout: { ...prev.layout, borderRadius: `${e.target.value}px` }
                        }))}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>مربع (0px)</span>
                        <span>متوسط (8px)</span>
                        <span>دائري (20px)</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        المسافات: {currentTheme.layout.spacing}
                      </label>
                      <input
                        type="range"
                        min="8"
                        max="32"
                        step="4"
                        value={parseInt(currentTheme.layout.spacing)}
                        onChange={(e) => setCurrentTheme(prev => ({
                          ...prev,
                          layout: { ...prev.layout, spacing: `${e.target.value}px` }
                        }))}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>ضيق (8px)</span>
                        <span>متوسط (16px)</span>
                        <span>واسع (32px)</span>
                      </div>
                    </div>

                    <div>
                      <label className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={currentTheme.layout.shadows}
                          onChange={(e) => setCurrentTheme(prev => ({
                            ...prev,
                            layout: { ...prev.layout, shadows: e.target.checked }
                          }))}
                          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <span className="text-sm font-medium text-gray-700">تفعيل الظلال</span>
                      </label>
                      <p className="text-xs text-gray-500 mt-1">
                        إضافة ظلال للبطاقات والعناصر لمظهر أكثر عمقاً
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'branding' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900">العلامة التجارية</h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                        🏫 اسم المدرسة
                        {schoolNameChanged && (
                          <span className="inline-flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">
                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                            تم التغيير
                          </span>
                        )}
                      </label>
                      <div className="relative">
                        <input
                          type="text"
                          value={currentTheme.branding.schoolName || ''}
                          onChange={(e) => {
                            const newName = e.target.value;
                            setCurrentTheme(prev => ({
                              ...prev,
                              branding: { ...prev.branding, schoolName: newName }
                            }));

                            // Apply theme immediately for live preview
                            const updatedTheme = {
                              ...currentTheme,
                              branding: { ...currentTheme.branding, schoolName: newName }
                            };
                            ThemeService.applyTheme(updatedTheme);
                            setTheme(updatedTheme);
                          }}
                          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:border-transparent transition-all ${
                            schoolNameChanged
                              ? 'border-orange-300 focus:ring-orange-500 bg-orange-50'
                              : 'border-gray-300 focus:ring-blue-500'
                          }`}
                          placeholder="اسم المدرسة"
                        />
                        {schoolNameChanged && (
                          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                            <svg className="w-4 h-4 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                      </div>
                      {schoolNameChanged && (
                        <p className="text-xs text-orange-600 mt-1 flex items-center gap-1">
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                          سيتم تحديث اسم المدرسة في قاعدة البيانات عند الحفظ
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        الشعار
                      </label>
                      <input
                        type="text"
                        value={currentTheme.branding.tagline || ''}
                        onChange={(e) => setCurrentTheme(prev => ({
                          ...prev,
                          branding: { ...prev.branding, tagline: e.target.value }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="شعار المدرسة"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        رابط اللوجو (اختياري)
                      </label>
                      <input
                        type="url"
                        value={currentTheme.branding.logo || ''}
                        onChange={(e) => setCurrentTheme(prev => ({
                          ...prev,
                          branding: { ...prev.branding, logo: e.target.value }
                        }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="https://example.com/logo.png"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        أدخل رابط صورة اللوجو أو اتركه فارغاً
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Preview */}
        <div className="xl:col-span-1">
          <div className="bg-white rounded-lg shadow-sm p-4 md:p-6 sticky top-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">معاينة الثيم</h3>
            
            <div 
              className="border-2 border-dashed border-gray-300 rounded-lg p-4 space-y-4"
              style={{
                backgroundColor: previewMode ? currentTheme.colors.background : undefined,
                color: previewMode ? currentTheme.colors.text : undefined,
              }}
            >
              <div 
                className="p-3 rounded-lg"
                style={{
                  backgroundColor: previewMode ? currentTheme.colors.primary : '#3b82f6',
                  color: 'white',
                }}
              >
                <h4 className="font-semibold">{currentTheme.branding.schoolName}</h4>
                <p className="text-sm opacity-90">{currentTheme.branding.tagline}</p>
              </div>
              
              <div 
                className="p-3 rounded-lg"
                style={{
                  backgroundColor: previewMode ? currentTheme.colors.surface : '#f8fafc',
                }}
              >
                <p className="text-sm">هذا مثال على النص في الثيم الجديد</p>
              </div>
              
              <button
                type="button"
                className="w-full py-2 px-4 rounded-lg text-white font-medium"
                style={{
                  backgroundColor: previewMode ? currentTheme.colors.accent : '#10b981',
                }}
              >
                زر تجريبي
              </button>

              {/* Sidebar Preview */}
              <div className="mt-6 bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-xl p-4 shadow-sm">
                <h4 className="text-sm font-bold text-blue-900 mb-3 flex items-center gap-2">
                  <div className="bg-blue-600 p-1.5 rounded-md">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  معاينة القائمة الجانبية
                </h4>
                <p className="text-xs text-blue-700 mb-3 bg-blue-100 p-2 rounded-lg">
                  👀 هذه معاينة لكيفية ظهور ألوان القائمة الجانبية
                </p>
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-3 space-y-1 min-h-[120px]"
                  style={{
                    backgroundColor: previewMode ? currentTheme.colors.sidebarBackground : '#ffffff',
                  }}
                >
                  <div
                    className="p-2 rounded text-sm flex items-center gap-2 transition-colors"
                    style={{
                      color: previewMode ? currentTheme.colors.sidebarText : '#374151',
                    }}
                  >
                    <span>📊</span>
                    <span>لوحة التحكم</span>
                  </div>
                  <div
                    className="p-2 rounded text-sm flex items-center gap-2 font-medium transition-colors"
                    style={{
                      backgroundColor: previewMode ? currentTheme.colors.sidebarActiveBackground : '#dbeafe',
                      color: previewMode ? currentTheme.colors.sidebarActiveText : '#1d4ed8',
                    }}
                  >
                    <span>🎨</span>
                    <span>الثيمات (نشط)</span>
                  </div>
                  <div
                    className="p-2 rounded text-sm flex items-center gap-2 transition-colors hover:bg-opacity-80"
                    style={{
                      color: previewMode ? currentTheme.colors.sidebarText : '#374151',
                      backgroundColor: 'transparent',
                    }}
                    onMouseEnter={(e) => {
                      if (previewMode) {
                        e.currentTarget.style.backgroundColor = currentTheme.colors.sidebarHoverBackground;
                      }
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                  >
                    <span>⚙️</span>
                    <span>الإعدادات</span>
                  </div>
                  <div
                    className="p-2 rounded text-sm flex items-center gap-2 transition-colors"
                    style={{
                      color: previewMode ? currentTheme.colors.sidebarText : '#374151',
                    }}
                  >
                    <span>👥</span>
                    <span>المستخدمين</span>
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  مرر الماوس على "الإعدادات" لرؤية لون التمرير
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
