import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  AlertTriangle,
  User,
  Calendar,
  MessageSquare,
  CheckCircle,
  Clock,
  XCircle,
} from "lucide-react";
import { getComplaints, updateComplaintStatus } from "../../lib/api";
import { useAuth } from "../../contexts/AuthContext";
import { Button } from "../ui/Button";
import type { Tables } from "../../lib/api";

interface ComplaintWithUser extends Tables<"complaints"> {
  user: {
    id: string;
    name: string;
    avatar_url?: string;
  };
}

interface ComplaintsListProps {
  showFilters?: boolean;
  showActions?: boolean;
}

export const ComplaintsList: React.FC<ComplaintsListProps> = ({
  showFilters = true,
  showActions = false,
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [complaints, setComplaints] = useState<ComplaintWithUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: "",
    type: "",
  });
  const [selectedComplaint, setSelectedComplaint] = useState<string | null>(
    null,
  );
  const [response, setResponse] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (user?.tenant_id) {
      fetchComplaints();
    }
  }, [user?.tenant_id, filters]);

  const fetchComplaints = async () => {
    if (!user?.tenant_id) return;

    setLoading(true);
    try {
      const data = await getComplaints(
        user.tenant_id,
        filters.status || undefined,
        showActions ? undefined : user.id,
      );

      let filteredData = data || [];

      // Apply type filter
      if (filters.type) {
        filteredData = filteredData.filter(
          (complaint) => complaint.type === filters.type,
        );
      }

      setComplaints(filteredData as ComplaintWithUser[]);
    } catch (error) {
      console.error("Error fetching complaints:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (complaintId: string, newStatus: string) => {
    setIsUpdating(true);
    try {
      await updateComplaintStatus(
        complaintId,
        newStatus,
        response || undefined,
      );
      setSelectedComplaint(null);
      setResponse("");
      fetchComplaints();
    } catch (error) {
      console.error("Error updating complaint status:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock size={16} className="text-yellow-500" />;
      case "in_progress":
        return <AlertTriangle size={16} className="text-blue-500" />;
      case "resolved":
        return <CheckCircle size={16} className="text-green-500" />;
      case "rejected":
        return <XCircle size={16} className="text-red-500" />;
      default:
        return <Clock size={16} className="text-gray-500" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "pending":
        return t("complaints.status.pending");
      case "in_progress":
        return t("complaints.status.inProgress");
      case "resolved":
        return t("complaints.status.resolved");
      case "rejected":
        return t("complaints.status.rejected");
      default:
        return status;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "driver":
        return t("complaints.type.driver");
      case "service":
        return t("complaints.type.service");
      case "route":
        return t("complaints.type.route");
      case "general":
        return t("complaints.type.general");
      default:
        return type;
    }
  };

  const getPriorityColor = (status: string, createdAt: string) => {
    const daysSinceCreated = Math.floor(
      (Date.now() - new Date(createdAt).getTime()) / (1000 * 60 * 60 * 24),
    );

    if (status === "pending" && daysSinceCreated > 7) {
      return "border-l-red-500";
    } else if (status === "pending" && daysSinceCreated > 3) {
      return "border-l-yellow-500";
    } else if (status === "in_progress") {
      return "border-l-blue-500";
    } else if (status === "resolved") {
      return "border-l-green-500";
    }
    return "border-l-gray-300";
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {t("complaints.complaints")} ({complaints.length})
          </h3>
        </div>

        {showFilters && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t("complaints.status.title")}
              </label>
              <select
                value={filters.status}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, status: e.target.value }))
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
              >
                <option value="">{t("common.all")}</option>
                <option value="pending">
                  {t("complaints.status.pending")}
                </option>
                <option value="in_progress">
                  {t("complaints.status.inProgress")}
                </option>
                <option value="resolved">
                  {t("complaints.status.resolved")}
                </option>
                <option value="rejected">
                  {t("complaints.status.rejected")}
                </option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {t("complaints.type.title")}
              </label>
              <select
                value={filters.type}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, type: e.target.value }))
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
              >
                <option value="">{t("common.all")}</option>
                <option value="driver">{t("complaints.type.driver")}</option>
                <option value="service">{t("complaints.type.service")}</option>
                <option value="route">{t("complaints.type.route")}</option>
                <option value="general">{t("complaints.type.general")}</option>
              </select>
            </div>
          </div>
        )}
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              {t("common.loading")}
            </p>
          </div>
        ) : complaints.length === 0 ? (
          <div className="p-8 text-center">
            <AlertTriangle size={48} className="mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {t("complaints.noComplaints")}
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {t("complaints.noComplaintsDescription")}
            </p>
          </div>
        ) : (
          complaints.map((complaint) => (
            <div
              key={complaint.id}
              className={`p-6 border-l-4 ${getPriorityColor(complaint.status, complaint.created_at)}`}
            >
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  {complaint.user?.avatar_url ? (
                    <img
                      src={complaint.user.avatar_url}
                      alt={complaint.user.name}
                      className="h-10 w-10 rounded-full"
                    />
                  ) : (
                    <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                      <User
                        size={20}
                        className="text-gray-500 dark:text-gray-400"
                      />
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {complaint.user?.name || t("common.anonymous")}
                      </p>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {getTypeLabel(complaint.type)}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(complaint.status)}
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {getStatusLabel(complaint.status)}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mb-2">
                    <Calendar size={12} className="mr-1" />
                    {new Date(complaint.created_at).toLocaleDateString()}
                  </div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    {complaint.title}
                  </h4>
                  <p className="text-sm text-gray-700 dark:text-gray-300 mb-3">
                    {complaint.description}
                  </p>

                  {complaint.response && (
                    <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="flex items-start space-x-1">
                        <MessageSquare
                          size={14}
                          className="text-blue-500 mt-0.5 flex-shrink-0"
                        />
                        <div>
                          <p className="text-xs font-medium text-blue-800 dark:text-blue-200 mb-1">
                            {t("complaints.response")}
                          </p>
                          <p className="text-sm text-blue-700 dark:text-blue-300">
                            {complaint.response}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {showActions && complaint.status === "pending" && (
                    <div className="mt-4">
                      {selectedComplaint === complaint.id ? (
                        <div className="space-y-3">
                          <textarea
                            value={response}
                            onChange={(e) => setResponse(e.target.value)}
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-sm"
                            placeholder={t("complaints.responsePlaceholder")}
                          />
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              onClick={() =>
                                handleStatusUpdate(complaint.id, "resolved")
                              }
                              disabled={isUpdating}
                            >
                              {t("complaints.resolve")}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() =>
                                handleStatusUpdate(complaint.id, "in_progress")
                              }
                              disabled={isUpdating}
                            >
                              {t("complaints.inProgress")}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setSelectedComplaint(null);
                                setResponse("");
                              }}
                              disabled={isUpdating}
                            >
                              {t("common.cancel")}
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setSelectedComplaint(complaint.id)}
                        >
                          {t("complaints.respond")}
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default ComplaintsList;
