import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import { EyeIcon, EyeOffIcon, LogIn, Sun, Moon, AlertTriangle } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { ForgotPasswordForm } from '../../components/auth/ForgotPasswordForm';
import { SignUpForm } from '../../components/auth/SignUpForm';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { LoginLogo } from '../../components/theme/SchoolLogo';
import { CentralizedPermissionService } from '../../services/CentralizedPermissionService';

export const LoginPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { theme, toggleTheme, direction, setDirection } = useTheme();
  const { login, isLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [showSignUp, setShowSignUp] = useState(false);

  const permissionService = CentralizedPermissionService.getInstance();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      setError(t('auth.errors.allFieldsRequired'));
      return;
    }

    setError('');

    try {
      const { success, error: loginError } = await login(email, password);

      if (success) {
        // تسجيل حدث أمني للدخول الناجح
        await permissionService.logSecurityEvent(
          'USER_LOGIN_SUCCESS',
          'INFO',
          'User logged in successfully',
          undefined,
          undefined,
          { email, timestamp: new Date().toISOString() }
        );

        // Redirect to the page user tried to access or dashboard
        const from = (location.state as any)?.from || '/dashboard';
        navigate(from, { replace: true });
      } else {
        // تسجيل حدث أمني للدخول الفاشل
        await permissionService.logSecurityEvent(
          'USER_LOGIN_FAILED',
          'WARNING',
          'Failed login attempt',
          undefined,
          undefined,
          { email, error: loginError, timestamp: new Date().toISOString() }
        );

        setError(loginError || t('auth.errors.invalidCredentials'));
        setPassword(''); // Clear password for security
      }
    } catch (error) {
      console.error('Login error:', error);
      setError(t('auth.errors.networkError') || 'حدث خطأ في الشبكة');
      setPassword('');
    }
  };
  
  const toggleLanguage = () => {
    const newLang = i18n.language === 'en' ? 'ar' : 'en';
    const newDir = newLang === 'ar' ? 'rtl' : 'ltr';
    i18n.changeLanguage(newLang);
    setDirection(newDir);
  };
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4 sm:px-6 lg:px-8">
      <div className="absolute top-4 right-4 flex space-x-2">
        <button
          onClick={toggleTheme}
          className="p-2 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 bg-white dark:bg-gray-800 shadow-sm"
          aria-label={theme === 'dark' ? t('settings.lightMode') : t('settings.darkMode')}
        >
          {theme === 'dark' ? <Sun size={20} /> : <Moon size={20} />}
        </button>
        
        <button
          onClick={toggleLanguage}
          className="p-2 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 bg-white dark:bg-gray-800 shadow-sm"
        >
          <span className="text-sm font-medium">
            {i18n.language === 'en' ? 'عربي' : 'EN'}
          </span>
        </button>
      </div>
      
      <div className="max-w-md w-full">
        {/* School Logo */}
        <LoginLogo />

        {/* Fallback for when no custom logo is set */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-extrabold text-gray-900 dark:text-white">{t('app.name')}</h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">{t('app.tagline')}</p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-8 shadow rounded-lg">
          {showForgotPassword ? (
            <ForgotPasswordForm onBack={() => setShowForgotPassword(false)} />
          ) : showSignUp ? (
            <SignUpForm onBack={() => setShowSignUp(false)} />
          ) : (
            <>
              <form className="space-y-6" onSubmit={handleSubmit}>
                {error && (
                  <div className="bg-error-50 dark:bg-error-900/20 border border-error-200 dark:border-error-800 rounded-md p-3">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <AlertTriangle className="w-5 h-5 text-error-600 dark:text-error-400" />
                      <p className="text-sm text-error-600 dark:text-error-400">{error}</p>
                    </div>
                  </div>
                )}

                {isLoading && (
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      <p className="text-sm text-blue-600 dark:text-blue-400">
                        {t('auth.signingIn') || 'جاري تسجيل الدخول...'}
                      </p>
                    </div>
                  </div>
                )}
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('auth.email')}
                  </label>
                  <div className="mt-1">
                    <input
                      id="email"
                      name="email"
                      type="email"
                      autoComplete="email"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      placeholder={i18n.language === 'ar' ? 'البريد الإلكتروني' : '<EMAIL>'}
                    />
                  </div>
                </div>
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('auth.password')}
                  </label>
                  <div className="mt-1 relative">
                    <input
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      autoComplete="current-password"
                      required
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      placeholder={i18n.language === 'ar' ? 'كلمة المرور' : '••••••••'}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                    >
                      {showPassword ? (
                        <EyeOffIcon className="h-5 w-5" />
                      ) : (
                        <EyeIcon className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <input
                      id="remember-me"
                      name="remember-me"
                      type="checkbox"
                      checked={rememberMe}
                      onChange={(e) => setRememberMe(e.target.checked)}
                      className="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600"
                    />
                    <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                      {t('auth.rememberMe')}
                    </label>
                  </div>
                  <div className="text-sm">
                    <button
                      type="button"
                      onClick={() => setShowForgotPassword(true)}
                      className="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
                    >
                      {t('auth.forgotPassword')}
                    </button>
                  </div>
                </div>
                <div className="flex flex-col gap-2">
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="w-full flex justify-center items-center gap-2"
                  >
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        {t('auth.signingIn') || 'جاري تسجيل الدخول...'}
                      </>
                    ) : (
                      <>
                        <LogIn size={18} />
                        {t('auth.signIn')}
                      </>
                    )}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full flex justify-center items-center gap-2"
                    onClick={() => setShowSignUp(true)}
                  >
                    {t('auth.signUp') || (i18n.language === 'ar' ? 'إنشاء حساب جديد' : 'Sign Up')}
                  </Button>
                </div>
              </form>
            </>
          )}
        </div>
      </div>
    </div>
  );
};