import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Calendar,
  Clock,
  Mail,
  Plus,
  Edit,
  Trash2,
  Play,
  Pause,
  Settings,
  Users,
  Bus,
  TrendingUp,
  Route,
} from "lucide-react";
import { Button } from "../ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import {
  reportGeneratorService,
  ScheduledReport,
  ReportTemplate,
} from "../../lib/reportGeneratorService";

interface ReportSchedulerProps {
  className?: string;
}

export const ReportScheduler: React.FC<ReportSchedulerProps> = ({
  className = "",
}) => {
  const { t } = useTranslation();
  const { user, tenant } = useAuth();
  const [scheduledReports, setScheduledReports] = useState<ScheduledReport[]>(
    [],
  );
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingSchedule, setEditingSchedule] =
    useState<ScheduledReport | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    template_id: "",
    frequency: "weekly" as "daily" | "weekly" | "monthly",
    time: "09:00",
    dayOfWeek: 1, // Monday
    dayOfMonth: 1,
    recipients: [""],
    is_active: true,
  });

  useEffect(() => {
    if (tenant?.id) {
      loadScheduledReports();
      loadTemplates();
    }
  }, [tenant?.id]);

  const loadScheduledReports = async () => {
    if (!tenant?.id) return;

    try {
      setLoading(true);
      const reports = await reportGeneratorService.getScheduledReports(
        tenant.id,
      );
      setScheduledReports(reports);
    } catch (error) {
      console.error("Error loading scheduled reports:", error);
    } finally {
      setLoading(false);
    }
  };

  const loadTemplates = async () => {
    if (!tenant?.id || !user?.id) return;

    try {
      const templateList = await reportGeneratorService.getReportTemplates(
        tenant.id,
        user.id,
      );
      setTemplates(templateList);
    } catch (error) {
      console.error("Error loading templates:", error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!tenant?.id) return;

    try {
      const scheduleData = {
        name: formData.name,
        template_id: formData.template_id,
        schedule: {
          frequency: formData.frequency,
          time: formData.time,
          ...(formData.frequency === "weekly" && {
            dayOfWeek: formData.dayOfWeek,
          }),
          ...(formData.frequency === "monthly" && {
            dayOfMonth: formData.dayOfMonth,
          }),
        },
        recipients: formData.recipients.filter((email) => email.trim() !== ""),
        tenant_id: tenant.id,
        is_active: formData.is_active,
      };

      await reportGeneratorService.scheduleReport(scheduleData);
      await loadScheduledReports();
      resetForm();
    } catch (error) {
      console.error("Error scheduling report:", error);
      alert("Failed to schedule report. Please try again.");
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      template_id: "",
      frequency: "weekly",
      time: "09:00",
      dayOfWeek: 1,
      dayOfMonth: 1,
      recipients: [""],
      is_active: true,
    });
    setShowForm(false);
    setEditingSchedule(null);
  };

  const addRecipient = () => {
    setFormData((prev) => ({
      ...prev,
      recipients: [...prev.recipients, ""],
    }));
  };

  const updateRecipient = (index: number, email: string) => {
    setFormData((prev) => ({
      ...prev,
      recipients: prev.recipients.map((r, i) => (i === index ? email : r)),
    }));
  };

  const removeRecipient = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      recipients: prev.recipients.filter((_, i) => i !== index),
    }));
  };

  const toggleScheduleStatus = async (
    scheduleId: string,
    isActive: boolean,
  ) => {
    try {
      // This would update the schedule status in the database
      // For now, we'll just update the local state
      setScheduledReports((prev) =>
        prev.map((schedule) =>
          schedule.id === scheduleId
            ? { ...schedule, is_active: !isActive }
            : schedule,
        ),
      );
    } catch (error) {
      console.error("Error toggling schedule status:", error);
    }
  };

  const deleteSchedule = async (scheduleId: string) => {
    if (!confirm("Are you sure you want to delete this scheduled report?"))
      return;

    try {
      // This would delete the schedule from the database
      setScheduledReports((prev) =>
        prev.filter((schedule) => schedule.id !== scheduleId),
      );
    } catch (error) {
      console.error("Error deleting schedule:", error);
    }
  };

  const getReportTypeIcon = (templateId: string) => {
    const template = templates.find((t) => t.id === templateId);
    if (!template) return Users;

    switch (template.config.type) {
      case "attendance":
        return Users;
      case "bus_utilization":
        return Bus;
      case "driver_performance":
        return TrendingUp;
      case "route_delays":
        return Route;
      default:
        return Users;
    }
  };

  const formatFrequency = (schedule: ScheduledReport["schedule"]) => {
    switch (schedule.frequency) {
      case "daily":
        return `Daily at ${schedule.time}`;
      case "weekly":
        const days = [
          "Sunday",
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
        ];
        return `Weekly on ${days[schedule.dayOfWeek || 1]} at ${schedule.time}`;
      case "monthly":
        return `Monthly on day ${schedule.dayOfMonth || 1} at ${schedule.time}`;
      default:
        return "Unknown";
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
            <Calendar className="mr-2 h-5 w-5 text-primary-500" />
            Report Scheduler
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Set up automated report generation and delivery via email.
          </p>
        </div>

        <Button
          onClick={() => setShowForm(true)}
          className="flex items-center gap-2"
        >
          <Plus size={16} />
          Schedule New Report
        </Button>
      </div>

      {/* Schedule Form */}
      {showForm && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {editingSchedule ? "Edit" : "Schedule New"} Report
          </h3>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Report Name
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, name: e.target.value }))
                  }
                  required
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  placeholder="e.g., Weekly Attendance Report"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Report Template
                </label>
                <select
                  value={formData.template_id}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      template_id: e.target.value,
                    }))
                  }
                  required
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Select a template</option>
                  {templates.map((template) => (
                    <option key={template.id} value={template.id}>
                      {template.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Frequency
                </label>
                <select
                  value={formData.frequency}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      frequency: e.target.value as
                        | "daily"
                        | "weekly"
                        | "monthly",
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Time
                </label>
                <input
                  type="time"
                  value={formData.time}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, time: e.target.value }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              {formData.frequency === "weekly" && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Day of Week
                  </label>
                  <select
                    value={formData.dayOfWeek}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        dayOfWeek: parseInt(e.target.value),
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value={0}>Sunday</option>
                    <option value={1}>Monday</option>
                    <option value={2}>Tuesday</option>
                    <option value={3}>Wednesday</option>
                    <option value={4}>Thursday</option>
                    <option value={5}>Friday</option>
                    <option value={6}>Saturday</option>
                  </select>
                </div>
              )}

              {formData.frequency === "monthly" && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Day of Month
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="31"
                    value={formData.dayOfMonth}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        dayOfMonth: parseInt(e.target.value),
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Email Recipients
              </label>
              <div className="space-y-2">
                {formData.recipients.map((email, index) => (
                  <div key={index} className="flex gap-2">
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => updateRecipient(index, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                      placeholder="<EMAIL>"
                    />
                    {formData.recipients.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeRecipient(index)}
                      >
                        <Trash2 size={14} />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addRecipient}
                  className="flex items-center gap-2"
                >
                  <Plus size={14} />
                  Add Recipient
                </Button>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="is_active"
                checked={formData.is_active}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    is_active: e.target.checked,
                  }))
                }
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <label
                htmlFor="is_active"
                className="text-sm text-gray-700 dark:text-gray-300"
              >
                Enable this schedule
              </label>
            </div>

            <div className="flex justify-end gap-3">
              <Button type="button" variant="outline" onClick={resetForm}>
                Cancel
              </Button>
              <Button type="submit">
                {editingSchedule ? "Update" : "Schedule"} Report
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* Scheduled Reports List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Scheduled Reports ({scheduledReports.length})
          </h3>
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
        ) : scheduledReports.length > 0 ? (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {scheduledReports.map((schedule) => {
              const Icon = getReportTypeIcon(schedule.template_id);
              const template = templates.find(
                (t) => t.id === schedule.template_id,
              );

              return (
                <div key={schedule.id} className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div
                        className={`p-2 rounded-lg ${
                          schedule.is_active
                            ? "bg-green-100 dark:bg-green-900/20"
                            : "bg-gray-100 dark:bg-gray-700"
                        }`}
                      >
                        <Icon
                          className={`h-5 w-5 ${
                            schedule.is_active
                              ? "text-green-600 dark:text-green-400"
                              : "text-gray-500"
                          }`}
                        />
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {schedule.name}
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {template?.name || "Unknown Template"} •{" "}
                          {formatFrequency(schedule.schedule)}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <Mail className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {schedule.recipients.length} recipient
                            {schedule.recipients.length !== 1 ? "s" : ""}
                          </span>
                          {schedule.last_run && (
                            <>
                              <span className="text-gray-400">•</span>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                Last run:{" "}
                                {new Date(
                                  schedule.last_run,
                                ).toLocaleDateString()}
                              </span>
                            </>
                          )}
                          <span className="text-gray-400">•</span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            Next run:{" "}
                            {new Date(schedule.next_run).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          toggleScheduleStatus(schedule.id, schedule.is_active)
                        }
                        className="flex items-center gap-2"
                      >
                        {schedule.is_active ? (
                          <>
                            <Pause size={14} />
                            Pause
                          </>
                        ) : (
                          <>
                            <Play size={14} />
                            Resume
                          </>
                        )}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setEditingSchedule(schedule);
                          setShowForm(true);
                          // Populate form with schedule data
                        }}
                      >
                        <Edit size={14} />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => deleteSchedule(schedule.id)}
                        className="text-red-600 border-red-300 hover:bg-red-50"
                      >
                        <Trash2 size={14} />
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <Calendar size={48} className="mx-auto mb-4 text-gray-300" />
            <p className="text-gray-500 dark:text-gray-400">
              No scheduled reports found.
            </p>
            <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">
              Create your first scheduled report to automate report delivery.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReportScheduler;
