/**
 * School Manager Theme Settings Component
 * Theme customization interface for school managers
 * Phase 3: UI/UX Enhancement - School Manager Controls
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { useThemePermissions } from '../../../hooks/useThemePermissions';
import { ThemeCustomizer } from '../../features/theme-customization/ThemeCustomizer';
import { tenantThemeManager, TenantThemeConfig } from '../../../design-system/themes/tenant/TenantTheme';

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '../../common/ui/Card';
import { Button } from '../../ui/Button';
import { Badge } from '../../common/ui/Badge';
import { Alert, AlertDescription } from '../../common/ui/Alert';

// Icons
import {
  Palette,
  Save,
  Eye,
  RotateCcw,
  Download,
  Upload,
  CheckCircle,
  AlertTriangle,
  Info,
  Settings
} from 'lucide-react';

interface SchoolThemeSettingsState {
  hasCustomTheme: boolean;
  themeConfig: TenantThemeConfig | null;
  loading: boolean;
  saving: boolean;
  error: string | null;
  success: string | null;
  previewMode: boolean;
  unsavedChanges: boolean;
}

/**
 * School Manager Theme Settings Component
 */
export const SchoolThemeSettings: React.FC = () => {
  const { user } = useAuth();
  const permissions = useThemePermissions();

  // Check school manager permissions
  const canManageOwnTheme = permissions.canManageOwnTheme;
  const canCustomizeColors = permissions.canCustomizeColors;
  const canUploadLogo = permissions.canUploadLogo;
  const canChangeFonts = permissions.canChangeFonts;

  const schoolId = user?.school_id;

  const [state, setState] = useState<SchoolThemeSettingsState>({
    hasCustomTheme: false,
    themeConfig: null,
    loading: false,
    saving: false,
    error: null,
    success: null,
    previewMode: false,
    unsavedChanges: false,
  });

  /**
   * Load current theme configuration
   */
  const loadThemeConfig = useCallback(async () => {
    if (!schoolId || !canManageOwnTheme) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Try to get existing theme
      const existingTheme = tenantThemeManager.getTenantTheme(schoolId);

      if (existingTheme) {
        setState(prev => ({
          ...prev,
          hasCustomTheme: true,
          themeConfig: existingTheme.tenant,
          loading: false,
        }));
      } else {
        // Create default theme config
        const defaultConfig: TenantThemeConfig = {
          id: schoolId,
          name: user?.school?.name || 'مدرستي',
          branding: {
            name: user?.school?.name || 'مدرستي',
            logo: {
              light: '/logos/default-light.svg',
              dark: '/logos/default-dark.svg',
              favicon: '/favicon.ico',
            },
            colors: {
              primary: {
                50: '#eff6ff',
                100: '#dbeafe',
                200: '#bfdbfe',
                300: '#93c5fd',
                400: '#60a5fa',
                500: '#3b82f6',
                600: '#2563eb',
                700: '#1d4ed8',
                800: '#1e40af',
                900: '#1e3a8a',
                950: '#172554',
              },
            },
            typography: {
              fontFamily: ['Cairo', 'Arial', 'sans-serif'],
            },
            assets: {},
          },
          baseTheme: 'light',
          customizations: {
            borderRadius: 'rounded',
            shadows: 'normal',
            animations: 'normal',
            density: 'normal',
          },
          rtl: true,
          locale: 'ar-SA',
        };

        setState(prev => ({
          ...prev,
          hasCustomTheme: false,
          themeConfig: defaultConfig,
          loading: false,
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'فشل في تحميل إعدادات الثيم',
        loading: false,
      }));
    }
  }, [schoolId, canManageOwnTheme, user]);

  useEffect(() => {
    loadThemeConfig();
  }, [loadThemeConfig]);

  /**
   * Handle theme save
   */
  const handleSaveTheme = useCallback(async (config: TenantThemeConfig) => {
    if (!schoolId || !canManageOwnTheme) return;

    setState(prev => ({ ...prev, saving: true, error: null, success: null }));

    try {
      // Create and apply theme
      const theme = tenantThemeManager.createTenantTheme(config);

      // Save to backend (if needed)
      const response = await fetch(`/api/schools/${schoolId}/theme`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config),
      });

      if (response.ok) {
        setState(prev => ({
          ...prev,
          hasCustomTheme: true,
          themeConfig: config,
          saving: false,
          success: 'تم حفظ إعدادات الثيم بنجاح',
          unsavedChanges: false,
        }));

        // Clear success message after 3 seconds
        setTimeout(() => {
          setState(prev => ({ ...prev, success: null }));
        }, 3000);
      } else {
        throw new Error('فشل في حفظ الإعدادات');
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'فشل في حفظ إعدادات الثيم',
        saving: false,
      }));
    }
  }, [schoolId, canManageOwnTheme]);

  /**
   * Handle theme preview
   */
  const handlePreviewTheme = useCallback((config: TenantThemeConfig) => {
    if (!schoolId) return;

    // Apply theme temporarily
    tenantThemeManager.createTenantTheme(config);

    setState(prev => ({
      ...prev,
      previewMode: true,
      themeConfig: config,
      unsavedChanges: true,
    }));
  }, [schoolId]);

  /**
   * Reset to default theme
   */
  const handleResetTheme = useCallback(() => {
    if (!confirm('هل أنت متأكد من إعادة تعيين الثيم للإعدادات الافتراضية؟')) {
      return;
    }

    loadThemeConfig();
    setState(prev => ({
      ...prev,
      previewMode: false,
      unsavedChanges: false,
    }));
  }, [loadThemeConfig]);

  /**
   * Export theme configuration
   */
  const handleExportTheme = useCallback(() => {
    if (!state.themeConfig) return;

    const dataStr = JSON.stringify(state.themeConfig, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `${state.themeConfig.name}-theme.json`;
    link.click();

    URL.revokeObjectURL(url);
  }, [state.themeConfig]);

  if (!canManageOwnTheme) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          ليس لديك صلاحية لإدارة ثيم المدرسة.
        </AlertDescription>
      </Alert>
    );
  }

  if (!schoolId) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          لم يتم العثور على معرف المدرسة.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">إعدادات ثيم المدرسة</h2>
          <p className="text-gray-600">
            تخصيص مظهر وهوية مدرستك في النظام
          </p>
        </div>

        <div className="flex items-center space-x-2">
          {state.hasCustomTheme && (
            <Badge variant="default">
              <CheckCircle className="w-3 h-3 mr-1" />
              ثيم مخصص
            </Badge>
          )}

          {state.previewMode && (
            <Badge variant="warning">
              <Eye className="w-3 h-3 mr-1" />
              وضع المعاينة
            </Badge>
          )}

          {state.unsavedChanges && (
            <Badge variant="destructive">
              تغييرات غير محفوظة
            </Badge>
          )}
        </div>
      </div>

      {/* Status Messages */}
      {state.error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {state.success && (
        <Alert variant="default">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{state.success}</AlertDescription>
        </Alert>
      )}

      {/* Permission Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Info className="w-5 h-5" />
            <span>الصلاحيات المتاحة</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${canCustomizeColors ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm">تخصيص الألوان</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${canUploadLogo ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm">رفع الشعار</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${canChangeFonts ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm">تغيير الخطوط</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-green-500" />
              <span className="text-sm">عرض الإعدادات</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>إجراءات سريعة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={handleResetTheme}
              variant="outline"
              disabled={state.loading}
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              إعادة تعيين
            </Button>

            <Button
              onClick={handleExportTheme}
              variant="outline"
              disabled={!state.themeConfig}
            >
              <Download className="w-4 h-4 mr-2" />
              تصدير الثيم
            </Button>

            {state.previewMode && (
              <Button
                onClick={() => setState(prev => ({ ...prev, previewMode: false }))}
                variant="outline"
              >
                <Eye className="w-4 h-4 mr-2" />
                إنهاء المعاينة
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Theme Customizer */}
      {state.themeConfig && (
        <ThemeCustomizer
          tenantId={schoolId}
          onSave={handleSaveTheme}
          onPreview={handlePreviewTheme}
          className="mt-6"
        />
      )}

      {/* Loading State */}
      {state.loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">جاري التحميل...</span>
        </div>
      )}
    </div>
  );
};
