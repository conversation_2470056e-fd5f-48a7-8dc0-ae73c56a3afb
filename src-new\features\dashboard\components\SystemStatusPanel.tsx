/**
 * لوحة حالة النظام - System Status Panel
 * تعرض مقاييس الأداء والأمان والامتثال
 */

import React from "react";
import { useTranslation } from "react-i18next";
import { usePermissions } from "../../hooks/usePermissions";
import { EnhancedStatCard as StatCard } from "./EnhancedStatCard";
import {
  Shield,
  CheckCircle,
  AlertTriangle,
  Activity,
  Zap,
  Clock,
  Globe,
  Heart,
  Award,
  TrendingUp,
  Server,
  Wifi,
  Database,
} from "lucide-react";

interface SystemMetrics {
  security: {
    score: number;
    status: string;
    threats: number;
    lastScan: string;
  };
  performance: {
    uptime: number;
    responseTime: number;
    throughput: number;
    errorRate: number;
  };
  compliance: {
    level: number;
    status: string;
    lastAudit: string;
    issues: number;
  };
  infrastructure: {
    serverHealth: number;
    databaseHealth: number;
    networkLatency: number;
    storageUsage: number;
  };
}

interface SystemStatusPanelProps {
  metrics?: SystemMetrics;
}

export const SystemStatusPanel: React.FC<SystemStatusPanelProps> = ({ 
  metrics = {
    security: {
      score: 95,
      status: "excellent",
      threats: 0,
      lastScan: new Date().toISOString().split('T')[0],
    },
    performance: {
      uptime: 99.9,
      responseTime: 245,
      throughput: 1250,
      errorRate: 0.1,
    },
    compliance: {
      level: 98,
      status: "compliant",
      lastAudit: "2025-01-25",
      issues: 0,
    },
    infrastructure: {
      serverHealth: 98,
      databaseHealth: 97,
      networkLatency: 12,
      storageUsage: 65,
    },
  }
}) => {
  const { t } = useTranslation();
  const { isAdmin, isSchoolManager } = usePermissions();

  // فقط الأدمن ومديرو المدارس يمكنهم رؤية هذه المعلومات
  if (!isAdmin && !isSchoolManager) {
    return null;
  }

  const getSecurityColor = (score: number) => {
    if (score >= 90) return "green";
    if (score >= 70) return "yellow";
    return "red";
  };

  const getPerformanceColor = (value: number, isGood: boolean) => {
    if (isGood) {
      return value >= 95 ? "green" : value >= 80 ? "yellow" : "red";
    } else {
      return value <= 1 ? "green" : value <= 5 ? "yellow" : "red";
    }
  };

  const getComplianceColor = (level: number) => {
    if (level >= 95) return "green";
    if (level >= 80) return "yellow";
    return "red";
  };

  const formatUptime = (uptime: number) => {
    const days = Math.floor(uptime * 365 / 100);
    const hours = Math.floor((uptime * 365 * 24 / 100) % 24);
    return `${days}د ${hours}س`;
  };

  return (
    <div className="space-y-8">
      {/* مقاييس الأمان */}
      <div>
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
          <Shield className="h-6 w-6 mr-2 text-green-600" />
          مقاييس الأمان والحماية
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="نقاط الأمان"
            value={`${metrics.security.score}/100`}
            icon={<Shield className="h-6 w-6" />}
            trend={{
              value: metrics.security.score,
              label: "مستوى الحماية",
              isPositive: metrics.security.score >= 90,
            }}
            color={getSecurityColor(metrics.security.score)}
            subtitle={`آخر فحص: ${metrics.security.lastScan}`}
          />

          <StatCard
            title="حالة الامتثال"
            value={`${metrics.compliance.level}%`}
            icon={<CheckCircle className="h-6 w-6" />}
            trend={{
              value: metrics.compliance.level,
              label: "مستوى الامتثال",
              isPositive: metrics.compliance.level >= 95,
            }}
            color={getComplianceColor(metrics.compliance.level)}
            subtitle={
              metrics.compliance.status === "compliant" 
                ? "متوافق مع المعايير" 
                : "يحتاج مراجعة"
            }
          />

          <StatCard
            title="التهديدات النشطة"
            value={metrics.security.threats}
            icon={<AlertTriangle className="h-6 w-6" />}
            trend={{
              value: metrics.security.threats,
              label: "تهديدات محتملة",
              isPositive: metrics.security.threats === 0,
            }}
            color={metrics.security.threats === 0 ? "green" : "red"}
            subtitle={
              metrics.security.threats === 0 
                ? "لا توجد تهديدات" 
                : `${metrics.security.threats} تهديد نشط`
            }
          />

          <StatCard
            title="مشاكل الامتثال"
            value={metrics.compliance.issues}
            icon={<Award className="h-6 w-6" />}
            trend={{
              value: metrics.compliance.issues,
              label: "مشاكل مفتوحة",
              isPositive: metrics.compliance.issues === 0,
            }}
            color={metrics.compliance.issues === 0 ? "green" : "yellow"}
            subtitle={
              metrics.compliance.issues === 0 
                ? "لا توجد مشاكل" 
                : `${metrics.compliance.issues} مشكلة مفتوحة`
            }
          />
        </div>
      </div>

      {/* مقاييس الأداء */}
      <div>
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
          <Activity className="h-6 w-6 mr-2 text-blue-600" />
          مقاييس الأداء والاستقرار
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="وقت التشغيل"
            value={`${metrics.performance.uptime}%`}
            icon={<Clock className="h-6 w-6" />}
            trend={{
              value: metrics.performance.uptime,
              label: "استقرار النظام",
              isPositive: metrics.performance.uptime >= 99,
            }}
            color={getPerformanceColor(metrics.performance.uptime, true)}
            subtitle={`${formatUptime(metrics.performance.uptime)} متواصل`}
          />

          <StatCard
            title="زمن الاستجابة"
            value={`${metrics.performance.responseTime}ms`}
            icon={<Zap className="h-6 w-6" />}
            trend={{
              value: metrics.performance.responseTime,
              label: "سرعة النظام",
              isPositive: metrics.performance.responseTime <= 300,
            }}
            color={metrics.performance.responseTime <= 300 ? "green" : "yellow"}
            subtitle="متوسط وقت الاستجابة"
          />

          <StatCard
            title="معدل الأخطاء"
            value={`${metrics.performance.errorRate}%`}
            icon={<AlertTriangle className="h-6 w-6" />}
            trend={{
              value: metrics.performance.errorRate,
              label: "استقرار العمليات",
              isPositive: metrics.performance.errorRate <= 1,
            }}
            color={getPerformanceColor(metrics.performance.errorRate, false)}
            subtitle="نسبة الأخطاء في العمليات"
          />

          <StatCard
            title="الإنتاجية"
            value={`${metrics.performance.throughput}`}
            icon={<TrendingUp className="h-6 w-6" />}
            trend={{
              value: 15.2,
              label: "نمو الاستخدام",
              isPositive: true,
            }}
            color="blue"
            subtitle="طلب/دقيقة"
          />
        </div>
      </div>

      {/* مقاييس البنية التحتية */}
      {isAdmin && (
        <div>
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <Server className="h-6 w-6 mr-2 text-purple-600" />
            حالة البنية التحتية
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="صحة الخادم"
              value={`${metrics.infrastructure.serverHealth}%`}
              icon={<Server className="h-6 w-6" />}
              trend={{
                value: metrics.infrastructure.serverHealth,
                label: "أداء الخادم",
                isPositive: metrics.infrastructure.serverHealth >= 95,
              }}
              color={getPerformanceColor(metrics.infrastructure.serverHealth, true)}
              subtitle="حالة خوادم التطبيق"
            />

            <StatCard
              title="صحة قاعدة البيانات"
              value={`${metrics.infrastructure.databaseHealth}%`}
              icon={<Database className="h-6 w-6" />}
              trend={{
                value: metrics.infrastructure.databaseHealth,
                label: "أداء قاعدة البيانات",
                isPositive: metrics.infrastructure.databaseHealth >= 95,
              }}
              color={getPerformanceColor(metrics.infrastructure.databaseHealth, true)}
              subtitle="حالة قواعد البيانات"
            />

            <StatCard
              title="زمن استجابة الشبكة"
              value={`${metrics.infrastructure.networkLatency}ms`}
              icon={<Wifi className="h-6 w-6" />}
              trend={{
                value: metrics.infrastructure.networkLatency,
                label: "سرعة الشبكة",
                isPositive: metrics.infrastructure.networkLatency <= 20,
              }}
              color={metrics.infrastructure.networkLatency <= 20 ? "green" : "yellow"}
              subtitle="زمن استجابة الشبكة"
            />

            <StatCard
              title="استخدام التخزين"
              value={`${metrics.infrastructure.storageUsage}%`}
              icon={<Globe className="h-6 w-6" />}
              trend={{
                value: metrics.infrastructure.storageUsage,
                label: "مساحة التخزين",
                isPositive: metrics.infrastructure.storageUsage <= 80,
              }}
              color={
                metrics.infrastructure.storageUsage <= 70 
                  ? "green" 
                  : metrics.infrastructure.storageUsage <= 85 
                    ? "yellow" 
                    : "red"
              }
              subtitle="استخدام مساحة التخزين"
            />
          </div>
        </div>
      )}

      {/* تنبيهات النظام */}
      {(metrics.security.threats > 0 || metrics.compliance.issues > 0 || metrics.performance.errorRate > 1) && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-4 flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2" />
            تنبيهات النظام
          </h3>
          <div className="space-y-2">
            {metrics.security.threats > 0 && (
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                🚨 تم اكتشاف {metrics.security.threats} تهديد أمني نشط
              </p>
            )}
            {metrics.compliance.issues > 0 && (
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                ⚠️ يوجد {metrics.compliance.issues} مشكلة امتثال تحتاج مراجعة
              </p>
            )}
            {metrics.performance.errorRate > 1 && (
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                📊 معدل الأخطاء مرتفع ({metrics.performance.errorRate}%) - يحتاج مراجعة
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemStatusPanel;
