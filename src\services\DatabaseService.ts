/**
 * Database Service - خدمة قاعدة البيانات المحسنة
 * Enhanced Database Service for Real Data Management
 */

import { supabase } from '../lib/supabase';

export interface BusWithLocation {
  id: string;
  plate_number: string;
  model: string;
  capacity: number;
  driver_name: string;
  current_students: number;
  tenant_id: string;
  latitude?: number;
  longitude?: number;
  speed?: number;
  heading?: number;
  accuracy?: number;
  location_timestamp?: string;
  route_name?: string;
  route_id?: string;
  last_updated?: string;
}

export interface MaintenanceRecord {
  id: string;
  bus_id: string;
  maintenance_type: string;
  description: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  scheduled_date: string;
  completed_date?: string;
  cost: number;
  notes?: string;
  tenant_id: string;
  plate_number?: string;
  model?: string;
  driver_name?: string;
  actual_status?: string;
  days_overdue?: number;
}

export interface MaintenanceStats {
  total_maintenance: number;
  scheduled_maintenance: number;
  in_progress_maintenance: number;
  completed_maintenance: number;
  overdue_maintenance: number;
  total_cost: number;
  average_cost: number;
}

export class DatabaseService {
  private static instance: DatabaseService;

  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  /**
   * الحصول على جميع الحافلات مع مواقعها الحالية
   * Get all buses with their current locations
   */
  async getBusesWithLocations(tenantId: string): Promise<BusWithLocation[]> {
    try {
      const { data, error } = await supabase
        .from('buses_with_latest_location')
        .select('*')
        .eq('tenant_id', tenantId);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching buses with locations:', error);
      return [];
    }
  }

  /**
   * الحصول على موقع حافلة محددة
   * Get specific bus location
   */
  async getBusLocation(busId: string): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('bus_locations')
        .select('*')
        .eq('bus_id', busId)
        .order('timestamp', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      console.error('Error fetching bus location:', error);
      return null;
    }
  }

  /**
   * تحديث موقع الحافلة
   * Update bus location
   */
  async updateBusLocation(
    busId: string,
    location: {
      latitude: number;
      longitude: number;
      speed?: number;
      heading?: number;
      accuracy?: number;
    },
    tenantId: string
  ): Promise<boolean> {
    try {
      // إدراج موقع جديد
      const { error: locationError } = await supabase
        .from('bus_locations')
        .insert([{
          bus_id: busId,
          latitude: location.latitude,
          longitude: location.longitude,
          speed: location.speed || 0,
          heading: location.heading || 0,
          accuracy: location.accuracy || 0,
          tenant_id: tenantId,
          timestamp: new Date().toISOString(),
        }]);

      if (locationError) throw locationError;

      // تحديث آخر موقع في جدول الحافلات
      const { error: busError } = await supabase
        .from('buses')
        .update({
          last_location: {
            latitude: location.latitude,
            longitude: location.longitude,
            speed: location.speed || 0,
            heading: location.heading || 0,
            timestamp: new Date().toISOString(),
          },
          last_updated: new Date().toISOString(),
        })
        .eq('id', busId);

      if (busError) throw busError;
      return true;
    } catch (error) {
      console.error('Error updating bus location:', error);
      return false;
    }
  }

  /**
   * الحصول على سجلات الصيانة مع تفاصيل الحافلة
   * Get maintenance records with bus details
   */
  async getMaintenanceRecords(tenantId: string): Promise<MaintenanceRecord[]> {
    try {
      const { data, error } = await supabase
        .from('maintenance_with_bus_details')
        .select('*')
        .eq('tenant_id', tenantId)
        .order('scheduled_date', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching maintenance records:', error);
      return [];
    }
  }

  /**
   * الحصول على إحصائيات الصيانة
   * Get maintenance statistics
   */
  async getMaintenanceStats(tenantId: string): Promise<MaintenanceStats> {
    try {
      const { data, error } = await supabase
        .rpc('get_maintenance_stats', { tenant_uuid: tenantId });

      if (error) throw error;
      
      if (data && data.length > 0) {
        const stats = data[0];
        return {
          total_maintenance: parseInt(stats.total_maintenance) || 0,
          scheduled_maintenance: parseInt(stats.scheduled_maintenance) || 0,
          in_progress_maintenance: parseInt(stats.in_progress_maintenance) || 0,
          completed_maintenance: parseInt(stats.completed_maintenance) || 0,
          overdue_maintenance: parseInt(stats.overdue_maintenance) || 0,
          total_cost: parseFloat(stats.total_cost) || 0,
          average_cost: parseFloat(stats.average_cost) || 0,
        };
      }

      return {
        total_maintenance: 0,
        scheduled_maintenance: 0,
        in_progress_maintenance: 0,
        completed_maintenance: 0,
        overdue_maintenance: 0,
        total_cost: 0,
        average_cost: 0,
      };
    } catch (error) {
      console.error('Error fetching maintenance stats:', error);
      return {
        total_maintenance: 0,
        scheduled_maintenance: 0,
        in_progress_maintenance: 0,
        completed_maintenance: 0,
        overdue_maintenance: 0,
        total_cost: 0,
        average_cost: 0,
      };
    }
  }

  /**
   * إنشاء سجل صيانة جديد
   * Create new maintenance record
   */
  async createMaintenanceRecord(
    maintenance: Omit<MaintenanceRecord, 'id' | 'plate_number' | 'model' | 'driver_name' | 'actual_status' | 'days_overdue'>
  ): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('bus_maintenance')
        .insert([maintenance])
        .select('id')
        .single();

      if (error) throw error;
      return data?.id || null;
    } catch (error) {
      console.error('Error creating maintenance record:', error);
      return null;
    }
  }

  /**
   * تحديث سجل صيانة
   * Update maintenance record
   */
  async updateMaintenanceRecord(
    id: string,
    updates: Partial<MaintenanceRecord>
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('bus_maintenance')
        .update(updates)
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating maintenance record:', error);
      return false;
    }
  }

  /**
   * الحصول على تنبيهات الصيانة
   * Get maintenance alerts
   */
  async getMaintenanceAlerts(tenantId: string): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .rpc('get_maintenance_alerts', { tenant_uuid: tenantId });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching maintenance alerts:', error);
      return [];
    }
  }

  /**
   * الحصول على المسارات مع محطات التوقف
   * Get routes with stops
   */
  async getRoutesWithStops(tenantId: string): Promise<any[]> {
    try {
      const { data: routes, error: routesError } = await supabase
        .from('routes')
        .select(`
          *,
          route_stops (
            stop_order,
            estimated_arrival_time,
            bus_stops (
              id,
              name,
              latitude,
              longitude,
              address
            )
          )
        `)
        .eq('tenant_id', tenantId)
        .eq('is_active', true)
        .order('name');

      if (routesError) throw routesError;
      return routes || [];
    } catch (error) {
      console.error('Error fetching routes with stops:', error);
      return [];
    }
  }

  /**
   * إنشاء محطة توقف جديدة
   * Create new bus stop
   */
  async createBusStop(stop: {
    name: string;
    latitude: number;
    longitude: number;
    address?: string;
    tenant_id: string;
  }): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('bus_stops')
        .insert([stop])
        .select('id')
        .single();

      if (error) throw error;
      return data?.id || null;
    } catch (error) {
      console.error('Error creating bus stop:', error);
      return null;
    }
  }

  /**
   * تنظيف البيانات القديمة
   * Clean old data
   */
  async cleanOldData(tenantId: string, daysToKeep: number = 30): Promise<boolean> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      // حذف مواقع الحافلات القديمة
      const { error: locationsError } = await supabase
        .from('bus_locations')
        .delete()
        .eq('tenant_id', tenantId)
        .lt('timestamp', cutoffDate.toISOString());

      if (locationsError) throw locationsError;

      console.log(`Cleaned bus locations older than ${daysToKeep} days`);
      return true;
    } catch (error) {
      console.error('Error cleaning old data:', error);
      return false;
    }
  }

  /**
   * التحقق من صحة قاعدة البيانات
   * Validate database health
   */
  async validateDatabaseHealth(): Promise<{
    isHealthy: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      // فحص وجود الجداول المطلوبة
      const tables = ['buses', 'bus_locations', 'bus_maintenance', 'routes', 'bus_stops'];
      
      for (const table of tables) {
        try {
          await supabase.from(table).select('id').limit(1);
        } catch (error) {
          issues.push(`Table ${table} is not accessible`);
          recommendations.push(`Create or fix table ${table}`);
        }
      }

      // فحص الفهارس
      // هذا يتطلب صلاحيات خاصة، لذا نتجاهله في الوقت الحالي

      return {
        isHealthy: issues.length === 0,
        issues,
        recommendations,
      };
    } catch (error) {
      console.error('Error validating database health:', error);
      return {
        isHealthy: false,
        issues: ['Database connection failed'],
        recommendations: ['Check database connection and credentials'],
      };
    }
  }
}

export default DatabaseService;
