/**
 * Application Configuration
 * Central configuration for the entire application
 * Phase 3: UI/UX Enhancement - Configuration
 */

import { env, validateEnvironment, logEnvironmentInfo } from '../utils/env';
import { ThemeService } from '../services/ThemeService';

/**
 * Application configuration object
 */
export const appConfig = {
  // App metadata
  name: env.app.name,
  version: env.app.version,
  
  // API configuration
  api: {
    baseUrl: env.api.url,
    timeout: 30000,
    retries: 3,
  },
  
  // Theme configuration
  theme: {
    default: env.theme.default,
    customizationEnabled: env.theme.customizationEnabled,
  },
  
  // Development configuration
  development: {
    enabled: env.isDevelopment(),
    toolsEnabled: env.dev.toolsEnabled,
    logLevel: env.dev.logLevel,
  },
  
  // Feature flags
  features: {
    themeCustomization: env.theme.customizationEnabled,
    rtlSupport: true,
    multiTenant: true,
    offlineMode: false,
    pushNotifications: true,
  },
  
  // UI configuration
  ui: {
    defaultLanguage: 'ar',
    supportedLanguages: ['ar', 'en'],
    rtlLanguages: ['ar', 'he', 'fa', 'ur'],
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
  },
  
  // Security configuration
  security: {
    sessionTimeout: 30 * 60 * 1000, // 30 minutes
    maxLoginAttempts: 5,
    passwordMinLength: 8,
    requireStrongPassword: true,
  },
  
  // Cache configuration
  cache: {
    defaultTTL: 5 * 60 * 1000, // 5 minutes
    maxSize: 100,
    enablePersistence: true,
  },
};

/**
 * Initialize application configuration
 */
export function initializeApp(): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      // Validate environment
      const validation = validateEnvironment();
      if (!validation.isValid) {
        console.error('❌ Environment validation failed:', validation.errors);
        reject(new Error(`Environment validation failed: ${validation.errors.join(', ')}`));
        return;
      }
      
      // Log environment info in development
      if (appConfig.development.enabled) {
        logEnvironmentInfo();
        console.log('🚀 App Configuration:', appConfig);
      }
      
      // Set up global error handlers
      setupGlobalErrorHandlers();
      
      // Initialize theme system
      initializeThemeSystem();

      // Load saved theme
      loadSavedTheme();

      console.log('✅ Application initialized successfully');
      resolve();
    } catch (error) {
      console.error('❌ Failed to initialize application:', error);
      reject(error);
    }
  });
}

/**
 * Set up global error handlers
 */
function setupGlobalErrorHandlers(): void {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    
    if (appConfig.development.enabled) {
      console.error('Promise rejection details:', event);
    }
    
    // Prevent the default browser behavior
    event.preventDefault();
  });
  
  // Handle uncaught errors
  window.addEventListener('error', (event) => {
    console.error('Uncaught error:', event.error);
    
    if (appConfig.development.enabled) {
      console.error('Error details:', event);
    }
  });
}

/**
 * Initialize theme system
 */
function initializeThemeSystem(): void {
  try {
    // Set default theme
    const savedTheme = localStorage.getItem('app-theme');
    const theme = savedTheme || appConfig.theme.default;

    // Apply theme to document
    document.documentElement.setAttribute('data-theme', theme);

    // Set RTL direction if needed
    const savedLanguage = localStorage.getItem('app-language');
    const language = savedLanguage || appConfig.ui.defaultLanguage;

    if (appConfig.ui.rtlLanguages.includes(language)) {
      document.documentElement.setAttribute('dir', 'rtl');
      document.documentElement.classList.add('rtl');
    } else {
      document.documentElement.setAttribute('dir', 'ltr');
      document.documentElement.classList.remove('rtl');
    }

    console.log('🎨 Theme system initialized:', { theme, language });
  } catch (error) {
    console.error('Failed to initialize theme system:', error);
  }
}

/**
 * Load saved custom theme
 */
function loadSavedTheme(): void {
  try {
    const savedTheme = ThemeService.loadStoredTheme();
    if (savedTheme) {
      ThemeService.applyTheme(savedTheme);
      console.log('🎨 Custom theme loaded and applied:', savedTheme.name);
    } else {
      console.log('🎨 No custom theme found, using default');
    }
  } catch (error) {
    console.error('Failed to load saved theme:', error);
  }
}

/**
 * Get feature flag value
 */
export function getFeatureFlag(flag: keyof typeof appConfig.features): boolean {
  return appConfig.features[flag];
}

/**
 * Check if feature is enabled
 */
export function isFeatureEnabled(feature: string): boolean {
  return getFeatureFlag(feature as keyof typeof appConfig.features);
}

/**
 * Get app metadata
 */
export function getAppMetadata() {
  return {
    name: appConfig.name,
    version: appConfig.version,
    environment: appConfig.development.enabled ? 'development' : 'production',
    buildTime: new Date().toISOString(),
  };
}

/**
 * Export configuration for external use
 */
export default appConfig;
