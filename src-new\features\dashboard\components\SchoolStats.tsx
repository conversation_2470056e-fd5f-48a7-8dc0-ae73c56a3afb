import React from 'react';
import { useTranslation } from 'react-i18next';
import { Bus, Users, MapPin, AlertTriangle } from 'lucide-react';
import { StatCard } from './StatCard';
import { useDatabase } from '../../contexts/DatabaseContext';

export const SchoolStats: React.FC = () => {
  const { t } = useTranslation();
  const { buses, routes, students } = useDatabase();
  
  // حساب الإحصائيات
  const activeBuses = buses.filter(b => b.is_active).length;
  const totalStudents = students.length;
  const activeRoutes = routes.filter(r => r.is_active).length;
  const busesInRoute = buses.filter(b => b.last_location).length;
  
  // حساب نسب التغيير (مثال)
  const busesChange = 5; // يمكن حساب هذا من البيانات التاريخية
  const studentsChange = 8;
  const routesChange = 3;
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <StatCard
        title={t('dashboard.activeBuses')}
        value={activeBuses}
        icon={<Bus size={24} />}
        trend={{ value: busesChange, isPositive: true }}
      />
      
      <StatCard
        title={t('dashboard.totalStudents')}
        value={totalStudents}
        icon={<Users size={24} />}
        trend={{ value: studentsChange, isPositive: true }}
      />
      
      <StatCard
        title={t('dashboard.totalRoutes')}
        value={activeRoutes}
        icon={<MapPin size={24} />}
        trend={{ value: routesChange, isPositive: true }}
      />
      
      <StatCard
        title={t('dashboard.busesOnRoute')}
        value={busesInRoute}
        icon={<AlertTriangle size={24} />}
      />
    </div>
  );
};