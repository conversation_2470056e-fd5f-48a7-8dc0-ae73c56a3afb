/**
 * Test Setup File
 * Global test configuration and mocks
 */

import { vi } from 'vitest';

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  warn: vi.fn(),
  error: vi.fn(),
  log: vi.fn(),
};

// Mock performance API
global.performance = {
  ...performance,
  now: vi.fn(() => Date.now()),
};

// Mock setTimeout and setInterval
global.setTimeout = vi.fn((fn, delay) => {
  if (typeof fn === 'function') {
    return setTimeout(fn, delay);
  }
  return 0;
}) as any;

global.setInterval = vi.fn((fn, delay) => {
  if (typeof fn === 'function') {
    return setInterval(fn, delay);
  }
  return 0;
}) as any;

global.clearTimeout = vi.fn(clearTimeout);
global.clearInterval = vi.fn(clearInterval);

// Reset all mocks before each test
beforeEach(() => {
  vi.clearAllMocks();
});
