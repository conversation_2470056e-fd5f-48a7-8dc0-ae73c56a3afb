import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Send, X, AlertTriangle } from "lucide-react";
import { Button } from "../ui/Button";
import { createComplaint } from "../../lib/api";
import { useAuth } from "../../contexts/AuthContext";

interface ComplaintFormProps {
  targetType: "driver" | "service" | "route" | "general";
  targetId: string;
  targetName?: string;
  onClose: () => void;
  onSuccess: () => void;
}

export const ComplaintForm: React.FC<ComplaintFormProps> = ({
  targetType,
  targetId,
  targetName,
  onClose,
  onSuccess,
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !title.trim() || !description.trim()) return;

    setIsSubmitting(true);
    try {
      await createComplaint(
        user.tenant_id!,
        user.id,
        targetType,
        targetId,
        title.trim(),
        description.trim(),
      );
      onSuccess();
      onClose();
    } catch (error) {
      console.error("Error submitting complaint:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getComplaintTitle = () => {
    switch (targetType) {
      case "driver":
        return t("complaints.submitDriverComplaint");
      case "service":
        return t("complaints.submitServiceComplaint");
      case "route":
        return t("complaints.submitRouteComplaint");
      case "general":
        return t("complaints.submitGeneralComplaint");
      default:
        return t("complaints.submitComplaint");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
            <AlertTriangle size={20} className="mr-2 text-orange-500" />
            {getComplaintTitle()}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          {targetName && (
            <div className="mb-6">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                {t("complaints.regarding")}:{" "}
                <span className="font-medium">{targetName}</span>
              </p>
            </div>
          )}

          {/* Title */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t("complaints.title")} *
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              placeholder={t("complaints.titlePlaceholder")}
              required
            />
          </div>

          {/* Description */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t("complaints.description")} *
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              placeholder={t("complaints.descriptionPlaceholder")}
              required
            />
          </div>

          {/* Guidelines */}
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
              {t("complaints.guidelines")}
            </h4>
            <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
              <li>• {t("complaints.guideline1")}</li>
              <li>• {t("complaints.guideline2")}</li>
              <li>• {t("complaints.guideline3")}</li>
              <li>• {t("complaints.guideline4")}</li>
            </ul>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              {t("common.cancel")}
            </Button>
            <Button
              type="submit"
              disabled={!title.trim() || !description.trim() || isSubmitting}
              leftIcon={<Send size={16} />}
            >
              {isSubmitting ? t("common.submitting") : t("complaints.submit")}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ComplaintForm;
