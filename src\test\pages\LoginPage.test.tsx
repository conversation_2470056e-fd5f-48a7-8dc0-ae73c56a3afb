import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '../utils/test-utils'
import { LoginPage } from '@pages/login/LoginPage'
import { mockSupabase } from '../utils/test-utils'

// Mock useNavigate
const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  }
})

// Mock AuthContext
const mockLogin = vi.fn()
const mockAuthContext = {
  user: null,
  tenant: null,
  isLoading: false,
  isAuthenticated: false,
  login: mockLogin,
  logout: vi.fn(),
  hasPermission: vi.fn(),
}

vi.mock('@contexts/AuthContext', () => ({
  useAuth: () => mockAuthContext,
}))

describe('LoginPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render login form', () => {
    render(<LoginPage />)
    
    expect(screen.getByRole('heading', { name: /تسجيل الدخول/i })).toBeInTheDocument()
    expect(screen.getByLabelText(/البريد الإلكتروني/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/كلمة المرور/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /تسجيل الدخول/i })).toBeInTheDocument()
  })

  it('should handle form submission with valid data', async () => {
    mockLogin.mockResolvedValue({ success: true })
    
    render(<LoginPage />)
    
    const emailInput = screen.getByLabelText(/البريد الإلكتروني/i)
    const passwordInput = screen.getByLabelText(/كلمة المرور/i)
    const submitButton = screen.getByRole('button', { name: /تسجيل الدخول/i })
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123')
    })
  })

  it('should show validation errors for empty fields', async () => {
    render(<LoginPage />)
    
    const submitButton = screen.getByRole('button', { name: /تسجيل الدخول/i })
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText(/البريد الإلكتروني مطلوب/i)).toBeInTheDocument()
      expect(screen.getByText(/كلمة المرور مطلوبة/i)).toBeInTheDocument()
    })
  })

  it('should show validation error for invalid email', async () => {
    render(<LoginPage />)
    
    const emailInput = screen.getByLabelText(/البريد الإلكتروني/i)
    const submitButton = screen.getByRole('button', { name: /تسجيل الدخول/i })
    
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } })
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText(/البريد الإلكتروني غير صحيح/i)).toBeInTheDocument()
    })
  })

  it('should handle login error', async () => {
    const errorMessage = 'بيانات الدخول غير صحيحة'
    mockLogin.mockRejectedValue(new Error(errorMessage))
    
    render(<LoginPage />)
    
    const emailInput = screen.getByLabelText(/البريد الإلكتروني/i)
    const passwordInput = screen.getByLabelText(/كلمة المرور/i)
    const submitButton = screen.getByRole('button', { name: /تسجيل الدخول/i })
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } })
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument()
    })
  })

  it('should show loading state during login', async () => {
    mockLogin.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)))
    
    render(<LoginPage />)
    
    const emailInput = screen.getByLabelText(/البريد الإلكتروني/i)
    const passwordInput = screen.getByLabelText(/كلمة المرور/i)
    const submitButton = screen.getByRole('button', { name: /تسجيل الدخول/i })
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.click(submitButton)
    
    expect(screen.getByText(/جاري تسجيل الدخول/i)).toBeInTheDocument()
    expect(submitButton).toBeDisabled()
  })

  it('should toggle password visibility', () => {
    render(<LoginPage />)
    
    const passwordInput = screen.getByLabelText(/كلمة المرور/i)
    const toggleButton = screen.getByRole('button', { name: /إظهار كلمة المرور/i })
    
    expect(passwordInput).toHaveAttribute('type', 'password')
    
    fireEvent.click(toggleButton)
    expect(passwordInput).toHaveAttribute('type', 'text')
    
    fireEvent.click(toggleButton)
    expect(passwordInput).toHaveAttribute('type', 'password')
  })

  it('should navigate to forgot password page', () => {
    render(<LoginPage />)
    
    const forgotPasswordLink = screen.getByRole('link', { name: /نسيت كلمة المرور/i })
    fireEvent.click(forgotPasswordLink)
    
    expect(mockNavigate).toHaveBeenCalledWith('/forgot-password')
  })

  it('should redirect authenticated users', () => {
    mockAuthContext.isAuthenticated = true
    mockAuthContext.user = { id: '1', email: '<EMAIL>' }
    
    render(<LoginPage />)
    
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard')
  })

  it('should handle remember me checkbox', () => {
    render(<LoginPage />)
    
    const rememberMeCheckbox = screen.getByLabelText(/تذكرني/i)
    
    expect(rememberMeCheckbox).not.toBeChecked()
    
    fireEvent.click(rememberMeCheckbox)
    expect(rememberMeCheckbox).toBeChecked()
  })

  it('should support keyboard navigation', () => {
    render(<LoginPage />)
    
    const emailInput = screen.getByLabelText(/البريد الإلكتروني/i)
    const passwordInput = screen.getByLabelText(/كلمة المرور/i)
    const submitButton = screen.getByRole('button', { name: /تسجيل الدخول/i })
    
    // اختبار التنقل بـ Tab
    emailInput.focus()
    expect(emailInput).toHaveFocus()
    
    fireEvent.keyDown(emailInput, { key: 'Tab' })
    expect(passwordInput).toHaveFocus()
    
    fireEvent.keyDown(passwordInput, { key: 'Tab' })
    expect(submitButton).toHaveFocus()
  })

  it('should handle form submission with Enter key', async () => {
    mockLogin.mockResolvedValue({ success: true })
    
    render(<LoginPage />)
    
    const emailInput = screen.getByLabelText(/البريد الإلكتروني/i)
    const passwordInput = screen.getByLabelText(/كلمة المرور/i)
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.keyDown(passwordInput, { key: 'Enter' })
    
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123')
    })
  })

  it('should clear error message on input change', async () => {
    mockLogin.mockRejectedValue(new Error('خطأ في تسجيل الدخول'))
    
    render(<LoginPage />)
    
    const emailInput = screen.getByLabelText(/البريد الإلكتروني/i)
    const passwordInput = screen.getByLabelText(/كلمة المرور/i)
    const submitButton = screen.getByRole('button', { name: /تسجيل الدخول/i })
    
    // إثارة خطأ
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } })
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText(/خطأ في تسجيل الدخول/i)).toBeInTheDocument()
    })
    
    // تغيير المدخل يجب أن يمحو الخطأ
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    
    expect(screen.queryByText(/خطأ في تسجيل الدخول/i)).not.toBeInTheDocument()
  })

  it('should handle different user roles after login', async () => {
    const adminUser = { id: '1', email: '<EMAIL>', role: 'admin' }
    mockLogin.mockResolvedValue({ user: adminUser })
    
    render(<LoginPage />)
    
    const emailInput = screen.getByLabelText(/البريد الإلكتروني/i)
    const passwordInput = screen.getByLabelText(/كلمة المرور/i)
    const submitButton = screen.getByRole('button', { name: /تسجيل الدخول/i })
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard')
    })
  })
})
