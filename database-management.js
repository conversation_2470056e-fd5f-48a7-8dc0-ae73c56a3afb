/**
 * إدارة قاعدة البيانات والتهجيرات وسياسات الأمان
 * Database Management and Migration Script
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// تحميل متغيرات البيئة
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ متغيرات البيئة مفقودة!');
  process.exit(1);
}

// إنشاء عميل إداري
const supabase = createClient(supabaseUrl, supabaseServiceKey);

class DatabaseManager {
  constructor() {
    this.supabase = supabase;
  }

  /**
   * تشغيل استعلام SQL مباشر
   */
  async executeSQL(query, description = '') {
    try {
      console.log(`🔧 ${description || 'تنفيذ استعلام SQL'}...`);
      
      const { data, error } = await this.supabase.rpc('exec_sql', {
        sql_query: query
      });

      if (error) {
        console.error('❌ خطأ في تنفيذ الاستعلام:', error);
        return { success: false, error };
      }

      console.log('✅ تم تنفيذ الاستعلام بنجاح');
      return { success: true, data };
    } catch (err) {
      console.error('❌ خطأ عام:', err);
      return { success: false, error: err };
    }
  }

  /**
   * تطبيق ملف تهجير
   */
  async applyMigration(filePath) {
    try {
      if (!fs.existsSync(filePath)) {
        console.error(`❌ ملف التهجير غير موجود: ${filePath}`);
        return false;
      }

      const sqlContent = fs.readFileSync(filePath, 'utf8');
      console.log(`📄 تطبيق تهجير: ${path.basename(filePath)}`);
      
      const result = await this.executeSQL(sqlContent, `تطبيق ${path.basename(filePath)}`);
      return result.success;
    } catch (error) {
      console.error('❌ خطأ في تطبيق التهجير:', error);
      return false;
    }
  }

  /**
   * تطبيق جميع التهجيرات في مجلد
   */
  async applyAllMigrations(migrationsDir = './supabase/migrations') {
    try {
      if (!fs.existsSync(migrationsDir)) {
        console.error(`❌ مجلد التهجيرات غير موجود: ${migrationsDir}`);
        return false;
      }

      const files = fs.readdirSync(migrationsDir)
        .filter(file => file.endsWith('.sql'))
        .sort();

      console.log(`📁 العثور على ${files.length} ملف تهجير`);

      let successCount = 0;
      for (const file of files) {
        const filePath = path.join(migrationsDir, file);
        const success = await this.applyMigration(filePath);
        if (success) successCount++;
      }

      console.log(`✅ تم تطبيق ${successCount}/${files.length} تهجير بنجاح`);
      return successCount === files.length;
    } catch (error) {
      console.error('❌ خطأ في تطبيق التهجيرات:', error);
      return false;
    }
  }

  /**
   * إنشاء مستخدم إداري
   */
  async createAdminUser(email, password, fullName) {
    try {
      console.log(`👤 إنشاء مستخدم إداري: ${email}`);

      const { data, error } = await this.supabase.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
        user_metadata: {
          full_name: fullName,
          role: 'admin'
        }
      });

      if (error) {
        console.error('❌ فشل إنشاء المستخدم الإداري:', error);
        return false;
      }

      // إضافة المستخدم إلى جدول users
      const { error: insertError } = await this.supabase
        .from('users')
        .insert({
          id: data.user.id,
          email: email,
          full_name: fullName,
          role: 'admin',
          is_active: true
        });

      if (insertError) {
        console.error('❌ فشل إضافة المستخدم إلى الجدول:', insertError);
        return false;
      }

      console.log('✅ تم إنشاء المستخدم الإداري بنجاح');
      return true;
    } catch (error) {
      console.error('❌ خطأ في إنشاء المستخدم الإداري:', error);
      return false;
    }
  }

  /**
   * تفعيل/إلغاء تفعيل RLS لجدول
   */
  async toggleRLS(tableName, enable = true) {
    const action = enable ? 'ENABLE' : 'DISABLE';
    const query = `ALTER TABLE ${tableName} ${action} ROW LEVEL SECURITY;`;
    
    return await this.executeSQL(query, `${action} RLS لجدول ${tableName}`);
  }

  /**
   * حذف جميع سياسات RLS لجدول
   */
  async dropAllPolicies(tableName) {
    try {
      console.log(`🗑️ حذف جميع سياسات ${tableName}...`);

      // الحصول على جميع السياسات
      const { data: policies, error } = await this.supabase
        .from('pg_policies')
        .select('policyname')
        .eq('tablename', tableName);

      if (error) {
        console.error('❌ خطأ في الحصول على السياسات:', error);
        return false;
      }

      // حذف كل سياسة
      for (const policy of policies) {
        const dropQuery = `DROP POLICY IF EXISTS "${policy.policyname}" ON ${tableName};`;
        await this.executeSQL(dropQuery, `حذف سياسة ${policy.policyname}`);
      }

      console.log(`✅ تم حذف ${policies.length} سياسة من ${tableName}`);
      return true;
    } catch (error) {
      console.error('❌ خطأ في حذف السياسات:', error);
      return false;
    }
  }

  /**
   * إنشاء سياسة RLS بسيطة
   */
  async createSimplePolicy(tableName, policyName, command, expression) {
    const query = `
      CREATE POLICY "${policyName}" ON ${tableName}
      FOR ${command}
      USING (${expression});
    `;
    
    return await this.executeSQL(query, `إنشاء سياسة ${policyName} لجدول ${tableName}`);
  }

  /**
   * فحص حالة قاعدة البيانات
   */
  async checkDatabaseHealth() {
    console.log('🏥 فحص حالة قاعدة البيانات...\n');

    // فحص الجداول الأساسية
    const tables = ['users', 'schools', 'buses', 'routes', 'students', 'notifications'];
    
    for (const table of tables) {
      try {
        const { count, error } = await this.supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        if (error) {
          console.log(`❌ ${table}: ${error.message}`);
        } else {
          console.log(`✅ ${table}: ${count} سجل`);
        }
      } catch (err) {
        console.log(`❌ ${table}: غير متاح`);
      }
    }

    // فحص RLS
    console.log('\n🛡️ فحص سياسات RLS:');
    for (const table of tables) {
      try {
        const { data: policies } = await this.supabase
          .from('pg_policies')
          .select('count(*)', { count: 'exact', head: true })
          .eq('tablename', table);
        
        console.log(`  ${table}: ${policies || 0} سياسة`);
      } catch (err) {
        console.log(`  ${table}: خطأ في فحص السياسات`);
      }
    }
  }

  /**
   * إعادة تعيين قاعدة البيانات (خطر!)
   */
  async resetDatabase() {
    console.log('⚠️ تحذير: إعادة تعيين قاعدة البيانات!');
    
    const tables = ['notifications', 'students', 'routes', 'buses', 'schools', 'users'];
    
    for (const table of tables) {
      // إلغاء RLS
      await this.toggleRLS(table, false);
      
      // حذف جميع السياسات
      await this.dropAllPolicies(table);
      
      // حذف البيانات (اختياري)
      // await this.executeSQL(`TRUNCATE TABLE ${table} CASCADE;`, `حذف بيانات ${table}`);
    }
    
    console.log('✅ تم إعادة تعيين قاعدة البيانات');
  }
}

// واجهة سطر الأوامر
async function main() {
  const dbManager = new DatabaseManager();
  const command = process.argv[2];

  switch (command) {
    case 'check':
      await dbManager.checkDatabaseHealth();
      break;
      
    case 'migrate':
      await dbManager.applyAllMigrations();
      break;
      
    case 'reset':
      await dbManager.resetDatabase();
      break;
      
    case 'create-admin':
      const email = process.argv[3];
      const password = process.argv[4];
      const name = process.argv[5] || 'مدير النظام';
      
      if (!email || !password) {
        console.error('❌ يجب تحديد البريد الإلكتروني وكلمة المرور');
        console.log('الاستخدام: node database-management.js create-admin <EMAIL> password123 "الاسم الكامل"');
        break;
      }
      
      await dbManager.createAdminUser(email, password, name);
      break;
      
    default:
      console.log('📋 الأوامر المتاحة:');
      console.log('  check       - فحص حالة قاعدة البيانات');
      console.log('  migrate     - تطبيق جميع التهجيرات');
      console.log('  reset       - إعادة تعيين قاعدة البيانات');
      console.log('  create-admin - إنشاء مستخدم إداري');
      break;
  }
}

// تشغيل البرنامج
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export default DatabaseManager;
