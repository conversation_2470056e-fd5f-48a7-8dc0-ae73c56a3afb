import { supabase } from "./supabase";

interface PushSubscriptionData {
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
}

interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  tag?: string;
  data?: any;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
  requireInteraction?: boolean;
}

class PushNotificationService {
  private registration: ServiceWorkerRegistration | null = null;
  private subscription: PushSubscription | null = null;
  private vapidPublicKey: string =
    "BEl62iUYgUivxIkv69yViEuiBIa40HI80NM9f8HtLlVLVWjbzHumfqHKOOWTA2wfbHBTHBD1rLpwPOl-oA8dDsA"; // VAPID public key - replace with your own
  private resubscriptionInterval: number | null = null;

  async initialize(): Promise<boolean> {
    if (!("serviceWorker" in navigator) || !("PushManager" in window)) {
      console.warn("Push notifications are not supported in this browser");
      return false;
    }

    try {
      // Register service worker
      this.registration = await navigator.serviceWorker.register("/sw.js");
      console.log("Service Worker registered successfully");

      // Wait for service worker to be ready
      await navigator.serviceWorker.ready;

      // Check for existing subscription
      this.subscription = await this.registration.pushManager.getSubscription();

      if (this.subscription) {
        // Verify subscription is still valid
        await this.verifySubscription();
      }

      // Start periodic subscription check
      this.startResubscriptionCheck();

      return true;
    } catch (error) {
      console.error("Failed to initialize push notifications:", error);
      return false;
    }
  }

  async requestPermission(): Promise<NotificationPermission> {
    if (!("Notification" in window)) {
      throw new Error("This browser does not support notifications");
    }

    let permission = Notification.permission;

    if (permission === "default") {
      permission = await Notification.requestPermission();
    }

    return permission;
  }

  async subscribe(
    userId: string,
    tenantId?: string,
  ): Promise<PushSubscription | null> {
    if (!this.registration) {
      throw new Error("Service worker not registered");
    }

    const permission = await this.requestPermission();
    if (permission !== "granted") {
      throw new Error("Notification permission denied");
    }

    try {
      // Subscribe to push notifications
      this.subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(this.vapidPublicKey),
      });

      // Save subscription to database
      await this.saveSubscription(this.subscription, userId, tenantId);

      return this.subscription;
    } catch (error) {
      console.error("Failed to subscribe to push notifications:", error);
      throw error;
    }
  }

  async unsubscribe(userId: string): Promise<boolean> {
    if (!this.subscription) {
      return true;
    }

    try {
      // Unsubscribe from push notifications
      const success = await this.subscription.unsubscribe();

      if (success) {
        // Remove subscription from database
        await this.removeSubscription(userId, this.subscription.endpoint);
        this.subscription = null;
      }

      return success;
    } catch (error) {
      console.error("Failed to unsubscribe from push notifications:", error);
      return false;
    }
  }

  async getSubscription(): Promise<PushSubscription | null> {
    if (!this.registration) {
      return null;
    }

    this.subscription = await this.registration.pushManager.getSubscription();
    return this.subscription;
  }

  async isSubscribed(): Promise<boolean> {
    const subscription = await this.getSubscription();
    return subscription !== null;
  }

  private async saveSubscription(
    subscription: PushSubscription,
    userId: string,
    tenantId?: string,
  ): Promise<void> {
    const subscriptionData = subscription.toJSON() as PushSubscriptionData;

    const { error } = await supabase.from("push_subscriptions").upsert(
      {
        user_id: userId,
        tenant_id: tenantId,
        endpoint: subscriptionData.endpoint,
        p256dh_key: subscriptionData.keys.p256dh,
        auth_key: subscriptionData.keys.auth,
        user_agent: navigator.userAgent,
        is_active: true,
        updated_at: new Date().toISOString(),
      },
      {
        onConflict: "user_id,endpoint",
      },
    );

    if (error) {
      throw new Error(`Failed to save subscription: ${error.message}`);
    }
  }

  private async removeSubscription(
    userId: string,
    endpoint: string,
  ): Promise<void> {
    const { error } = await supabase
      .from("push_subscriptions")
      .update({ is_active: false })
      .eq("user_id", userId)
      .eq("endpoint", endpoint);

    if (error) {
      console.error("Failed to remove subscription from database:", error);
    }
  }

  private async verifySubscription(): Promise<void> {
    if (!this.subscription) return;

    try {
      // Check if subscription is still valid by attempting to get it
      const currentSubscription =
        await this.registration?.pushManager.getSubscription();

      if (
        !currentSubscription ||
        currentSubscription.endpoint !== this.subscription.endpoint
      ) {
        // Subscription is invalid, clear it
        this.subscription = null;
      }
    } catch (error) {
      console.error("Error verifying subscription:", error);
      this.subscription = null;
    }
  }

  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = "=".repeat((4 - (base64String.length % 4)) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, "+")
      .replace(/_/g, "/");

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  // Method to send test notification
  async sendTestNotification(payload: NotificationPayload): Promise<void> {
    if (!this.subscription) {
      throw new Error("No active subscription");
    }

    // This would typically be done on the server, but for testing we can use the service worker
    if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: "TEST_NOTIFICATION",
        payload,
      });
    }
  }

  // Method to handle subscription expiry and resubscription
  async handleSubscriptionExpiry(
    userId: string,
    tenantId?: string,
  ): Promise<void> {
    try {
      // Check if current subscription is still valid
      const currentSubscription = await this.getSubscription();

      if (!currentSubscription) {
        // Try to resubscribe
        await this.subscribe(userId, tenantId);
        console.log("Successfully resubscribed to push notifications");
      }
    } catch (error) {
      console.error("Failed to handle subscription expiry:", error);
    }
  }

  // Start periodic subscription check
  private startResubscriptionCheck(): void {
    // Check subscription validity every 24 hours
    this.resubscriptionInterval = window.setInterval(
      async () => {
        try {
          const subscription = await this.getSubscription();
          if (!subscription) {
            console.log("Subscription expired, attempting resubscription");
            // Get user info from local storage or context if available
            const userInfo = this.getUserInfoFromStorage();
            if (userInfo) {
              await this.handleSubscriptionExpiry(
                userInfo.userId,
                userInfo.tenantId,
              );
            }
          }
        } catch (error) {
          console.error("Error during periodic subscription check:", error);
        }
      },
      24 * 60 * 60 * 1000,
    ); // 24 hours
  }

  // Stop periodic subscription check
  private stopResubscriptionCheck(): void {
    if (this.resubscriptionInterval) {
      clearInterval(this.resubscriptionInterval);
      this.resubscriptionInterval = null;
    }
  }

  // Get user info from storage (fallback for resubscription)
  private getUserInfoFromStorage(): {
    userId: string;
    tenantId?: string;
  } | null {
    try {
      const userInfo = localStorage.getItem("pushNotificationUserInfo");
      return userInfo ? JSON.parse(userInfo) : null;
    } catch {
      return null;
    }
  }

  // Save user info to storage for resubscription
  private saveUserInfoToStorage(userId: string, tenantId?: string): void {
    try {
      localStorage.setItem(
        "pushNotificationUserInfo",
        JSON.stringify({ userId, tenantId }),
      );
    } catch (error) {
      console.error("Failed to save user info to storage:", error);
    }
  }

  // Clear user info from storage
  private clearUserInfoFromStorage(): void {
    try {
      localStorage.removeItem("pushNotificationUserInfo");
    } catch (error) {
      console.error("Failed to clear user info from storage:", error);
    }
  }

  // Enhanced subscribe method with user info storage
  async subscribeWithStorage(
    userId: string,
    tenantId?: string,
  ): Promise<PushSubscription | null> {
    const subscription = await this.subscribe(userId, tenantId);
    if (subscription) {
      this.saveUserInfoToStorage(userId, tenantId);
    }
    return subscription;
  }

  // Enhanced unsubscribe method with user info cleanup
  async unsubscribeWithStorage(userId: string): Promise<boolean> {
    const success = await this.unsubscribe(userId);
    if (success) {
      this.clearUserInfoFromStorage();
      this.stopResubscriptionCheck();
    }
    return success;
  }

  // Cleanup method
  cleanup(): void {
    this.stopResubscriptionCheck();
    this.clearUserInfoFromStorage();
  }
}

// Export singleton instance
export const pushNotificationService = new PushNotificationService();

// Utility function to send push notifications via edge function
export async function sendPushNotification(
  userIds: string[],
  payload: NotificationPayload,
  tenantId?: string,
): Promise<{ success: boolean; sentCount: number; error?: string }> {
  try {
    const { data, error } = await supabase.functions.invoke(
      "supabase-functions-send-push-notification",
      {
        body: {
          userIds,
          payload,
          tenantId,
        },
      },
    );

    if (error) {
      throw new Error(error.message || "Failed to send push notifications");
    }

    return {
      success: data.success,
      sentCount: data.sentCount || 0,
    };
  } catch (error: any) {
    console.error("Failed to send push notification:", error);
    return {
      success: false,
      sentCount: 0,
      error: error.message || "Unknown error occurred",
    };
  }
}

// Utility function to send push notifications to all users in a tenant
export async function sendPushNotificationToTenant(
  tenantId: string,
  payload: NotificationPayload,
  userRoles?: string[],
): Promise<{ success: boolean; sentCount: number; error?: string }> {
  try {
    // Get all users in the tenant
    let query = supabase
      .from("users")
      .select("id")
      .eq("tenant_id", tenantId)
      .eq("is_active", true);

    if (userRoles && userRoles.length > 0) {
      query = query.in("role", userRoles);
    }

    const { data: users, error: usersError } = await query;

    if (usersError) {
      throw new Error(`Failed to get users: ${usersError.message}`);
    }

    if (!users || users.length === 0) {
      return {
        success: true,
        sentCount: 0,
      };
    }

    const userIds = users.map((user) => user.id);
    return await sendPushNotification(userIds, payload, tenantId);
  } catch (error: any) {
    console.error("Failed to send push notification to tenant:", error);
    return {
      success: false,
      sentCount: 0,
      error: error.message || "Unknown error occurred",
    };
  }
}

// Utility function to send emergency notifications
export async function sendEmergencyNotification(
  tenantId: string,
  payload: NotificationPayload,
): Promise<{ success: boolean; sentCount: number; error?: string }> {
  // Emergency notifications go to all users except students
  const emergencyRoles = [
    "admin",
    "school_manager",
    "supervisor",
    "driver",
    "parent",
  ];

  const emergencyPayload = {
    ...payload,
    requireInteraction: true,
    data: {
      ...payload.data,
      type: "emergency",
      priority: "high",
    },
  };

  return await sendPushNotificationToTenant(
    tenantId,
    emergencyPayload,
    emergencyRoles,
  );
}

// Utility function to get user's push subscription status
export async function getUserPushSubscriptionStatus(userId: string): Promise<{
  hasSubscription: boolean;
  isActive: boolean;
  subscription?: any;
}> {
  try {
    const { data: subscriptions, error } = await supabase
      .from("push_subscriptions")
      .select("*")
      .eq("user_id", userId)
      .eq("is_active", true)
      .limit(1);

    if (error) {
      throw new Error(`Failed to get subscription status: ${error.message}`);
    }

    const hasSubscription = subscriptions && subscriptions.length > 0;
    const subscription = hasSubscription ? subscriptions[0] : null;

    return {
      hasSubscription,
      isActive: hasSubscription,
      subscription,
    };
  } catch (error) {
    console.error("Failed to get push subscription status:", error);
    return {
      hasSubscription: false,
      isActive: false,
    };
  }
}

export default pushNotificationService;
