/**
 * Spacing Design Tokens
 * Centralized spacing system for consistent layout
 * Phase 3: UI/UX Enhancement
 */

export interface SpacingScale {
  0: string;
  px: string;
  0.5: string;
  1: string;
  1.5: string;
  2: string;
  2.5: string;
  3: string;
  3.5: string;
  4: string;
  5: string;
  6: string;
  7: string;
  8: string;
  9: string;
  10: string;
  11: string;
  12: string;
  14: string;
  16: string;
  20: string;
  24: string;
  28: string;
  32: string;
  36: string;
  40: string;
  44: string;
  48: string;
  52: string;
  56: string;
  60: string;
  64: string;
  72: string;
  80: string;
  96: string;
}

export interface SemanticSpacing {
  component: {
    padding: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
    margin: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
    gap: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
  };
  layout: {
    container: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      '2xl': string;
    };
    section: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
    grid: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
  };
  interactive: {
    touch: {
      min: string;
      comfortable: string;
      spacious: string;
    };
    focus: {
      offset: string;
      width: string;
    };
  };
}

/**
 * Base Spacing Scale
 * Based on 0.25rem (4px) increments
 */
export const spacing: SpacingScale = {
  0: '0',
  px: '1px',
  0.5: '0.125rem',  // 2px
  1: '0.25rem',     // 4px
  1.5: '0.375rem',  // 6px
  2: '0.5rem',      // 8px
  2.5: '0.625rem',  // 10px
  3: '0.75rem',     // 12px
  3.5: '0.875rem',  // 14px
  4: '1rem',        // 16px
  5: '1.25rem',     // 20px
  6: '1.5rem',      // 24px
  7: '1.75rem',     // 28px
  8: '2rem',        // 32px
  9: '2.25rem',     // 36px
  10: '2.5rem',     // 40px
  11: '2.75rem',    // 44px
  12: '3rem',       // 48px
  14: '3.5rem',     // 56px
  16: '4rem',       // 64px
  20: '5rem',       // 80px
  24: '6rem',       // 96px
  28: '7rem',       // 112px
  32: '8rem',       // 128px
  36: '9rem',       // 144px
  40: '10rem',      // 160px
  44: '11rem',      // 176px
  48: '12rem',      // 192px
  52: '13rem',      // 208px
  56: '14rem',      // 224px
  60: '15rem',      // 240px
  64: '16rem',      // 256px
  72: '18rem',      // 288px
  80: '20rem',      // 320px
  96: '24rem',      // 384px
};

/**
 * Semantic Spacing
 * Context-aware spacing tokens
 */
export const semanticSpacing: SemanticSpacing = {
  component: {
    padding: {
      xs: spacing[2],   // 8px
      sm: spacing[3],   // 12px
      md: spacing[4],   // 16px
      lg: spacing[6],   // 24px
      xl: spacing[8],   // 32px
    },
    margin: {
      xs: spacing[2],   // 8px
      sm: spacing[4],   // 16px
      md: spacing[6],   // 24px
      lg: spacing[8],   // 32px
      xl: spacing[12],  // 48px
    },
    gap: {
      xs: spacing[1],   // 4px
      sm: spacing[2],   // 8px
      md: spacing[4],   // 16px
      lg: spacing[6],   // 24px
      xl: spacing[8],   // 32px
    },
  },
  layout: {
    container: {
      xs: spacing[4],   // 16px
      sm: spacing[6],   // 24px
      md: spacing[8],   // 32px
      lg: spacing[12],  // 48px
      xl: spacing[16],  // 64px
      '2xl': spacing[20], // 80px
    },
    section: {
      xs: spacing[8],   // 32px
      sm: spacing[12],  // 48px
      md: spacing[16],  // 64px
      lg: spacing[20],  // 80px
      xl: spacing[24],  // 96px
    },
    grid: {
      xs: spacing[2],   // 8px
      sm: spacing[3],   // 12px
      md: spacing[4],   // 16px
      lg: spacing[6],   // 24px
      xl: spacing[8],   // 32px
    },
  },
  interactive: {
    touch: {
      min: spacing[11],        // 44px - minimum touch target
      comfortable: spacing[12], // 48px - comfortable touch target
      spacious: spacing[14],   // 56px - spacious touch target
    },
    focus: {
      offset: spacing[0.5],    // 2px - focus ring offset
      width: spacing[0.5],     // 2px - focus ring width
    },
  },
};

/**
 * Breakpoint-aware spacing
 */
export interface ResponsiveSpacing {
  xs?: string;
  sm?: string;
  md?: string;
  lg?: string;
  xl?: string;
  '2xl'?: string;
}

/**
 * Spacing utilities
 */
export const spacingUtils = {
  /**
   * Get responsive spacing value
   */
  getResponsiveSpacing(
    spacingValue: string | ResponsiveSpacing,
    breakpoint: keyof ResponsiveSpacing = 'md'
  ): string {
    if (typeof spacingValue === 'string') {
      return spacingValue;
    }
    
    // Find the appropriate value for the breakpoint
    const breakpoints: (keyof ResponsiveSpacing)[] = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
    const currentIndex = breakpoints.indexOf(breakpoint);
    
    // Look for the value at current breakpoint or fallback to smaller ones
    for (let i = currentIndex; i >= 0; i--) {
      const bp = breakpoints[i];
      if (spacingValue[bp]) {
        return spacingValue[bp]!;
      }
    }
    
    // Fallback to the first available value
    return spacingValue[breakpoints.find(bp => spacingValue[bp])!] || spacing[4];
  },

  /**
   * Convert spacing token to CSS custom property
   */
  toCSSCustomProperty(token: keyof SpacingScale): string {
    return `var(--spacing-${token})`;
  },

  /**
   * Generate CSS custom properties for all spacing tokens
   */
  generateCSSCustomProperties(): Record<string, string> {
    const properties: Record<string, string> = {};
    
    Object.entries(spacing).forEach(([key, value]) => {
      properties[`--spacing-${key}`] = value;
    });
    
    return properties;
  },

  /**
   * Calculate spacing for specific use cases
   */
  calculate: {
    /**
     * Calculate optimal padding for text content
     */
    textPadding(fontSize: string): string {
      const size = parseFloat(fontSize);
      if (size <= 14) return spacing[3];
      if (size <= 18) return spacing[4];
      if (size <= 24) return spacing[6];
      return spacing[8];
    },

    /**
     * Calculate optimal gap for flex/grid layouts
     */
    layoutGap(itemCount: number): string {
      if (itemCount <= 2) return spacing[6];
      if (itemCount <= 4) return spacing[4];
      if (itemCount <= 8) return spacing[3];
      return spacing[2];
    },

    /**
     * Calculate container padding based on screen size
     */
    containerPadding(screenWidth: number): string {
      if (screenWidth < 640) return spacing[4];   // mobile
      if (screenWidth < 768) return spacing[6];   // tablet
      if (screenWidth < 1024) return spacing[8];  // desktop
      if (screenWidth < 1280) return spacing[12]; // large desktop
      return spacing[16];                         // extra large
    },

    /**
     * Calculate section spacing
     */
    sectionSpacing(importance: 'low' | 'medium' | 'high'): string {
      switch (importance) {
        case 'low': return spacing[8];
        case 'medium': return spacing[12];
        case 'high': return spacing[16];
        default: return spacing[12];
      }
    },
  },

  /**
   * Spacing presets for common patterns
   */
  presets: {
    card: {
      padding: spacing[6],
      margin: spacing[4],
      gap: spacing[4],
    },
    form: {
      fieldGap: spacing[4],
      sectionGap: spacing[8],
      padding: spacing[6],
    },
    navigation: {
      itemGap: spacing[2],
      sectionGap: spacing[6],
      padding: spacing[4],
    },
    content: {
      paragraphGap: spacing[4],
      sectionGap: spacing[8],
      headingGap: spacing[6],
    },
    grid: {
      tightGap: spacing[2],
      normalGap: spacing[4],
      looseGap: spacing[6],
    },
  },

  /**
   * RTL-aware spacing utilities
   */
  rtl: {
    /**
     * Get margin/padding for RTL layouts
     */
    getLogicalSpacing(
      property: 'margin' | 'padding',
      side: 'start' | 'end' | 'top' | 'bottom',
      value: string
    ): Record<string, string> {
      const logicalProperties: Record<string, string> = {};
      
      if (side === 'start') {
        logicalProperties[`${property}-inline-start`] = value;
      } else if (side === 'end') {
        logicalProperties[`${property}-inline-end`] = value;
      } else {
        logicalProperties[`${property}-${side}`] = value;
      }
      
      return logicalProperties;
    },

    /**
     * Convert directional spacing to logical properties
     */
    toLogicalProperties(styles: {
      marginLeft?: string;
      marginRight?: string;
      paddingLeft?: string;
      paddingRight?: string;
      [key: string]: any;
    }): Record<string, string> {
      const logical: Record<string, string> = {};
      
      Object.entries(styles).forEach(([key, value]) => {
        switch (key) {
          case 'marginLeft':
            logical['margin-inline-start'] = value;
            break;
          case 'marginRight':
            logical['margin-inline-end'] = value;
            break;
          case 'paddingLeft':
            logical['padding-inline-start'] = value;
            break;
          case 'paddingRight':
            logical['padding-inline-end'] = value;
            break;
          default:
            logical[key] = value;
        }
      });
      
      return logical;
    },
  },
};
