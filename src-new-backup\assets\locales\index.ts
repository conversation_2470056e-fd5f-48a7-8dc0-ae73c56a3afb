import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";

import enTranslation from "./locales/en.json";
import arTranslation from "./locales/ar.json";

// Function to update document direction based on language
const updateDirection = (language: string) => {
  const direction = language === "ar" ? "rtl" : "ltr";
  document.documentElement.setAttribute("dir", direction);
  document.documentElement.setAttribute("lang", language);
  localStorage.setItem("direction", direction);
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        translation: enTranslation,
      },
      ar: {
        translation: arTranslation,
      },
    },
    fallbackLng: "en",
    debug: false,
    interpolation: {
      escapeValue: false,
    },
    detection: {
      order: ["localStorage", "navigator", "htmlTag"],
      caches: ["localStorage"],
    },
  });

// Update direction when language changes
i18n.on("languageChanged", (lng) => {
  updateDirection(lng);
});

// Set initial direction
updateDirection(i18n.language);

export default i18n;
